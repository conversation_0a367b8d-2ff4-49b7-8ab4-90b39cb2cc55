{"FileVersion": 3, "Version": 1, "VersionName": "1.0.0", "FriendlyName": "AURACRON - Sistema de Sígilos", "Description": "Sistema avançado de sígilos espectrais para MOBA 5x5 com fusão automática, reforge e efeitos visuais Niagara", "Category": "Gameplay", "CreatedBy": "AURACRON Team", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "AURACRON", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine", "GameplayAbilities", "UMG", "Niagara"]}], "Plugins": [{"Name": "GameplayAbilities", "Enabled": true, "MarketplaceURL": "", "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"]}, {"Name": "Niagara", "Enabled": true, "MarketplaceURL": "", "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"]}, {"Name": "EnhancedInput", "Enabled": true, "MarketplaceURL": "", "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"]}, {"Name": "ReplicationGraph", "Enabled": true, "MarketplaceURL": "", "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"]}], "TargetPlatforms": ["Win64", "Linux", "<PERSON>"], "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"], "SupportedPrograms": ["UnrealHeaderTool"]}