# 🌟 AURACRON - GAME DESIGN DOCUMENT UNIFICADO
**Versão**: 2.1 - Documento Corrigido  
**Data**: Janeiro de 2025  
**Plataforma**: Mobile (Android/iOS) + PC  
**Engine**: Unreal Engine 5.6  
**Tagline**: _"Domine os ambientes. Desperte o Auracron."_

---

## 📋 **ÍNDICE**
1. [Visão Geral](#visão-geral)
2. [Análise Competitiva](#análise-competitiva)
3. [Mecânicas Inovadoras](#mecânicas-inovadoras)
   - 3.1 [Sistema de Alternância de Mapas](#sistema-de-alternância-de-mapas)
   - 3.2 [Sistema de Sígilos Auracron](#sistema-de-sígilos-auracron)
   - 3.3 [Sistema de Combate Horizontal](#sistema-de-combate-horizontal)
   - 3.4 [Selva Adaptativa com IA](#selva-adaptativa-com-ia)
   - 3.5 [Objet<PERSON>s Procedurais](#objetivos-procedurais)
4. [Direção Visual e Arte](#direção-visual-e-arte)
5. [Arquitetura Técnica](#arquitetura-técnica)
6. [Progressão e Monetização](#progressão-e-monetização)

---

## 🎯 **VISÃO GERAL**

### **Conceito Central**
**AURACRON** é um MOBA 5v5 revolucionário que combina elementos tradicionais com **mapas dinâmicos alternantes** e **IA adaptativa**. O diferencial está na capacidade do mapa alternar entre ambientes durante a partida, criando experiências de combate horizontais únicas e objetivos procedurais.

### **Público-Alvo**
- **Primário**: Players de MOBA mobile (18-35 anos)
- **Secundário**: Gamers PC buscando inovação no gênero
- **Terciário**: Streamers/criadores de conteúdo

### **Pillars de Design**
1. **📐 EVOLUÇÃO CONSTANTE**: Mapas que mudam, estratégias que adaptam
2. **🎮 ACESSIBILIDADE INTELIGENTE**: Complexo para mestres, simples para iniciantes
3. **🤝 COOPERAÇÃO AMPLIADA**: Mecânicas que recompensam teamwork criativo
4. **⚡ INOVAÇÃO TECNOLÓGICA**: IA, procedural generation, physics avançada

### **Identidade da Marca**
- Nome oficial: **AURACRON**
- Universo: Reino dos Auracrons
- Domínios reservados: auracron.com / auracron.gg / auracron.game

---

## ⚔️ **ANÁLISE COMPETITIVA**

### **Wild Rift vs AURACRON**

| **ASPECTO** | **WILD RIFT** | **AURACRON** | **VANTAGEM** |
|-------------|---------------|---------------|--------------|
| **Mapa** | Estático, 3 lanes fixas | Dinâmico, 3 ambientes alternantes | 🟢 **NOSSA** |
| **Combate** | 2D horizontal | Alternância horizontal de ambientes | 🟢 **NOSSA** |
| **Objectives** | Fixos e previsíveis | Procedurais + IA adaptativa | 🟢 **NOSSA** |
| **Champions** | 164 fixos | Sistema de Sígilos Auracron | 🟢 **NOSSA** |
| **Jungle** | Spawn patterns fixos | IA que aprende e adapta | 🟢 **NOSSA** |
| **Reconhecimento** | ✅ Brand estabelecido | ❌ Brand novo | 🔴 **DELES** |
| **Player Base** | ✅ 50M+ players | ❌ Zero players | 🔴 **DELES** |
| **Recursos** | ✅ Budget Riot Games | ❌ Indie/Startup | 🔴 **DELES** |

### **Outros Competidores**
- **Mobile Legends**: Nosso diferencial é superior tecnicamente
- **Arena of Valor**: Inovação vs established gameplay
- **Heroes Evolved**: Superamos em todos os aspectos técnicos

---

## 🚀 **MECÂNICAS INOVADORAS**

### **1. SISTEMA DE ALTERNÂNCIA DE MAPAS** 🌍

#### **Estrutura de Ambientes (Nomenclatura Própria)**

##### **I. PLANÍCIE RADIANTE**
**Função**: Campo base acessível com três Trilhos principais
**Características Geológicas**:
- **Platôs Cristalinos**: Plataformas elevadas com nós de recursos que mudam de elevação durante as fases da partida
- **Cânions Vivos**: Ravinas profundas que se expandem/contraem baseadas em ações dos jogadores, revelando passagens ocultas
- **Florestas Respirantes**: Clusters orgânicos de árvores que migram pelo mapa, fornecendo cobertura dinâmica
- **Pontes Tectônicas**: Pontes naturais de pedra que se formam e desmoronam baseadas no timer da partida
- **Respiradouros Geotermais**: Fornecem mobilidade entre ambientes, ativam periodicamente

**Objetivos Exclusivos**:
- **Guardião Prismal**: Empurra rota e concede controle territorial
- **Torre Prismal**: Aura de dano em área que protege objetivos chave

##### **II. FIRMAMENTO ZEPHYR**
**Função**: Plataformas flutuantes; domínio de visão aérea
**Características Celestiais**:
- **Arquipélagos Orbitais**: Cadeias de ilhas flutuantes que orbitam ao redor de pontos centrais do nexus
- **Pontes Aurora**: Caminhos baseados em luz que aparecem/desaparecem com ciclos dia/noite
- **Fortalezas Nuvem**: Posições defensivas que derivam pelo mapa
- **Jardins Estelares**: Áreas ricas em recursos com campos de baixa gravidade
- **Fendas do Vazio**: Pontos de teletransporte entre plataformas celestiais distantes

**Objetivos Exclusivos**:
- **Núcleo de Tempestade**: Buff ofensivo que aumenta dano de área
- **Santuários dos Ventos**: Reduz recarga de habilidades de mobilidade

##### **III. REINO PURGATÓRIO**
**Função**: Dimensão espelhada com mecânicas invertidas; foco em estratégia reversa
**Características Dimensionais**:
- **Planícies Espectrais**: Versão invertida das Planícies Radiantes com física alterada
- **Rios de Almas**: Correntes etéreas que fluem na direção oposta ao Fluxo Prismal
- **Estruturas Fragmentadas**: Versões corrompidas dos marcos terrestres com propriedades únicas
- **Zonas de Distorção Temporal**: Áreas onde o tempo flui diferentemente
- **Nexos Sombrios**: Pontos de controle que espelham os objetivos terrestres

**Objetivos Exclusivos**:
- **Guardião Espectral**: Versão sombria do Guardião Prismal com habilidades invertidas
- **Torres de Lamentação**: Estruturas defensivas que drenam energia dos inimigos

#### **Fluxo Prismal - O Núcleo Serpentino**

##### **Conceito de Design**
Um rio de energia massivo, similar a uma serpente, que serpenteia através dos três ambientes, servindo como o principal objetivo e espinha dorsal estratégica do mapa.

**Características Físicas**:
- **Largura**: Varia de 20-50 unidades, criando pontos de estrangulamento naturais
- **Padrão de Fluxo**: Caminho serpentino que muda a cada 10 minutos
- **Design Visual**: Energia prismática que muda de cor baseada na equipe controladora
- **Força da Corrente**: Velocidade de fluxo variável afeta movimento e habilidades

##### **Ilha Estratégica no Fluxo Prismal**

**Ilha Central Auracron (1 total)**
- **Localização**: Posicionada na curva central do Fluxo Prismal
- **Características**: Torre de controle central com múltiplas funcionalidades:
  - Setor Nexus: Geradores de recursos e manipulação do Fluxo
  - Setor Santuário: Fontes de cura e amplificadores de visão
  - Setor Arsenal: Upgrades de armas e potencializadores de habilidades
  - Setor Caos: Perigos ambientais com recompensas de alto risco
- **Valor Estratégico**: Controle total da ilha concede acesso a todos os benefícios, mas controle parcial permite acesso apenas aos setores dominados

#### **Timeline da Partida - Fases de Evolução**

**FASE 1: DESPERTAR (0-15 minutos) - Acessível**
- **Dispositivos Entry**: Apenas Planície Radiante ativa, outros ambientes como "preview zones"
- **Dispositivos Mid/High**: Todos os ambientes estáveis e acessíveis
- Trilhos a 50% de poder, efeitos visuais adaptados ao hardware
- Fluxo Prismal flui em padrão predeterminado
- Todas as ilhas totalmente emergidas
- Deformação de terreno opcional (apenas em dispositivos capazes)

**FASE 2: CONVERGÊNCIA (15-25 minutos) - Escalável**
- **Dispositivos Entry**: Transição suave para Firmamento Zephyr, Reino Purgatório como área de preview
- **Dispositivos Mid**: 2 ambientes simultâneos com transições simplificadas
- **Dispositivos High**: Fronteiras entre ambientes começam a se confundir
- Trilhos atingem poder baseado na capacidade do dispositivo
- Corrente do Fluxo Prismal se fortalece gradualmente
- Mudanças de ilhas adaptadas à performance do dispositivo

**FASE 3: INTENSIFICAÇÃO (25-35 minutos) - Adaptativa**
- **Dispositivos Entry**: Foco em uma camada principal com elementos visuais das outras
- **Dispositivos Mid**: Mudanças moderadas de terreno, efeitos reduzidos
- **Dispositivos High**: Mudanças dramáticas de terreno completas
- Trilhos se intersectam baseado na capacidade de renderização
- Fluxo Prismal com volatilidade adaptada ao hardware
- Novos caminhos aparecem de forma escalonada

**FASE 4: RESOLUÇÃO (35+ minutos) - Unificada**
- **Todos os Dispositivos**: Convergência final adaptada à capacidade
- Mapa se contrai de forma proporcional à performance
- Trilhos convergem com efeitos escaláveis
- Surto final do Fluxo Prismal com intensidade adaptativa
- Efeitos finais ajustados automaticamente ao hardware

#### **Sistema de Trilhos Dinâmicos**

##### **Solar Trilhos**
- **Aparência**: Correntes de energia dourada que fluem através dos três ambientes
- **Função**: Fornece boost de velocidade de movimento e regeneração de vida
- **Comportamento Dinâmico**: Segue a posição do sol, mais forte ao meio-dia
- **Valor Estratégico**: Controla o ritmo do mapa e permite rotações agressivas
- **Mecânicas Especiais**: Partículas douradas que deixam rastros de luz, distorção de calor nas bordas

##### **Axis Trilhos**
- **Aparência**: Canais cinza/prata neutros que conectam pontos de transição entre ambientes
- **Função**: Permite transição instantânea entre ambientes
- **Comportamento Dinâmico**: Ativa baseado no controle de equipe dos pontos nexus
- **Valor Estratégico**: Crítico para estratégias multi-ambientes e ataques surpresa
- **Mecânicas Especiais**: Padrões geométricos prateados, efeitos de distorção gravitacional

##### **Lunar Trilhos**
- **Aparência**: Caminhos etéreos azul-branco visíveis apenas à noite
- **Função**: Concede furtividade e visão aprimorada
- **Comportamento Dinâmico**: Fases com ciclos lunares, cria rotas alternativas
- **Valor Estratégico**: Permite manobras de flanqueamento e operações secretas
- **Mecânicas Especiais**: Névoa azul suave, partículas de poeira estelar

#### **Sistema de Alternância de Mapas**

##### **Conceito Técnico**
Cada ambiente existe como uma **área distinta** dentro do mesmo mapa. Os portais servem como mecanismo de posicionamento tático, transportando jogadores para diferentes localizações estratégicas dentro do ambiente atual.

##### **Mecânica de Transição**
1. **Ativação**: Interação com **Portais de Posicionamento Tático**
2. **Fade Out/In**: Efeito visual de 1-2 segundos de transição
3. **Teletransporte**: O jogador é transportado para outro ponto estratégico no mesmo mapa
4. **Reposicionamento**: Jogadores aparecem em posições táticas predefinidas

##### **Portais de Posicionamento Tático**
- **Portais Radiantes**: Teletransporte para pontos estratégicos na Planície Radiante (energia dourada)
- **Portais Zephyr**: Teletransporte para pontos estratégicos no Firmamento Zephyr (energia prateada)
- **Portais Umbrais**: Teletransporte para pontos estratégicos no Reino Purgatório (energia violeta)

##### **Animações de Teletransporte**
**Portais Radiantes:**
- Fade out com partículas douradas
- Efeito de teletransporte com rastro de luz
- Materialização do jogador no ponto de destino com brilho dourado

**Portais Zephyr:**
- Dissolução com efeitos de vento
- Efeito de teletransporte com correntes de ar
- Materialização do jogador no ponto de destino com redemoinhos prateados

**Portais Umbrais:**
- Distorção espectral com energia violeta
- Efeito de teletransporte com sombras fluidas
- Materialização do jogador no ponto de destino com aura violeta

#### **Mecânicas Específicas por Mapa**

##### **Planície Radiante - Mapa Base**
- **Layout**: Estruturas tradicionais de MOBA
- **Recursos**: Cristais de energia e pontos de captura padrão
- **Objetivos**: Torres, inibidores e Nexus tradicional
- **Vantagem**: Familiaridade e estratégias clássicas

##### **Firmamento Zephyr - Mapa Elevado**
- **Layout**: Plataformas conectadas por pontes
- **Recursos**: Correntes de vento que aceleram movimento
- **Objetivos**: Torres aéreas e pontos de controle estratégicos
- **Vantagem**: Controle de rotas e posicionamento tático

##### **Reino Purgatório - Mapa Especial**
- **Layout**: Ambiente sombrio com caminhos alternativos
- **Recursos**: Energia espectral e cristais únicos
- **Objetivos**: Estruturas especiais com mecânicas diferenciadas
- **Vantagem**: Estratégias de emboscada e controle territorial

#### **Impacto Estratégico**
- **Early Game**: Foco em farming e positioning no mapa inicial
- **Mid Game**: Adaptação às mecânicas específicas de cada ambiente
- **Late Game**: Maestria na transição entre mapas e aproveitamento de vantagens únicas

### **2. SISTEMA DE SÍGILOS AURACRON** 👥 _(Fusion 2.0)_

#### **Mecânica Central**
- Durante a **tela de seleção de campeões**, cada jogador escolhe **1 de 3 "Sígilos Auracron"** (Tanque, Dano, Utilidade)
- O Sígilo funde-se ao campeão aos 6 min, desbloqueando árvore de habilidades alternativa
- Pode ser re-forjado no Nexus uma vez por partida (recarga global de 2 min)
- Cria combinatória de 50 campeões × 3 Sígilos = 150 arquétipos sem depender de cooperação específica

#### **Tipos de Sígilos**
| Sígilo | Bônus Passivo | Habilidade Exclusiva | Arquétipo-chave |
|--------|--------------|----------------------|-----------------|
| **Aegis** (Tanque) | +15% HP, Armadura adaptativa | "Murallion" – cria barreira circular 3s | Linha de Frente / Iniciador |
| **Ruin** (Dano) | +12% ATK / AP adaptativo | "Fracasso Prismal" – reset parcial de recarga | Explosão / Escaramuçador |
| **Vesper** (Utilidade) | +10% Vel. Move + 8% Recarga | "Sopro de Fluxo" – dash aliado + escudo | Roamer / Suporte |

#### **Impacto em Balanceamento**
- Incentiva expressão individual (paralelo às Runas de LoL)
- Mantém identidade "fusão" como pico de poder temático

### **3. SISTEMA DE COMBATE HORIZONTAL** ➡️

#### **Combate Unificado**
- **Características**: Combate tradicional de MOBA em ambiente único
- **Alcance**: Padrão (800 unidades)
- **Área de Efeito**: Média (300 unidades)
- **Interação**: Focada em posicionamento tático horizontal

#### **Alternância de Mapas**
- **Características**: Diferentes ambientes com mecânicas únicas
- **Transições**: Instantâneas entre mapas completos
- **Vantagem**: Adaptação estratégica a diferentes terrenos
- **Mecânicas**: Cada mapa oferece vantagens táticas específicas

### **4. SELVA ADAPTATIVA COM IA** 🤖

#### **Sistema de Aprendizado Adaptativo**
O sistema de IA da selva utiliza machine learning para analisar padrões de comportamento dos jogadores e adaptar dinamicamente o ambiente de jogo:

- **Análise de Padrões**: Monitora comportamentos individuais e estratégias de equipe
- **Adaptação de Spawns**: Ajusta dificuldade e timing dos camps baseado nos padrões de clear
- **Objetivos Dinâmicos**: Cria contra-objetivos quando detecta foco excessivo na selva
- **Previsão Estratégica**: Antecipa estratégias baseadas na composição de equipe e histórico

#### **Elementos Adaptativos**
- **Spawn de Acampamentos**: Baseado em padrões de limpeza
- **Timing de Objetivos**: Adaptado ao ritmo da partida
- **Comportamento de Criaturas**: "Lembram" de encontros anteriores
- **Escalonamento de Recompensas**: Balanceamento dinâmico baseado em performance

### **5. OBJETIVOS PROCEDURAIS** 🎲

#### **Sistema de Geração Dinâmica**
Os objetivos são gerados proceduralmente baseados no estado atual da partida:

- **Análise de Estado**: Monitora tempo de jogo, diferença de kills e ouro entre equipes
- **Objetivos de Recuperação**: Spawnam quando uma equipe está significativamente atrás
- **Recompensas de Agressão**: Incentivam combate precoce e ativo
- **Forçadores de Engajamento**: Criam situações que obrigam team fights quando o jogo está muito passivo

#### **Tipos de Objetivos Procedurais**
1. **Fragmentos Auracron**: Mini-objetivos espalhados que constroem para buff maior
2. **Fendas Temporais**: Permite "rewind" de 10 segundos em área específica
3. **Âncoras de Ambiente**: Controlam qual ambiente está ativo
4. **Catalisadores de Fusão**: Reduzem cooldown de Sígilos Auracron
5. **Portais de Transição**: Ativam transições entre ambientes

#### **Categorização**
- **Essenciais**: Sempre presentes
- **Recuperação**: Ativados quando uma equipe está >10% atrás em ouro/kills


### **11. TERMINOLOGIA PADRONIZADA**

| Antigo termo | Novo termo Nexus |
|--------------|-----------------|
| Lane | Trilho |
| Brush | Canopy |
| Ward | Baliza |
| River | Fluxo |
| Baron/Herald | Guardião Prismal / Núcleo de Tempestade |
| Dragon | Leviatã Umbrático |

---

## 🎨 **DIREÇÃO VISUAL E ARTE**

### **Identidade Visual Específica por Camada**

#### **Planície Radiante - Linguagem Visual Terrestre**

**Paleta de Cores:**
- **Primárias**: Verde esmeralda profundo, marrons terrosos ricos
- **Secundárias**: Azuis cristalinos, laranjas vulcânicos
- **Acentos**: Dourados metálicos para nós de recursos

**Filosofia de Texturas:**
- Superfícies ásperas e táteis com padrões de erosão visíveis
- Musgo vivo e vegetação crescendo nas estruturas
- Efeitos de intemperismo dinâmicos baseados na progressão da partida

**Abordagem de Iluminação:**
- Ciclo natural de luz solar com sombras realistas
- Efeitos de névoa em vales durante amanhecer/anoitecer
- Plantas bioluminescentes fornecem iluminação noturna sutil

#### **Firmamento Zephyr - Linguagem Visual Celestial**

**Paleta de Cores:**
- **Primárias**: Roxos suaves, brancos etéreos
- **Secundárias**: Verdes aurora, azuis cósmicos
- **Acentos**: Pratas luz das estrelas para correntes de energia

**Filosofia de Texturas:**
- Superfícies translúcidas, similares a vidro para plataformas
- Texturas de nuvens etéreas que reagem ao movimento do jogador
- Padrões de constelações incorporados nas estruturas

**Abordagem de Iluminação:**
- Brilho estelar ambiente de todas as direções
- Refrações de luz prismática através de cristais flutuantes
- Efeitos dinâmicos de aurora durante eventos celestiais

#### **Reino Purgatório - Linguagem Visual Espectral**

**Paleta de Cores:**
- **Primárias**: Violetas espectrais, cinzas etéreos
- **Secundárias**: Azuis fantasmagóricos, vermelhos sangue
- **Acentos**: Pratas espectrais para energia espectral

**Filosofia de Texturas:**
- Superfícies que parecem "vazadas" ou semi-transparentes
- Estruturas terrestres corrompidas com rachaduras luminosas
- Reflexos distorcidos que não correspondem à realidade
- Materiais que parecem existir entre dimensões

**Abordagem de Iluminação:**
- Iluminação invertida: sombras brilham, luzes escurecem
- Auras espectrais emanando de objetos e personagens
- Distorções de luz que criam efeitos de "glitch" espectral
- Pulsos de energia violeta que revelam a natureza espelhada do reino

**Efeitos Visuais Únicos:**
- **Espelhamento Fantasmagórico**: Estruturas aparecem como reflexos distorcidos
- **Fragmentação Espectral**: Objetos parecem quebrados entre realidades
- **Inversão Cromática**: Cores se invertem periodicamente
- **Rastros Espectrais**: Movimentos deixam ecos visuais temporários

### **Design Visual dos Trilhos**

**Solar Trilhos**: Partículas douradas com efeitos de calor e lens flare
**Axis Trilhos**: Padrões geométricos prateados com distorção gravitacional
**Lunar Trilhos**: Névoa azul etérea com efeitos de mudança de fase

### **Dinâmicas Visuais do Fluxo Prismal**

#### **Aparência do Estado Base**
- **Superfície**: Textura de cristal líquido com reflexões prismáticas
- **Profundidade**: Múltiplas camadas visíveis através da transparência
- **Movimento**: Fluxo serpentino com velocidades de corrente variáveis
- **Bordas**: Tentáculos de energia que se estendem em direção a objetos próximos

#### **Estados de Controle de Equipe**
**Estado Neutro:**
- Efeito prismático puro branco/arco-íris
- Padrões de fluxo calmos e previsíveis
- Luminosidade média

**Controle Equipe A:**
- Muda para cor primária da equipe
- Padrões de fluxo agressivos e rápidos
- Alta luminosidade com faíscas de energia

**Controle Equipe B:**
- Muda para cor secundária da equipe
- Padrões de fluxo defensivos e lentos
- Luminosidade pulsante com efeitos de escudo

#### **Integração das Ilhas**
**Ancoragem Visual:**
- Ilhas projetam sombras prismáticas no Fluxo
- Pontes de energia se formam entre ilhas e Fluxo
- Formações cristalinas crescem onde o Fluxo toca a terra

### **Efeitos de Evolução e Narrativa Ambiental**

**Transições de Fase**
- Efeitos visuais progressivos que intensificam com cada fase
- Mudanças geológicas e atmosféricas dinâmicas
- Convergência final com distorções temporais

**Elementos Ambientais**
- Ruínas antigas que reagem à presença dos jogadores
- Vegetação e criaturas que respondem ao combate
- Sistemas climáticos dinâmicos

### **Indicadores Visuais Estratégicos**

**Controle Territorial**: Partículas coloridas e mudanças de textura indicam influência das equipes
**Estados de Recursos**: Cristais com diferentes níveis de brilho e atividade
**Zonas de Perigo**: Fronteiras visuais e símbolos de aviso com contagem regressiva

### **Transições Visuais Entre Mapas**

#### **Sistema de Alternância de Ambientes**

**Conceito Visual:**
- Cada ambiente existe como um mapa independente completo
- Transições são substituições instantâneas com efeitos visuais
- Portais de Transição servem como pontos de ativação

**Transição para Planície Radiante:**
- **Duração**: 1-2 segundos
- **Efeito**: Materialização natural com partículas douradas
- **Sequência**:
  1. Fade out do ambiente atual
  2. Partículas douradas preenchem a tela
  3. Fade in com vegetação emergindo
  4. Cristais se materializam no terreno

**Transição para Firmamento Zephyr:**
- **Duração**: 1-2 segundos
- **Efeito**: Formação de plataformas com efeitos de vento
- **Sequência**:
  1. Dissolução com rajadas de vento
  2. Plataformas se materializam no ar
  3. Pontes de energia se formam
  4. Correntes de vento se tornam visíveis

**Transição para Reino Purgatório:**
- **Duração**: 1-2 segundos
- **Efeito**: Materialização sombria com energia espectral
- **Sequência**:
  1. Escuridão gradual envolve o ambiente
  2. Energia violeta pulsa pela tela
  3. Estruturas sombrias emergem
  4. Cristais espectrais se materializam

#### **Indicadores Visuais de Mapa Ativo**
- **HUD**: Ícone indicando o ambiente atual
- **Borda da Tela**: Coloração que reflete o mapa ativo
- **Minimap**: Estilo visual adapta-se ao ambiente atual

### **Otimização de Performance Visual Acessível**

#### **Estratégia LOD Adaptativa por Hardware**

**Dispositivos Entry (2GB RAM, GPU básica):**
- **Próximo (0-50m)**: Geometria simplificada, partículas mínimas
- **Médio (50-150m)**: Geometria muito básica, sem partículas
- **Distante (150m+)**: Sprites 2D, sem detalhes

**Dispositivos Mid-range (3GB RAM, GPU intermediária):**
- **Próximo (0-75m)**: Geometria moderada, partículas reduzidas
- **Médio (75-200m)**: Geometria simplificada, partículas ocasionais
- **Distante (200m+)**: Geometria básica, sem partículas

**Dispositivos High-end (4GB+ RAM, GPU avançada):**
- **Próximo (0-100m)**: Detalhes completos, todas as partículas
- **Médio (100-300m)**: Partículas reduzidas, shaders simplificados
- **Distante (300m+)**: Geometria básica, partículas mínimas

#### **Orçamentos de Partículas Escaláveis**

**Entry Level:**
- **Trilhos**: 100 partículas por seção (apenas trilho ativo)
- **Fluxo Prismal**: 300 partículas por tela
- **Ambiental**: 200 partículas total
- **Efeitos de Combate**: 500 partículas máximo

**Mid-range:**
- **Trilhos**: 250 partículas por seção (2 trilhos máximo)
- **Fluxo Prismal**: 800 partículas por tela
- **Ambiental**: 500 partículas total
- **Efeitos de Combate**: 1500 partículas máximo

**High-end:**
- **Trilhos**: 500 partículas por seção (todos os trilhos)
- **Fluxo Prismal**: 2000 partículas por tela
- **Ambiental**: 1000 partículas total
- **Efeitos de Combate**: 3000 partículas máximo

#### **Sistema de Streaming Inteligente**
- **Preloading Preditivo**: Carrega apenas próximo realm provável
- **Unloading Agressivo**: Remove assets não utilizados rapidamente
- **Compressão Adaptativa**: Diferentes níveis de compressão por hardware
- **Fallback 2D**: Modo 2D completo para dispositivos muito limitados

#### **Modos de Acessibilidade**

**Modo Performance (Entry devices):**
- **Realms Simplificados**: Apenas 1 realm ativo por vez
- **Trilhos Básicos**: Apenas indicadores visuais simples
- **Efeitos Mínimos**: Sem partículas decorativas
- **UI Simplificada**: Interface otimizada para telas pequenas

**Modo Balanceado (Mid-range):**
- **2 Realms Simultâneos**: Transições mais rápidas
- **Trilhos Moderados**: Efeitos reduzidos mas visíveis
- **Efeitos Seletivos**: Apenas efeitos importantes para gameplay
- **UI Adaptativa**: Interface que se ajusta ao tamanho da tela

**Modo Qualidade (High-end):**
- **Todos os Realms**: Experiência visual completa
- **Trilhos Completos**: Todos os efeitos visuais
- **Efeitos Completos**: Experiência visual máxima
- **UI Avançada**: Interface com todos os detalhes visuais

---

## 🔧 **SISTEMAS TÉCNICOS**

### **Arquitetura Core - Unreal Engine 5.6**

#### **🛠️ TECH STACK DETALHADO**

**Core Engine: Unreal Engine 5.6 - Configuração Escalável**

**Recursos Adaptativos por Hardware:**

**Entry Level (2-3GB RAM):**
- **Lumen**: Desabilitado, iluminação estática pré-calculada
- **Nanite**: Desabilitado, geometria tradicional otimizada
- **Chaos Physics**: Física simplificada, sem destruição de terreno
- **MetaHuman**: Personagens simplificados com animações básicas
- **World Partition**: Streaming básico com chunks maiores
- **Rendering**: Forward rendering, sem ray tracing

**Mid-Range (3-4GB RAM):**
- **Lumen**: Lumen simplificado apenas para áreas principais
- **Nanite**: Nanite seletivo para objetos principais
- **Chaos Physics**: Física moderada com destruição limitada
- **MetaHuman**: Personagens com qualidade média
- **World Partition**: Streaming otimizado com preloading
- **Rendering**: Deferred rendering básico, TSR habilitado

**High-End (4GB+ RAM):**
- **Lumen**: Sistema completo de iluminação global dinâmica
- **Nanite**: Geometria virtualizada completa
- **Chaos Physics**: Sistema completo de física e destruição
- **MetaHuman**: Personagens com qualidade máxima
- **World Partition**: Streaming avançado com predição
- **Rendering**: Rendering completo com ray tracing opcional

**Sistemas de Renderização Adaptativos**
- **Virtual Shadow Maps**: Habilitado apenas em hardware compatível
- **Temporal Super Resolution (TSR)**: Upscaling inteligente para dispositivos mid/high
- **Hardware Ray Tracing**: Opcional, apenas em hardware dedicado
- **Variable Rate Shading**: Otimização automática baseada na capacidade do dispositivo

#### **Arquitetura de Rede Multiplayer**

**Servidor Autoritativo com Replicação Otimizada**
- **Replicação de Objetos Dinâmicos**: Sistema de replicação para Fluxo Prismal, Trilhos e elementos dinâmicos do ambiente
- **Controle de Estado de Equipe**: Replicação em tempo real do controle territorial e estados de objetivos
- **Sincronização de Transformações**: Replicação otimizada de mudanças de terreno e transições de ambiente

**Sistema de Predição do Cliente**
- **Predição de Movimento**: Predição de movimento para reduzir latência percebida
- **Predição de Habilidades**: Execução local de habilidades com validação do servidor
- **Rede com Rollback**: Sistema de rollback para correção de dessincronia
- **Compressão Delta**: Compressão de dados de rede para reduzir largura de banda

**Validação Anti-Cheat Server-Side**
- **Validação de Ações Críticas**: Todas as ações importantes são validadas no servidor
- **Verificação de Velocidade de Movimento**: Detecção de speed hacks e movimento impossível
- **Validação de Cooldowns**: Verificação server-side de cooldowns de habilidades
- **Controle de Recursos**: Validação de consumo e geração de recursos

#### **Sistema de IA Adaptativa da Selva**

**Integração de Aprendizado de Máquina**
- **Coleta de Dados Comportamentais**: Sistema que monitora e armazena padrões de comportamento dos jogadores
- **Modelo de Predição**: IA que aprende com dados históricos para prever ações futuras
- **Parâmetros de Adaptação**: Sistema que ajusta dinamicamente a dificuldade e comportamento da selva
- **Processamento de Padrões**: Análise em tempo real de estratégias e rotas dos jogadores

**Sistema de Spawn Adaptativo**
- **Reconhecimento de Padrões**: Análise de padrões de limpeza da selva por jogador
- **Dificuldade Dinâmica**: Ajuste automático de dificuldade baseado em performance
- **Spawn Preditivo**: Antecipação de rotas da selva baseada em histórico
- **Geração de Contra-Estratégias**: Criação de contra-estratégias para padrões repetitivos
- **Aprendizado Comportamental**: Sistema que "lembra" de encontros anteriores e adapta comportamento
- **Ajuste de Taxa de Spawn**: Modificação dinâmica de taxas de spawn baseada em análise comportamental

#### **Sistema de Geração Procedural de Objetivos**

**Procedural Objective Generator**
- **Geração Baseada no Estado do Jogo**: Sistema que analisa o estado atual da partida para gerar objetivos apropriados
- **Tipos de Objetivos Variados**: Pool de diferentes tipos de objetivos que podem ser spawned dinamicamente
- **Sistema de Pesos**: Algoritmo que determina a probabilidade de cada tipo de objetivo baseado no contexto
- **Parâmetros de Geração**: Cálculo dinâmico de parâmetros como localização, recompensas e dificuldade
- **Validação de Spawn**: Sistema que garante que objetivos são spawned em localizações estratégicas válidas

**Dynamic Balancing System**
- **Real-time Analytics**: Coleta de dados de performance em tempo real
- **Catch-up Mechanics**: Objetivos automáticos para equipes em desvantagem
- **Engagement Forcing**: Objetivos que forçam team fights quando o jogo está passivo
- **Reward Scaling**: Ajuste dinâmico de recompensas baseado no estado do jogo
- **Adaptive Timing**: Modificação de timing de objetivos baseada no ritmo da partida
- **Strategic Balancing**: Objetivos que incentivam diversidade estratégica

#### **Sistema de Alternância de Mapas**

**Gerenciador de Transição de Ambientes**
- **Troca de Mapas**: Sistema que coordena substituições instantâneas de ambientes
- **Efeitos de Transição**: Gerenciamento de efeitos visuais durante mudanças de mapa
- **Rastreamento de Ambiente Ativo**: Monitoramento de qual ambiente está atualmente ativo
- **Gerenciamento de Portais**: Sistema para gerenciar efeitos e timing de portais de transição
- **Pré-carregamento de Assets**: Carregamento antecipado de recursos necessários para próximos ambientes
- **Transições de Iluminação**: Mudanças suaves de iluminação entre diferentes ambientes
- **Atualizações de Portais**: Modificação dinâmica de portais de transição baseada no ambiente ativo

**Streaming Seamless de Ambientes**
- **Predictive Loading**: Pré-carregamento de assets baseado em probabilidade de transição
- **Memory Management**: Garbage collection inteligente de assets de ambientes não utilizados
- **Gerenciamento de LOD**: Otimização de nível de detalhe para diferentes ambientes
- **Ambiente de Áudio**: Adaptação do sistema de áudio 3D para cada ambiente
- **Asset Streaming**: Carregamento dinâmico de recursos baseado na proximidade de portais
- **Performance Optimization**: Otimização automática durante transições para manter framerate

#### **Backend Services & Infrastructure**

**Unreal Engine Multiplayer Framework**
- **Dedicated Servers**: Servidores dedicados para partidas ranqueadas
- **Listen Servers**: Servidores P2P para partidas casuais
- **Session Management**: Gerenciamento de sessões com Epic Online Services
- **Matchmaking**: Sistema de matchmaking baseado em skill rating

**Firebase Integration**
- **Gerenciamento de Dados Persistentes**: Sistema para salvar e carregar progresso do jogador
- **Carregamento de Progresso**: Sistema assíncrono para carregar dados do jogador
- **Atualização de Estatísticas**: Sistema para atualizar estatísticas de partida em tempo real
- **Inicialização do Firebase**: Processo de setup e configuração do Firebase
- **Tratamento de Erros**: Sistema robusto de tratamento de erros de conexão e dados
- **Componente Firebase**: Interface dedicada para comunicação com serviços Firebase

**Epic Online Services (EOS)**
- **Cross-Platform Friends**: Sistema de amigos cross-platform
- **Achievements**: Sistema unificado de conquistas
- **Leaderboards**: Classificações globais e regionais
- **Voice Chat**: Integração com Vivox para comunicação por voz
- **Anti-Cheat**: EOS Anti-Cheat para detecção de trapaças

**Analytics & Telemetria**
- **Sistema de Telemetria Customizado**: Coleta detalhada de dados de gameplay e performance
- **Rastreamento de Ações**: Monitoramento de ações específicas dos jogadores com parâmetros customizados
- **Eventos de Partida**: Coleta de dados sobre eventos importantes durante as partidas
- **Métricas de Performance**: Monitoramento de framerate, latência e uso de recursos
- **Envio em Lote**: Sistema otimizado para enviar dados de telemetria em batches
- **Processamento de Dados de Balanceamento**: Análise de dados para ajustes de balanceamento
- **Eventos Pendentes**: Sistema de queue para eventos de telemetria
- **Timer de Envio**: Gerenciamento automático de timing para envio de dados

#### **Sistema de Partículas e Otimização**

**Niagara Integration**
- Gerenciamento dinâmico de efeitos visuais dos Trilhos
- Sistemas únicos para Solar, Axis e Lunar Trilhos
- Otimização automática baseada em hardware e proximidade
- GPU-driven culling e LOD adaptativo

#### **Performance e Otimização Multiplataforma**

**Targets de Performance**
- **Mobile Entry**: 30 FPS, 720p, <2GB RAM
- **Mobile Mid/High**: 45-60 FPS, 1080p, <4GB RAM
- **PC**: 45-90 FPS, 1080p-1440p+, <12GB RAM

**Sistema Adaptativo**
- Detecção automática de hardware e ajuste de qualidade
- Três níveis de configuração (Entry/Mid/High-end)
- Gerenciamento inteligente de memória e assets
- Fallback automático para manter performance estável

#### **Arquitetura de Rede e Segurança**

**Sistema Multiplayer**
- Servidor autoritativo com otimizações de bandwidth
- Network prediction e lag compensation
- Interest management baseado em relevância espacial

**Anti-Cheat**
- Integração com Epic Online Services Anti-Cheat
- Detecção de speed hacks, bots e anomalias estatísticas
- Sistema de validação server-side para habilidades e movimento

#### **Integração Cross-Platform**

**Epic Online Services**
- Sistema unificado de amigos e matchmaking
- Progressão sincronizada entre dispositivos
- Voice chat multiplataforma
- Otimizações específicas para Mobile e PC

---

## 💰 **PROGRESSÃO E MONETIZAÇÃO**

### **Modelo de Monetização Ética**

#### **Battle Pass Evoluído**

**🎁 ADAPTIVE BATTLE PASS:**
- **Traditional Track**: Progressão linear padrão
- **Role-Specific Tracks**: Trilhas específicas por função (Tank, DPS, Support, Jungle, Mid)
- **Playstyle Tracks**: Trilhas por estilo de jogo (Agressivo, Defensivo, Estratégico)
- **Community Tracks**: Desbloqueadas através de objetivos comunitários

**Exemplo de Funcionamento:**
Jogador que atua principalmente como Support desbloqueia a Support Track:
- Skins exclusivas para campeões de suporte
- Customizações de Baliza
- Variações de VFX para cura e escudo
- Emotes e voice lines específicos de suporte

#### **Champion Acquisition Inclusivo**
- **Free Rotation**: 20 champions/week (vs 10 do Wild Rift) + rotação de "Community Favorites"
- **Earn Rate**: 1 novo champion/semana jogando casual + bônus por comportamento positivo
- **Currency**: Blue Essence (earned) + Realm Crystals (premium) + Harmony Tokens (comportamento positivo)
- **No P2W**: Champions purchasable apenas com earned currency
- **Accessibility Fund**: Champions gratuitos para jogadores com dificuldades financeiras

#### **Cosmetics Premium**

**✨ FUNCTIONAL COSMETICS:**

**Champion Skins**
- Alterações de modelo
- Customização de VFX
- Variações de voice pack
- Alterações na aparência dos Sígilos

**Map Themes (Votação Comunitária)**
- Aparências sazonais dos realms
- Efeitos climáticos
- Pacotes de som ambiente

**Elementos Customizáveis**
- Cores de partículas de habilidades
- Animações de recall
- Celebrações de vitória/derrota

### **Progression Systems**

#### **Account Level (1-500)**

**MARCOS DE PROGRESSÃO:**
- **Level 10**: Desbloqueio do modo ranqueado
- **Level 25**: Desbloqueio dos Sígilos Auracron
- **Level 50**: Rastreamento de maestria de realm
- **Level 100**: Criação de lobbies customizados
- **Level 200**: Privilégios de beta tester
- **Level 500**: Status lendário + recompensas únicas

#### **Champion Mastery (1-10)**

**PROGRESSÃO POR CAMPEÃO:**
- **Maestria 1-3**: Recompensas cosméticas básicas
- **Maestria 4-6**: Chromas avançados de skin
- **Maestria 7-8**: Emotes e animações exclusivos
- **Maestria 9-10**: Título de campeão + borda
- **Maestria 10**: Nomes customizados de habilidades + recompensas raras

#### **Realm Mastery (Sistema Novo)**

**PROGRESSÃO ESPECÍFICA POR REALM:**

**Planície Radiante - Expertise em Combate Terrestre**
- Bônus de XP na fase de trilhos
- Eficiência na limpeza da selva
- Prioridade em objetivos terrestres

**Firmamento Zephyr - Maestria em Combate Celestial**
- Bônus de dano no ambiente celestial
- Consciência de posicionamento vertical
- Sinergia com elementos aéreos

**Reino Purgatório - Expertise Espectral**
- Raio de detecção de furtividade
- Velocidade de navegação em dimensões
- Multiplicador de dano de emboscada


### **TERMINOLOGIA PADRONIZADA AURACRON**

| Termo Tradicional | Termo AURACRON |
|-------------------|------------------|
| Lane | Trilho |
| Brush | Canopy |
| Ward | Baliza |
| River | Fluxo Prismal |
| Baron/Herald | Guardião Prismal / Núcleo de Tempestade |
| Dragon | Leviatã Umbrático |
| Jungle | Selva Adaptativa |
| Minions | Servos Auracron |
| Nexus | Núcleo Auracron |

---

## 📝 **NOTAS IMPORTANTES**

### **Padronização de Nomenclatura**
- Toda documentação deve utilizar a terminologia AURACRON padronizada
- Evitar termos em inglês quando houver equivalente em português
- Manter consistência entre "Sígilos Auracron" (não "Champion Fusion")
- Usar "ambientes" ao invés de "mapas" ou "realms"
- Referir-se sempre ao "Fluxo Prismal" como elemento central

### **Estrutura de Ilha Unificada**
- Uma única Ilha Central Auracron com múltiplos setores
- Controle parcial ou total baseado na dominação de setores
- Mecânica estratégica mais focada e balanceada