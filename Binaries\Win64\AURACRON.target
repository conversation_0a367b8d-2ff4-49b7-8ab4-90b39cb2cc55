{"TargetName": "AURACRON", "Platform": "Win64", "Configuration": "Development", "TargetType": "Game", "IsTestTarget": false, "Architecture": "x64", "Project": "../../AURACRON.uproject", "Launch": "$(ProjectDir)/Binaries/Win64/AURACRON.exe", "Version": {"MajorVersion": 5, "MinorVersion": 6, "PatchVersion": 1, "Changelist": 44394996, "CompatibleChangelist": 43139311, "IsLicenseeVersion": 0, "IsPromotedBuild": 0, "BranchName": "++UE5+Release-5.6", "BuildId": "43139311"}, "BuildProducts": [{"Path": "$(ProjectDir)/Binaries/Win64/AURACRON.exe", "Type": "Executable"}, {"Path": "$(ProjectDir)/Binaries/Win64/AURACRON.pdb", "Type": "SymbolFile"}, {"Path": "$(ProjectDir)/Binaries/Win64/D3D12/x64/D3D12Core.dll", "Type": "DynamicLibrary"}, {"Path": "$(ProjectDir)/Binaries/Win64/D3D12/x64/d3d12SDKLayers.dll", "Type": "DynamicLibrary"}, {"Path": "$(ProjectDir)/Binaries/Win64/DML/x64/DirectML.dll", "Type": "DynamicLibrary"}, {"Path": "$(ProjectDir)/Binaries/Win64/tbb12.dll", "Type": "DynamicLibrary"}, {"Path": "$(ProjectDir)/Binaries/Win64/tbbmalloc.dll", "Type": "DynamicLibrary"}], "RuntimeDependencies": [{"Path": "$(ProjectDir)/AURACRON.uproject", "Type": "UFS"}, {"Path": "$(ProjectDir)/Binaries/Win64/D3D12/x64/D3D12Core.dll", "Type": "NonUFS"}, {"Path": "$(ProjectDir)/Binaries/Win64/D3D12/x64/d3d12SDKLayers.dll", "Type": "NonUFS"}, {"Path": "$(ProjectDir)/Binaries/Win64/DML/x64/DirectML.dll", "Type": "NonUFS"}, {"Path": "$(ProjectDir)/Binaries/Win64/tbb12.dll", "Type": "NonUFS"}, {"Path": "$(ProjectDir)/Binaries/Win64/tbbmalloc.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/DbgHelp/dbghelp.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/MsQuic/v220/win64/msquic.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/NVIDIA/NVaftermath/Win64/GFSDK_Aftermath_Lib.x64.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/Ogg/Win64/VS2015/libogg_64.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/Vorbis/Win64/VS2015/libvorbis_64.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/Vorbis/Win64/VS2015/libvorbisfile_64.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64/WinPixEventRuntime.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/Windows/XAudio2_9/x64/xaudio2_9redist.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Content/Renderer/TessellationTable.bin", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Content/SlateDebug/Fonts/LastResort.tps", "Type": "DebugNonUFS"}, {"Path": "$(EngineDir)/Content/SlateDebug/Fonts/LastResort.ttf", "Type": "DebugNonUFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/DeveloperDirectoryContent.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/EditorGroupBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/ErrorFilter.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/ExcludedTestsFilter.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/Fail.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/GameGroupBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/Groups.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/InProcess.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/NoSessionWarning.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/NotEnoughParticipants.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/NotRun.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/Participant.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/ParticipantsWarning.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/RefreshTests.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/RefreshWorkers.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/RunTests.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/SmokeTest.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/SmokeTestFilter.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/SmokeTestParent.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/StopTests.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/Success.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/TrackTestHistory.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/VisualCommandlet.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/Warning.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Automation/WarningFilter.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Checkerboard.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/BoxShadow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Button.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Button_Disabled.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Button_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Button_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Check.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CheckBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CheckBox_Checked.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CheckBox_Checked_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CheckBox_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CheckBox_Undetermined.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CheckBox_Undetermined_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Checker.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Circle.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorGradingWheel.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorPicker_Mode_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorPicker_Separator.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorPicker_SliderHandle.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorSpectrum.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorSpectrum_20x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorWheel.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColorWheel_20x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColumnHeader.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColumnHeaderMenuButton_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColumnHeaderMenuButton_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColumnHeader_Arrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ColumnHeader_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ComboArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/CursorPing.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/DarkGroupBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/DebugBorder.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Delimiter.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/DownArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/DropZoneIndicator_Above.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/DropZoneIndicator_Below.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/DropZoneIndicator_Onto.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/EditableTextSelectionBackground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/EventMessage_Default.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ExpansionButton_CloseOverlay.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/GroupBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/GroupBorder_Shape.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/HeaderSplitterGrip.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/LastColumnHeader_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/LeftArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/LightGroupBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/NoiseBackground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/PlainBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ProfileVisualizer_Mono.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ProfileVisualizer_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ProfileVisualizer_Selected.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ProgressBar_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ProgressBar_Fill.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ProgressBar_Marquee.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/RadioButton_SelectedBack_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/RadioButton_Selected_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/RadioButton_Unselected_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/RoundedSelection_16x.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ScrollBorderShadowBottom.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ScrollBorderShadowTop.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ScrollBoxShadowBottom.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ScrollBoxShadowLeft.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ScrollBoxShadowRight.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/ScrollBoxShadowTop.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Scrollbar_Background_Horizontal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Scrollbar_Background_Vertical.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Scrollbar_Thumb.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SearchGlass.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Selection.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Selector.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Separator.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheck.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheckBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheckBox_Checked.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheckBox_Checked_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheckBox_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheckBox_Undetermined.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SmallCheckBox_Undetermined_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SortDownArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SortDownArrows.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SortUpArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SortUpArrows.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SpinArrows.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Spinbox.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Spinbox_Fill.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Spinbox_Fill_Dark.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Spinbox_Fill_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Spinbox_Fill_Hovered_Dark.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Spinbox_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SplitterHandleHighlight.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/SubmenuArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TableViewHeader.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TableViewMajorColumn.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBlockHighlightShape.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBlockHighlightShape_Empty.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBoxLabelBorder.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox_Dark.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox_Hovered_Dark.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox_ReadOnly.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox_Special.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TextBox_Special_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Throbber_Piece.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TreeArrow_Collapsed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TreeArrow_Collapsed_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TreeArrow_Expanded.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/TreeArrow_Expanded_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/UpArrow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VerticalBoxDragIndicator.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VerticalBoxDragIndicatorShort.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VolumeControl_High.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VolumeControl_Low.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VolumeControl_Mid.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VolumeControl_Muted.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/VolumeControl_Off.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/WhiteGroupBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowBackground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Close_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Close_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Close_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Maximize_Disabled.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Maximize_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Maximize_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Maximize_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Minimize_Disabled.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Minimize_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Minimize_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Minimize_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Restore_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Restore_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowButton_Restore_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowOutline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowTitle.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowTitle_Flashing.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/Window/WindowTitle_Inactive.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/X.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Common/menu.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/CrashTracker/MouseCursor.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/CrashTracker/Record.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Cursor/invisible.cur", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTabContentArea.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTabWellSeparator.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTab_Active.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTab_ColorOverlay.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTab_ColorOverlayIcon.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTab_Foreground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTab_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/AppTab_Inactive.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/CloseApp_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/CloseApp_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/CloseApp_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/DockingIndicator_Center.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/OuterDockingIndicator.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/ShowTabwellButton_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/ShowTabwellButton_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/ShowTabwellButton_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/TabContentArea.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/TabWellSeparator.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_Active.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_ColorOverlay.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_ColorOverlayIcon.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_Foreground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_Inactive.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Docking/Tab_Shape.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/DroidSans.tps", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/DroidSansFallback.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/DroidSansMono.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Noto.tps", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/NotoNaskhArabicUI-Regular.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/NotoSansThai-Regular.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-Black.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-BlackItalic.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-Bold.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-BoldCondensed.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-BoldCondensedItalic.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-BoldItalic.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-Italic.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-Light.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-Medium.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto-Regular.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Fonts/Roboto.tps", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/BackIcon.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Cross_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/DefaultAppIcon.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Edit/icon_Edit_Copy_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Edit/icon_Edit_Cut_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Edit/icon_Edit_Delete_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Edit/icon_Edit_Duplicate_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Edit/icon_Edit_Paste_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Edit/icon_Edit_Rename_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Empty_14x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/NextIcon.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/SmallRoundedButton.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/SmallRoundedButtonBottom.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/SmallRoundedButtonCentre.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/SmallRoundedButtonLeft.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/SmallRoundedButtonRight.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/SmallRoundedButtonTop.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_025x_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_025x_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_025x_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_05x_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_05x_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_05x_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_1x_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_1x_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_1x_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_Screen_Rotation_Hovered.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_Screen_Rotation_Normal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PIEWindow/WindowButton_Screen_Rotation_Pressed.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/PlusSymbol_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/GroupBorder-16Gray.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Average_Event_Graph_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Border_L_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Border_R_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Border_TB_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Cull_Events_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Culled_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Custom_Tooltip_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Data_Capture_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Events_Flat_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Events_Flat_Coalesced_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Events_Hierarchial_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_FPS_Chart_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Filter_Events_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Filter_Presets_Tab_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Filtered_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Graph_View_Tab_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Has_Culled_Children_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_History_Back_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_History_Fwd_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_LoadMultiple_Profiler_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Load_Profiler_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Max_Event_Graph_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Settings_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_Tab_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/Profiler_ThreadView_SampleBorder_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_Calls_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_CollapseAll_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_CollapseSelection_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_CollapseThread_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_CopyToClipboard_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_CulledEvents_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_Disconnect_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_Event_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ExpandAll_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ExpandHotPath_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ExpandSelection_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ExpandThread_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_GameThread_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_GenericFilter_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_GenericGroup_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_HotPath_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_Memory_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_Number_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_OpenEventGraph_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_RenderThread_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ResetColumn_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ResetToDefault_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_SelectStack_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_SetRoot_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ShowGraphData_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_SortAscending_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_SortBy_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_SortDescending_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_ViewColumn_32x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_mem_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_stats_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/Profiler/profiler_sync_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/TrashCan.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/TrashCan_Small.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/cursor_cardinal_cross.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/cursor_grab.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/denied_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/ellipsis_12x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/eyedropper_16px.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_Downloads_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_error_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_generic_toolbar.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_help_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_info_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_redo_16px.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_tab_Tools_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_tab_WidgetReflector_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_tab_WidgetReflector_40x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_tab_toolbar_16px.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_undo_16px.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/icon_warning_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/notificationlist_fail.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/notificationlist_success.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Icons/toolbar_expand_16x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/All_Platforms_128x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/All_Platforms_24x.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Instance_Commandlet.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Instance_Editor.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Instance_Game.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Instance_Other.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Instance_Server.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Instance_Unknown.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Advanced.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Back.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Build.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Delete.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Deploy.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_EditSettings.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Launch.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Launcher/Launcher_Run.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/MessageLog/Log_Error.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/MessageLog/Log_Note.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/MessageLog/Log_Warning.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Border.PNG", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Button.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/DashedBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/HyperlinkDotted.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/HyperlinkUnderline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Menu_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Menu_Background_Inverted_Border_Bold.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Notification_Border_Flash.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/ActionMenuButtonBG.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/ArrowBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/ArrowLeft.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Arrow_D.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Arrow_L.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Arrow_R.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Arrow_U.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/BoxEdgeHighlight.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CalloutBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CalloutBox2.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CalloutBox3.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Callout_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Callout_Glow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Callout_Outline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CircleBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CircleBox2.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CodeBlock_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CodeBlock_Glow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/CodeBlock_Outline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DiamondBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DiamondBox_B.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DiamondBox_T.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedCircleBox_L.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedCircleBox_LR.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedCircleBox_LR_E.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedCircleBox_L_E.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedCircleBox_R.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedCircleBox_R_E.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedSquareBox_L.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedSquareBox_LR.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedSquareBox_LR_E.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedSquareBox_R.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/DottedSquareBox_R_E.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Hat.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Outer/alertOutline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Outer/alertSolid.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/PrePost_RoundedBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/PrePost_RoundedBox_B.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/PrePost_RoundedBox_T.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/QMark.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Roboto-Bold.ttf", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/RoundedBoxBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/RoundedTileFaded.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/RoundedTile_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/RoundedTile_Glow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/RoundedTile_Outline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/SolidWhite.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/SquareBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/SquareBox_Solid_L.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/SquigglyBox.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Tile_Highlight.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/Underline.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/bigdot.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/blank.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin_glow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin_head.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin_head_glow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin_highlight.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin_shadow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/pin_stick.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/pin/ping.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/selectionbar/selectionbar_0.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/selectionbar/selectionbar_1.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/selectionbar/selectionbar_2.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/Tiles/smalldot.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/ToolBar_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/ToolTip_Background.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/ToolTip_BrightBackground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Old/White.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Advanced.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/AllSavedAssets.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/AutomationTools.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/CPP.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Calendar.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ColorThemesOff_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ColorThemes_16.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ColorThemes_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Console.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Copy.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Cut.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Dash_Horizontal.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Dash_Vertical.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Delete.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Developer.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/DropTargetBackground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Duplicate.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/EditToolbar.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/EyeDropper.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/EyeDropper_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Favorite.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/FavoriteOutline.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/FilterAuto.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/FlipHorizontal.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/FlipVertical.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Group_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/HiddenInGame.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Info.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Layout.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Linked.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/LookAt.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Merge.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Monitor.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/OutputLog.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ParentHierarchy.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Paste.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/PlayerController.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Preferences.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ProjectLauncher.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Recent.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Redo.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Rename.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Role.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Rotate180.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Rotate90Clockwise.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Rotate90Counterclockwise.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Search_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/SelectAll_16.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/SessionFrontend.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/SortDown.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/SortUp.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/TagSimple_16.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Test.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/TokenTextBox_16.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/UELogo.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/UELogo.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Undo.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/UndoHistory.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Unlinked.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/UnsavedAssets.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/UnsavedAssetsWarning.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Update.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/VisibleInGame.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/Visualizer.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/add-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/alert-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/alert-triangle-64.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/alert-triangle-large.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/alert-triangle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/arrow-down.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/arrow-left.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/arrow-right.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/arrow-up.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/badge-modified.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/badge.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/blueprint.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/box-perspective.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/bullet-point.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/bullet-point16.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/caret-down.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/caret-right.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/check-circle-large.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/check-circle-solid.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/check-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/check.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/checker.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/chevron-down.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/chevron-left.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/chevron-right.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/chevron-up.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/circle-arrow-down.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/circle-arrow-left.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/circle-arrow-right.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/circle-arrow-up.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/close-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/close-small.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/close.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/color-grading-cross.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/color-grading-selector.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/color-grading-spinbox-selector-v.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/color-grading-spinbox-selector.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/curve-editor-append-key-20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/cylinder.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/delete-outline.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/drag-handle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/edit.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ellipsis-horizontal-narrow.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/ellipsis-vertical-narrow.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/export.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/export_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/favorites-category.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/fieldnotify_off.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/fieldnotify_on.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/file-tree-open.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/file-tree.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/file.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/filled-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/filter.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/folder-cleanup.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/folder-closed.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/folder-open.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/folder-plus.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/folder-virtual-closed.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/folder-virtual-open.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/help-solid.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/help.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/hidden.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/import.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/import_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/info-circle-solid.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/layout-header-body.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/layout-spreadsheet.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/lock-unlocked.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/lock.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/menu.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/minus-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/minus.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/normalize.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/play.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/plus-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/plus.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/preview-default.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/pyriamid.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/refresh.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/reimport.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/reject.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/save-modified.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/save.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/search.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/server.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/settings.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/sphere.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/stop.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/tile.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/unreal-circle-thick.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/unreal-circle-thin.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/unreal-small.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/visible.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/world.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Common/x-circle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/CheckBox/CheckBoxIndeterminate_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/CheckBox/CheckBoxIndeterminate_14.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/CheckBox/check.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/CheckBox/indeterminate.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/CheckBox/radio-off.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/CheckBox/radio-on.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/ComboBox/corner-dropdown.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/ComboBox/wide-chevron-down.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/FilterBar/FilterColorSegment.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/NumericEntryBox/NarrowDecorator.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/ProgressBar/ProgressMarquee.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/SegmentedBox/left.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/SegmentedBox/left.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/SegmentedBox/right.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/SegmentedBox/right.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/TableView/sort-down-arrow.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/TableView/sort-down-arrows.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/TableView/sort-up-arrow.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/TableView/sort-up-arrows.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/Window/close.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/Window/enter_fullscreen.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/Window/exit_fullscreen.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/Window/maximize.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/Window/minimize.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/CoreWidgets/Window/restore.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/DockTab_Active.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/DockTab_Foreground.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/DockTab_Hover.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/Dock_Tab_Active.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/drawer-shadow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/pin.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Docking/show-tab-well.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/AllTracks_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/AutoScrollDown_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/AutoScrollRight_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/BudgetSettings.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Callees.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Callees_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Callers.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Callers_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Connection.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/ControlsFirst.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/ControlsLast.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/ControlsNext.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/ControlsPrevious.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Counter.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Counter_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/CpuGpuTracks_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Filter.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/FilterConfig.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Frames.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Frames_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Function.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/HotPath_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/InfoTag_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Log.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Log_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemAllocTable.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemInvestigation.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemInvestigation_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTagSet_AssetClasses.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTagSet_Assets.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTagSet_Systems.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTag_Asset_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTag_Class_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTag_System_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTags.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/MemTags_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Memory.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/NetStats.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/NetStats_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Networking.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/PacketContent.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/PacketContent_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Packets.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Packets_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/PluginTracks_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/RoundedBullet.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Session.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SizeLarge.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SizeLarge_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SizeMedium.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SizeMedium_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SizeSmall.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SizeSmall_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/SpecialTracks_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Tasks.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Tasks_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TimeMarkerSettings.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Timer.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Timer_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Timing.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/Timing_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceStore.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceStore_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/RecordTraceCenter.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/RecordTraceOutline.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/RecordTraceRecording.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/TracePause.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/TraceResume.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/TraceSnapshot.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/TraceStart.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/TraceTools/TraceStop.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/UObject.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/UObject_12.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/UTrace.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/UnrealInsights.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/ViewMode_20.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Insights/ZeroCountFilter.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Launcher/PaperAirplane.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/Notifications/Throbber.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Added.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_BranchModifiedBadge.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckCircleLine.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckIn.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckInAvailable.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckInAvailableRewound.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckedBranch.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckedBranchBadge.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckedOther.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_CheckedOtherBadge.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_ConflictResolution_Clear.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_ConflictResolution_OpenExternal.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Conflicted.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_ConflictedState.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Diff.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_DiskSize.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_File.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_LineCircle.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_MarkedForAdd.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Modified.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_ModifiedLocally.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_NewerVersion.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Promote.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Promote_Large.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Removed.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Rewind.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Rewound.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_StatusLocalUpToDate.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_StatusLocalUpload.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_StatusRemoteDownload.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_StatusRemoteUpToDate.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_Sync.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_SyncAndCheckOut.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_VerticalLine.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_VerticalLineDashed.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/RC_VerticalLineStart.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_Action_Diff.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_Action_Integrate.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_Branched.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_Changelist.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_CheckedOut.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_ContentAdd.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_DlgCheckedOutOther.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_DlgNotCurrent.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_DlgReadOnly.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_Lock.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_MarkedForDelete.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_ModifiedOtherBranch.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_NotInDepot.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SCC_Shelved.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/SourceControl.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/Status/RevisionControl.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/Status/RevisionControlBadgeConnected.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/Status/RevisionControlBadgeWarning.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/icon_SCC_Change_Source_Control_Settings.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/icon_SCC_History.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/SourceControl/icon_SCC_Revert.svg", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Starship/StatusBar/drawer-shadow-bottom.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/BrushWireframe.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/DefaultPawn_16px.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/FlatColorSquare.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/Hyperlink.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/Lit.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/NewLevelBlank.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/TestRotation.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/Unlit.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Testing/Wireframe.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Tutorials/TutorialBorder.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Content/Slate/Tutorials/TutorialShadow.png", "Type": "UFS"}, {"Path": "$(EngineDir)/Extras/GPUDumpViewer/GPUDumpViewer.html", "Type": "DebugNonUFS"}, {"Path": "$(EngineDir)/Extras/GPUDumpViewer/OpenGPUDumpViewer.bat", "Type": "DebugNonUFS"}, {"Path": "$(EngineDir)/Extras/GPUDumpViewer/OpenGPUDumpViewer.sh", "Type": "DebugNonUFS"}, {"Path": "$(EngineDir)/Plugins/2D/Paper2D/Paper2D.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/AI/AISupport/AISupport.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/AI/EnvironmentQueryEditor/EnvironmentQueryEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/ACLPlugin/ACLPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/AnimationData/AnimationData.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/ControlRigModules/ControlRigModules.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/ControlRigSpline/ControlRigSpline.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/ControlRig/ControlRig.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/DeformerGraph/DeformerGraph.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/IKRig/IKRig.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/RigLogic/RigLogic.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/SkeletalMeshModelingTools/SkeletalMeshModelingTools.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Animation/TweeningUtils/TweeningUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Bridge/Bridge.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Cameras/CameraShakePreviewer/CameraShakePreviewer.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Cameras/EngineCameras/EngineCameras.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Cameras/GameplayCameras/GameplayCameras.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/ChaosCloth/ChaosCloth.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/ChaosVD/ChaosVD.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Compression/OodleNetwork/OodleNetwork.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/AnimationSharing/AnimationSharing.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/DumpGPUServices/DumpGPUServices.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/NamingTokens/NamingTokens.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/PixWinPlugin/PixWinPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/PluginUtils/PluginUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/RenderDocPlugin/RenderDocPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Developer/UObjectPlugin/UObjectPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/AssetManagerEditor/AssetManagerEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/AssetReferenceRestrictions/AssetReferenceRestrictions.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/BlueprintHeaderView/BlueprintHeaderView.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/BlueprintMaterialTextureNodes/BlueprintMaterialTextureNodes.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/ColorGrading/ColorGrading.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/ContentBrowser/ContentBrowserAssetDataSource/ContentBrowserAssetDataSource.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/CurveEditorTools/CurveEditorTools.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/DataValidation/DataValidation.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/EditorScriptingUtilities/EditorScriptingUtilities.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/EngineAssetDefinitions/EngineAssetDefinitions.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/FacialAnimation/FacialAnimation.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/GameplayTagsEditor/GameplayTagsEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/GeometryMode/GeometryMode.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/ObjectMixer/LightMixer/LightMixer.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/ObjectMixer/ObjectMixer/ObjectMixer.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/ProxyLODPlugin/ProxyLODPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/SequencerAnimTools/SequencerAnimTools.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/SpeedTreeImporter/SpeedTreeImporter.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/UMGWidgetPreview/UMGWidgetPreview.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Editor/UVEditor/UVEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/EnhancedInput/EnhancedInput.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Enterprise/DatasmithContent/DatasmithContent.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Enterprise/GLTFExporter/GLTFExporter.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Enterprise/VariantManagerContent/VariantManagerContent.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Enterprise/VariantManager/VariantManager.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/AutomationUtils/AutomationUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/BackChannel/BackChannel.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/ChaosCaching/ChaosCaching.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/ChaosEditor/ChaosEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/ChaosNiagara/ChaosNiagara.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/ChaosSolverPlugin/ChaosSolverPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/ChaosUserDataPT/ChaosUserDataPT.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/CharacterAI/CharacterAI.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/Compositing/CompositeCore/CompositeCore.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/Dataflow/Dataflow.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/EditorDataStorageFeatures/EditorDataStorageFeatures.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/EditorDataStorage/EditorDataStorage.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/Fracture/Fracture.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/FullBodyIK/FullBodyIK.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/GameplayTargetingSystem/TargetingSystem.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/GeometryCollectionPlugin/GeometryCollectionPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/GeometryDataflow/GeometryDataflow.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/Iris/Iris.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/Landmass/Landmass.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/LevelSequenceNavigatorBridge/LevelSequenceNavigatorBridge.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/LocalizableMessage/LocalizableMessage.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/NFORDenoise/NFORDenoise.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PCGBiomeCore/PCGBiomeCore.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PCGBiomeSample/PCGBiomeSample.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PCGInterops/PCGInstancedActorsInterop/PCGInstancedActorsInterop.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PCGInterops/PCGNiagaraInterop/PCGNiagaraInterop.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PCGInterops/PCGWaterInterop/PCGWaterInterop.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlanarCutPlugin/PlanarCut.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/PlatformCrypto.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PythonScriptPlugin/PythonScriptPlugin.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/RuntimeTelemetry/RuntimeTelemetry.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/ToolPresets/ToolPresets.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/Water/Water.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/FX/Cascade/Cascade.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/FX/NiagaraSimCaching/NiagaraSimCaching.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/FX/Niagara/Niagara.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Fab/Fab.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Importers/AlembicImporter/AlembicImporter.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Interchange/Assets/InterchangeAssets.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Interchange/Editor/InterchangeEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Interchange/Runtime/Interchange.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Media/AvfMedia/AvfMedia.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Media/ImgMedia/ImgMedia.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Media/MediaCompositing/MediaCompositing.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Media/MediaPlate/MediaPlate.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Media/WebMMedia/WebMMedia.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Media/WmfMedia/WmfMedia.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/MeshPainting/MeshPainting.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Messaging/TcpMessaging/TcpMessaging.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Messaging/UdpMessaging/UdpMessaging.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/MetaHuman/MetaHumanSDK/MetaHumanSDK.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/MovieScene/ActorSequence/ActorSequence.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/MovieScene/LevelSequenceEditor/LevelSequenceEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/MovieScene/SequencerScripting/SequencerScripting.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/MovieScene/TemplateSequence/TemplateSequence.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/NNE/NNEDenoiser/NNEDenoiser.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/NNE/NNERuntimeORT/Binaries/ThirdParty/Onnxruntime/Win64/onnxruntime.dll", "Type": "NonUFS"}, {"Path": "$(EngineDir)/Plugins/NNE/NNERuntimeORT/NNERuntimeORT.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Online/OnlineBase/OnlineBase.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Online/OnlineServices/OnlineServices.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Online/OnlineSubsystemNull/OnlineSubsystemNull.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Online/OnlineSubsystemUtils/OnlineSubsystemUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Online/OnlineSubsystem/OnlineSubsystem.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/PCGInterops/PCGExternalDataInterop/PCGExternalDataInterop.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/PCGInterops/PCGGeometryScriptInterop/PCGGeometryScriptInterop.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/PCG/PCG.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Portal/LauncherChunkInstaller/LauncherChunkInstaller.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ActorLayerUtilities/ActorLayerUtilities.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AndroidFileServer/AndroidFileServer.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AndroidPermission/AndroidPermission.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AppleImageUtils/AppleImageUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ArchVisCharacter/ArchVisCharacter.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AssetTags/AssetTags.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AudioCapture/AudioCapture.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AudioSynesthesia/AudioSynesthesia.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/AudioWidgets/AudioWidgets.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/CableComponent/CableComponent.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ChunkDownloader/ChunkDownloader.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ComputeFramework/ComputeFramework.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/CustomMeshComponent/CustomMeshComponent.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/DataRegistry/DataRegistry.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ExampleDeviceProfileSelector/ExampleDeviceProfileSelector.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/GameFeatures/GameFeatures.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/GameplayAbilities/GameplayAbilities.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/GeometryCache/GeometryCache.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/GeometryProcessing/GeometryProcessing.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/GeometryScripting/GeometryScripting.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/GooglePAD/GooglePAD.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/HairStrands/HairStrands.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/InputDebugging/InputDebugging.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/InstancedActors/InstancedActors.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/LocationServicesBPLibrary/LocationServicesBPLibrary.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/MassGameplay/MassGameplay.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/MeshModelingToolset/MeshModelingToolset.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/Metasound/Metasound.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/MobilePatchingUtils/MobilePatchingUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ModularGameplay/ModularGameplay.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/MsQuic/MsQuic.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ProceduralMeshComponent/ProceduralMeshComponent.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/PropertyAccess/PropertyAccessEditor.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/PropertyBindingUtils/PropertyBindingUtils.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ResonanceAudio/ResonanceAudio.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/RigVM/RigVM.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/SignificanceManager/SignificanceManager.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/SmartObjects/SmartObjects.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/SoundFields/SoundFields.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/StateTree/StateTree.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/Synthesis/Synthesis.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/WaveTable/WaveTable.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/WebMMoviePlayer/WebMMoviePlayer.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/WindowsDeviceProfileSelector/WindowsDeviceProfileSelector.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/WindowsMoviePlayer/WindowsMoviePlayer.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/Windows/XInputDevice/XInputDevice.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/WorldConditions/WorldConditions.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ZoneGraphAnnotations/ZoneGraphAnnotations.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Runtime/ZoneGraph/ZoneGraph.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Tests/InterchangeTests/InterchangeTests.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/TraceUtilities/TraceUtilities.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/VirtualProduction/CameraCalibrationCore/CameraCalibrationCore.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/VirtualProduction/Takes/Takes.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/WorldMetrics/WorldMetrics.uplugin", "Type": "UFS"}], "BuildPlugins": ["ACLPlugin", "AISupport", "ActorLayerUtilities", "ActorSequence", "AdvancedRenamer", "AlembicImporter", "AndroidDeviceProfileSelector", "AndroidFileServer", "AndroidMedia", "AndroidMoviePlayer", "AndroidPermission", "AnimationData", "AnimationModifierLibrary", "AnimationSharing", "AppleImageUtils", "AppleMoviePlayer", "ArchVisCharacter", "AssetManagerEditor", "AssetReferenceRestrictions", "AssetTags", "AudioCapture", "AudioSynesthesia", "AudioWidgets", "AutomationUtils", "AvfMedia", "BackChannel", "BlendSpaceMotionAnalysis", "BlueprintHeaderView", "BlueprintMaterialTextureNodes", "Bridge", "CLionSourceCodeAccess", "CableComponent", "CameraCalibrationCore", "CameraShakePreviewer", "Cascade", "ChangelistReview", "ChaosCaching", "ChaosCloth", "ChaosEditor", "ChaosInsights", "ChaosNiagara", "ChaosSolverPlugin", "ChaosUserDataPT", "ChaosVD", "CharacterAI", "ChunkDownloader", "CmdLinkServer", "CodeLiteSourceCodeAccess", "ColorGrading", "CompositeCore", "ComputeFramework", "ContentBrowserAssetDataSource", "ContentBrowserClassDataSource", "ControlRig", "ControlRigModules", "ControlRigSpline", "CryptoKeys", "CurveEditorTools", "CustomMeshComponent", "DataRegistry", "DataValidation", "Dataflow", "DatasmithContent", "DeformerGraph", "DumpGPUServices", "EditorDataStorage", "EditorDataStorageFeatures", "EditorDebugTools", "EditorPerformance", "EditorScriptingUtilities", "EditorTelemetry", "EngineAssetDefinitions", "EngineCameras", "EnhancedInput", "EnvironmentQueryEditor", "ExampleDeviceProfileSelector", "Fab", "FacialAnimation", "FastBuildController", "Fracture", "FullBodyIK", "GLTFExporter", "GameFeatures", "GameplayAbilities", "GameplayCameras", "GameplayTagsEditor", "GeometryCache", "GeometryCollectionPlugin", "GeometryDataflow", "GeometryMode", "GeometryProcessing", "GeometryScripting", "GitSourceControl", "GoogleCloudMessaging", "GooglePAD", "HairStrands", "IKRig", "IOSDeviceProfileSelector", "ImgMedia", "InputDebugging", "InstancedActors", "Interchange", "InterchangeAssets", "InterchangeEditor", "InterchangeTests", "IoStoreInsights", "Iris", "KDevelopSourceCodeAccess", "Landmass", "LauncherChunkInstaller", "LevelSequenceEditor", "LevelSequenceNavigatorBridge", "LightMixer", "LinuxDeviceProfileSelector", "LocalizableMessage", "LocationServicesBPLibrary", "MacGraphicsSwitching", "MassGameplay", "MassInsights", "MaterialAnalyzer", "MediaCompositing", "MediaPlate", "MediaPlayerEditor", "MeshModelingToolset", "MeshPainting", "MetaHumanSDK", "Metasound", "MobileLauncherProfileWizard", "MobilePatchingUtils", "ModularGameplay", "<PERSON><PERSON><PERSON><PERSON>", "N10XSourceCodeAccess", "NFORDenoise", "NNEDenoiser", "NNERuntimeORT", "NamingTokens", "Niagara", "NiagaraSimCaching", "NullSourceCodeAccess", "ObjectMixer", "OnlineBase", "OnlineServices", "OnlineSubsystem", "OnlineSubsystemNull", "OnlineSubsystemUtils", "OodleNetwork", "PCG", "PCGBiomeCore", "PCGBiomeSample", "PCGExternalDataInterop", "PCGGeometryScriptInterop", "PCGInstancedActorsInterop", "PCGNiagaraInterop", "PCGWaterInterop", "Paper2D", "PerforceSourceControl", "PixWinPlugin", "PlanarCut", "PlasticSourceControl", "PlatformCrypto", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Plug<PERSON><PERSON><PERSON><PERSON>", "PortableObjectFileDataSource", "ProceduralMeshComponent", "ProjectLauncher", "PropertyAccessEditor", "PropertyAccessNode", "PropertyBindingUtils", "ProxyLODPlugin", "PythonScriptPlugin", "RenderDocPlugin", "RenderGraphInsights", "ResonanceAudio", "RiderSourceCodeAccess", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RigVM", "RuntimeTelemetry", "SequenceNavigator", "SequencerAnimTools", "SequencerScripting", "SignificanceManager", "SkeletalMeshModelingTools", "SkeletalReduction", "SmartObjects", "SoundFields", "SpeedTreeImporter", "StateTree", "SubversionSourceControl", "Synthesis", "Takes", "TargetingSystem", "TcpMessaging", "TemplateSequence", "TextureFormatOodle", "ToolPresets", "TraceUtilities", "TweeningUtils", "UMGWidgetPreview", "UObjectPlugin", "UVEditor", "UbaController", "UdpMessaging", "VariantManager", "VariantManager<PERSON><PERSON>nt", "VisualStudioCodeSourceCodeAccess", "VisualStudioSourceCodeAccess", "Water", "WaveTable", "WebMMedia", "WebMMoviePlayer", "WindowsDeviceProfileSelector", "WindowsMoviePlayer", "WmfMedia", "WorldConditions", "WorldMetrics", "WorldPartitionHLODUtilities", "XGEController", "XInputDevice", "ZoneGraph", "ZoneGraphAnnotations"], "AdditionalProperties": [{"Name": "SDK", "Value": "Not Applicable"}]}