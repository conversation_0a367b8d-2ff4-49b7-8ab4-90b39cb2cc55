; DefaultGameplayTags.ini
; AURACRON - Sistema de Sígilos
; Configuração de GameplayTags hierárquicos para UE 5.6
; Tags verificados para compatibilidade com GameplayAbilities System

[/Script/GameplayTags.GameplayTagsSettings]

; ========================================
; SIGIL TYPE TAGS - Tipos de Sígilos
; ========================================
+GameplayTagList=(Tag="Sigil.Type.Tank",DevComment="Tank type sigils - defensive focus, health and armor bonuses")
+GameplayTagList=(Tag="Sigil.Type.Damage",DevComment="Damage type sigils - offensive focus, attack power and crit")
+GameplayTagList=(Tag="Sigil.Type.Utility",DevComment="Utility type sigils - support focus, cooldown and movement")

; ========================================
; SIGIL RARITY TAGS - Raridade dos Sígilos
; ========================================
+GameplayTagList=(Tag="Sigil.Rarity.Common",DevComment="Common rarity sigils - basic stat bonuses")
+GameplayTagList=(Tag="Sigil.Rarity.Rare",DevComment="Rare rarity sigils - moderate stat bonuses")
+GameplayTagList=(Tag="Sigil.Rarity.Epic",DevComment="Epic rarity sigils - high stat bonuses")
+GameplayTagList=(Tag="Sigil.Rarity.Legendary",DevComment="Legendary rarity sigils - maximum stat bonuses and unique effects")

; ========================================
; SIGIL STATE TAGS - Estados do Sistema
; ========================================
+GameplayTagList=(Tag="Sigil.State.ChampionSelect",DevComment="During champion selection phase - sigils can be equipped")
+GameplayTagList=(Tag="Sigil.State.FusionReady",DevComment="Ready for fusion at 6 minutes - automatic fusion available")
+GameplayTagList=(Tag="Sigil.State.Fused",DevComment="Sigil has been fused - enhanced stats and effects")
+GameplayTagList=(Tag="Sigil.State.ReforgeAvailable",DevComment="Can reforge at Nexus - reroll sigil properties")
+GameplayTagList=(Tag="Sigil.State.ReforgeCD",DevComment="Reforge on cooldown - 2 minute global cooldown")
+GameplayTagList=(Tag="Sigil.State.Locked",DevComment="Sigil slot is locked - cannot equip or modify")
+GameplayTagList=(Tag="Sigil.State.Empty",DevComment="Sigil slot is empty - ready for new sigil")

; ========================================
; TEAM TAGS - Sistema de Times MOBA
; ========================================
+GameplayTagList=(Tag="Team.Blue",DevComment="Blue team players - 5 players maximum")
+GameplayTagList=(Tag="Team.Red",DevComment="Red team players - 5 players maximum")
+GameplayTagList=(Tag="Team.Neutral",DevComment="Neutral entities - jungle monsters, minions")

; ========================================
; ABILITY TAGS - Habilidades dos Sígilos
; ========================================
+GameplayTagList=(Tag="Ability.Sigil.Unique",DevComment="Unique sigil abilities - legendary tier effects")
+GameplayTagList=(Tag="Ability.Sigil.Passive",DevComment="Passive sigil effects - always active bonuses")
+GameplayTagList=(Tag="Ability.Sigil.Active",DevComment="Active sigil abilities - triggered effects")
+GameplayTagList=(Tag="Ability.Sigil.Fusion",DevComment="Fusion-enhanced abilities - post-6min effects")

; ========================================
; EFFECT TAGS - Modificadores Espectrais
; ========================================
+GameplayTagList=(Tag="Effect.Sigil.Spectral.Power",DevComment="Spectral power modifier - increases attack damage")
+GameplayTagList=(Tag="Effect.Sigil.Spectral.Resilience",DevComment="Spectral resilience modifier - increases defense")
+GameplayTagList=(Tag="Effect.Sigil.Spectral.Velocity",DevComment="Spectral velocity modifier - increases movement speed")
+GameplayTagList=(Tag="Effect.Sigil.Spectral.Focus",DevComment="Spectral focus modifier - reduces cooldowns")
+GameplayTagList=(Tag="Effect.Sigil.Buff.Temporary",DevComment="Temporary buff effects - limited duration")
+GameplayTagList=(Tag="Effect.Sigil.Buff.Permanent",DevComment="Permanent buff effects - lasts until death")
+GameplayTagList=(Tag="Effect.Sigil.Debuff",DevComment="Debuff effects - negative status effects")

; ========================================
; EVENT TAGS - Eventos do Sistema
; ========================================
+GameplayTagList=(Tag="Event.Sigil.Equipped",DevComment="Sigil equipped event - triggers UI updates")
+GameplayTagList=(Tag="Event.Sigil.Unequipped",DevComment="Sigil unequipped event - removes effects")
+GameplayTagList=(Tag="Event.Sigil.Fused",DevComment="Sigil fusion event - 6 minute automatic fusion")
+GameplayTagList=(Tag="Event.Sigil.Reforged",DevComment="Sigil reforge event - Nexus reroll")
+GameplayTagList=(Tag="Event.Sigil.SlotUnlocked",DevComment="New sigil slot unlocked - level progression")
+GameplayTagList=(Tag="Event.Sigil.EffectApplied",DevComment="Sigil effect applied to target")
+GameplayTagList=(Tag="Event.Sigil.EffectRemoved",DevComment="Sigil effect removed from target")

; ========================================
; MOBA PHASE TAGS - Fases da Partida
; ========================================
+GameplayTagList=(Tag="MOBA.Phase.ChampionSelect",DevComment="Champion selection phase - 0-2 minutes")
+GameplayTagList=(Tag="MOBA.Phase.EarlyGame",DevComment="Early game phase - 0-6 minutes, pre-fusion")
+GameplayTagList=(Tag="MOBA.Phase.MidGame",DevComment="Mid game phase - 6+ minutes, post-fusion")
+GameplayTagList=(Tag="MOBA.Phase.LateGame",DevComment="Late game phase - 15+ minutes, full builds")
+GameplayTagList=(Tag="MOBA.Phase.Overtime",DevComment="Overtime phase - extended matches")

; ========================================
; GAMEPLAY TAGS - Mecânicas Específicas
; ========================================
+GameplayTagList=(Tag="Gameplay.Sigil.CanEquip",DevComment="Player can equip sigils - not in combat")
+GameplayTagList=(Tag="Gameplay.Sigil.CanReforge",DevComment="Player can reforge - at Nexus location")
+GameplayTagList=(Tag="Gameplay.Sigil.InCombat",DevComment="Player in combat - cannot modify sigils")
+GameplayTagList=(Tag="Gameplay.Sigil.AtNexus",DevComment="Player at Nexus - reforge available")
+GameplayTagList=(Tag="Gameplay.Sigil.FusionWindow",DevComment="6-minute fusion window active")

; ========================================
; VFX TAGS - Efeitos Visuais Niagara
; ========================================
+GameplayTagList=(Tag="VFX.Sigil.Equip",DevComment="Sigil equip visual effect")
+GameplayTagList=(Tag="VFX.Sigil.Unequip",DevComment="Sigil unequip visual effect")
+GameplayTagList=(Tag="VFX.Sigil.Fusion",DevComment="Sigil fusion visual effect - 6 minute trigger")
+GameplayTagList=(Tag="VFX.Sigil.Reforge",DevComment="Sigil reforge visual effect - Nexus interaction")
+GameplayTagList=(Tag="VFX.Sigil.Glow.Common",DevComment="Common rarity glow effect")
+GameplayTagList=(Tag="VFX.Sigil.Glow.Rare",DevComment="Rare rarity glow effect")
+GameplayTagList=(Tag="VFX.Sigil.Glow.Epic",DevComment="Epic rarity glow effect")
+GameplayTagList=(Tag="VFX.Sigil.Glow.Legendary",DevComment="Legendary rarity glow effect")

; ========================================
; UI TAGS - Interface do Usuário
; ========================================
+GameplayTagList=(Tag="UI.Sigil.Slot.Highlight",DevComment="Highlight sigil slot for interaction")
+GameplayTagList=(Tag="UI.Sigil.Slot.Invalid",DevComment="Invalid drop target for sigil")
+GameplayTagList=(Tag="UI.Sigil.DragDrop.Start",DevComment="Start drag and drop operation")
+GameplayTagList=(Tag="UI.Sigil.DragDrop.End",DevComment="End drag and drop operation")
+GameplayTagList=(Tag="UI.Sigil.Notification.Fusion",DevComment="Fusion ready notification")
+GameplayTagList=(Tag="UI.Sigil.Notification.Reforge",DevComment="Reforge available notification")

; ========================================
; DEBUG TAGS - Ferramentas de Debug
; ========================================
+GameplayTagList=(Tag="Debug.Sigil.ShowStats",DevComment="Show sigil statistics overlay")
+GameplayTagList=(Tag="Debug.Sigil.ForceEquip",DevComment="Force equip sigil regardless of restrictions")
+GameplayTagList=(Tag="Debug.Sigil.ForceFusion",DevComment="Force trigger fusion before 6 minutes")
+GameplayTagList=(Tag="Debug.Sigil.ResetCooldowns",DevComment="Reset all sigil cooldowns")
+GameplayTagList=(Tag="Debug.Sigil.ShowReplication",DevComment="Show replication debug info")

; ========================================
; NETWORK TAGS - Replicação Multiplayer
; ========================================
+GameplayTagList=(Tag="Network.Sigil.Replicated",DevComment="Sigil data is replicated to clients")
+GameplayTagList=(Tag="Network.Sigil.ServerOnly",DevComment="Server-only sigil operations")
+GameplayTagList=(Tag="Network.Sigil.ClientPredicted",DevComment="Client-predicted sigil interactions")
+GameplayTagList=(Tag="Network.Sigil.Authoritative",DevComment="Server authoritative sigil state")