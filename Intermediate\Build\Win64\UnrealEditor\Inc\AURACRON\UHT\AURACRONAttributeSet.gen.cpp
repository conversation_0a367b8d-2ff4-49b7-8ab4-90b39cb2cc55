// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "GAS/AURACRONAttributeSet.h"
#include "AttributeSet.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONAttributeSet() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UAURACRONAttributeSet();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONAttributeSet_NoRegister();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAttributeSet();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAttribute();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAttributeData();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnHealthChanged ******************************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnHealthChanged_Parms
	{
		float OldValue;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates para eventos de atributos - UE 5.6 Dynamic Delegates para BlueprintAssignable\n" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para eventos de atributos - UE 5.6 Dynamic Delegates para BlueprintAssignable" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OldValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::NewProp_OldValue = { "OldValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnHealthChanged_Parms, OldValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnHealthChanged_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnHealthChanged_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::NewProp_OldValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::NewProp_Source,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnHealthChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnHealthChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnHealthChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float OldValue, float NewValue, UAbilitySystemComponent* Source)
{
	struct AURACRONAttributeSet_eventOnHealthChanged_Parms
	{
		float OldValue;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
	AURACRONAttributeSet_eventOnHealthChanged_Parms Parms;
	Parms.OldValue=OldValue;
	Parms.NewValue=NewValue;
	Parms.Source=Source;
	OnHealthChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHealthChanged ********************************************************

// ********** Begin Delegate FOnManaChanged ********************************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnManaChanged_Parms
	{
		float OldValue;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OldValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::NewProp_OldValue = { "OldValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnManaChanged_Parms, OldValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnManaChanged_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnManaChanged_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::NewProp_OldValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::NewProp_Source,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnManaChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnManaChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnManaChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnManaChanged_DelegateWrapper(const FMulticastScriptDelegate& OnManaChanged, float OldValue, float NewValue, UAbilitySystemComponent* Source)
{
	struct AURACRONAttributeSet_eventOnManaChanged_Parms
	{
		float OldValue;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
	AURACRONAttributeSet_eventOnManaChanged_Parms Parms;
	Parms.OldValue=OldValue;
	Parms.NewValue=NewValue;
	Parms.Source=Source;
	OnManaChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnManaChanged **********************************************************

// ********** Begin Delegate FOnMovementSpeedChanged ***********************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms
	{
		float NewValue;
		UAbilitySystemComponent* Source;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::NewProp_Source,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnMovementSpeedChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnMovementSpeedChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMovementSpeedChanged, float NewValue, UAbilitySystemComponent* Source)
{
	struct AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms
	{
		float NewValue;
		UAbilitySystemComponent* Source;
	};
	AURACRONAttributeSet_eventOnMovementSpeedChanged_Parms Parms;
	Parms.NewValue=NewValue;
	Parms.Source=Source;
	OnMovementSpeedChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMovementSpeedChanged *************************************************

// ********** Begin Delegate FOnCombatAttributeChanged *********************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms
	{
		FGameplayAttribute Attribute;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Attribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::NewProp_Attribute = { "Attribute", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms, Attribute), Z_Construct_UScriptStruct_FGameplayAttribute, METADATA_PARAMS(0, nullptr) }; // 1212282043
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::NewProp_Attribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::NewProp_Source,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnCombatAttributeChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnCombatAttributeChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCombatAttributeChanged, FGameplayAttribute Attribute, float NewValue, UAbilitySystemComponent* Source)
{
	struct AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms
	{
		FGameplayAttribute Attribute;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
	AURACRONAttributeSet_eventOnCombatAttributeChanged_Parms Parms;
	Parms.Attribute=Attribute;
	Parms.NewValue=NewValue;
	Parms.Source=Source;
	OnCombatAttributeChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCombatAttributeChanged ***********************************************

// ********** Begin Delegate FOnSigilAttributeChanged **********************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms
	{
		FGameplayAttribute Attribute;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Attribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::NewProp_Attribute = { "Attribute", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms, Attribute), Z_Construct_UScriptStruct_FGameplayAttribute, METADATA_PARAMS(0, nullptr) }; // 1212282043
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::NewProp_Attribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::NewProp_Source,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnSigilAttributeChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnSigilAttributeChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSigilAttributeChanged, FGameplayAttribute Attribute, float NewValue, UAbilitySystemComponent* Source)
{
	struct AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms
	{
		FGameplayAttribute Attribute;
		float NewValue;
		UAbilitySystemComponent* Source;
	};
	AURACRONAttributeSet_eventOnSigilAttributeChanged_Parms Parms;
	Parms.Attribute=Attribute;
	Parms.NewValue=NewValue;
	Parms.Source=Source;
	OnSigilAttributeChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilAttributeChanged ************************************************

// ********** Begin Delegate FOnDamageReceived *****************************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnDamageReceived_Parms
	{
		float DamageAmount;
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnDamageReceived_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnDamageReceived_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::NewProp_SourceTags = { "SourceTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnDamageReceived_Parms, SourceTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceTags_MetaData), NewProp_SourceTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::NewProp_SourceTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnDamageReceived__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::AURACRONAttributeSet_eventOnDamageReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::AURACRONAttributeSet_eventOnDamageReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnDamageReceived_DelegateWrapper(const FMulticastScriptDelegate& OnDamageReceived, float DamageAmount, UAbilitySystemComponent* Source, FGameplayTagContainer const& SourceTags)
{
	struct AURACRONAttributeSet_eventOnDamageReceived_Parms
	{
		float DamageAmount;
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
	AURACRONAttributeSet_eventOnDamageReceived_Parms Parms;
	Parms.DamageAmount=DamageAmount;
	Parms.Source=Source;
	Parms.SourceTags=SourceTags;
	OnDamageReceived.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDamageReceived *******************************************************

// ********** Begin Delegate FOnHealingReceived ****************************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnHealingReceived_Parms
	{
		float HealingAmount;
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::NewProp_HealingAmount = { "HealingAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnHealingReceived_Parms, HealingAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnHealingReceived_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::NewProp_SourceTags = { "SourceTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnHealingReceived_Parms, SourceTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceTags_MetaData), NewProp_SourceTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::NewProp_HealingAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::NewProp_SourceTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnHealingReceived__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::AURACRONAttributeSet_eventOnHealingReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::AURACRONAttributeSet_eventOnHealingReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnHealingReceived_DelegateWrapper(const FMulticastScriptDelegate& OnHealingReceived, float HealingAmount, UAbilitySystemComponent* Source, FGameplayTagContainer const& SourceTags)
{
	struct AURACRONAttributeSet_eventOnHealingReceived_Parms
	{
		float HealingAmount;
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
	AURACRONAttributeSet_eventOnHealingReceived_Parms Parms;
	Parms.HealingAmount=HealingAmount;
	Parms.Source=Source;
	Parms.SourceTags=SourceTags;
	OnHealingReceived.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHealingReceived ******************************************************

// ********** Begin Delegate FOnShieldReceived *****************************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnShieldReceived_Parms
	{
		float ShieldAmount;
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShieldAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::NewProp_ShieldAmount = { "ShieldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnShieldReceived_Parms, ShieldAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnShieldReceived_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::NewProp_SourceTags = { "SourceTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnShieldReceived_Parms, SourceTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceTags_MetaData), NewProp_SourceTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::NewProp_ShieldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::NewProp_SourceTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnShieldReceived__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::AURACRONAttributeSet_eventOnShieldReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::AURACRONAttributeSet_eventOnShieldReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnShieldReceived_DelegateWrapper(const FMulticastScriptDelegate& OnShieldReceived, float ShieldAmount, UAbilitySystemComponent* Source, FGameplayTagContainer const& SourceTags)
{
	struct AURACRONAttributeSet_eventOnShieldReceived_Parms
	{
		float ShieldAmount;
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
	AURACRONAttributeSet_eventOnShieldReceived_Parms Parms;
	Parms.ShieldAmount=ShieldAmount;
	Parms.Source=Source;
	Parms.SourceTags=SourceTags;
	OnShieldReceived.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnShieldReceived *******************************************************

// ********** Begin Delegate FOnDeath **************************************************************
struct Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics
{
	struct AURACRONAttributeSet_eventOnDeath_Parms
	{
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Source;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnDeath_Parms, Source), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::NewProp_SourceTags = { "SourceTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnDeath_Parms, SourceTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceTags_MetaData), NewProp_SourceTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::NewProp_SourceTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnDeath__DelegateSignature", Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::AURACRONAttributeSet_eventOnDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::AURACRONAttributeSet_eventOnDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAURACRONAttributeSet::FOnDeath_DelegateWrapper(const FMulticastScriptDelegate& OnDeath, UAbilitySystemComponent* Source, FGameplayTagContainer const& SourceTags)
{
	struct AURACRONAttributeSet_eventOnDeath_Parms
	{
		UAbilitySystemComponent* Source;
		FGameplayTagContainer SourceTags;
	};
	AURACRONAttributeSet_eventOnDeath_Parms Parms;
	Parms.Source=Source;
	Parms.SourceTags=SourceTags;
	OnDeath.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDeath ****************************************************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_AbilityPower ************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics
{
	struct AURACRONAttributeSet_eventOnRep_AbilityPower_Parms
	{
		FGameplayAttributeData OldAbilityPower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldAbilityPower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldAbilityPower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::NewProp_OldAbilityPower = { "OldAbilityPower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_AbilityPower_Parms, OldAbilityPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldAbilityPower_MetaData), NewProp_OldAbilityPower_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::NewProp_OldAbilityPower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_AbilityPower", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::AURACRONAttributeSet_eventOnRep_AbilityPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::AURACRONAttributeSet_eventOnRep_AbilityPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_AbilityPower)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldAbilityPower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_AbilityPower(Z_Param_Out_OldAbilityPower);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_AbilityPower **************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_Accuracy ****************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics
{
	struct AURACRONAttributeSet_eventOnRep_Accuracy_Parms
	{
		FGameplayAttributeData OldAccuracy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldAccuracy_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldAccuracy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::NewProp_OldAccuracy = { "OldAccuracy", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_Accuracy_Parms, OldAccuracy), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldAccuracy_MetaData), NewProp_OldAccuracy_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::NewProp_OldAccuracy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_Accuracy", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::AURACRONAttributeSet_eventOnRep_Accuracy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::AURACRONAttributeSet_eventOnRep_Accuracy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_Accuracy)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldAccuracy);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_Accuracy(Z_Param_Out_OldAccuracy);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_Accuracy ******************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_Armor *******************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics
{
	struct AURACRONAttributeSet_eventOnRep_Armor_Parms
	{
		FGameplayAttributeData OldArmor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldArmor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldArmor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::NewProp_OldArmor = { "OldArmor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_Armor_Parms, OldArmor), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldArmor_MetaData), NewProp_OldArmor_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::NewProp_OldArmor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_Armor", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::AURACRONAttributeSet_eventOnRep_Armor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::AURACRONAttributeSet_eventOnRep_Armor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_Armor)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldArmor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_Armor(Z_Param_Out_OldArmor);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_Armor *********************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_AttackDamage ************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics
{
	struct AURACRONAttributeSet_eventOnRep_AttackDamage_Parms
	{
		FGameplayAttributeData OldAttackDamage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldAttackDamage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldAttackDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::NewProp_OldAttackDamage = { "OldAttackDamage", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_AttackDamage_Parms, OldAttackDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldAttackDamage_MetaData), NewProp_OldAttackDamage_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::NewProp_OldAttackDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_AttackDamage", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::AURACRONAttributeSet_eventOnRep_AttackDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::AURACRONAttributeSet_eventOnRep_AttackDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_AttackDamage)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldAttackDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_AttackDamage(Z_Param_Out_OldAttackDamage);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_AttackDamage **************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_AttackSpeed *************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics
{
	struct AURACRONAttributeSet_eventOnRep_AttackSpeed_Parms
	{
		FGameplayAttributeData OldAttackSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldAttackSpeed_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldAttackSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::NewProp_OldAttackSpeed = { "OldAttackSpeed", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_AttackSpeed_Parms, OldAttackSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldAttackSpeed_MetaData), NewProp_OldAttackSpeed_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::NewProp_OldAttackSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_AttackSpeed", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::AURACRONAttributeSet_eventOnRep_AttackSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::AURACRONAttributeSet_eventOnRep_AttackSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_AttackSpeed)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldAttackSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_AttackSpeed(Z_Param_Out_OldAttackSpeed);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_AttackSpeed ***************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_CooldownReduction *******************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics
{
	struct AURACRONAttributeSet_eventOnRep_CooldownReduction_Parms
	{
		FGameplayAttributeData OldCooldownReduction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCooldownReduction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCooldownReduction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::NewProp_OldCooldownReduction = { "OldCooldownReduction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_CooldownReduction_Parms, OldCooldownReduction), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCooldownReduction_MetaData), NewProp_OldCooldownReduction_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::NewProp_OldCooldownReduction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_CooldownReduction", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::AURACRONAttributeSet_eventOnRep_CooldownReduction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::AURACRONAttributeSet_eventOnRep_CooldownReduction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_CooldownReduction)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCooldownReduction);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CooldownReduction(Z_Param_Out_OldCooldownReduction);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_CooldownReduction *********************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_CriticalChance **********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics
{
	struct AURACRONAttributeSet_eventOnRep_CriticalChance_Parms
	{
		FGameplayAttributeData OldCriticalChance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCriticalChance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCriticalChance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::NewProp_OldCriticalChance = { "OldCriticalChance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_CriticalChance_Parms, OldCriticalChance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCriticalChance_MetaData), NewProp_OldCriticalChance_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::NewProp_OldCriticalChance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_CriticalChance", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::AURACRONAttributeSet_eventOnRep_CriticalChance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::AURACRONAttributeSet_eventOnRep_CriticalChance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_CriticalChance)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCriticalChance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CriticalChance(Z_Param_Out_OldCriticalChance);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_CriticalChance ************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_CriticalDamage **********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics
{
	struct AURACRONAttributeSet_eventOnRep_CriticalDamage_Parms
	{
		FGameplayAttributeData OldCriticalDamage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCriticalDamage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCriticalDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::NewProp_OldCriticalDamage = { "OldCriticalDamage", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_CriticalDamage_Parms, OldCriticalDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCriticalDamage_MetaData), NewProp_OldCriticalDamage_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::NewProp_OldCriticalDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_CriticalDamage", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::AURACRONAttributeSet_eventOnRep_CriticalDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::AURACRONAttributeSet_eventOnRep_CriticalDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_CriticalDamage)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCriticalDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CriticalDamage(Z_Param_Out_OldCriticalDamage);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_CriticalDamage ************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_ExperienceMultiplier ****************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics
{
	struct AURACRONAttributeSet_eventOnRep_ExperienceMultiplier_Parms
	{
		FGameplayAttributeData OldExperienceMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldExperienceMultiplier_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldExperienceMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::NewProp_OldExperienceMultiplier = { "OldExperienceMultiplier", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_ExperienceMultiplier_Parms, OldExperienceMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldExperienceMultiplier_MetaData), NewProp_OldExperienceMultiplier_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::NewProp_OldExperienceMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_ExperienceMultiplier", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::AURACRONAttributeSet_eventOnRep_ExperienceMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::AURACRONAttributeSet_eventOnRep_ExperienceMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_ExperienceMultiplier)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldExperienceMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ExperienceMultiplier(Z_Param_Out_OldExperienceMultiplier);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_ExperienceMultiplier ******************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_Health ******************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics
{
	struct AURACRONAttributeSet_eventOnRep_Health_Parms
	{
		FGameplayAttributeData OldHealth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldHealth_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::NewProp_OldHealth = { "OldHealth", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_Health_Parms, OldHealth), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldHealth_MetaData), NewProp_OldHealth_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::NewProp_OldHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_Health", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::AURACRONAttributeSet_eventOnRep_Health_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::AURACRONAttributeSet_eventOnRep_Health_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_Health)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldHealth);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_Health(Z_Param_Out_OldHealth);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_Health ********************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_HealthRegeneration ******************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics
{
	struct AURACRONAttributeSet_eventOnRep_HealthRegeneration_Parms
	{
		FGameplayAttributeData OldHealthRegeneration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldHealthRegeneration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldHealthRegeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::NewProp_OldHealthRegeneration = { "OldHealthRegeneration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_HealthRegeneration_Parms, OldHealthRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldHealthRegeneration_MetaData), NewProp_OldHealthRegeneration_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::NewProp_OldHealthRegeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_HealthRegeneration", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::AURACRONAttributeSet_eventOnRep_HealthRegeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::AURACRONAttributeSet_eventOnRep_HealthRegeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_HealthRegeneration)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldHealthRegeneration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_HealthRegeneration(Z_Param_Out_OldHealthRegeneration);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_HealthRegeneration ********************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_IncomingDamage **********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics
{
	struct AURACRONAttributeSet_eventOnRep_IncomingDamage_Parms
	{
		FGameplayAttributeData OldIncomingDamage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es OnRep para meta-atributos\n" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es OnRep para meta-atributos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldIncomingDamage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldIncomingDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::NewProp_OldIncomingDamage = { "OldIncomingDamage", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_IncomingDamage_Parms, OldIncomingDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldIncomingDamage_MetaData), NewProp_OldIncomingDamage_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::NewProp_OldIncomingDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_IncomingDamage", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::AURACRONAttributeSet_eventOnRep_IncomingDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::AURACRONAttributeSet_eventOnRep_IncomingDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_IncomingDamage)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldIncomingDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_IncomingDamage(Z_Param_Out_OldIncomingDamage);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_IncomingDamage ************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_IncomingHealing *********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics
{
	struct AURACRONAttributeSet_eventOnRep_IncomingHealing_Parms
	{
		FGameplayAttributeData OldIncomingHealing;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldIncomingHealing_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldIncomingHealing;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::NewProp_OldIncomingHealing = { "OldIncomingHealing", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_IncomingHealing_Parms, OldIncomingHealing), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldIncomingHealing_MetaData), NewProp_OldIncomingHealing_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::NewProp_OldIncomingHealing,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_IncomingHealing", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::AURACRONAttributeSet_eventOnRep_IncomingHealing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::AURACRONAttributeSet_eventOnRep_IncomingHealing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_IncomingHealing)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldIncomingHealing);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_IncomingHealing(Z_Param_Out_OldIncomingHealing);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_IncomingHealing ***********************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_IncomingShield **********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics
{
	struct AURACRONAttributeSet_eventOnRep_IncomingShield_Parms
	{
		FGameplayAttributeData OldIncomingShield;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldIncomingShield_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldIncomingShield;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::NewProp_OldIncomingShield = { "OldIncomingShield", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_IncomingShield_Parms, OldIncomingShield), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldIncomingShield_MetaData), NewProp_OldIncomingShield_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::NewProp_OldIncomingShield,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_IncomingShield", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::AURACRONAttributeSet_eventOnRep_IncomingShield_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::AURACRONAttributeSet_eventOnRep_IncomingShield_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_IncomingShield)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldIncomingShield);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_IncomingShield(Z_Param_Out_OldIncomingShield);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_IncomingShield ************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_MagicResistance *********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics
{
	struct AURACRONAttributeSet_eventOnRep_MagicResistance_Parms
	{
		FGameplayAttributeData OldMagicResistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMagicResistance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMagicResistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::NewProp_OldMagicResistance = { "OldMagicResistance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_MagicResistance_Parms, OldMagicResistance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMagicResistance_MetaData), NewProp_OldMagicResistance_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::NewProp_OldMagicResistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_MagicResistance", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::AURACRONAttributeSet_eventOnRep_MagicResistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::AURACRONAttributeSet_eventOnRep_MagicResistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_MagicResistance)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMagicResistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MagicResistance(Z_Param_Out_OldMagicResistance);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_MagicResistance ***********************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_Mana ********************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics
{
	struct AURACRONAttributeSet_eventOnRep_Mana_Parms
	{
		FGameplayAttributeData OldMana;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMana_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMana;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::NewProp_OldMana = { "OldMana", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_Mana_Parms, OldMana), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMana_MetaData), NewProp_OldMana_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::NewProp_OldMana,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_Mana", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::AURACRONAttributeSet_eventOnRep_Mana_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::AURACRONAttributeSet_eventOnRep_Mana_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_Mana)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMana);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_Mana(Z_Param_Out_OldMana);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_Mana **********************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_ManaRegeneration ********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics
{
	struct AURACRONAttributeSet_eventOnRep_ManaRegeneration_Parms
	{
		FGameplayAttributeData OldManaRegeneration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldManaRegeneration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldManaRegeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::NewProp_OldManaRegeneration = { "OldManaRegeneration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_ManaRegeneration_Parms, OldManaRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldManaRegeneration_MetaData), NewProp_OldManaRegeneration_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::NewProp_OldManaRegeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_ManaRegeneration", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::AURACRONAttributeSet_eventOnRep_ManaRegeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::AURACRONAttributeSet_eventOnRep_ManaRegeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_ManaRegeneration)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldManaRegeneration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ManaRegeneration(Z_Param_Out_OldManaRegeneration);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_ManaRegeneration **********************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_MaxHealth ***************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics
{
	struct AURACRONAttributeSet_eventOnRep_MaxHealth_Parms
	{
		FGameplayAttributeData OldMaxHealth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMaxHealth_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMaxHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::NewProp_OldMaxHealth = { "OldMaxHealth", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_MaxHealth_Parms, OldMaxHealth), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMaxHealth_MetaData), NewProp_OldMaxHealth_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::NewProp_OldMaxHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_MaxHealth", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::AURACRONAttributeSet_eventOnRep_MaxHealth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::AURACRONAttributeSet_eventOnRep_MaxHealth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_MaxHealth)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMaxHealth);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MaxHealth(Z_Param_Out_OldMaxHealth);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_MaxHealth *****************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_MaxMana *****************************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics
{
	struct AURACRONAttributeSet_eventOnRep_MaxMana_Parms
	{
		FGameplayAttributeData OldMaxMana;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMaxMana_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMaxMana;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::NewProp_OldMaxMana = { "OldMaxMana", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_MaxMana_Parms, OldMaxMana), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMaxMana_MetaData), NewProp_OldMaxMana_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::NewProp_OldMaxMana,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_MaxMana", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::AURACRONAttributeSet_eventOnRep_MaxMana_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::AURACRONAttributeSet_eventOnRep_MaxMana_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_MaxMana)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMaxMana);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MaxMana(Z_Param_Out_OldMaxMana);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_MaxMana *******************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_MaxSigilSlots ***********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics
{
	struct AURACRONAttributeSet_eventOnRep_MaxSigilSlots_Parms
	{
		FGameplayAttributeData OldMaxSigilSlots;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMaxSigilSlots_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMaxSigilSlots;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::NewProp_OldMaxSigilSlots = { "OldMaxSigilSlots", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_MaxSigilSlots_Parms, OldMaxSigilSlots), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMaxSigilSlots_MetaData), NewProp_OldMaxSigilSlots_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::NewProp_OldMaxSigilSlots,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_MaxSigilSlots", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::AURACRONAttributeSet_eventOnRep_MaxSigilSlots_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::AURACRONAttributeSet_eventOnRep_MaxSigilSlots_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_MaxSigilSlots)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMaxSigilSlots);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MaxSigilSlots(Z_Param_Out_OldMaxSigilSlots);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_MaxSigilSlots *************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_MovementSpeed ***********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics
{
	struct AURACRONAttributeSet_eventOnRep_MovementSpeed_Parms
	{
		FGameplayAttributeData OldMovementSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMovementSpeed_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMovementSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::NewProp_OldMovementSpeed = { "OldMovementSpeed", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_MovementSpeed_Parms, OldMovementSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMovementSpeed_MetaData), NewProp_OldMovementSpeed_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::NewProp_OldMovementSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_MovementSpeed", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::AURACRONAttributeSet_eventOnRep_MovementSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::AURACRONAttributeSet_eventOnRep_MovementSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_MovementSpeed)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMovementSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MovementSpeed(Z_Param_Out_OldMovementSpeed);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_MovementSpeed *************************

// ********** Begin Class UAURACRONAttributeSet Function OnRep_SigilEfficiency *********************
struct Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics
{
	struct AURACRONAttributeSet_eventOnRep_SigilEfficiency_Parms
	{
		FGameplayAttributeData OldSigilEfficiency;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilEfficiency_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilEfficiency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::NewProp_OldSigilEfficiency = { "OldSigilEfficiency", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONAttributeSet_eventOnRep_SigilEfficiency_Parms, OldSigilEfficiency), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilEfficiency_MetaData), NewProp_OldSigilEfficiency_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::NewProp_OldSigilEfficiency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONAttributeSet, nullptr, "OnRep_SigilEfficiency", Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::AURACRONAttributeSet_eventOnRep_SigilEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::AURACRONAttributeSet_eventOnRep_SigilEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONAttributeSet::execOnRep_SigilEfficiency)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilEfficiency);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilEfficiency(Z_Param_Out_OldSigilEfficiency);
	P_NATIVE_END;
}
// ********** End Class UAURACRONAttributeSet Function OnRep_SigilEfficiency ***********************

// ********** Begin Class UAURACRONAttributeSet ****************************************************
void UAURACRONAttributeSet::StaticRegisterNativesUAURACRONAttributeSet()
{
	UClass* Class = UAURACRONAttributeSet::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "OnRep_AbilityPower", &UAURACRONAttributeSet::execOnRep_AbilityPower },
		{ "OnRep_Accuracy", &UAURACRONAttributeSet::execOnRep_Accuracy },
		{ "OnRep_Armor", &UAURACRONAttributeSet::execOnRep_Armor },
		{ "OnRep_AttackDamage", &UAURACRONAttributeSet::execOnRep_AttackDamage },
		{ "OnRep_AttackSpeed", &UAURACRONAttributeSet::execOnRep_AttackSpeed },
		{ "OnRep_CooldownReduction", &UAURACRONAttributeSet::execOnRep_CooldownReduction },
		{ "OnRep_CriticalChance", &UAURACRONAttributeSet::execOnRep_CriticalChance },
		{ "OnRep_CriticalDamage", &UAURACRONAttributeSet::execOnRep_CriticalDamage },
		{ "OnRep_ExperienceMultiplier", &UAURACRONAttributeSet::execOnRep_ExperienceMultiplier },
		{ "OnRep_Health", &UAURACRONAttributeSet::execOnRep_Health },
		{ "OnRep_HealthRegeneration", &UAURACRONAttributeSet::execOnRep_HealthRegeneration },
		{ "OnRep_IncomingDamage", &UAURACRONAttributeSet::execOnRep_IncomingDamage },
		{ "OnRep_IncomingHealing", &UAURACRONAttributeSet::execOnRep_IncomingHealing },
		{ "OnRep_IncomingShield", &UAURACRONAttributeSet::execOnRep_IncomingShield },
		{ "OnRep_MagicResistance", &UAURACRONAttributeSet::execOnRep_MagicResistance },
		{ "OnRep_Mana", &UAURACRONAttributeSet::execOnRep_Mana },
		{ "OnRep_ManaRegeneration", &UAURACRONAttributeSet::execOnRep_ManaRegeneration },
		{ "OnRep_MaxHealth", &UAURACRONAttributeSet::execOnRep_MaxHealth },
		{ "OnRep_MaxMana", &UAURACRONAttributeSet::execOnRep_MaxMana },
		{ "OnRep_MaxSigilSlots", &UAURACRONAttributeSet::execOnRep_MaxSigilSlots },
		{ "OnRep_MovementSpeed", &UAURACRONAttributeSet::execOnRep_MovementSpeed },
		{ "OnRep_SigilEfficiency", &UAURACRONAttributeSet::execOnRep_SigilEfficiency },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONAttributeSet;
UClass* UAURACRONAttributeSet::GetPrivateStaticClass()
{
	using TClass = UAURACRONAttributeSet;
	if (!Z_Registration_Info_UClass_UAURACRONAttributeSet.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONAttributeSet"),
			Z_Registration_Info_UClass_UAURACRONAttributeSet.InnerSingleton,
			StaticRegisterNativesUAURACRONAttributeSet,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONAttributeSet.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONAttributeSet_NoRegister()
{
	return UAURACRONAttributeSet::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONAttributeSet_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Conjunto de atributos para personagens AURACRON\n * Implementa todos os atributos necess\xc3\xa1rios para o sistema de S\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "GAS/AURACRONAttributeSet.h" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conjunto de atributos para personagens AURACRON\nImplementa todos os atributos necess\xc3\xa1rios para o sistema de S\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Atributos|Prim\xc3\xa1rios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vida atual do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vida atual do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Atributos|Prim\xc3\xa1rios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vida m\xc3\xa1xima do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vida m\xc3\xa1xima do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mana_MetaData[] = {
		{ "Category", "Atributos|Prim\xc3\xa1rios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana atual do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana atual do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMana_MetaData[] = {
		{ "Category", "Atributos|Prim\xc3\xa1rios" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana m\xc3\xa1xima do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana m\xc3\xa1xima do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamage_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano de ataque f\xc3\xadsico */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano de ataque f\xc3\xadsico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityPower_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Poder de habilidade (dano m\xc3\xa1gico) */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder de habilidade (dano m\xc3\xa1gico)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Armor_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Armadura f\xc3\xadsica */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Armadura f\xc3\xadsica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MagicResistance_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resist\xc3\xaancia m\xc3\xa1gica */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resist\xc3\xaancia m\xc3\xa1gica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSpeed_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de ataque */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de ataque" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalChance_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chance de cr\xc3\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chance de cr\xc3\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalDamage_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano cr\xc3\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano cr\xc3\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Accuracy_MetaData[] = {
		{ "Category", "Atributos|Combate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Precis\xc3\xa3o/Acur\xc3\xa1""cia */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Precis\xc3\xa3o/Acur\xc3\xa1""cia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "Atributos|Movimento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegeneration_MetaData[] = {
		{ "Category", "Atributos|Regenera\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regenera\xc3\xa7\xc3\xa3o de vida por segundo */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\xa7\xc3\xa3o de vida por segundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegeneration_MetaData[] = {
		{ "Category", "Atributos|Regenera\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regenera\xc3\xa7\xc3\xa3o de mana por segundo */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\xa7\xc3\xa3o de mana por segundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReduction_MetaData[] = {
		{ "Category", "Atributos|Cooldown" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Redu\xc3\xa7\xc3\xa3o de cooldown (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\xa7\xc3\xa3o de cooldown (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilEfficiency_MetaData[] = {
		{ "Category", "Atributos|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efici\xc3\xaancia dos S\xc3\xadgilos (multiplicador de efeitos) */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efici\xc3\xaancia dos S\xc3\xadgilos (multiplicador de efeitos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSigilSlots_MetaData[] = {
		{ "Category", "Atributos|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Capacidade m\xc3\xa1xima de S\xc3\xadgilos equipados */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Capacidade m\xc3\xa1xima de S\xc3\xadgilos equipados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceMultiplier_MetaData[] = {
		{ "Category", "Atributos|Progress\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de experi\xc3\xaancia */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de experi\xc3\xaancia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IncomingDamage_MetaData[] = {
		{ "Category", "Meta-Atributos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meta-atributo para dano recebido (processado server-side) */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meta-atributo para dano recebido (processado server-side)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IncomingHealing_MetaData[] = {
		{ "Category", "Meta-Atributos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meta-atributo para cura recebida (processado server-side) */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meta-atributo para cura recebida (processado server-side)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IncomingShield_MetaData[] = {
		{ "Category", "Meta-Atributos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meta-atributo para escudo recebido (processado server-side) */" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meta-atributo para escudo recebido (processado server-side)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnHealthChanged_MetaData[] = {
		{ "Category", "Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos p\xc3\xba""blicos\n" },
#endif
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos p\xc3\xba""blicos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnManaChanged_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMovementSpeedChanged_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatAttributeChanged_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilAttributeChanged_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDamageReceived_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnHealingReceived_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnShieldReceived_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDeath_MetaData[] = {
		{ "Category", "Eventos" },
		{ "ModuleRelativePath", "Public/GAS/AURACRONAttributeSet.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Health;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Mana;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaxMana;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttackDamage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AbilityPower;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Armor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MagicResistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttackSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CriticalChance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CriticalDamage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Accuracy;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealthRegeneration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ManaRegeneration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CooldownReduction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaxSigilSlots;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExperienceMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IncomingDamage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IncomingHealing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IncomingShield;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnHealthChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnManaChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMovementSpeedChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatAttributeChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilAttributeChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDamageReceived;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnHealingReceived;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnShieldReceived;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDeath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature, "OnCombatAttributeChanged__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature, "OnDamageReceived__DelegateSignature" }, // 205251569
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature, "OnDeath__DelegateSignature" }, // 92968060
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature, "OnHealingReceived__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature, "OnHealthChanged__DelegateSignature" }, // 530186526
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature, "OnManaChanged__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature, "OnMovementSpeedChanged__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AbilityPower, "OnRep_AbilityPower" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Accuracy, "OnRep_Accuracy" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Armor, "OnRep_Armor" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackDamage, "OnRep_AttackDamage" }, // 825611216
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_AttackSpeed, "OnRep_AttackSpeed" }, // 173106463
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CooldownReduction, "OnRep_CooldownReduction" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalChance, "OnRep_CriticalChance" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_CriticalDamage, "OnRep_CriticalDamage" }, // 331308900
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ExperienceMultiplier, "OnRep_ExperienceMultiplier" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Health, "OnRep_Health" }, // 716636961
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_HealthRegeneration, "OnRep_HealthRegeneration" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingDamage, "OnRep_IncomingDamage" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingHealing, "OnRep_IncomingHealing" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_IncomingShield, "OnRep_IncomingShield" }, // 160279615
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MagicResistance, "OnRep_MagicResistance" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_Mana, "OnRep_Mana" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_ManaRegeneration, "OnRep_ManaRegeneration" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxHealth, "OnRep_MaxHealth" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxMana, "OnRep_MaxMana" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MaxSigilSlots, "OnRep_MaxSigilSlots" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_MovementSpeed, "OnRep_MovementSpeed" }, // **********
		{ &Z_Construct_UFunction_UAURACRONAttributeSet_OnRep_SigilEfficiency, "OnRep_SigilEfficiency" }, // **********
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature, "OnShieldReceived__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature, "OnSigilAttributeChanged__DelegateSignature" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONAttributeSet>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Health = { "Health", "OnRep_Health", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, Health), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MaxHealth = { "MaxHealth", "OnRep_MaxHealth", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, MaxHealth), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Mana = { "Mana", "OnRep_Mana", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, Mana), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mana_MetaData), NewProp_Mana_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MaxMana = { "MaxMana", "OnRep_MaxMana", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, MaxMana), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMana_MetaData), NewProp_MaxMana_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_AttackDamage = { "AttackDamage", "OnRep_AttackDamage", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, AttackDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamage_MetaData), NewProp_AttackDamage_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_AbilityPower = { "AbilityPower", "OnRep_AbilityPower", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, AbilityPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityPower_MetaData), NewProp_AbilityPower_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Armor = { "Armor", "OnRep_Armor", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, Armor), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Armor_MetaData), NewProp_Armor_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MagicResistance = { "MagicResistance", "OnRep_MagicResistance", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, MagicResistance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MagicResistance_MetaData), NewProp_MagicResistance_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_AttackSpeed = { "AttackSpeed", "OnRep_AttackSpeed", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, AttackSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSpeed_MetaData), NewProp_AttackSpeed_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_CriticalChance = { "CriticalChance", "OnRep_CriticalChance", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, CriticalChance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalChance_MetaData), NewProp_CriticalChance_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_CriticalDamage = { "CriticalDamage", "OnRep_CriticalDamage", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, CriticalDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalDamage_MetaData), NewProp_CriticalDamage_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Accuracy = { "Accuracy", "OnRep_Accuracy", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, Accuracy), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Accuracy_MetaData), NewProp_Accuracy_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MovementSpeed = { "MovementSpeed", "OnRep_MovementSpeed", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, MovementSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_HealthRegeneration = { "HealthRegeneration", "OnRep_HealthRegeneration", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, HealthRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegeneration_MetaData), NewProp_HealthRegeneration_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_ManaRegeneration = { "ManaRegeneration", "OnRep_ManaRegeneration", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, ManaRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegeneration_MetaData), NewProp_ManaRegeneration_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_CooldownReduction = { "CooldownReduction", "OnRep_CooldownReduction", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, CooldownReduction), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReduction_MetaData), NewProp_CooldownReduction_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_SigilEfficiency = { "SigilEfficiency", "OnRep_SigilEfficiency", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, SigilEfficiency), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilEfficiency_MetaData), NewProp_SigilEfficiency_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MaxSigilSlots = { "MaxSigilSlots", "OnRep_MaxSigilSlots", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, MaxSigilSlots), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSigilSlots_MetaData), NewProp_MaxSigilSlots_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_ExperienceMultiplier = { "ExperienceMultiplier", "OnRep_ExperienceMultiplier", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, ExperienceMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceMultiplier_MetaData), NewProp_ExperienceMultiplier_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_IncomingDamage = { "IncomingDamage", "OnRep_IncomingDamage", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, IncomingDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IncomingDamage_MetaData), NewProp_IncomingDamage_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_IncomingHealing = { "IncomingHealing", "OnRep_IncomingHealing", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, IncomingHealing), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IncomingHealing_MetaData), NewProp_IncomingHealing_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_IncomingShield = { "IncomingShield", "OnRep_IncomingShield", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, IncomingShield), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IncomingShield_MetaData), NewProp_IncomingShield_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnHealthChanged = { "OnHealthChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnHealthChanged), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnHealthChanged_MetaData), NewProp_OnHealthChanged_MetaData) }; // 530186526
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnManaChanged = { "OnManaChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnManaChanged), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnManaChanged_MetaData), NewProp_OnManaChanged_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnMovementSpeedChanged = { "OnMovementSpeedChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnMovementSpeedChanged), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMovementSpeedChanged_MetaData), NewProp_OnMovementSpeedChanged_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnCombatAttributeChanged = { "OnCombatAttributeChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnCombatAttributeChanged), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatAttributeChanged_MetaData), NewProp_OnCombatAttributeChanged_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnSigilAttributeChanged = { "OnSigilAttributeChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnSigilAttributeChanged), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilAttributeChanged_MetaData), NewProp_OnSigilAttributeChanged_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnDamageReceived = { "OnDamageReceived", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnDamageReceived), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDamageReceived_MetaData), NewProp_OnDamageReceived_MetaData) }; // 205251569
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnHealingReceived = { "OnHealingReceived", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnHealingReceived), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnHealingReceived_MetaData), NewProp_OnHealingReceived_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnShieldReceived = { "OnShieldReceived", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnShieldReceived), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnShieldReceived_MetaData), NewProp_OnShieldReceived_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnDeath = { "OnDeath", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONAttributeSet, OnDeath), Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDeath_MetaData), NewProp_OnDeath_MetaData) }; // 92968060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAURACRONAttributeSet_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Mana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MaxMana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_AttackDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_AbilityPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Armor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MagicResistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_AttackSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_CriticalChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_CriticalDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_Accuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_HealthRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_ManaRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_CooldownReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_SigilEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_MaxSigilSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_ExperienceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_IncomingDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_IncomingHealing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_IncomingShield,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnHealthChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnManaChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnMovementSpeedChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnCombatAttributeChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnSigilAttributeChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnDamageReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnHealingReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnShieldReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONAttributeSet_Statics::NewProp_OnDeath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONAttributeSet_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAURACRONAttributeSet_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAttributeSet,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONAttributeSet_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONAttributeSet_Statics::ClassParams = {
	&UAURACRONAttributeSet::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAURACRONAttributeSet_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONAttributeSet_Statics::PropPointers),
	0,
	0x00B000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONAttributeSet_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONAttributeSet_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONAttributeSet()
{
	if (!Z_Registration_Info_UClass_UAURACRONAttributeSet.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONAttributeSet.OuterSingleton, Z_Construct_UClass_UAURACRONAttributeSet_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONAttributeSet.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAURACRONAttributeSet::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_Health(TEXT("Health"));
	static FName Name_MaxHealth(TEXT("MaxHealth"));
	static FName Name_Mana(TEXT("Mana"));
	static FName Name_MaxMana(TEXT("MaxMana"));
	static FName Name_AttackDamage(TEXT("AttackDamage"));
	static FName Name_AbilityPower(TEXT("AbilityPower"));
	static FName Name_Armor(TEXT("Armor"));
	static FName Name_MagicResistance(TEXT("MagicResistance"));
	static FName Name_AttackSpeed(TEXT("AttackSpeed"));
	static FName Name_CriticalChance(TEXT("CriticalChance"));
	static FName Name_CriticalDamage(TEXT("CriticalDamage"));
	static FName Name_Accuracy(TEXT("Accuracy"));
	static FName Name_MovementSpeed(TEXT("MovementSpeed"));
	static FName Name_HealthRegeneration(TEXT("HealthRegeneration"));
	static FName Name_ManaRegeneration(TEXT("ManaRegeneration"));
	static FName Name_CooldownReduction(TEXT("CooldownReduction"));
	static FName Name_SigilEfficiency(TEXT("SigilEfficiency"));
	static FName Name_MaxSigilSlots(TEXT("MaxSigilSlots"));
	static FName Name_ExperienceMultiplier(TEXT("ExperienceMultiplier"));
	static FName Name_IncomingDamage(TEXT("IncomingDamage"));
	static FName Name_IncomingHealing(TEXT("IncomingHealing"));
	static FName Name_IncomingShield(TEXT("IncomingShield"));
	const bool bIsValid = true
		&& Name_Health == ClassReps[(int32)ENetFields_Private::Health].Property->GetFName()
		&& Name_MaxHealth == ClassReps[(int32)ENetFields_Private::MaxHealth].Property->GetFName()
		&& Name_Mana == ClassReps[(int32)ENetFields_Private::Mana].Property->GetFName()
		&& Name_MaxMana == ClassReps[(int32)ENetFields_Private::MaxMana].Property->GetFName()
		&& Name_AttackDamage == ClassReps[(int32)ENetFields_Private::AttackDamage].Property->GetFName()
		&& Name_AbilityPower == ClassReps[(int32)ENetFields_Private::AbilityPower].Property->GetFName()
		&& Name_Armor == ClassReps[(int32)ENetFields_Private::Armor].Property->GetFName()
		&& Name_MagicResistance == ClassReps[(int32)ENetFields_Private::MagicResistance].Property->GetFName()
		&& Name_AttackSpeed == ClassReps[(int32)ENetFields_Private::AttackSpeed].Property->GetFName()
		&& Name_CriticalChance == ClassReps[(int32)ENetFields_Private::CriticalChance].Property->GetFName()
		&& Name_CriticalDamage == ClassReps[(int32)ENetFields_Private::CriticalDamage].Property->GetFName()
		&& Name_Accuracy == ClassReps[(int32)ENetFields_Private::Accuracy].Property->GetFName()
		&& Name_MovementSpeed == ClassReps[(int32)ENetFields_Private::MovementSpeed].Property->GetFName()
		&& Name_HealthRegeneration == ClassReps[(int32)ENetFields_Private::HealthRegeneration].Property->GetFName()
		&& Name_ManaRegeneration == ClassReps[(int32)ENetFields_Private::ManaRegeneration].Property->GetFName()
		&& Name_CooldownReduction == ClassReps[(int32)ENetFields_Private::CooldownReduction].Property->GetFName()
		&& Name_SigilEfficiency == ClassReps[(int32)ENetFields_Private::SigilEfficiency].Property->GetFName()
		&& Name_MaxSigilSlots == ClassReps[(int32)ENetFields_Private::MaxSigilSlots].Property->GetFName()
		&& Name_ExperienceMultiplier == ClassReps[(int32)ENetFields_Private::ExperienceMultiplier].Property->GetFName()
		&& Name_IncomingDamage == ClassReps[(int32)ENetFields_Private::IncomingDamage].Property->GetFName()
		&& Name_IncomingHealing == ClassReps[(int32)ENetFields_Private::IncomingHealing].Property->GetFName()
		&& Name_IncomingShield == ClassReps[(int32)ENetFields_Private::IncomingShield].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAURACRONAttributeSet"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONAttributeSet);
UAURACRONAttributeSet::~UAURACRONAttributeSet() {}
// ********** End Class UAURACRONAttributeSet ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONAttributeSet, UAURACRONAttributeSet::StaticClass, TEXT("UAURACRONAttributeSet"), &Z_Registration_Info_UClass_UAURACRONAttributeSet, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONAttributeSet), 1878725127U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h__Script_AURACRON_4235637338(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_GAS_AURACRONAttributeSet_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
