// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGArsenalIsland.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGArsenalIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AArsenalIsland();
AURACRON_API UClass* Z_Construct_UClass_AArsenalIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AArsenalIsland Function GrantAbilityBoost ********************************
struct Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics
{
	struct ArsenalIsland_eventGrantAbilityBoost_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede potencializador de habilidades ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede potencializador de habilidades ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ArsenalIsland_eventGrantAbilityBoost_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AArsenalIsland, nullptr, "GrantAbilityBoost", Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::PropPointers), sizeof(Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::ArsenalIsland_eventGrantAbilityBoost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::Function_MetaDataParams), Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::ArsenalIsland_eventGrantAbilityBoost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AArsenalIsland::execGrantAbilityBoost)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantAbilityBoost(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AArsenalIsland Function GrantAbilityBoost **********************************

// ********** Begin Class AArsenalIsland Function GrantSpecialAmmo *********************************
struct Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics
{
	struct ArsenalIsland_eventGrantSpecialAmmo_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede muni\xc3\xa7\xc3\xa3o especial ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede muni\xc3\xa7\xc3\xa3o especial ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ArsenalIsland_eventGrantSpecialAmmo_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AArsenalIsland, nullptr, "GrantSpecialAmmo", Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::PropPointers), sizeof(Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::ArsenalIsland_eventGrantSpecialAmmo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::Function_MetaDataParams), Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::ArsenalIsland_eventGrantSpecialAmmo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AArsenalIsland::execGrantSpecialAmmo)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantSpecialAmmo(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AArsenalIsland Function GrantSpecialAmmo ***********************************

// ********** Begin Class AArsenalIsland Function GrantWeaponBonus *********************************
struct Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics
{
	struct ArsenalIsland_eventGrantWeaponBonus_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede b\xc3\xb4nus de armas ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede b\xc3\xb4nus de armas ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ArsenalIsland_eventGrantWeaponBonus_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AArsenalIsland, nullptr, "GrantWeaponBonus", Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::ArsenalIsland_eventGrantWeaponBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::ArsenalIsland_eventGrantWeaponBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AArsenalIsland::execGrantWeaponBonus)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantWeaponBonus(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AArsenalIsland Function GrantWeaponBonus ***********************************

// ********** Begin Class AArsenalIsland Function IsNearEnvironmentTransition **********************
struct Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics
{
	struct ArsenalIsland_eventIsNearEnvironmentTransition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Verifica se esta ilha est\xc3\xa1 pr\xc3\xb3xima a um ponto de transi\xc3\xa7\xc3\xa3o entre ambientes\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se esta ilha est\xc3\xa1 pr\xc3\xb3xima a um ponto de transi\xc3\xa7\xc3\xa3o entre ambientes" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ArsenalIsland_eventIsNearEnvironmentTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ArsenalIsland_eventIsNearEnvironmentTransition_Parms), &Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AArsenalIsland, nullptr, "IsNearEnvironmentTransition", Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::ArsenalIsland_eventIsNearEnvironmentTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::ArsenalIsland_eventIsNearEnvironmentTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AArsenalIsland::execIsNearEnvironmentTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsNearEnvironmentTransition();
	P_NATIVE_END;
}
// ********** End Class AArsenalIsland Function IsNearEnvironmentTransition ************************

// ********** Begin Class AArsenalIsland Function RemoveArsenalEffects *****************************
struct Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics
{
	struct ArsenalIsland_eventRemoveArsenalEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove os efeitos de b\xc3\xb4nus\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove os efeitos de b\xc3\xb4nus" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ArsenalIsland_eventRemoveArsenalEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AArsenalIsland, nullptr, "RemoveArsenalEffects", Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::ArsenalIsland_eventRemoveArsenalEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::ArsenalIsland_eventRemoveArsenalEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AArsenalIsland::execRemoveArsenalEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveArsenalEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AArsenalIsland Function RemoveArsenalEffects *******************************

// ********** Begin Class AArsenalIsland Function SetNearEnvironmentTransition *********************
struct Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics
{
	struct ArsenalIsland_eventSetNearEnvironmentTransition_Parms
	{
		bool bIsNearTransition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Define se esta ilha est\xc3\xa1 pr\xc3\xb3xima a um ponto de transi\xc3\xa7\xc3\xa3o entre ambientes\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define se esta ilha est\xc3\xa1 pr\xc3\xb3xima a um ponto de transi\xc3\xa7\xc3\xa3o entre ambientes" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsNearTransition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsNearTransition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::NewProp_bIsNearTransition_SetBit(void* Obj)
{
	((ArsenalIsland_eventSetNearEnvironmentTransition_Parms*)Obj)->bIsNearTransition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::NewProp_bIsNearTransition = { "bIsNearTransition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ArsenalIsland_eventSetNearEnvironmentTransition_Parms), &Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::NewProp_bIsNearTransition_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::NewProp_bIsNearTransition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AArsenalIsland, nullptr, "SetNearEnvironmentTransition", Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::ArsenalIsland_eventSetNearEnvironmentTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::ArsenalIsland_eventSetNearEnvironmentTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AArsenalIsland::execSetNearEnvironmentTransition)
{
	P_GET_UBOOL(Z_Param_bIsNearTransition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetNearEnvironmentTransition(Z_Param_bIsNearTransition);
	P_NATIVE_END;
}
// ********** End Class AArsenalIsland Function SetNearEnvironmentTransition ***********************

// ********** Begin Class AArsenalIsland ***********************************************************
void AArsenalIsland::StaticRegisterNativesAArsenalIsland()
{
	UClass* Class = AArsenalIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GrantAbilityBoost", &AArsenalIsland::execGrantAbilityBoost },
		{ "GrantSpecialAmmo", &AArsenalIsland::execGrantSpecialAmmo },
		{ "GrantWeaponBonus", &AArsenalIsland::execGrantWeaponBonus },
		{ "IsNearEnvironmentTransition", &AArsenalIsland::execIsNearEnvironmentTransition },
		{ "RemoveArsenalEffects", &AArsenalIsland::execRemoveArsenalEffects },
		{ "SetNearEnvironmentTransition", &AArsenalIsland::execSetNearEnvironmentTransition },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AArsenalIsland;
UClass* AArsenalIsland::GetPrivateStaticClass()
{
	using TClass = AArsenalIsland;
	if (!Z_Registration_Info_UClass_AArsenalIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("ArsenalIsland"),
			Z_Registration_Info_UClass_AArsenalIsland.InnerSingleton,
			StaticRegisterNativesAArsenalIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AArsenalIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_AArsenalIsland_NoRegister()
{
	return AArsenalIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AArsenalIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Arsenal Island\n * Ilha com plataformas de armas, dep\xc3\xb3sitos de muni\xc3\xa7\xc3\xa3o e rampas t\xc3\xa1ticas\n * \n * Conforme documenta\xc3\xa7\xc3\xa3o:\n * - Localiza\xc3\xa7\xc3\xa3o: Pr\xc3\xb3ximas a pontos de transi\xc3\xa7\xc3\xa3o entre ambientes\n * - Caracter\xc3\xadsticas: Upgrades de armas, potencializadores de habilidades, buffs tempor\xc3\xa1rios\n * - Valor Estrat\xc3\xa9gico: Oportunidades de power spike\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGArsenalIsland.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Arsenal Island\nIlha com plataformas de armas, dep\xc3\xb3sitos de muni\xc3\xa7\xc3\xa3o e rampas t\xc3\xa1ticas\n\nConforme documenta\xc3\xa7\xc3\xa3o:\n- Localiza\xc3\xa7\xc3\xa3o: Pr\xc3\xb3ximas a pontos de transi\xc3\xa7\xc3\xa3o entre ambientes\n- Caracter\xc3\xadsticas: Upgrades de armas, potencializadores de habilidades, buffs tempor\xc3\xa1rios\n- Valor Estrat\xc3\xa9gico: Oportunidades de power spike" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArsenalTower_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Torre central do arsenal\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Torre central do arsenal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponPlatforms_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Plataformas de armas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Plataformas de armas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmmoDepots_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dep\xc3\xb3sitos de muni\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dep\xc3\xb3sitos de muni\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TacticalRamps_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rampas t\xc3\xa1ticas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rampas t\xc3\xa1ticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsNearEnvironmentTransition_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Indica se esta ilha est\xc3\xa1 pr\xc3\xb3xima a um ponto de transi\xc3\xa7\xc3\xa3o entre ambientes\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indica se esta ilha est\xc3\xa1 pr\xc3\xb3xima a um ponto de transi\xc3\xa7\xc3\xa3o entre ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEnvironments_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipos de ambiente entre os quais esta ilha faz a transi\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "EditCondition", "bIsNearEnvironmentTransition" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de ambiente entre os quais esta ilha faz a transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponBonusIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do b\xc3\xb4nus de armas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do b\xc3\xb4nus de armas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponBonusDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do b\xc3\xb4nus de armas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do b\xc3\xb4nus de armas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecialAmmoCount_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quantidade de muni\xc3\xa7\xc3\xa3o especial\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quantidade de muni\xc3\xa7\xc3\xa3o especial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityBoostIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do potencializador de habilidades\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do potencializador de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityBoostDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do potencializador de habilidades\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do potencializador de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponBonusVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual do b\xc3\xb4nus de armas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual do b\xc3\xb4nus de armas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponBonusEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para b\xc3\xb4nus de armas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para b\xc3\xb4nus de armas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecialAmmoEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para muni\xc3\xa7\xc3\xa3o especial\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para muni\xc3\xa7\xc3\xa3o especial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityBoostEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para potencializador de habilidades\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para potencializador de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityBoostVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual do potencializador de habilidades\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual do potencializador de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo acumulado para efeitos visuais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityBoostMultiplier_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multiplicador de potencializa\xc3\xa7\xc3\xa3o de habilidades\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de potencializa\xc3\xa7\xc3\xa3o de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponPlatform_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Plataforma de armas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Plataforma de armas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlatformEnergyEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de energia da plataforma\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de energia da plataforma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmmoDeposits_MetaData[] = {
		{ "Category", "Prismal Flow|Arsenal Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dep\xc3\xb3sitos de muni\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGArsenalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dep\xc3\xb3sitos de muni\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ArsenalTower;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponPlatforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WeaponPlatforms;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AmmoDepots_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AmmoDepots;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TacticalRamps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TacticalRamps;
	static void NewProp_bIsNearEnvironmentTransition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsNearEnvironmentTransition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionEnvironments_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionEnvironments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TransitionEnvironments;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeaponBonusIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeaponBonusDuration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SpecialAmmoCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityBoostIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityBoostDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponBonusVisualEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_WeaponBonusEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_SpecialAmmoEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AbilityBoostEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilityBoostVisualEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityBoostMultiplier;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponPlatform;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlatformEnergyEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AmmoDeposits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AmmoDeposits;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AArsenalIsland_GrantAbilityBoost, "GrantAbilityBoost" }, // 1193345252
		{ &Z_Construct_UFunction_AArsenalIsland_GrantSpecialAmmo, "GrantSpecialAmmo" }, // 2805000357
		{ &Z_Construct_UFunction_AArsenalIsland_GrantWeaponBonus, "GrantWeaponBonus" }, // 2243464065
		{ &Z_Construct_UFunction_AArsenalIsland_IsNearEnvironmentTransition, "IsNearEnvironmentTransition" }, // 2319122527
		{ &Z_Construct_UFunction_AArsenalIsland_RemoveArsenalEffects, "RemoveArsenalEffects" }, // 1495920967
		{ &Z_Construct_UFunction_AArsenalIsland_SetNearEnvironmentTransition, "SetNearEnvironmentTransition" }, // 878188365
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AArsenalIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_ArsenalTower = { "ArsenalTower", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, ArsenalTower), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArsenalTower_MetaData), NewProp_ArsenalTower_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponPlatforms_Inner = { "WeaponPlatforms", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponPlatforms = { "WeaponPlatforms", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, WeaponPlatforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponPlatforms_MetaData), NewProp_WeaponPlatforms_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDepots_Inner = { "AmmoDepots", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDepots = { "AmmoDepots", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AmmoDepots), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmmoDepots_MetaData), NewProp_AmmoDepots_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TacticalRamps_Inner = { "TacticalRamps", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TacticalRamps = { "TacticalRamps", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, TacticalRamps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TacticalRamps_MetaData), NewProp_TacticalRamps_MetaData) };
void Z_Construct_UClass_AArsenalIsland_Statics::NewProp_bIsNearEnvironmentTransition_SetBit(void* Obj)
{
	((AArsenalIsland*)Obj)->bIsNearEnvironmentTransition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_bIsNearEnvironmentTransition = { "bIsNearEnvironmentTransition", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AArsenalIsland), &Z_Construct_UClass_AArsenalIsland_Statics::NewProp_bIsNearEnvironmentTransition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsNearEnvironmentTransition_MetaData), NewProp_bIsNearEnvironmentTransition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TransitionEnvironments_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TransitionEnvironments_Inner = { "TransitionEnvironments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TransitionEnvironments = { "TransitionEnvironments", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, TransitionEnvironments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEnvironments_MetaData), NewProp_TransitionEnvironments_MetaData) }; // 2509470107
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusIntensity = { "WeaponBonusIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, WeaponBonusIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponBonusIntensity_MetaData), NewProp_WeaponBonusIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusDuration = { "WeaponBonusDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, WeaponBonusDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponBonusDuration_MetaData), NewProp_WeaponBonusDuration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_SpecialAmmoCount = { "SpecialAmmoCount", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, SpecialAmmoCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecialAmmoCount_MetaData), NewProp_SpecialAmmoCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostIntensity = { "AbilityBoostIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AbilityBoostIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityBoostIntensity_MetaData), NewProp_AbilityBoostIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostDuration = { "AbilityBoostDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AbilityBoostDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityBoostDuration_MetaData), NewProp_AbilityBoostDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusVisualEffect = { "WeaponBonusVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, WeaponBonusVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponBonusVisualEffect_MetaData), NewProp_WeaponBonusVisualEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusEffect = { "WeaponBonusEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, WeaponBonusEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponBonusEffect_MetaData), NewProp_WeaponBonusEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_SpecialAmmoEffect = { "SpecialAmmoEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, SpecialAmmoEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecialAmmoEffect_MetaData), NewProp_SpecialAmmoEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostEffect = { "AbilityBoostEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AbilityBoostEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityBoostEffect_MetaData), NewProp_AbilityBoostEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostVisualEffect = { "AbilityBoostVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AbilityBoostVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityBoostVisualEffect_MetaData), NewProp_AbilityBoostVisualEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostMultiplier = { "AbilityBoostMultiplier", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AbilityBoostMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityBoostMultiplier_MetaData), NewProp_AbilityBoostMultiplier_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponPlatform = { "WeaponPlatform", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, WeaponPlatform), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponPlatform_MetaData), NewProp_WeaponPlatform_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_PlatformEnergyEffect = { "PlatformEnergyEffect", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, PlatformEnergyEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlatformEnergyEffect_MetaData), NewProp_PlatformEnergyEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDeposits_Inner = { "AmmoDeposits", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDeposits = { "AmmoDeposits", nullptr, (EPropertyFlags)0x012408800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AArsenalIsland, AmmoDeposits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmmoDeposits_MetaData), NewProp_AmmoDeposits_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AArsenalIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_ArsenalTower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponPlatforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponPlatforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDepots_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDepots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TacticalRamps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TacticalRamps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_bIsNearEnvironmentTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TransitionEnvironments_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TransitionEnvironments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_TransitionEnvironments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_SpecialAmmoCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponBonusEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_SpecialAmmoEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AbilityBoostMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_WeaponPlatform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_PlatformEnergyEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDeposits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AArsenalIsland_Statics::NewProp_AmmoDeposits,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AArsenalIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AArsenalIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APrismalFlowIsland,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AArsenalIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AArsenalIsland_Statics::ClassParams = {
	&AArsenalIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AArsenalIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AArsenalIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AArsenalIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_AArsenalIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AArsenalIsland()
{
	if (!Z_Registration_Info_UClass_AArsenalIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AArsenalIsland.OuterSingleton, Z_Construct_UClass_AArsenalIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AArsenalIsland.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AArsenalIsland::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_WeaponBonusIntensity(TEXT("WeaponBonusIntensity"));
	static FName Name_WeaponBonusDuration(TEXT("WeaponBonusDuration"));
	static FName Name_SpecialAmmoCount(TEXT("SpecialAmmoCount"));
	static FName Name_AbilityBoostIntensity(TEXT("AbilityBoostIntensity"));
	static FName Name_AbilityBoostDuration(TEXT("AbilityBoostDuration"));
	static FName Name_WeaponBonusEffect(TEXT("WeaponBonusEffect"));
	static FName Name_SpecialAmmoEffect(TEXT("SpecialAmmoEffect"));
	static FName Name_AbilityBoostEffect(TEXT("AbilityBoostEffect"));
	const bool bIsValid = true
		&& Name_WeaponBonusIntensity == ClassReps[(int32)ENetFields_Private::WeaponBonusIntensity].Property->GetFName()
		&& Name_WeaponBonusDuration == ClassReps[(int32)ENetFields_Private::WeaponBonusDuration].Property->GetFName()
		&& Name_SpecialAmmoCount == ClassReps[(int32)ENetFields_Private::SpecialAmmoCount].Property->GetFName()
		&& Name_AbilityBoostIntensity == ClassReps[(int32)ENetFields_Private::AbilityBoostIntensity].Property->GetFName()
		&& Name_AbilityBoostDuration == ClassReps[(int32)ENetFields_Private::AbilityBoostDuration].Property->GetFName()
		&& Name_WeaponBonusEffect == ClassReps[(int32)ENetFields_Private::WeaponBonusEffect].Property->GetFName()
		&& Name_SpecialAmmoEffect == ClassReps[(int32)ENetFields_Private::SpecialAmmoEffect].Property->GetFName()
		&& Name_AbilityBoostEffect == ClassReps[(int32)ENetFields_Private::AbilityBoostEffect].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AArsenalIsland"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AArsenalIsland);
AArsenalIsland::~AArsenalIsland() {}
// ********** End Class AArsenalIsland *************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AArsenalIsland, AArsenalIsland::StaticClass, TEXT("AArsenalIsland"), &Z_Registration_Info_UClass_AArsenalIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AArsenalIsland), 434185771U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h__Script_AURACRON_2052722935(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGArsenalIsland_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
