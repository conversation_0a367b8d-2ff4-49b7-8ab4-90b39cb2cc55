// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGChaosPortal.h"

#ifdef AURACRON_AURACRONPCGChaosPortal_generated_h
#error "AURACRONPCGChaosPortal.generated.h already included, missing '#pragma once' in AURACRONPCGChaosPortal.h"
#endif
#define AURACRON_AURACRONPCGChaosPortal_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UPrimitiveComponent;
enum class EAURACRONMapPhase : uint8;
enum class EChaosPortalType : uint8;
struct FHitResult;

// ********** Begin Class AAURACRONPCGChaosPortal **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsAtFlowIntersection); \
	DECLARE_FUNCTION(execSpawnHighRiskRewardVisual); \
	DECLARE_FUNCTION(execSpawnTerrainInstabilityVisual); \
	DECLARE_FUNCTION(execSpawnEnvironmentalHazardVisual); \
	DECLARE_FUNCTION(execOnPlayerExitPortalRadius); \
	DECLARE_FUNCTION(execOnPlayerEnterPortalRadius); \
	DECLARE_FUNCTION(execSetPortalType); \
	DECLARE_FUNCTION(execSetPortalLifetime); \
	DECLARE_FUNCTION(execSetPortalIntensity); \
	DECLARE_FUNCTION(execSetPortalRadius); \
	DECLARE_FUNCTION(execActivateTerrainInstability); \
	DECLARE_FUNCTION(execActivateEnvironmentalHazard); \
	DECLARE_FUNCTION(execSpawnHighRiskReward); \
	DECLARE_FUNCTION(execTriggerPortalEffect); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execSetQualityScale); \
	DECLARE_FUNCTION(execDeactivatePortal); \
	DECLARE_FUNCTION(execActivatePortal);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGChaosPortal(); \
	friend struct Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGChaosPortal, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGChaosPortal)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGChaosPortal(AAURACRONPCGChaosPortal&&) = delete; \
	AAURACRONPCGChaosPortal(const AAURACRONPCGChaosPortal&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGChaosPortal); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGChaosPortal); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGChaosPortal) \
	NO_API virtual ~AAURACRONPCGChaosPortal();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_37_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h_40_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGChaosPortal;

// ********** End Class AAURACRONPCGChaosPortal ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h

// ********** Begin Enum EChaosPortalType **********************************************************
#define FOREACH_ENUM_ECHAOSPORTALTYPE(op) \
	op(EChaosPortalType::Standard) \
	op(EChaosPortalType::Elite) \
	op(EChaosPortalType::Legendary) 

enum class EChaosPortalType : uint8;
template<> struct TIsUEnumClass<EChaosPortalType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EChaosPortalType>();
// ********** End Enum EChaosPortalType ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
