// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGEnvironment.h"

#ifdef AURACRON_AURACRONPCGEnvironment_generated_h
#error "AURACRONPCGEnvironment.generated.h already included, missing '#pragma once' in AURACRONPCGEnvironment.h"
#endif
#define AURACRON_AURACRONPCGEnvironment_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AAURACRONPCGPhaseManager;
class ACharacter;
class UPCGComponent;
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;
enum class EEnvironmentBlurType : uint8;
struct FLinearColor;

// ********** Begin ScriptStruct FBreathingForestData **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_31_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBreathingForestData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FBreathingForestData;
// ********** End ScriptStruct FBreathingForestData ************************************************

// ********** Begin ScriptStruct FFloatingIslandData ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_74_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFloatingIslandData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FFloatingIslandData;
// ********** End ScriptStruct FFloatingIslandData *************************************************

// ********** Begin ScriptStruct FCloudFortressData ************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_98_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FCloudFortressData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FCloudFortressData;
// ********** End ScriptStruct FCloudFortressData **************************************************

// ********** Begin ScriptStruct FStellarGardenData ************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_147_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStellarGardenData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FStellarGardenData;
// ********** End ScriptStruct FStellarGardenData **************************************************

// ********** Begin ScriptStruct FAuroraBridgeData *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_184_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuroraBridgeData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FAuroraBridgeData;
// ********** End ScriptStruct FAuroraBridgeData ***************************************************

// ********** Begin ScriptStruct FRiverOfSoulsData *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_211_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRiverOfSoulsData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FRiverOfSoulsData;
// ********** End ScriptStruct FRiverOfSoulsData ***************************************************

// ********** Begin ScriptStruct FFragmentedStructureData ******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_244_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFragmentedStructureData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FFragmentedStructureData;
// ********** End ScriptStruct FFragmentedStructureData ********************************************

// ********** Begin ScriptStruct FTemporalDistortionData *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_280_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTemporalDistortionData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FTemporalDistortionData;
// ********** End ScriptStruct FTemporalDistortionData *********************************************

// ********** Begin ScriptStruct FShadowNexusData **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_307_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FShadowNexusData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FShadowNexusData;
// ********** End ScriptStruct FShadowNexusData ****************************************************

// ********** Begin ScriptStruct FTowerOfLamentationData *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_356_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTowerOfLamentationData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FTowerOfLamentationData;
// ********** End ScriptStruct FTowerOfLamentationData *********************************************

// ********** Begin ScriptStruct FPurgatoryAnchorData **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_405_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPurgatoryAnchorData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FPurgatoryAnchorData;
// ********** End ScriptStruct FPurgatoryAnchorData ************************************************

// ********** Begin ScriptStruct FSpectralGuardianData *********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_471_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSpectralGuardianData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FSpectralGuardianData;
// ********** End ScriptStruct FSpectralGuardianData ***********************************************

// ********** Begin Class AAURACRONPCGEnvironment **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetCrossBlendStrength); \
	DECLARE_FUNCTION(execEnableCrossEnvironmentBlending); \
	DECLARE_FUNCTION(execSetEnvironmentPriority); \
	DECLARE_FUNCTION(execEnableSimultaneousMode); \
	DECLARE_FUNCTION(execSetZephyrTransitionStrength); \
	DECLARE_FUNCTION(execSetTransitionToZephyr); \
	DECLARE_FUNCTION(execUpdateTransitionSettings); \
	DECLARE_FUNCTION(execSetCloudTransitionSpeed); \
	DECLARE_FUNCTION(execEnableVolumetricClouds); \
	DECLARE_FUNCTION(execSetParticleTransitionRate); \
	DECLARE_FUNCTION(execEnableHeightBasedTransition); \
	DECLARE_FUNCTION(execSetTransitionGradient); \
	DECLARE_FUNCTION(execSetAltitudeEffect); \
	DECLARE_FUNCTION(execEnableSmoothTransitions); \
	DECLARE_FUNCTION(execEnableSpectralInteractions); \
	DECLARE_FUNCTION(execEnableWindTransitions); \
	DECLARE_FUNCTION(execEnableLightBlending); \
	DECLARE_FUNCTION(execSetInteractionRadius); \
	DECLARE_FUNCTION(execEnableInteractiveElements); \
	DECLARE_FUNCTION(execSetTransitionComplexity); \
	DECLARE_FUNCTION(execEnableAdvancedTransitions); \
	DECLARE_FUNCTION(execSetBlendingStrength); \
	DECLARE_FUNCTION(execEnableEnvironmentBlending); \
	DECLARE_FUNCTION(execSetPhaseManagerReference); \
	DECLARE_FUNCTION(execHasPhaseManagerReference); \
	DECLARE_FUNCTION(execSetCurrentMapPhase); \
	DECLARE_FUNCTION(execGetCurrentMapPhase); \
	DECLARE_FUNCTION(execUpdateVisualEffects); \
	DECLARE_FUNCTION(execApplyPhaseConfiguration); \
	DECLARE_FUNCTION(execSetSpectralActivity); \
	DECLARE_FUNCTION(execSetShadowIntensity); \
	DECLARE_FUNCTION(execSetCloudDensity); \
	DECLARE_FUNCTION(execSetWindStrength); \
	DECLARE_FUNCTION(execSetParticleSpawnRate); \
	DECLARE_FUNCTION(execSetLightIntensity); \
	DECLARE_FUNCTION(execStartGradualEmergence); \
	DECLARE_FUNCTION(execSetEmergenceRate); \
	DECLARE_FUNCTION(execSetBlendRadius); \
	DECLARE_FUNCTION(execSetTransitionSpeed); \
	DECLARE_FUNCTION(execGetEnvironmentIntensity); \
	DECLARE_FUNCTION(execSetEnvironmentIntensity); \
	DECLARE_FUNCTION(execUpdateRenderingQuality); \
	DECLARE_FUNCTION(execSetHighEndDevice); \
	DECLARE_FUNCTION(execUpdateBoundaryEffects); \
	DECLARE_FUNCTION(execSetSpaceDistortionStrength); \
	DECLARE_FUNCTION(execEnableSpaceDistortion); \
	DECLARE_FUNCTION(execSetBoundaryParticleIntensity); \
	DECLARE_FUNCTION(execEnableBoundaryParticles); \
	DECLARE_FUNCTION(execSetBoundaryBlurType); \
	DECLARE_FUNCTION(execSetBoundaryBlurColor); \
	DECLARE_FUNCTION(execSetBoundaryGradientStrength); \
	DECLARE_FUNCTION(execEnableBoundaryBlur); \
	DECLARE_FUNCTION(execSetBoundaryBlurRadius); \
	DECLARE_FUNCTION(execSetBoundaryBlurStrength); \
	DECLARE_FUNCTION(execGetEnvironmentHeightAt); \
	DECLARE_FUNCTION(execSetPCGParameterModern); \
	DECLARE_FUNCTION(execGetPhaseBasedColor); \
	DECLARE_FUNCTION(execGetCurrentPhaseIntensity); \
	DECLARE_FUNCTION(execSetDensityScale); \
	DECLARE_FUNCTION(execOnMapContraction); \
	DECLARE_FUNCTION(execGetTacticalPortalLocations); \
	DECLARE_FUNCTION(execIsEnvironmentActive); \
	DECLARE_FUNCTION(execGetPCGComponent); \
	DECLARE_FUNCTION(execApplyTransitionEffect); \
	DECLARE_FUNCTION(execDeactivateEnvironment); \
	DECLARE_FUNCTION(execActivateEnvironment); \
	DECLARE_FUNCTION(execRegisterWithEnvironmentManager); \
	DECLARE_FUNCTION(execGetActivityScale); \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execSetEnvironmentVisibility); \
	DECLARE_FUNCTION(execUpdatePurgatoryAnchor); \
	DECLARE_FUNCTION(execGeneratePurgatoryAnchor); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateEnvironment); \
	DECLARE_FUNCTION(execGetEnvironmentType); \
	DECLARE_FUNCTION(execSetEnvironmentType); \
	DECLARE_FUNCTION(execGetTemporalDistortionIntensity); \
	DECLARE_FUNCTION(execIsInTemporalDistortionZone); \
	DECLARE_FUNCTION(execIsNearGeothermalVent); \
	DECLARE_FUNCTION(execHasActiveGeothermalVents); \
	DECLARE_FUNCTION(execApplyEnvironmentEffectsToCharacter); \
	DECLARE_FUNCTION(execIsCharacterInEnvironment);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGEnvironment(); \
	friend struct Z_Construct_UClass_AAURACRONPCGEnvironment_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGEnvironment, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGEnvironment) \
	NO_API void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override; \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ActivityScale=NETFIELD_REP_START, \
		bHasCrystallinePlateaus, \
		bHasLivingCanyons, \
		bHasBreathingForests, \
		bHasTectonicBridges, \
		bHasOrbitalArchipelagos, \
		bHasAuroraBridges, \
		bHasCloudFortresses, \
		bHasStellarGardens, \
		bHasVoidRifts, \
		bHasSpectralPlains, \
		bHasRiversOfSouls, \
		bHasFragmentedStructures, \
		bHasTemporalDistortionZones, \
		bHasAerialIslands, \
		bHasFloatingCrystals, \
		bHasWindCurrents, \
		bHasAbyssalChasms, \
		bHasEtherealFog, \
		bHasTemporalDistortions, \
		EnvironmentType, \
		bIsActive, \
		NETFIELD_REP_END=bIsActive	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGEnvironment(AAURACRONPCGEnvironment&&) = delete; \
	AAURACRONPCGEnvironment(const AAURACRONPCGEnvironment&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGEnvironment); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGEnvironment); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGEnvironment) \
	NO_API virtual ~AAURACRONPCGEnvironment();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_570_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_573_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGEnvironment;

// ********** End Class AAURACRONPCGEnvironment ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
