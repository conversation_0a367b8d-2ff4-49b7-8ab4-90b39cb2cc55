// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGEnvironmentManager.h"

#ifdef AURACRON_AURACRONPCGEnvironmentManager_generated_h
#error "AURACRONPCGEnvironmentManager.generated.h already included, missing '#pragma once' in AURACRONPCGEnvironmentManager.h"
#endif
#define AURACRON_AURACRONPCGEnvironmentManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AAURACRONPCGEnvironment;
class AAURACRONPCGPortal;
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;
struct FAURACRONEnvironmentSettings;
struct FAURACRONMapTacticalAdvantages;

// ********** Begin ScriptStruct FAURACRONEnvironmentArray *****************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_35_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONEnvironmentArray;
// ********** End ScriptStruct FAURACRONEnvironmentArray *******************************************

// ********** Begin ScriptStruct FAURACRONTeleportDestinations *************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_51_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONTeleportDestinations;
// ********** End ScriptStruct FAURACRONTeleportDestinations ***************************************

// ********** Begin ScriptStruct FAURACRONDeviceEnvironmentSettings ********************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_72_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONDeviceEnvironmentSettings;
// ********** End ScriptStruct FAURACRONDeviceEnvironmentSettings **********************************

// ********** Begin ScriptStruct FAURACRONEnvironmentSettings **************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONEnvironmentSettings;
// ********** End ScriptStruct FAURACRONEnvironmentSettings ****************************************

// ********** Begin Class AAURACRONPCGEnvironmentManager *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetTeleportDestinations); \
	DECLARE_FUNCTION(execGetPortalsForEnvironment); \
	DECLARE_FUNCTION(execDeactivateAllPortals); \
	DECLARE_FUNCTION(execActivatePortalsForEnvironment); \
	DECLARE_FUNCTION(execUpdateTacticalPortals); \
	DECLARE_FUNCTION(execCreateTacticalPortals); \
	DECLARE_FUNCTION(execApplyTransitionToInstances); \
	DECLARE_FUNCTION(execDeactivateEnvironmentInstances); \
	DECLARE_FUNCTION(execActivateEnvironmentInstances); \
	DECLARE_FUNCTION(execGetEnvironmentInstances); \
	DECLARE_FUNCTION(execUnregisterEnvironmentInstance); \
	DECLARE_FUNCTION(execRegisterEnvironmentInstance); \
	DECLARE_FUNCTION(execOnMapPhaseChanged); \
	DECLARE_FUNCTION(execConfigureForResolutionPhaseModern); \
	DECLARE_FUNCTION(execConfigureForIntensificationPhaseModern); \
	DECLARE_FUNCTION(execConfigureForConvergencePhaseModern); \
	DECLARE_FUNCTION(execConfigureForAwakeningPhaseModern); \
	DECLARE_FUNCTION(execAreResolutionSystemsReady); \
	DECLARE_FUNCTION(execAreIntensificationSystemsReady); \
	DECLARE_FUNCTION(execAreConvergenceSystemsReady); \
	DECLARE_FUNCTION(execOnMapContraction); \
	DECLARE_FUNCTION(execApplyTemporaryEnvironmentEffect); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGetEnvironmentSettings); \
	DECLARE_FUNCTION(execIsInTransition); \
	DECLARE_FUNCTION(execGetTransitionProgress); \
	DECLARE_FUNCTION(execGetTimeRemainingInCurrentEnvironment); \
	DECLARE_FUNCTION(execGetNextEnvironment); \
	DECLARE_FUNCTION(execGetCurrentEnvironment); \
	DECLARE_FUNCTION(execGetCurrentMapTacticalAdvantages); \
	DECLARE_FUNCTION(execRemoveMapTacticalAdvantages); \
	DECLARE_FUNCTION(execApplyMapTacticalAdvantages); \
	DECLARE_FUNCTION(execForceTransitionToEnvironment); \
	DECLARE_FUNCTION(execStopEnvironmentRotation); \
	DECLARE_FUNCTION(execStartEnvironmentRotation); \
	DECLARE_FUNCTION(execGetCurrentSanctuaryIslandCount); \
	DECLARE_FUNCTION(execValidateSanctuaryIslandDistribution); \
	DECLARE_FUNCTION(execFixPhaseManagerIntegrationIssues); \
	DECLARE_FUNCTION(execValidatePhaseManagerIntegration); \
	DECLARE_FUNCTION(execConfigureBlurredEnvironmentBoundaries); \
	DECLARE_FUNCTION(execConfigureSimultaneousEnvironments); \
	DECLARE_FUNCTION(execConfigureSmoothTransitionToZephyr); \
	DECLARE_FUNCTION(execConfigureForConvergencePhase); \
	DECLARE_FUNCTION(execConfigureForAwakeningPhase); \
	DECLARE_FUNCTION(execConfigureForDeviceType); \
	DECLARE_FUNCTION(execInitializeEnvironmentSystem);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGEnvironmentManager(); \
	friend struct Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGEnvironmentManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGEnvironmentManager)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGEnvironmentManager(AAURACRONPCGEnvironmentManager&&) = delete; \
	AAURACRONPCGEnvironmentManager(const AAURACRONPCGEnvironmentManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGEnvironmentManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGEnvironmentManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGEnvironmentManager) \
	NO_API virtual ~AAURACRONPCGEnvironmentManager();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_199_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h_202_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGEnvironmentManager;

// ********** End Class AAURACRONPCGEnvironmentManager *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
