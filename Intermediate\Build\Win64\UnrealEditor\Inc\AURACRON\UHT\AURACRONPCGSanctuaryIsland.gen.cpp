// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGSanctuaryIsland.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGSanctuaryIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland();
AURACRON_API UClass* Z_Construct_UClass_ASanctuaryIsland();
AURACRON_API UClass* Z_Construct_UClass_ASanctuaryIsland_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class ASanctuaryIsland Function ActivateSecureZone *****************************
struct Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar zona segura */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar zona segura" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "ActivateSecureZone", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execActivateSecureZone)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateSecureZone();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function ActivateSecureZone *******************************

// ********** Begin Class ASanctuaryIsland Function ApplyAllSanctuaryEffects ***********************
struct Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics
{
	struct SanctuaryIsland_eventApplyAllSanctuaryEffects_Parms
	{
		AActor* Actor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplica todos os efeitos ben\xc3\xa9""ficos da Ilha Santu\xc3\xa1rio a um ator */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica todos os efeitos ben\xc3\xa9""ficos da Ilha Santu\xc3\xa1rio a um ator" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventApplyAllSanctuaryEffects_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::NewProp_Actor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "ApplyAllSanctuaryEffects", Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::SanctuaryIsland_eventApplyAllSanctuaryEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::SanctuaryIsland_eventApplyAllSanctuaryEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execApplyAllSanctuaryEffects)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyAllSanctuaryEffects(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function ApplyAllSanctuaryEffects *************************

// ********** Begin Class ASanctuaryIsland Function ApplyHealing ***********************************
struct Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics
{
	struct SanctuaryIsland_eventApplyHealing_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es que estavam faltando\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es que estavam faltando" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventApplyHealing_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "ApplyHealing", Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::SanctuaryIsland_eventApplyHealing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::SanctuaryIsland_eventApplyHealing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execApplyHealing)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyHealing(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function ApplyHealing *************************************

// ********** Begin Class ASanctuaryIsland Function DeactivateSecureZone ***************************
struct Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desativar zona segura */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar zona segura" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "DeactivateSecureZone", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execDeactivateSecureZone)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateSecureZone();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function DeactivateSecureZone *****************************

// ********** Begin Class ASanctuaryIsland Function GetEnvironmentType *****************************
struct Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics
{
	struct SanctuaryIsland_eventGetEnvironmentType_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter tipo de ambiente da ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tipo de ambiente da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventGetEnvironmentType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "GetEnvironmentType", Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::SanctuaryIsland_eventGetEnvironmentType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::SanctuaryIsland_eventGetEnvironmentType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execGetEnvironmentType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetEnvironmentType();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function GetEnvironmentType *******************************

// ********** Begin Class ASanctuaryIsland Function GetStrategicValue ******************************
struct Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics
{
	struct SanctuaryIsland_eventGetStrategicValue_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter valor estrat\xc3\xa9gico da ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter valor estrat\xc3\xa9gico da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventGetStrategicValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "GetStrategicValue", Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::SanctuaryIsland_eventGetStrategicValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::SanctuaryIsland_eventGetStrategicValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execGetStrategicValue)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStrategicValue();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function GetStrategicValue ********************************

// ********** Begin Class ASanctuaryIsland Function GrantHealingEffect *****************************
struct Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics
{
	struct SanctuaryIsland_eventGrantHealingEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede efeito de cura ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede efeito de cura ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventGrantHealingEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "GrantHealingEffect", Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::SanctuaryIsland_eventGrantHealingEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::SanctuaryIsland_eventGrantHealingEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execGrantHealingEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantHealingEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function GrantHealingEffect *******************************

// ********** Begin Class ASanctuaryIsland Function GrantProtection ********************************
struct Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics
{
	struct SanctuaryIsland_eventGrantProtection_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sanctuary Island" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventGrantProtection_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "GrantProtection", Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::SanctuaryIsland_eventGrantProtection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::SanctuaryIsland_eventGrantProtection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_GrantProtection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_GrantProtection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execGrantProtection)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantProtection(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function GrantProtection **********************************

// ********** Begin Class ASanctuaryIsland Function GrantProtectionEffect **************************
struct Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics
{
	struct SanctuaryIsland_eventGrantProtectionEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede efeito de prote\xc3\xa7\xc3\xa3o ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede efeito de prote\xc3\xa7\xc3\xa3o ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventGrantProtectionEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "GrantProtectionEffect", Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::SanctuaryIsland_eventGrantProtectionEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::SanctuaryIsland_eventGrantProtectionEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execGrantProtectionEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantProtectionEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function GrantProtectionEffect ****************************

// ********** Begin Class ASanctuaryIsland Function GrantVisionAmplificationEffect *****************
struct Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics
{
	struct SanctuaryIsland_eventGrantVisionAmplificationEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede efeito de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede efeito de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventGrantVisionAmplificationEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "GrantVisionAmplificationEffect", Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::SanctuaryIsland_eventGrantVisionAmplificationEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::SanctuaryIsland_eventGrantVisionAmplificationEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execGrantVisionAmplificationEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantVisionAmplificationEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function GrantVisionAmplificationEffect *******************

// ********** Begin Class ASanctuaryIsland Function HasValidHealingConfiguration *******************
struct Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics
{
	struct SanctuaryIsland_eventHasValidHealingConfiguration_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se tem configura\xc3\xa7\xc3\xa3o de cura v\xc3\xa1lida */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se tem configura\xc3\xa7\xc3\xa3o de cura v\xc3\xa1lida" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SanctuaryIsland_eventHasValidHealingConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventHasValidHealingConfiguration_Parms), &Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "HasValidHealingConfiguration", Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::SanctuaryIsland_eventHasValidHealingConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::SanctuaryIsland_eventHasValidHealingConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execHasValidHealingConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasValidHealingConfiguration();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function HasValidHealingConfiguration *********************

// ********** Begin Class ASanctuaryIsland Function HasValidProtectionConfiguration ****************
struct Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics
{
	struct SanctuaryIsland_eventHasValidProtectionConfiguration_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se tem configura\xc3\xa7\xc3\xa3o de prote\xc3\xa7\xc3\xa3o v\xc3\xa1lida */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se tem configura\xc3\xa7\xc3\xa3o de prote\xc3\xa7\xc3\xa3o v\xc3\xa1lida" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SanctuaryIsland_eventHasValidProtectionConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventHasValidProtectionConfiguration_Parms), &Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "HasValidProtectionConfiguration", Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::SanctuaryIsland_eventHasValidProtectionConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::SanctuaryIsland_eventHasValidProtectionConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execHasValidProtectionConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasValidProtectionConfiguration();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function HasValidProtectionConfiguration ******************

// ********** Begin Class ASanctuaryIsland Function HealCharactersInRange **************************
struct Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sanctuary Island" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "HealCharactersInRange", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execHealCharactersInRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HealCharactersInRange();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function HealCharactersInRange ****************************

// ********** Begin Class ASanctuaryIsland Function InitializeSanctuaryIsland **********************
struct Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar ilha santu\xc3\xa1rio */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar ilha santu\xc3\xa1rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "InitializeSanctuaryIsland", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execInitializeSanctuaryIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeSanctuaryIsland();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function InitializeSanctuaryIsland ************************

// ********** Begin Class ASanctuaryIsland Function IsActorInSecureZone ****************************
struct Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics
{
	struct SanctuaryIsland_eventIsActorInSecureZone_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "///** Verifica se um ator est\xc3\xa1 dentro da zona segura */\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se um ator est\xc3\xa1 dentro da zona segura" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventIsActorInSecureZone_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SanctuaryIsland_eventIsActorInSecureZone_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventIsActorInSecureZone_Parms), &Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "IsActorInSecureZone", Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::SanctuaryIsland_eventIsActorInSecureZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::SanctuaryIsland_eventIsActorInSecureZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execIsActorInSecureZone)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsActorInSecureZone(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function IsActorInSecureZone ******************************

// ********** Begin Class ASanctuaryIsland Function IsInCalmFlowSection ****************************
struct Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics
{
	struct SanctuaryIsland_eventIsInCalmFlowSection_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se esta ilha est\xc3\xa1 posicionada em uma se\xc3\xa7\xc3\xa3o calma do fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se esta ilha est\xc3\xa1 posicionada em uma se\xc3\xa7\xc3\xa3o calma do fluxo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SanctuaryIsland_eventIsInCalmFlowSection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventIsInCalmFlowSection_Parms), &Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "IsInCalmFlowSection", Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::SanctuaryIsland_eventIsInCalmFlowSection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::SanctuaryIsland_eventIsInCalmFlowSection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execIsInCalmFlowSection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInCalmFlowSection();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function IsInCalmFlowSection ******************************

// ********** Begin Class ASanctuaryIsland Function IsSecureZoneActive *****************************
struct Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics
{
	struct SanctuaryIsland_eventIsSecureZoneActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Implementa a funcionalidade de \"zona segura para reagrupamento e cura\"\n// conforme especificado na documenta\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa a funcionalidade de \"zona segura para reagrupamento e cura\"\nconforme especificado na documenta\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SanctuaryIsland_eventIsSecureZoneActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventIsSecureZoneActive_Parms), &Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "IsSecureZoneActive", Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::SanctuaryIsland_eventIsSecureZoneActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::SanctuaryIsland_eventIsSecureZoneActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execIsSecureZoneActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSecureZoneActive();
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function IsSecureZoneActive *******************************

// ********** Begin Class ASanctuaryIsland Function RemoveIslandEffects ****************************
struct Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics
{
	struct SanctuaryIsland_eventRemoveIslandEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove os efeitos de cura, prote\xc3\xa7\xc3\xa3o e amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove os efeitos de cura, prote\xc3\xa7\xc3\xa3o e amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventRemoveIslandEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "RemoveIslandEffects", Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::SanctuaryIsland_eventRemoveIslandEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::SanctuaryIsland_eventRemoveIslandEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execRemoveIslandEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveIslandEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function RemoveIslandEffects ******************************

// ********** Begin Class ASanctuaryIsland Function SetEnvironmentType *****************************
struct Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics
{
	struct SanctuaryIsland_eventSetEnvironmentType_Parms
	{
		EAURACRONEnvironmentType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir tipo de ambiente da ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir tipo de ambiente da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventSetEnvironmentType_Parms, NewType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "SetEnvironmentType", Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::SanctuaryIsland_eventSetEnvironmentType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::SanctuaryIsland_eventSetEnvironmentType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execSetEnvironmentType)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnvironmentType(EAURACRONEnvironmentType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function SetEnvironmentType *******************************

// ********** Begin Class ASanctuaryIsland Function SetInCalmFlowSection ***************************
struct Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics
{
	struct SanctuaryIsland_eventSetInCalmFlowSection_Parms
	{
		bool bInCalmSection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Define se esta ilha est\xc3\xa1 em uma se\xc3\xa7\xc3\xa3o calma do fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define se esta ilha est\xc3\xa1 em uma se\xc3\xa7\xc3\xa3o calma do fluxo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bInCalmSection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInCalmSection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::NewProp_bInCalmSection_SetBit(void* Obj)
{
	((SanctuaryIsland_eventSetInCalmFlowSection_Parms*)Obj)->bInCalmSection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::NewProp_bInCalmSection = { "bInCalmSection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventSetInCalmFlowSection_Parms), &Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::NewProp_bInCalmSection_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::NewProp_bInCalmSection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "SetInCalmFlowSection", Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::SanctuaryIsland_eventSetInCalmFlowSection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::SanctuaryIsland_eventSetInCalmFlowSection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execSetInCalmFlowSection)
{
	P_GET_UBOOL(Z_Param_bInCalmSection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetInCalmFlowSection(Z_Param_bInCalmSection);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function SetInCalmFlowSection *****************************

// ********** Begin Class ASanctuaryIsland Function SetSecureZoneActive ****************************
struct Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics
{
	struct SanctuaryIsland_eventSetSecureZoneActive_Parms
	{
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ativa/desativa a zona segura\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa/desativa a zona segura" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((SanctuaryIsland_eventSetSecureZoneActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SanctuaryIsland_eventSetSecureZoneActive_Parms), &Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "SetSecureZoneActive", Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::SanctuaryIsland_eventSetSecureZoneActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::SanctuaryIsland_eventSetSecureZoneActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execSetSecureZoneActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSecureZoneActive(Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function SetSecureZoneActive ******************************

// ********** Begin Class ASanctuaryIsland Function UpdateBasedOnMapPhase **************************
struct Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics
{
	struct SanctuaryIsland_eventUpdateBasedOnMapPhase_Parms
	{
		EAURACRONMapPhase CurrentPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualiza a ilha com base na fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza a ilha com base na fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SanctuaryIsland_eventUpdateBasedOnMapPhase_Parms, CurrentPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::NewProp_CurrentPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ASanctuaryIsland, nullptr, "UpdateBasedOnMapPhase", Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::SanctuaryIsland_eventUpdateBasedOnMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::SanctuaryIsland_eventUpdateBasedOnMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASanctuaryIsland::execUpdateBasedOnMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_CurrentPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateBasedOnMapPhase(EAURACRONMapPhase(Z_Param_CurrentPhase));
	P_NATIVE_END;
}
// ********** End Class ASanctuaryIsland Function UpdateBasedOnMapPhase ****************************

// ********** Begin Class ASanctuaryIsland *********************************************************
void ASanctuaryIsland::StaticRegisterNativesASanctuaryIsland()
{
	UClass* Class = ASanctuaryIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateSecureZone", &ASanctuaryIsland::execActivateSecureZone },
		{ "ApplyAllSanctuaryEffects", &ASanctuaryIsland::execApplyAllSanctuaryEffects },
		{ "ApplyHealing", &ASanctuaryIsland::execApplyHealing },
		{ "DeactivateSecureZone", &ASanctuaryIsland::execDeactivateSecureZone },
		{ "GetEnvironmentType", &ASanctuaryIsland::execGetEnvironmentType },
		{ "GetStrategicValue", &ASanctuaryIsland::execGetStrategicValue },
		{ "GrantHealingEffect", &ASanctuaryIsland::execGrantHealingEffect },
		{ "GrantProtection", &ASanctuaryIsland::execGrantProtection },
		{ "GrantProtectionEffect", &ASanctuaryIsland::execGrantProtectionEffect },
		{ "GrantVisionAmplificationEffect", &ASanctuaryIsland::execGrantVisionAmplificationEffect },
		{ "HasValidHealingConfiguration", &ASanctuaryIsland::execHasValidHealingConfiguration },
		{ "HasValidProtectionConfiguration", &ASanctuaryIsland::execHasValidProtectionConfiguration },
		{ "HealCharactersInRange", &ASanctuaryIsland::execHealCharactersInRange },
		{ "InitializeSanctuaryIsland", &ASanctuaryIsland::execInitializeSanctuaryIsland },
		{ "IsActorInSecureZone", &ASanctuaryIsland::execIsActorInSecureZone },
		{ "IsInCalmFlowSection", &ASanctuaryIsland::execIsInCalmFlowSection },
		{ "IsSecureZoneActive", &ASanctuaryIsland::execIsSecureZoneActive },
		{ "RemoveIslandEffects", &ASanctuaryIsland::execRemoveIslandEffects },
		{ "SetEnvironmentType", &ASanctuaryIsland::execSetEnvironmentType },
		{ "SetInCalmFlowSection", &ASanctuaryIsland::execSetInCalmFlowSection },
		{ "SetSecureZoneActive", &ASanctuaryIsland::execSetSecureZoneActive },
		{ "UpdateBasedOnMapPhase", &ASanctuaryIsland::execUpdateBasedOnMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ASanctuaryIsland;
UClass* ASanctuaryIsland::GetPrivateStaticClass()
{
	using TClass = ASanctuaryIsland;
	if (!Z_Registration_Info_UClass_ASanctuaryIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SanctuaryIsland"),
			Z_Registration_Info_UClass_ASanctuaryIsland.InnerSingleton,
			StaticRegisterNativesASanctuaryIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ASanctuaryIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_ASanctuaryIsland_NoRegister()
{
	return ASanctuaryIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ASanctuaryIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Sanctuary Island\n * Ilha de ref\xc3\xbagio e recupera\xc3\xa7\xc3\xa3o com fontes de cura, prote\xc3\xa7\xc3\xa3o e amplificadores de vis\xc3\xa3o\n * Conforme documenta\xc3\xa7\xc3\xa3o: Fontes de cura, escudos tempor\xc3\xa1rios, amplificadores de vis\xc3\xa3o\n * Valor Estrat\xc3\xa9gico: Zonas seguras para reagrupamento e cura\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGSanctuaryIsland.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Sanctuary Island\nIlha de ref\xc3\xbagio e recupera\xc3\xa7\xc3\xa3o com fontes de cura, prote\xc3\xa7\xc3\xa3o e amplificadores de vis\xc3\xa3o\nConforme documenta\xc3\xa7\xc3\xa3o: Fontes de cura, escudos tempor\xc3\xa1rios, amplificadores de vis\xc3\xa3o\nValor Estrat\xc3\xa9gico: Zonas seguras para reagrupamento e cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingFountain_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fonte de cura central\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fonte de cura central" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual da fonte de cura\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual da fonte de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProtectiveBarrier_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Barreira protetora\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Barreira protetora" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecureZone_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Zona segura (componente de colis\xc3\xa3o)\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Zona segura (componente de colis\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual da barreira\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AncientTree_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xc3\x81rvore antiga\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x81rvore antiga" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionAmplifier_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Amplificador de vis\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Amplificador de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionAmplifierEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual do amplificador de vis\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual do amplificador de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceNodes_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// N\xc3\xb3s de recursos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xb3s de recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do efeito de cura\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do efeito de cura\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProtectionIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do efeito de prote\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito de prote\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProtectionDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do efeito de prote\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito de prote\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual de cura\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProtectionVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual de prote\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual de prote\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingGameplayEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para cura\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProtectionGameplayEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para prote\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para prote\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionAmplificationIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do efeito de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionAmplificationDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do efeito de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionAmplificationVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual de amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionAmplificationGameplayEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para amplifica\xc3\xa7\xc3\xa3o de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingPower_MetaData[] = {
		{ "Category", "Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades que estavam faltando\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades que estavam faltando" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastHealTime_MetaData[] = {
		{ "Category", "Sanctuary Island" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo acumulado para efeitos visuais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSecureZoneActive_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Indica se a zona segura est\xc3\xa1 ativa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indica se a zona segura est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecureZoneRadius_MetaData[] = {
		{ "Category", "Prismal Flow|Sanctuary Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Raio da zona segura\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da zona segura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInCalmFlowSection_MetaData[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Indica se esta ilha est\xc3\xa1 em uma se\xc3\xa7\xc3\xa3o calma do fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indica se esta ilha est\xc3\xa1 em uma se\xc3\xa7\xc3\xa3o calma do fluxo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades adicionais para compatibilidade\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades adicionais para compatibilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicValue_MetaData[] = {
		{ "Category", "AURACRON|SanctuaryIsland" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSanctuaryIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HealingFountain;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HealingEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ProtectiveBarrier;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SecureZone;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BarrierEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AncientTree;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VisionAmplifier;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VisionAmplifierEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResourceNodes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ResourceNodes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProtectionIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProtectionDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HealingVisualEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ProtectionVisualEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_HealingGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ProtectionGameplayEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionAmplificationIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionAmplificationDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VisionAmplificationVisualEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_VisionAmplificationGameplayEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingPower;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastHealTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static void NewProp_bSecureZoneActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSecureZoneActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SecureZoneRadius;
	static void NewProp_bInCalmFlowSection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInCalmFlowSection;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrategicValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ASanctuaryIsland_ActivateSecureZone, "ActivateSecureZone" }, // 2503254814
		{ &Z_Construct_UFunction_ASanctuaryIsland_ApplyAllSanctuaryEffects, "ApplyAllSanctuaryEffects" }, // 2150445041
		{ &Z_Construct_UFunction_ASanctuaryIsland_ApplyHealing, "ApplyHealing" }, // 1031601898
		{ &Z_Construct_UFunction_ASanctuaryIsland_DeactivateSecureZone, "DeactivateSecureZone" }, // 2056232209
		{ &Z_Construct_UFunction_ASanctuaryIsland_GetEnvironmentType, "GetEnvironmentType" }, // 2270841115
		{ &Z_Construct_UFunction_ASanctuaryIsland_GetStrategicValue, "GetStrategicValue" }, // 910214516
		{ &Z_Construct_UFunction_ASanctuaryIsland_GrantHealingEffect, "GrantHealingEffect" }, // 839954111
		{ &Z_Construct_UFunction_ASanctuaryIsland_GrantProtection, "GrantProtection" }, // 3980268374
		{ &Z_Construct_UFunction_ASanctuaryIsland_GrantProtectionEffect, "GrantProtectionEffect" }, // 2725667752
		{ &Z_Construct_UFunction_ASanctuaryIsland_GrantVisionAmplificationEffect, "GrantVisionAmplificationEffect" }, // 311872558
		{ &Z_Construct_UFunction_ASanctuaryIsland_HasValidHealingConfiguration, "HasValidHealingConfiguration" }, // 1307348962
		{ &Z_Construct_UFunction_ASanctuaryIsland_HasValidProtectionConfiguration, "HasValidProtectionConfiguration" }, // 2154933157
		{ &Z_Construct_UFunction_ASanctuaryIsland_HealCharactersInRange, "HealCharactersInRange" }, // 534091269
		{ &Z_Construct_UFunction_ASanctuaryIsland_InitializeSanctuaryIsland, "InitializeSanctuaryIsland" }, // 2329332811
		{ &Z_Construct_UFunction_ASanctuaryIsland_IsActorInSecureZone, "IsActorInSecureZone" }, // 1180628704
		{ &Z_Construct_UFunction_ASanctuaryIsland_IsInCalmFlowSection, "IsInCalmFlowSection" }, // 222058493
		{ &Z_Construct_UFunction_ASanctuaryIsland_IsSecureZoneActive, "IsSecureZoneActive" }, // 1183952201
		{ &Z_Construct_UFunction_ASanctuaryIsland_RemoveIslandEffects, "RemoveIslandEffects" }, // 2031655461
		{ &Z_Construct_UFunction_ASanctuaryIsland_SetEnvironmentType, "SetEnvironmentType" }, // 694591979
		{ &Z_Construct_UFunction_ASanctuaryIsland_SetInCalmFlowSection, "SetInCalmFlowSection" }, // 1451172562
		{ &Z_Construct_UFunction_ASanctuaryIsland_SetSecureZoneActive, "SetSecureZoneActive" }, // 3703832428
		{ &Z_Construct_UFunction_ASanctuaryIsland_UpdateBasedOnMapPhase, "UpdateBasedOnMapPhase" }, // 3122073728
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ASanctuaryIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingFountain = { "HealingFountain", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingFountain), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingFountain_MetaData), NewProp_HealingFountain_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingEffect = { "HealingEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingEffect_MetaData), NewProp_HealingEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectiveBarrier = { "ProtectiveBarrier", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, ProtectiveBarrier), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProtectiveBarrier_MetaData), NewProp_ProtectiveBarrier_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_SecureZone = { "SecureZone", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, SecureZone), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecureZone_MetaData), NewProp_SecureZone_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_BarrierEffect = { "BarrierEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, BarrierEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierEffect_MetaData), NewProp_BarrierEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_AncientTree = { "AncientTree", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, AncientTree), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AncientTree_MetaData), NewProp_AncientTree_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplifier = { "VisionAmplifier", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, VisionAmplifier), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionAmplifier_MetaData), NewProp_VisionAmplifier_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplifierEffect = { "VisionAmplifierEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, VisionAmplifierEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionAmplifierEffect_MetaData), NewProp_VisionAmplifierEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ResourceNodes_Inner = { "ResourceNodes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ResourceNodes = { "ResourceNodes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, ResourceNodes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceNodes_MetaData), NewProp_ResourceNodes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingIntensity = { "HealingIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingIntensity_MetaData), NewProp_HealingIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingDuration = { "HealingDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingDuration_MetaData), NewProp_HealingDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionIntensity = { "ProtectionIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, ProtectionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProtectionIntensity_MetaData), NewProp_ProtectionIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionDuration = { "ProtectionDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, ProtectionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProtectionDuration_MetaData), NewProp_ProtectionDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingVisualEffect = { "HealingVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingVisualEffect_MetaData), NewProp_HealingVisualEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionVisualEffect = { "ProtectionVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, ProtectionVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProtectionVisualEffect_MetaData), NewProp_ProtectionVisualEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingGameplayEffect = { "HealingGameplayEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingGameplayEffect_MetaData), NewProp_HealingGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionGameplayEffect = { "ProtectionGameplayEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, ProtectionGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProtectionGameplayEffect_MetaData), NewProp_ProtectionGameplayEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationIntensity = { "VisionAmplificationIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, VisionAmplificationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionAmplificationIntensity_MetaData), NewProp_VisionAmplificationIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationDuration = { "VisionAmplificationDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, VisionAmplificationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionAmplificationDuration_MetaData), NewProp_VisionAmplificationDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationVisualEffect = { "VisionAmplificationVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, VisionAmplificationVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionAmplificationVisualEffect_MetaData), NewProp_VisionAmplificationVisualEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationGameplayEffect = { "VisionAmplificationGameplayEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, VisionAmplificationGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionAmplificationGameplayEffect_MetaData), NewProp_VisionAmplificationGameplayEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingPower = { "HealingPower", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, HealingPower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingPower_MetaData), NewProp_HealingPower_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_LastHealTime = { "LastHealTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, LastHealTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastHealTime_MetaData), NewProp_LastHealTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
void Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bSecureZoneActive_SetBit(void* Obj)
{
	((ASanctuaryIsland*)Obj)->bSecureZoneActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bSecureZoneActive = { "bSecureZoneActive", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ASanctuaryIsland), &Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bSecureZoneActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSecureZoneActive_MetaData), NewProp_bSecureZoneActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_SecureZoneRadius = { "SecureZoneRadius", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, SecureZoneRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecureZoneRadius_MetaData), NewProp_SecureZoneRadius_MetaData) };
void Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bInCalmFlowSection_SetBit(void* Obj)
{
	((ASanctuaryIsland*)Obj)->bInCalmFlowSection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bInCalmFlowSection = { "bInCalmFlowSection", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ASanctuaryIsland), &Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bInCalmFlowSection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInCalmFlowSection_MetaData), NewProp_bInCalmFlowSection_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2509470107
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_StrategicValue = { "StrategicValue", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASanctuaryIsland, StrategicValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicValue_MetaData), NewProp_StrategicValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ASanctuaryIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingFountain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectiveBarrier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_SecureZone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_BarrierEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_AncientTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplifier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplifierEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ResourceNodes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ResourceNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_ProtectionGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_VisionAmplificationGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_HealingPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_LastHealTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bSecureZoneActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_SecureZoneRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_bInCalmFlowSection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASanctuaryIsland_Statics::NewProp_StrategicValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASanctuaryIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ASanctuaryIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APrismalFlowIsland,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASanctuaryIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ASanctuaryIsland_Statics::ClassParams = {
	&ASanctuaryIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ASanctuaryIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ASanctuaryIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ASanctuaryIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_ASanctuaryIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ASanctuaryIsland()
{
	if (!Z_Registration_Info_UClass_ASanctuaryIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ASanctuaryIsland.OuterSingleton, Z_Construct_UClass_ASanctuaryIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ASanctuaryIsland.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void ASanctuaryIsland::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_HealingIntensity(TEXT("HealingIntensity"));
	static FName Name_HealingDuration(TEXT("HealingDuration"));
	static FName Name_ProtectionIntensity(TEXT("ProtectionIntensity"));
	static FName Name_ProtectionDuration(TEXT("ProtectionDuration"));
	static FName Name_HealingGameplayEffect(TEXT("HealingGameplayEffect"));
	static FName Name_ProtectionGameplayEffect(TEXT("ProtectionGameplayEffect"));
	static FName Name_VisionAmplificationIntensity(TEXT("VisionAmplificationIntensity"));
	static FName Name_VisionAmplificationDuration(TEXT("VisionAmplificationDuration"));
	static FName Name_VisionAmplificationGameplayEffect(TEXT("VisionAmplificationGameplayEffect"));
	static FName Name_bSecureZoneActive(TEXT("bSecureZoneActive"));
	static FName Name_SecureZoneRadius(TEXT("SecureZoneRadius"));
	const bool bIsValid = true
		&& Name_HealingIntensity == ClassReps[(int32)ENetFields_Private::HealingIntensity].Property->GetFName()
		&& Name_HealingDuration == ClassReps[(int32)ENetFields_Private::HealingDuration].Property->GetFName()
		&& Name_ProtectionIntensity == ClassReps[(int32)ENetFields_Private::ProtectionIntensity].Property->GetFName()
		&& Name_ProtectionDuration == ClassReps[(int32)ENetFields_Private::ProtectionDuration].Property->GetFName()
		&& Name_HealingGameplayEffect == ClassReps[(int32)ENetFields_Private::HealingGameplayEffect].Property->GetFName()
		&& Name_ProtectionGameplayEffect == ClassReps[(int32)ENetFields_Private::ProtectionGameplayEffect].Property->GetFName()
		&& Name_VisionAmplificationIntensity == ClassReps[(int32)ENetFields_Private::VisionAmplificationIntensity].Property->GetFName()
		&& Name_VisionAmplificationDuration == ClassReps[(int32)ENetFields_Private::VisionAmplificationDuration].Property->GetFName()
		&& Name_VisionAmplificationGameplayEffect == ClassReps[(int32)ENetFields_Private::VisionAmplificationGameplayEffect].Property->GetFName()
		&& Name_bSecureZoneActive == ClassReps[(int32)ENetFields_Private::bSecureZoneActive].Property->GetFName()
		&& Name_SecureZoneRadius == ClassReps[(int32)ENetFields_Private::SecureZoneRadius].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in ASanctuaryIsland"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(ASanctuaryIsland);
ASanctuaryIsland::~ASanctuaryIsland() {}
// ********** End Class ASanctuaryIsland ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ASanctuaryIsland, ASanctuaryIsland::StaticClass, TEXT("ASanctuaryIsland"), &Z_Registration_Info_UClass_ASanctuaryIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ASanctuaryIsland), 780478346U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h__Script_AURACRON_213737943(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
