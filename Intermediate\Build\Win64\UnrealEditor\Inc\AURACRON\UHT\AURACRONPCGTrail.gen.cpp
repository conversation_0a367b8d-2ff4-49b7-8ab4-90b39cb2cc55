// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGTrail.h"
#include "Engine/HitResult.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGTrail() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAxisTrail();
AURACRON_API UClass* Z_Construct_UClass_AAxisTrail_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_ALunarTrail();
AURACRON_API UClass* Z_Construct_UClass_ALunarTrail_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_ASolarTrail();
AURACRON_API UClass* Z_Construct_UClass_ASolarTrail_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_ATrailBase();
AURACRON_API UClass* Z_Construct_UClass_ATrailBase_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ACharacter_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveLinearColor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnPlayerEnterTrailSignature ******************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnPlayerEnterTrailSignature_Parms
	{
		ACharacter* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate para eventos de entrada de jogador na trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate para eventos de entrada de jogador na trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPlayerEnterTrailSignature_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnPlayerEnterTrailSignature__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::_Script_AURACRON_eventOnPlayerEnterTrailSignature_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::_Script_AURACRON_eventOnPlayerEnterTrailSignature_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlayerEnterTrailSignature_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerEnterTrailSignature, ACharacter* Player)
{
	struct _Script_AURACRON_eventOnPlayerEnterTrailSignature_Parms
	{
		ACharacter* Player;
	};
	_Script_AURACRON_eventOnPlayerEnterTrailSignature_Parms Parms;
	Parms.Player=Player;
	OnPlayerEnterTrailSignature.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerEnterTrailSignature ********************************************

// ********** Begin Class ATrailBase Function ApplyTrailEffect *************************************
struct Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics
{
	struct TrailBase_eventApplyTrailEffect_Parms
	{
		AActor* OverlappingActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeito ao jogador quando est\xc3\xa1 no trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeito ao jogador quando est\xc3\xa1 no trail" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappingActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::NewProp_OverlappingActor = { "OverlappingActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventApplyTrailEffect_Parms, OverlappingActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::NewProp_OverlappingActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "ApplyTrailEffect", Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::TrailBase_eventApplyTrailEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::TrailBase_eventApplyTrailEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_ApplyTrailEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_ApplyTrailEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execApplyTrailEffect)
{
	P_GET_OBJECT(AActor,Z_Param_OverlappingActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTrailEffect(Z_Param_OverlappingActor);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function ApplyTrailEffect ***************************************

// ********** Begin Class ATrailBase Function ConfigureForAwakeningPhase ***************************
struct Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics
{
	struct TrailBase_eventConfigureForAwakeningPhase_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar trilho para Fase 1: Despertar (50% de poder)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar trilho para Fase 1: Despertar (50% de poder)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((TrailBase_eventConfigureForAwakeningPhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TrailBase_eventConfigureForAwakeningPhase_Parms), &Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "ConfigureForAwakeningPhase", Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::TrailBase_eventConfigureForAwakeningPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::TrailBase_eventConfigureForAwakeningPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execConfigureForAwakeningPhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForAwakeningPhase(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function ConfigureForAwakeningPhase *****************************

// ********** Begin Class ATrailBase Function ConfigureForConvergencePhase *************************
struct Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics
{
	struct TrailBase_eventConfigureForConvergencePhase_Parms
	{
		bool bIsEntryDevice;
		bool bIsMidDevice;
		bool bIsHighDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar trilho para Fase 2: Converg\xc3\xaancia (poder baseado na capacidade do dispositivo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar trilho para Fase 2: Converg\xc3\xaancia (poder baseado na capacidade do dispositivo)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static void NewProp_bIsMidDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMidDevice;
	static void NewProp_bIsHighDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHighDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((TrailBase_eventConfigureForConvergencePhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TrailBase_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit(void* Obj)
{
	((TrailBase_eventConfigureForConvergencePhase_Parms*)Obj)->bIsMidDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice = { "bIsMidDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TrailBase_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit(void* Obj)
{
	((TrailBase_eventConfigureForConvergencePhase_Parms*)Obj)->bIsHighDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice = { "bIsHighDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TrailBase_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "ConfigureForConvergencePhase", Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::TrailBase_eventConfigureForConvergencePhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::TrailBase_eventConfigureForConvergencePhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execConfigureForConvergencePhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_GET_UBOOL(Z_Param_bIsMidDevice);
	P_GET_UBOOL(Z_Param_bIsHighDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForConvergencePhase(Z_Param_bIsEntryDevice,Z_Param_bIsMidDevice,Z_Param_bIsHighDevice);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function ConfigureForConvergencePhase ***************************

// ********** Begin Class ATrailBase Function OnOverlapBegin ***************************************
struct Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics
{
	struct TrailBase_eventOnOverlapBegin_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de callback para colis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de callback para colis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapBegin_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapBegin_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapBegin_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapBegin_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((TrailBase_eventOnOverlapBegin_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TrailBase_eventOnOverlapBegin_Parms), &Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapBegin_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "OnOverlapBegin", Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::TrailBase_eventOnOverlapBegin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::TrailBase_eventOnOverlapBegin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_OnOverlapBegin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_OnOverlapBegin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execOnOverlapBegin)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnOverlapBegin(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function OnOverlapBegin *****************************************

// ********** Begin Class ATrailBase Function OnOverlapEnd *****************************************
struct Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics
{
	struct TrailBase_eventOnOverlapEnd_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapEnd_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapEnd_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapEnd_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventOnOverlapEnd_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "OnOverlapEnd", Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::TrailBase_eventOnOverlapEnd_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::TrailBase_eventOnOverlapEnd_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_OnOverlapEnd()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_OnOverlapEnd_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execOnOverlapEnd)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnOverlapEnd(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function OnOverlapEnd *******************************************

// ********** Begin Class ATrailBase Function SetPowerPercentage ***********************************
struct Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics
{
	struct TrailBase_eventSetPowerPercentage_Parms
	{
		float PowerPercentage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir percentual de poder do trilho (0.0 a 1.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir percentual de poder do trilho (0.0 a 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerPercentage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::NewProp_PowerPercentage = { "PowerPercentage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TrailBase_eventSetPowerPercentage_Parms, PowerPercentage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::NewProp_PowerPercentage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "SetPowerPercentage", Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::TrailBase_eventSetPowerPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::TrailBase_eventSetPowerPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_SetPowerPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_SetPowerPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execSetPowerPercentage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_PowerPercentage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPowerPercentage(Z_Param_PowerPercentage);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function SetPowerPercentage *************************************

// ********** Begin Class ATrailBase Function SetTrailActive ***************************************
struct Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics
{
	struct TrailBase_eventSetTrailActive_Parms
	{
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ativa/desativa o trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa/desativa o trail" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((TrailBase_eventSetTrailActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TrailBase_eventSetTrailActive_Parms), &Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ATrailBase, nullptr, "SetTrailActive", Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::TrailBase_eventSetTrailActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::TrailBase_eventSetTrailActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ATrailBase_SetTrailActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ATrailBase_SetTrailActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ATrailBase::execSetTrailActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailActive(Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class ATrailBase Function SetTrailActive *****************************************

// ********** Begin Class ATrailBase ***************************************************************
void ATrailBase::StaticRegisterNativesATrailBase()
{
	UClass* Class = ATrailBase::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyTrailEffect", &ATrailBase::execApplyTrailEffect },
		{ "ConfigureForAwakeningPhase", &ATrailBase::execConfigureForAwakeningPhase },
		{ "ConfigureForConvergencePhase", &ATrailBase::execConfigureForConvergencePhase },
		{ "OnOverlapBegin", &ATrailBase::execOnOverlapBegin },
		{ "OnOverlapEnd", &ATrailBase::execOnOverlapEnd },
		{ "SetPowerPercentage", &ATrailBase::execSetPowerPercentage },
		{ "SetTrailActive", &ATrailBase::execSetTrailActive },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ATrailBase;
UClass* ATrailBase::GetPrivateStaticClass()
{
	using TClass = ATrailBase;
	if (!Z_Registration_Info_UClass_ATrailBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("TrailBase"),
			Z_Registration_Info_UClass_ATrailBase.InnerSingleton,
			StaticRegisterNativesATrailBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ATrailBase.InnerSingleton;
}
UClass* Z_Construct_UClass_ATrailBase_NoRegister()
{
	return ATrailBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ATrailBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe base para todos os tipos de Trails\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGTrail.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe base para todos os tipos de Trails" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailEffectComponent_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de part\xc3\xad""culas Niagara para efeitos visuais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas Niagara para efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComponent_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente de colis\xc3\xa3o para detectar jogadores\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para detectar jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spline que define o caminho do trail\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline que define o caminho do trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailLightComponent_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Luz din\xc3\xa2mica para ilumina\xc3\xa7\xc3\xa3o do trail\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Luz din\xc3\xa2mica para ilumina\xc3\xa7\xc3\xa3o do trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailNiagaraComponent_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente Niagara principal para trilhas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente Niagara principal para trilhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estado do trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado do trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Trail" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailType_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo do trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerPercentage_MetaData[] = {
		{ "Category", "Trail" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Percentual de poder do trail (0.0 a 1.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Percentual de poder do trail (0.0 a 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdaptVisualEffectsToHardware_MetaData[] = {
		{ "Category", "Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeitos visuais adaptados ao hardware\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos visuais adaptados ao hardware" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailEffectComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailLightComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailNiagaraComponent;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrailType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerPercentage;
	static void NewProp_bAdaptVisualEffectsToHardware_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdaptVisualEffectsToHardware;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ATrailBase_ApplyTrailEffect, "ApplyTrailEffect" }, // 1528018522
		{ &Z_Construct_UFunction_ATrailBase_ConfigureForAwakeningPhase, "ConfigureForAwakeningPhase" }, // 438397468
		{ &Z_Construct_UFunction_ATrailBase_ConfigureForConvergencePhase, "ConfigureForConvergencePhase" }, // 2720880347
		{ &Z_Construct_UFunction_ATrailBase_OnOverlapBegin, "OnOverlapBegin" }, // 1067375618
		{ &Z_Construct_UFunction_ATrailBase_OnOverlapEnd, "OnOverlapEnd" }, // 534600589
		{ &Z_Construct_UFunction_ATrailBase_SetPowerPercentage, "SetPowerPercentage" }, // 3619280988
		{ &Z_Construct_UFunction_ATrailBase_SetTrailActive, "SetTrailActive" }, // 690223089
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ATrailBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailEffectComponent = { "TrailEffectComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, TrailEffectComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailEffectComponent_MetaData), NewProp_TrailEffectComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_CollisionComponent = { "CollisionComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, CollisionComponent), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComponent_MetaData), NewProp_CollisionComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailLightComponent = { "TrailLightComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, TrailLightComponent), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailLightComponent_MetaData), NewProp_TrailLightComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailNiagaraComponent = { "TrailNiagaraComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, TrailNiagaraComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailNiagaraComponent_MetaData), NewProp_TrailNiagaraComponent_MetaData) };
void Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((ATrailBase*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ATrailBase), &Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((ATrailBase*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ATrailBase), &Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailType = { "TrailType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, TrailType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailType_MetaData), NewProp_TrailType_MetaData) }; // 2049964576
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_PowerPercentage = { "PowerPercentage", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ATrailBase, PowerPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerPercentage_MetaData), NewProp_PowerPercentage_MetaData) };
void Z_Construct_UClass_ATrailBase_Statics::NewProp_bAdaptVisualEffectsToHardware_SetBit(void* Obj)
{
	((ATrailBase*)Obj)->bAdaptVisualEffectsToHardware = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ATrailBase_Statics::NewProp_bAdaptVisualEffectsToHardware = { "bAdaptVisualEffectsToHardware", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ATrailBase), &Z_Construct_UClass_ATrailBase_Statics::NewProp_bAdaptVisualEffectsToHardware_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdaptVisualEffectsToHardware_MetaData), NewProp_bAdaptVisualEffectsToHardware_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ATrailBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailEffectComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_CollisionComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailLightComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailNiagaraComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_TrailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_PowerPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ATrailBase_Statics::NewProp_bAdaptVisualEffectsToHardware,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ATrailBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ATrailBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ATrailBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ATrailBase_Statics::ClassParams = {
	&ATrailBase::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ATrailBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ATrailBase_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ATrailBase_Statics::Class_MetaDataParams), Z_Construct_UClass_ATrailBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ATrailBase()
{
	if (!Z_Registration_Info_UClass_ATrailBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ATrailBase.OuterSingleton, Z_Construct_UClass_ATrailBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ATrailBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ATrailBase);
ATrailBase::~ATrailBase() {}
// ********** End Class ATrailBase *****************************************************************

// ********** Begin Class ASolarTrail **************************************************************
void ASolarTrail::StaticRegisterNativesASolarTrail()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_ASolarTrail;
UClass* ASolarTrail::GetPrivateStaticClass()
{
	using TClass = ASolarTrail;
	if (!Z_Registration_Info_UClass_ASolarTrail.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SolarTrail"),
			Z_Registration_Info_UClass_ASolarTrail.InnerSingleton,
			StaticRegisterNativesASolarTrail,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ASolarTrail.InnerSingleton;
}
UClass* Z_Construct_UClass_ASolarTrail_NoRegister()
{
	return ASolarTrail::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ASolarTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do Solar Trail\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGTrail.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do Solar Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SunPositionIntensityCurve_MetaData[] = {
		{ "Category", "Solar Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade da luz baseada na posi\xc3\xa7\xc3\xa3o do sol\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da luz baseada na posi\xc3\xa7\xc3\xa3o do sol" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DaytimeColorCurve_MetaData[] = {
		{ "Category", "Solar Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cor da luz baseada na hora do dia\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da luz baseada na hora do dia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SunPositionIntensityCurve;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DaytimeColorCurve;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ASolarTrail>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASolarTrail_Statics::NewProp_SunPositionIntensityCurve = { "SunPositionIntensityCurve", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASolarTrail, SunPositionIntensityCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SunPositionIntensityCurve_MetaData), NewProp_SunPositionIntensityCurve_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASolarTrail_Statics::NewProp_DaytimeColorCurve = { "DaytimeColorCurve", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASolarTrail, DaytimeColorCurve), Z_Construct_UClass_UCurveLinearColor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DaytimeColorCurve_MetaData), NewProp_DaytimeColorCurve_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ASolarTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASolarTrail_Statics::NewProp_SunPositionIntensityCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASolarTrail_Statics::NewProp_DaytimeColorCurve,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASolarTrail_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ASolarTrail_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ATrailBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASolarTrail_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ASolarTrail_Statics::ClassParams = {
	&ASolarTrail::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_ASolarTrail_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_ASolarTrail_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ASolarTrail_Statics::Class_MetaDataParams), Z_Construct_UClass_ASolarTrail_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ASolarTrail()
{
	if (!Z_Registration_Info_UClass_ASolarTrail.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ASolarTrail.OuterSingleton, Z_Construct_UClass_ASolarTrail_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ASolarTrail.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ASolarTrail);
ASolarTrail::~ASolarTrail() {}
// ********** End Class ASolarTrail ****************************************************************

// ********** Begin Class AAxisTrail ***************************************************************
void AAxisTrail::StaticRegisterNativesAAxisTrail()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAxisTrail;
UClass* AAxisTrail::GetPrivateStaticClass()
{
	using TClass = AAxisTrail;
	if (!Z_Registration_Info_UClass_AAxisTrail.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AxisTrail"),
			Z_Registration_Info_UClass_AAxisTrail.InnerSingleton,
			StaticRegisterNativesAAxisTrail,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAxisTrail.InnerSingleton;
}
UClass* Z_Construct_UClass_AAxisTrail_NoRegister()
{
	return AAxisTrail::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAxisTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do Axis Trail\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGTrail.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do Axis Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ForceStrength_MetaData[] = {
		{ "Category", "Axis Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// For\xc3\xa7""a do efeito de atra\xc3\xa7\xc3\xa3o/repuls\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a do efeito de atra\xc3\xa7\xc3\xa3o/repuls\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectRadius_MetaData[] = {
		{ "Category", "Axis Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Raio de efeito\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAttractMode_MetaData[] = {
		{ "Category", "Axis Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Se o trail atrai (true) ou repele (false)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o trail atrai (true) ou repele (false)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ForceStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectRadius;
	static void NewProp_bAttractMode_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAttractMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAxisTrail>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAxisTrail_Statics::NewProp_ForceStrength = { "ForceStrength", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAxisTrail, ForceStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ForceStrength_MetaData), NewProp_ForceStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAxisTrail_Statics::NewProp_EffectRadius = { "EffectRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAxisTrail, EffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectRadius_MetaData), NewProp_EffectRadius_MetaData) };
void Z_Construct_UClass_AAxisTrail_Statics::NewProp_bAttractMode_SetBit(void* Obj)
{
	((AAxisTrail*)Obj)->bAttractMode = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAxisTrail_Statics::NewProp_bAttractMode = { "bAttractMode", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAxisTrail), &Z_Construct_UClass_AAxisTrail_Statics::NewProp_bAttractMode_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAttractMode_MetaData), NewProp_bAttractMode_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAxisTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAxisTrail_Statics::NewProp_ForceStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAxisTrail_Statics::NewProp_EffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAxisTrail_Statics::NewProp_bAttractMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAxisTrail_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAxisTrail_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ATrailBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAxisTrail_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAxisTrail_Statics::ClassParams = {
	&AAxisTrail::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_AAxisTrail_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_AAxisTrail_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAxisTrail_Statics::Class_MetaDataParams), Z_Construct_UClass_AAxisTrail_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAxisTrail()
{
	if (!Z_Registration_Info_UClass_AAxisTrail.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAxisTrail.OuterSingleton, Z_Construct_UClass_AAxisTrail_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAxisTrail.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAxisTrail);
AAxisTrail::~AAxisTrail() {}
// ********** End Class AAxisTrail *****************************************************************

// ********** Begin Class ALunarTrail **************************************************************
void ALunarTrail::StaticRegisterNativesALunarTrail()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_ALunarTrail;
UClass* ALunarTrail::GetPrivateStaticClass()
{
	using TClass = ALunarTrail;
	if (!Z_Registration_Info_UClass_ALunarTrail.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("LunarTrail"),
			Z_Registration_Info_UClass_ALunarTrail.InnerSingleton,
			StaticRegisterNativesALunarTrail,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ALunarTrail.InnerSingleton;
}
UClass* Z_Construct_UClass_ALunarTrail_NoRegister()
{
	return ALunarTrail::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ALunarTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do Lunar Trail\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGTrail.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica do Lunar Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvisibilityStrength_MetaData[] = {
		{ "Category", "Lunar Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do efeito de invisibilidade\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito de invisibilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectDuration_MetaData[] = {
		{ "Category", "Lunar Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do efeito ap\xc3\xb3s sair do trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito ap\xc3\xb3s sair do trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedBonus_MetaData[] = {
		{ "Category", "Lunar Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Velocidade de movimento adicional\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de movimento adicional" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InvisibilityStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedBonus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ALunarTrail>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALunarTrail_Statics::NewProp_InvisibilityStrength = { "InvisibilityStrength", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALunarTrail, InvisibilityStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvisibilityStrength_MetaData), NewProp_InvisibilityStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALunarTrail_Statics::NewProp_EffectDuration = { "EffectDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALunarTrail, EffectDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectDuration_MetaData), NewProp_EffectDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALunarTrail_Statics::NewProp_MovementSpeedBonus = { "MovementSpeedBonus", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALunarTrail, MovementSpeedBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedBonus_MetaData), NewProp_MovementSpeedBonus_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ALunarTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALunarTrail_Statics::NewProp_InvisibilityStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALunarTrail_Statics::NewProp_EffectDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALunarTrail_Statics::NewProp_MovementSpeedBonus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ALunarTrail_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ALunarTrail_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ATrailBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ALunarTrail_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ALunarTrail_Statics::ClassParams = {
	&ALunarTrail::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_ALunarTrail_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_ALunarTrail_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ALunarTrail_Statics::Class_MetaDataParams), Z_Construct_UClass_ALunarTrail_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ALunarTrail()
{
	if (!Z_Registration_Info_UClass_ALunarTrail.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ALunarTrail.OuterSingleton, Z_Construct_UClass_ALunarTrail_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ALunarTrail.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ALunarTrail);
ALunarTrail::~ALunarTrail() {}
// ********** End Class ALunarTrail ****************************************************************

// ********** Begin Delegate FOnAxisTransitionAvailable ********************************************
struct Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics
{
	struct AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms
	{
		ACharacter* Character;
		FVector Destination;
		int32 ConnectionPoint;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos para efeitos de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos para efeitos de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Character;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Destination;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ConnectionPoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::NewProp_Character = { "Character", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms, Character), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::NewProp_Destination = { "Destination", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms, Destination), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::NewProp_ConnectionPoint = { "ConnectionPoint", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms, ConnectionPoint), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::NewProp_Character,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::NewProp_Destination,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::NewProp_ConnectionPoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "OnAxisTransitionAvailable__DelegateSignature", Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void AAURACRONPCGTrail::FOnAxisTransitionAvailable_DelegateWrapper(const FMulticastScriptDelegate& OnAxisTransitionAvailable, ACharacter* Character, FVector Destination, int32 ConnectionPoint)
{
	struct AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms
	{
		ACharacter* Character;
		FVector Destination;
		int32 ConnectionPoint;
	};
	AURACRONPCGTrail_eventOnAxisTransitionAvailable_Parms Parms;
	Parms.Character=Character;
	Parms.Destination=Destination;
	Parms.ConnectionPoint=ConnectionPoint;
	OnAxisTransitionAvailable.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAxisTransitionAvailable **********************************************

// ********** Begin Delegate FOnSolarHealthRegeneration ********************************************
struct Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics
{
	struct AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms
	{
		AActor* Player;
		float HealthRegen;
		float SpeedBoost;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegen;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedBoost;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::NewProp_HealthRegen = { "HealthRegen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms, HealthRegen), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::NewProp_SpeedBoost = { "SpeedBoost", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms, SpeedBoost), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::NewProp_HealthRegen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::NewProp_SpeedBoost,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "OnSolarHealthRegeneration__DelegateSignature", Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void AAURACRONPCGTrail::FOnSolarHealthRegeneration_DelegateWrapper(const FMulticastScriptDelegate& OnSolarHealthRegeneration, AActor* Player, float HealthRegen, float SpeedBoost)
{
	struct AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms
	{
		AActor* Player;
		float HealthRegen;
		float SpeedBoost;
	};
	AURACRONPCGTrail_eventOnSolarHealthRegeneration_Parms Parms;
	Parms.Player=Player;
	Parms.HealthRegen=HealthRegen;
	Parms.SpeedBoost=SpeedBoost;
	OnSolarHealthRegeneration.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSolarHealthRegeneration **********************************************

// ********** Begin Delegate FOnLunarVisionEffect **************************************************
struct Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics
{
	struct AURACRONPCGTrail_eventOnLunarVisionEffect_Parms
	{
		APawn* Pawn;
		float NightVisionFactor;
		float StealthFactor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Pawn;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NightVisionFactor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StealthFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::NewProp_Pawn = { "Pawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnLunarVisionEffect_Parms, Pawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::NewProp_NightVisionFactor = { "NightVisionFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnLunarVisionEffect_Parms, NightVisionFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::NewProp_StealthFactor = { "StealthFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnLunarVisionEffect_Parms, StealthFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::NewProp_Pawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::NewProp_NightVisionFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::NewProp_StealthFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "OnLunarVisionEffect__DelegateSignature", Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::AURACRONPCGTrail_eventOnLunarVisionEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::AURACRONPCGTrail_eventOnLunarVisionEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void AAURACRONPCGTrail::FOnLunarVisionEffect_DelegateWrapper(const FMulticastScriptDelegate& OnLunarVisionEffect, APawn* Pawn, float NightVisionFactor, float StealthFactor)
{
	struct AURACRONPCGTrail_eventOnLunarVisionEffect_Parms
	{
		APawn* Pawn;
		float NightVisionFactor;
		float StealthFactor;
	};
	AURACRONPCGTrail_eventOnLunarVisionEffect_Parms Parms;
	Parms.Pawn=Pawn;
	Parms.NightVisionFactor=NightVisionFactor;
	Parms.StealthFactor=StealthFactor;
	OnLunarVisionEffect.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLunarVisionEffect ****************************************************

// ********** Begin Class AAURACRONPCGTrail Function AddControlPoint *******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics
{
	struct AURACRONPCGTrail_eventAddControlPoint_Parms
	{
		FVector ControlPoint;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Adicionar um ponto de controle \xc3\xa0 trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar um ponto de controle \xc3\xa0 trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ControlPoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::NewProp_ControlPoint = { "ControlPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventAddControlPoint_Parms, ControlPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlPoint_MetaData), NewProp_ControlPoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::NewProp_ControlPoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "AddControlPoint", Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::AURACRONPCGTrail_eventAddControlPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::AURACRONPCGTrail_eventAddControlPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execAddControlPoint)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ControlPoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddControlPoint(Z_Param_Out_ControlPoint);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function AddControlPoint *********************************

// ********** Begin Class AAURACRONPCGTrail Function ApplyTrailEffectsToPlayer *********************
struct Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics
{
	struct AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms
	{
		ACharacter* Player;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplicar efeitos ao jogador baseado no tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos ao jogador baseado no tipo de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "ApplyTrailEffectsToPlayer", Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execApplyTrailEffectsToPlayer)
{
	P_GET_OBJECT(ACharacter,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTrailEffectsToPlayer(Z_Param_Player,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function ApplyTrailEffectsToPlayer ***********************

// ********** Begin Class AAURACRONPCGTrail Function AssociateWithDataLayer ************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics
{
	struct AURACRONPCGTrail_eventAssociateWithDataLayer_Parms
	{
		FName DataLayerName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail|DataLayers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integra\xc3\xa7\xc3\xa3o com Data Layers - Associa a trilha a uma Data Layer espec\xc3\xad""fica\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integra\xc3\xa7\xc3\xa3o com Data Layers - Associa a trilha a uma Data Layer espec\xc3\xad""fica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_DataLayerName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::NewProp_DataLayerName = { "DataLayerName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventAssociateWithDataLayer_Parms, DataLayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerName_MetaData), NewProp_DataLayerName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::NewProp_DataLayerName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "AssociateWithDataLayer", Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::AURACRONPCGTrail_eventAssociateWithDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::AURACRONPCGTrail_eventAssociateWithDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execAssociateWithDataLayer)
{
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_DataLayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AssociateWithDataLayer(Z_Param_Out_DataLayerName);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function AssociateWithDataLayer **************************

// ********** Begin Class AAURACRONPCGTrail Function ClearControlPoints ****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Limpar todos os pontos de controle\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar todos os pontos de controle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "ClearControlPoints", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execClearControlPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearControlPoints();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function ClearControlPoints ******************************

// ********** Begin Class AAURACRONPCGTrail Function ConfigureWorldPartitionStreaming **************
struct Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics
{
	struct AURACRONPCGTrail_eventConfigureWorldPartitionStreaming_Parms
	{
		FAURACRONPCGStreamingConfig StreamingConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integra\xc3\xa7\xc3\xa3o com World Partition - Configura streaming para a trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integra\xc3\xa7\xc3\xa3o com World Partition - Configura streaming para a trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::NewProp_StreamingConfig = { "StreamingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventConfigureWorldPartitionStreaming_Parms, StreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfig_MetaData), NewProp_StreamingConfig_MetaData) }; // 3330627406
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::NewProp_StreamingConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "ConfigureWorldPartitionStreaming", Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::AURACRONPCGTrail_eventConfigureWorldPartitionStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::AURACRONPCGTrail_eventConfigureWorldPartitionStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execConfigureWorldPartitionStreaming)
{
	P_GET_STRUCT_REF(FAURACRONPCGStreamingConfig,Z_Param_Out_StreamingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureWorldPartitionStreaming(Z_Param_Out_StreamingConfig);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function ConfigureWorldPartitionStreaming ****************

// ********** Begin Class AAURACRONPCGTrail Function GenerateTrail *********************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerar a trilha procedural\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar a trilha procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GenerateTrail", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGenerateTrail)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateTrail();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GenerateTrail ***********************************

// ********** Begin Class AAURACRONPCGTrail Function GetActivityScale ******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics
{
	struct AURACRONPCGTrail_eventGetActivityScale_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Retorna a escala de atividade atual\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retorna a escala de atividade atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetActivityScale_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GetActivityScale", Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::AURACRONPCGTrail_eventGetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::AURACRONPCGTrail_eventGetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGetActivityScale)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetActivityScale();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GetActivityScale ********************************

// ********** Begin Class AAURACRONPCGTrail Function GetPCGComponent *******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics
{
	struct AURACRONPCGTrail_eventGetPCGComponent_Parms
	{
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter componente PCG para acesso externo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter componente PCG para acesso externo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetPCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GetPCGComponent", Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::AURACRONPCGTrail_eventGetPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::AURACRONPCGTrail_eventGetPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGetPCGComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->GetPCGComponent();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GetPCGComponent *********************************

// ********** Begin Class AAURACRONPCGTrail Function GetPlayerPositionAlongTrail *******************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics
{
	struct AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms
	{
		ACharacter* Player;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter a posi\xc3\xa7\xc3\xa3o relativa do jogador na trilha (0.0 = in\xc3\xad""cio, 1.0 = fim)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter a posi\xc3\xa7\xc3\xa3o relativa do jogador na trilha (0.0 = in\xc3\xad""cio, 1.0 = fim)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GetPlayerPositionAlongTrail", Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGetPlayerPositionAlongTrail)
{
	P_GET_OBJECT(ACharacter,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPlayerPositionAlongTrail(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GetPlayerPositionAlongTrail *********************

// ********** Begin Class AAURACRONPCGTrail Function GetTrailType **********************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics
{
	struct AURACRONPCGTrail_eventGetTrailType_Parms
	{
		EAURACRONTrailType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter o tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter o tipo de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetTrailType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 2049964576
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GetTrailType", Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::AURACRONPCGTrail_eventGetTrailType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::AURACRONPCGTrail_eventGetTrailType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGetTrailType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONTrailType*)Z_Param__Result=P_THIS->GetTrailType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GetTrailType ************************************

// ********** Begin Class AAURACRONPCGTrail Function HandlePlayerEndOverlap ************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics
{
	struct AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "HandlePlayerEndOverlap", Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execHandlePlayerEndOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandlePlayerEndOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function HandlePlayerEndOverlap **************************

// ********** Begin Class AAURACRONPCGTrail Function HandlePlayerOverlap ***************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics
{
	struct AURACRONPCGTrail_eventHandlePlayerOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de intera\xc3\xa7\xc3\xa3o com jogadores (UE 5.6 API moderna)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de intera\xc3\xa7\xc3\xa3o com jogadores (UE 5.6 API moderna)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AURACRONPCGTrail_eventHandlePlayerOverlap_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms), &Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "HandlePlayerOverlap", Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execHandlePlayerOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandlePlayerOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function HandlePlayerOverlap *****************************

// ********** Begin Class AAURACRONPCGTrail Function IsPlayerInTrail *******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics
{
	struct AURACRONPCGTrail_eventIsPlayerInTrail_Parms
	{
		ACharacter* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Verificar se um jogador est\xc3\xa1 dentro da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um jogador est\xc3\xa1 dentro da trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventIsPlayerInTrail_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGTrail_eventIsPlayerInTrail_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGTrail_eventIsPlayerInTrail_Parms), &Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "IsPlayerInTrail", Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::AURACRONPCGTrail_eventIsPlayerInTrail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::AURACRONPCGTrail_eventIsPlayerInTrail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execIsPlayerInTrail)
{
	P_GET_OBJECT(ACharacter,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerInTrail(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function IsPlayerInTrail *********************************

// ********** Begin Class AAURACRONPCGTrail Function IsVisible *************************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics
{
	struct AURACRONPCGTrail_eventIsVisible_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Retorna se a trilha est\xc3\xa1 vis\xc3\xadvel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retorna se a trilha est\xc3\xa1 vis\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGTrail_eventIsVisible_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGTrail_eventIsVisible_Parms), &Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "IsVisible", Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::AURACRONPCGTrail_eventIsVisible_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::AURACRONPCGTrail_eventIsVisible_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execIsVisible)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsVisible();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function IsVisible ***************************************

// ********** Begin Class AAURACRONPCGTrail Function OnMapContraction ******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics
{
	struct AURACRONPCGTrail_eventOnMapContraction_Parms
	{
		float ContractionFactor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar contra\xc3\xa7\xc3\xa3o do mapa \xc3\xa0 trilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar contra\xc3\xa7\xc3\xa3o do mapa \xc3\xa0 trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ContractionFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::NewProp_ContractionFactor = { "ContractionFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventOnMapContraction_Parms, ContractionFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::NewProp_ContractionFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "OnMapContraction", Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::AURACRONPCGTrail_eventOnMapContraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::AURACRONPCGTrail_eventOnMapContraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execOnMapContraction)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ContractionFactor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnMapContraction(Z_Param_ContractionFactor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function OnMapContraction ********************************

// ********** Begin Class AAURACRONPCGTrail Function SetActivityScale ******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics
{
	struct AURACRONPCGTrail_eventSetActivityScale_Parms
	{
		float Scale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a escala de atividade da trilha (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a escala de atividade da trilha (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetActivityScale_Parms, Scale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::AURACRONPCGTrail_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::AURACRONPCGTrail_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_Scale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetActivityScale ********************************

// ********** Begin Class AAURACRONPCGTrail Function SetTrailEndpoints *****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics
{
	struct AURACRONPCGTrail_eventSetTrailEndpoints_Parms
	{
		FVector StartPoint;
		FVector EndPoint;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir os pontos de in\xc3\xad""cio e fim da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir os pontos de in\xc3\xad""cio e fim da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_StartPoint = { "StartPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetTrailEndpoints_Parms, StartPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPoint_MetaData), NewProp_StartPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_EndPoint = { "EndPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetTrailEndpoints_Parms, EndPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPoint_MetaData), NewProp_EndPoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_StartPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_EndPoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetTrailEndpoints", Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::AURACRONPCGTrail_eventSetTrailEndpoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::AURACRONPCGTrail_eventSetTrailEndpoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetTrailEndpoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPoint);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailEndpoints(Z_Param_Out_StartPoint,Z_Param_Out_EndPoint);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetTrailEndpoints *******************************

// ********** Begin Class AAURACRONPCGTrail Function SetTrailType **********************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics
{
	struct AURACRONPCGTrail_eventSetTrailType_Parms
	{
		EAURACRONTrailType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar o tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar o tipo de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetTrailType_Parms, NewType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 2049964576
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetTrailType", Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::AURACRONPCGTrail_eventSetTrailType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::AURACRONPCGTrail_eventSetTrailType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetTrailType)
{
	P_GET_ENUM(EAURACRONTrailType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailType(EAURACRONTrailType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetTrailType ************************************

// ********** Begin Class AAURACRONPCGTrail Function SetTrailVisibility ****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics
{
	struct AURACRONPCGTrail_eventSetTrailVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a visibilidade da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a visibilidade da trilha" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AURACRONPCGTrail_eventSetTrailVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGTrail_eventSetTrailVisibility_Parms), &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetTrailVisibility", Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::AURACRONPCGTrail_eventSetTrailVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::AURACRONPCGTrail_eventSetTrailVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetTrailVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetTrailVisibility ******************************

// ********** Begin Class AAURACRONPCGTrail Function UpdateForMapPhase *****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics
{
	struct AURACRONPCGTrail_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar a trilha com base na fase do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar a trilha com base na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::AURACRONPCGTrail_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::AURACRONPCGTrail_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function UpdateForMapPhase *******************************

// ********** Begin Class AAURACRONPCGTrail Function UpdateObjectiveConnections ********************
struct Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics
{
	struct AURACRONPCGTrail_eventUpdateObjectiveConnections_Parms
	{
		TArray<FVector> ObjectivePositions;
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar conex\xc3\xb5""es entre a trilha e os objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar conex\xc3\xb5""es entre a trilha e os objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectivePositions_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectivePositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectivePositions;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_ObjectivePositions_Inner = { "ObjectivePositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_ObjectivePositions = { "ObjectivePositions", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventUpdateObjectiveConnections_Parms, ObjectivePositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectivePositions_MetaData), NewProp_ObjectivePositions_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventUpdateObjectiveConnections_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_ObjectivePositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_ObjectivePositions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "UpdateObjectiveConnections", Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::AURACRONPCGTrail_eventUpdateObjectiveConnections_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::AURACRONPCGTrail_eventUpdateObjectiveConnections_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execUpdateObjectiveConnections)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_ObjectivePositions);
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateObjectiveConnections(Z_Param_Out_ObjectivePositions,EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function UpdateObjectiveConnections **********************

// ********** Begin Class AAURACRONPCGTrail ********************************************************
void AAURACRONPCGTrail::StaticRegisterNativesAAURACRONPCGTrail()
{
	UClass* Class = AAURACRONPCGTrail::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddControlPoint", &AAURACRONPCGTrail::execAddControlPoint },
		{ "ApplyTrailEffectsToPlayer", &AAURACRONPCGTrail::execApplyTrailEffectsToPlayer },
		{ "AssociateWithDataLayer", &AAURACRONPCGTrail::execAssociateWithDataLayer },
		{ "ClearControlPoints", &AAURACRONPCGTrail::execClearControlPoints },
		{ "ConfigureWorldPartitionStreaming", &AAURACRONPCGTrail::execConfigureWorldPartitionStreaming },
		{ "GenerateTrail", &AAURACRONPCGTrail::execGenerateTrail },
		{ "GetActivityScale", &AAURACRONPCGTrail::execGetActivityScale },
		{ "GetPCGComponent", &AAURACRONPCGTrail::execGetPCGComponent },
		{ "GetPlayerPositionAlongTrail", &AAURACRONPCGTrail::execGetPlayerPositionAlongTrail },
		{ "GetTrailType", &AAURACRONPCGTrail::execGetTrailType },
		{ "HandlePlayerEndOverlap", &AAURACRONPCGTrail::execHandlePlayerEndOverlap },
		{ "HandlePlayerOverlap", &AAURACRONPCGTrail::execHandlePlayerOverlap },
		{ "IsPlayerInTrail", &AAURACRONPCGTrail::execIsPlayerInTrail },
		{ "IsVisible", &AAURACRONPCGTrail::execIsVisible },
		{ "OnMapContraction", &AAURACRONPCGTrail::execOnMapContraction },
		{ "SetActivityScale", &AAURACRONPCGTrail::execSetActivityScale },
		{ "SetTrailEndpoints", &AAURACRONPCGTrail::execSetTrailEndpoints },
		{ "SetTrailType", &AAURACRONPCGTrail::execSetTrailType },
		{ "SetTrailVisibility", &AAURACRONPCGTrail::execSetTrailVisibility },
		{ "UpdateForMapPhase", &AAURACRONPCGTrail::execUpdateForMapPhase },
		{ "UpdateObjectiveConnections", &AAURACRONPCGTrail::execUpdateObjectiveConnections },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGTrail;
UClass* AAURACRONPCGTrail::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGTrail;
	if (!Z_Registration_Info_UClass_AAURACRONPCGTrail.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGTrail"),
			Z_Registration_Info_UClass_AAURACRONPCGTrail.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGTrail,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGTrail.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister()
{
	return AAURACRONPCGTrail::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator para gerenciar uma trilha din\xc3\xa2mica espec\xc3\xad""fica no AURACRON\n * Cada tipo de trilha (Prismal Flow, Ethereal Path, Nexus Connection) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia\n * Integra o sistema PCG com o sistema de trilhas\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGTrail.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator para gerenciar uma trilha din\xc3\xa2mica espec\xc3\xad""fica no AURACRON\nCada tipo de trilha (Prismal Flow, Ethereal Path, Nexus Connection) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia\nIntegra o sistema PCG com o sistema de trilhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerEnterTrailEvent_MetaData[] = {
		{ "Category", "Trail|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Evento disparado quando um jogador entra na trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando um jogador entra na trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnApplySolarEffectEvent_MetaData[] = {
		{ "Category", "Trail|Events|Solar" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Evento disparado quando um jogador entra em um Solar Trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando um jogador entra em um Solar Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnApplyAxisEffectEvent_MetaData[] = {
		{ "Category", "Trail|Events|Axis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Evento disparado quando um jogador entra em um Axis Trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando um jogador entra em um Axis Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnApplyLunarEffectEvent_MetaData[] = {
		{ "Category", "Trail|Events|Lunar" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Evento disparado quando um jogador entra em um Lunar Trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando um jogador entra em um Lunar Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolarTrailEffectAsset_MetaData[] = {
		{ "Category", "AURACRON|PCG|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeitos Niagara para os diferentes tipos de trilhas (Assets)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos Niagara para os diferentes tipos de trilhas (Assets)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AxisTrailEffectAsset_MetaData[] = {
		{ "Category", "AURACRON|PCG|Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LunarTrailEffectAsset_MetaData[] = {
		{ "Category", "AURACRON|PCG|Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolarTrailEffect_MetaData[] = {
		{ "Category", "AURACRON|PCG|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes Niagara para os diferentes tipos de trilhas (Inst\xc3\xa2ncias)\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes Niagara para os diferentes tipos de trilhas (Inst\xc3\xa2ncias)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AxisTrailEffect_MetaData[] = {
		{ "Category", "AURACRON|PCG|Effects" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LunarTrailEffect_MetaData[] = {
		{ "Category", "AURACRON|PCG|Effects" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AxisConnectionMesh_MetaData[] = {
		{ "Category", "AURACRON|PCG|Meshes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh para pontos de conex\xc3\xa3o do Axis Trail\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh para pontos de conex\xc3\xa3o do Axis Trail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailDistancePublic_MetaData[] = {
		{ "Category", "PCG|Trail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dist\xc3\xa2ncia total da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia total da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "PCG|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente PCG principal para gera\xc3\xa7\xc3\xa3o da trilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal para gera\xc3\xa7\xc3\xa3o da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGSplineComponent_MetaData[] = {
		{ "Category", "PCG|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente Spline para definir o caminho da trilha PCG\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente Spline para definir o caminho da trilha PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailSettings_MetaData[] = {
		{ "Category", "PCG|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es PCG para esta trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es PCG para esta trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowIntensity_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Caracter\xc3\xadsticas espec\xc3\xad""ficas da trilha\n// Prismal Flow\n" },
#endif
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Caracter\xc3\xadsticas espec\xc3\xad""ficas da trilha\nPrismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasFlowObstacles_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowColor_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathVisibility_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ethereal Path\n" },
#endif
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ethereal Path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFluctuation_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathAlpha_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasPathGuides_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathColor_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionStrength_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Nexus Connection\n" },
#endif
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nexus Connection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionPulseRate_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasConnectionNodes_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionColor_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionVolume_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente de colis\xc3\xa3o para intera\xc3\xa7\xc3\xa3o com jogadores\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para intera\xc3\xa7\xc3\xa3o com jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGTrailType_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo de trilha PCG espec\xc3\xad""fico\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de trilha PCG espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Array para armazenar elementos gerados dinamicamente\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Array para armazenar elementos gerados dinamicamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSinceLastUpdate_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo decorrido desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo decorrido desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervalo de atualiza\xc3\xa7\xc3\xa3o da trilha em segundos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o da trilha em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Armazenar os pontos de in\xc3\xad""cio e fim da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Armazenar os pontos de in\xc3\xad""cio e fim da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplinePoints_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pontos da spline para c\xc3\xa1lculos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos da spline para c\xc3\xa1lculos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFlowWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Valores padr\xc3\xa3o para as propriedades da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valores padr\xc3\xa3o para as propriedades da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfiguration_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades de streaming e data layer\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades de streaming e data layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStreamingEnabled_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingDistance_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssociatedDataLayer_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFlowSpeed_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFlowIntensity_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPathWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPathVisibility_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPathFluctuation_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultConnectionWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultConnectionStrength_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultConnectionPulseRate_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappingPlayers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lista de jogadores// Jogadores atualmente na trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de jogadores Jogadores atualmente na trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvisiblePlayers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Jogadores com efeito de invisibilidade aplicado (Lunar Trail)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jogadores com efeito de invisibilidade aplicado (Lunar Trail)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalPlayerSpeeds_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Armazena as velocidades originais dos jogadores (Solar Trail)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Armazena as velocidades originais dos jogadores (Solar Trail)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumOverlappingPlayers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Contador de jogadores na trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contador de jogadores na trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAxisTransitionAvailable_MetaData[] = {
		{ "Category", "AURACRON|Trail Events" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSolarHealthRegeneration_MetaData[] = {
		{ "Category", "AURACRON|Trail Events" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLunarVisionEffect_MetaData[] = {
		{ "Category", "AURACRON|Trail Events" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerEnterTrailEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnApplySolarEffectEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnApplyAxisEffectEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnApplyLunarEffectEvent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolarTrailEffectAsset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AxisTrailEffectAsset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LunarTrailEffectAsset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolarTrailEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AxisTrailEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LunarTrailEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AxisConnectionMesh;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TrailDistancePublic;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGSplineComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailSettings;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static void NewProp_bHasFlowObstacles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasFlowObstacles;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathVisibility;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathFluctuation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathAlpha;
	static void NewProp_bHasPathGuides_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasPathGuides;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionPulseRate;
	static void NewProp_bHasConnectionNodes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasConnectionNodes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectionColor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionVolume;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PCGTrailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PCGTrailType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedElements_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedElements;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeSinceLastUpdate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SplinePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SplinePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFlowWidth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfiguration;
	static void NewProp_bStreamingEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStreamingEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingDistance;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssociatedDataLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultPathWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultPathVisibility;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultPathFluctuation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultConnectionWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultConnectionStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultConnectionPulseRate;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappingPlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OverlappingPlayers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InvisiblePlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InvisiblePlayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OriginalPlayerSpeeds_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OriginalPlayerSpeeds_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_OriginalPlayerSpeeds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumOverlappingPlayers;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAxisTransitionAvailable;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSolarHealthRegeneration;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLunarVisionEffect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint, "AddControlPoint" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer, "ApplyTrailEffectsToPlayer" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_AssociateWithDataLayer, "AssociateWithDataLayer" }, // 44694747
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints, "ClearControlPoints" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_ConfigureWorldPartitionStreaming, "ConfigureWorldPartitionStreaming" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail, "GenerateTrail" }, // 126328267
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GetActivityScale, "GetActivityScale" }, // 4220757301
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GetPCGComponent, "GetPCGComponent" }, // 1667808662
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail, "GetPlayerPositionAlongTrail" }, // 3130572048
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType, "GetTrailType" }, // 1620576493
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap, "HandlePlayerEndOverlap" }, // 672364439
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap, "HandlePlayerOverlap" }, // 3717316254
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail, "IsPlayerInTrail" }, // 1089466054
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_IsVisible, "IsVisible" }, // **********
		{ &Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature, "OnAxisTransitionAvailable__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature, "OnLunarVisionEffect__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_OnMapContraction, "OnMapContraction" }, // **********
		{ &Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature, "OnSolarHealthRegeneration__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale, "SetActivityScale" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints, "SetTrailEndpoints" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType, "SetTrailType" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility, "SetTrailVisibility" }, // **********
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase, "UpdateForMapPhase" }, // 787855815
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_UpdateObjectiveConnections, "UpdateObjectiveConnections" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGTrail>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnPlayerEnterTrailEvent = { "OnPlayerEnterTrailEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnPlayerEnterTrailEvent), Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerEnterTrailEvent_MetaData), NewProp_OnPlayerEnterTrailEvent_MetaData) }; // 2883922136
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnApplySolarEffectEvent = { "OnApplySolarEffectEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnApplySolarEffectEvent), Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnApplySolarEffectEvent_MetaData), NewProp_OnApplySolarEffectEvent_MetaData) }; // 2883922136
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnApplyAxisEffectEvent = { "OnApplyAxisEffectEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnApplyAxisEffectEvent), Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnApplyAxisEffectEvent_MetaData), NewProp_OnApplyAxisEffectEvent_MetaData) }; // 2883922136
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnApplyLunarEffectEvent = { "OnApplyLunarEffectEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnApplyLunarEffectEvent), Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnApplyLunarEffectEvent_MetaData), NewProp_OnApplyLunarEffectEvent_MetaData) }; // 2883922136
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SolarTrailEffectAsset = { "SolarTrailEffectAsset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, SolarTrailEffectAsset), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolarTrailEffectAsset_MetaData), NewProp_SolarTrailEffectAsset_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AxisTrailEffectAsset = { "AxisTrailEffectAsset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, AxisTrailEffectAsset), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AxisTrailEffectAsset_MetaData), NewProp_AxisTrailEffectAsset_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_LunarTrailEffectAsset = { "LunarTrailEffectAsset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, LunarTrailEffectAsset), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LunarTrailEffectAsset_MetaData), NewProp_LunarTrailEffectAsset_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SolarTrailEffect = { "SolarTrailEffect", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, SolarTrailEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolarTrailEffect_MetaData), NewProp_SolarTrailEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AxisTrailEffect = { "AxisTrailEffect", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, AxisTrailEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AxisTrailEffect_MetaData), NewProp_AxisTrailEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_LunarTrailEffect = { "LunarTrailEffect", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, LunarTrailEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LunarTrailEffect_MetaData), NewProp_LunarTrailEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AxisConnectionMesh = { "AxisConnectionMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, AxisConnectionMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AxisConnectionMesh_MetaData), NewProp_AxisConnectionMesh_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailDistancePublic = { "TrailDistancePublic", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, TrailDistancePublic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailDistancePublic_MetaData), NewProp_TrailDistancePublic_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGSplineComponent = { "PCGSplineComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PCGSplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGSplineComponent_MetaData), NewProp_PCGSplineComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailSettings = { "TrailSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, TrailSettings), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailSettings_MetaData), NewProp_TrailSettings_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowIntensity = { "FlowIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowIntensity_MetaData), NewProp_FlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowWidth = { "FlowWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowWidth_MetaData), NewProp_FlowWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bHasFlowObstacles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles = { "bHasFlowObstacles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasFlowObstacles_MetaData), NewProp_bHasFlowObstacles_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowColor = { "FlowColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowColor_MetaData), NewProp_FlowColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathVisibility = { "PathVisibility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathVisibility), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathVisibility_MetaData), NewProp_PathVisibility_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathWidth = { "PathWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathWidth_MetaData), NewProp_PathWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathFluctuation = { "PathFluctuation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathFluctuation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFluctuation_MetaData), NewProp_PathFluctuation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathAlpha = { "PathAlpha", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathAlpha), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathAlpha_MetaData), NewProp_PathAlpha_MetaData) };
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bHasPathGuides = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides = { "bHasPathGuides", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasPathGuides_MetaData), NewProp_bHasPathGuides_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathColor = { "PathColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathColor_MetaData), NewProp_PathColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionStrength = { "ConnectionStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionStrength_MetaData), NewProp_ConnectionStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionWidth = { "ConnectionWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionWidth_MetaData), NewProp_ConnectionWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionPulseRate = { "ConnectionPulseRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionPulseRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionPulseRate_MetaData), NewProp_ConnectionPulseRate_MetaData) };
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bHasConnectionNodes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes = { "bHasConnectionNodes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasConnectionNodes_MetaData), NewProp_bHasConnectionNodes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionColor = { "ConnectionColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionColor_MetaData), NewProp_ConnectionColor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InteractionVolume = { "InteractionVolume", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, InteractionVolume), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionVolume_MetaData), NewProp_InteractionVolume_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGTrailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGTrailType = { "PCGTrailType", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PCGTrailType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGTrailType_MetaData), NewProp_PCGTrailType_MetaData) }; // 2049964576
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_GeneratedElements_Inner = { "GeneratedElements", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_GeneratedElements = { "GeneratedElements", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, GeneratedElements), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedElements_MetaData), NewProp_GeneratedElements_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TimeSinceLastUpdate = { "TimeSinceLastUpdate", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, TimeSinceLastUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSinceLastUpdate_MetaData), NewProp_TimeSinceLastUpdate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SplinePoints_Inner = { "SplinePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SplinePoints = { "SplinePoints", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, SplinePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplinePoints_MetaData), NewProp_SplinePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowWidth = { "DefaultFlowWidth", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultFlowWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFlowWidth_MetaData), NewProp_DefaultFlowWidth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StreamingConfiguration = { "StreamingConfiguration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, StreamingConfiguration), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfiguration_MetaData), NewProp_StreamingConfiguration_MetaData) }; // 3330627406
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bStreamingEnabled_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bStreamingEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bStreamingEnabled = { "bStreamingEnabled", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bStreamingEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStreamingEnabled_MetaData), NewProp_bStreamingEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StreamingDistance = { "StreamingDistance", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, StreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingDistance_MetaData), NewProp_StreamingDistance_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AssociatedDataLayer = { "AssociatedDataLayer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, AssociatedDataLayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssociatedDataLayer_MetaData), NewProp_AssociatedDataLayer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowSpeed = { "DefaultFlowSpeed", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFlowSpeed_MetaData), NewProp_DefaultFlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowIntensity = { "DefaultFlowIntensity", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultFlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFlowIntensity_MetaData), NewProp_DefaultFlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathWidth = { "DefaultPathWidth", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultPathWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPathWidth_MetaData), NewProp_DefaultPathWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathVisibility = { "DefaultPathVisibility", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultPathVisibility), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPathVisibility_MetaData), NewProp_DefaultPathVisibility_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathFluctuation = { "DefaultPathFluctuation", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultPathFluctuation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPathFluctuation_MetaData), NewProp_DefaultPathFluctuation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionWidth = { "DefaultConnectionWidth", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultConnectionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultConnectionWidth_MetaData), NewProp_DefaultConnectionWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionStrength = { "DefaultConnectionStrength", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultConnectionStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultConnectionStrength_MetaData), NewProp_DefaultConnectionStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionPulseRate = { "DefaultConnectionPulseRate", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultConnectionPulseRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultConnectionPulseRate_MetaData), NewProp_DefaultConnectionPulseRate_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers_Inner = { "OverlappingPlayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers = { "OverlappingPlayers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OverlappingPlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappingPlayers_MetaData), NewProp_OverlappingPlayers_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InvisiblePlayers_Inner = { "InvisiblePlayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InvisiblePlayers = { "InvisiblePlayers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, InvisiblePlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvisiblePlayers_MetaData), NewProp_InvisiblePlayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OriginalPlayerSpeeds_ValueProp = { "OriginalPlayerSpeeds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OriginalPlayerSpeeds_Key_KeyProp = { "OriginalPlayerSpeeds_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OriginalPlayerSpeeds = { "OriginalPlayerSpeeds", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OriginalPlayerSpeeds), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalPlayerSpeeds_MetaData), NewProp_OriginalPlayerSpeeds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_NumOverlappingPlayers = { "NumOverlappingPlayers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, NumOverlappingPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumOverlappingPlayers_MetaData), NewProp_NumOverlappingPlayers_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnAxisTransitionAvailable = { "OnAxisTransitionAvailable", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnAxisTransitionAvailable), Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAxisTransitionAvailable_MetaData), NewProp_OnAxisTransitionAvailable_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnSolarHealthRegeneration = { "OnSolarHealthRegeneration", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnSolarHealthRegeneration), Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSolarHealthRegeneration_MetaData), NewProp_OnSolarHealthRegeneration_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnLunarVisionEffect = { "OnLunarVisionEffect", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OnLunarVisionEffect), Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLunarVisionEffect_MetaData), NewProp_OnLunarVisionEffect_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnPlayerEnterTrailEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnApplySolarEffectEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnApplyAxisEffectEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnApplyLunarEffectEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SolarTrailEffectAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AxisTrailEffectAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_LunarTrailEffectAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SolarTrailEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AxisTrailEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_LunarTrailEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AxisConnectionMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailDistancePublic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGSplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathVisibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathFluctuation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionPulseRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InteractionVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGTrailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGTrailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_GeneratedElements_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_GeneratedElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TimeSinceLastUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_UpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SplinePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StreamingConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bStreamingEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_AssociatedDataLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathVisibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathFluctuation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionPulseRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InvisiblePlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InvisiblePlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OriginalPlayerSpeeds_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OriginalPlayerSpeeds_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OriginalPlayerSpeeds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_NumOverlappingPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnAxisTransitionAvailable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnSolarHealthRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OnLunarVisionEffect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGTrail_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ATrailBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::ClassParams = {
	&AAURACRONPCGTrail::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGTrail_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGTrail()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGTrail.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGTrail.OuterSingleton, Z_Construct_UClass_AAURACRONPCGTrail_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGTrail.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGTrail);
AAURACRONPCGTrail::~AAURACRONPCGTrail() {}
// ********** End Class AAURACRONPCGTrail **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ATrailBase, ATrailBase::StaticClass, TEXT("ATrailBase"), &Z_Registration_Info_UClass_ATrailBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ATrailBase), 62282460U) },
		{ Z_Construct_UClass_ASolarTrail, ASolarTrail::StaticClass, TEXT("ASolarTrail"), &Z_Registration_Info_UClass_ASolarTrail, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ASolarTrail), 1387779079U) },
		{ Z_Construct_UClass_AAxisTrail, AAxisTrail::StaticClass, TEXT("AAxisTrail"), &Z_Registration_Info_UClass_AAxisTrail, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAxisTrail), 2555109626U) },
		{ Z_Construct_UClass_ALunarTrail, ALunarTrail::StaticClass, TEXT("ALunarTrail"), &Z_Registration_Info_UClass_ALunarTrail, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ALunarTrail), 1574234880U) },
		{ Z_Construct_UClass_AAURACRONPCGTrail, AAURACRONPCGTrail::StaticClass, TEXT("AAURACRONPCGTrail"), &Z_Registration_Info_UClass_AAURACRONPCGTrail, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGTrail), 172159888U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_406182741(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
