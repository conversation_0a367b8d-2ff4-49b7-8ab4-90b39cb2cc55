// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGTrail.h"

#ifdef AURACRON_AURACRONPCGTrail_generated_h
#error "AURACRONPCGTrail.generated.h already included, missing '#pragma once' in AURACRONPCGTrail.h"
#endif
#define AURACRON_AURACRONPCGTrail_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ACharacter;
class APawn;
class UPCGComponent;
class UPrimitiveComponent;
enum class EAURACRONMapPhase : uint8;
enum class EAURACRONTrailType : uint8;
struct FAURACRONPCGStreamingConfig;
struct FHitResult;

// ********** Begin Delegate FOnPlayerEnterTrailSignature ******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_29_DELEGATE \
AURACRON_API void FOnPlayerEnterTrailSignature_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerEnterTrailSignature, ACharacter* Player);


// ********** End Delegate FOnPlayerEnterTrailSignature ********************************************

// ********** Begin Class ATrailBase ***************************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnOverlapEnd); \
	DECLARE_FUNCTION(execOnOverlapBegin); \
	DECLARE_FUNCTION(execApplyTrailEffect); \
	DECLARE_FUNCTION(execSetPowerPercentage); \
	DECLARE_FUNCTION(execConfigureForConvergencePhase); \
	DECLARE_FUNCTION(execConfigureForAwakeningPhase); \
	DECLARE_FUNCTION(execSetTrailActive);


AURACRON_API UClass* Z_Construct_UClass_ATrailBase_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesATrailBase(); \
	friend struct Z_Construct_UClass_ATrailBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_ATrailBase_NoRegister(); \
public: \
	DECLARE_CLASS2(ATrailBase, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_ATrailBase_NoRegister) \
	DECLARE_SERIALIZER(ATrailBase)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ATrailBase(ATrailBase&&) = delete; \
	ATrailBase(const ATrailBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ATrailBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ATrailBase); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ATrailBase) \
	NO_API virtual ~ATrailBase();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_34_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_37_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ATrailBase;

// ********** End Class ATrailBase *****************************************************************

// ********** Begin Class ASolarTrail **************************************************************
AURACRON_API UClass* Z_Construct_UClass_ASolarTrail_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_132_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesASolarTrail(); \
	friend struct Z_Construct_UClass_ASolarTrail_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_ASolarTrail_NoRegister(); \
public: \
	DECLARE_CLASS2(ASolarTrail, ATrailBase, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_ASolarTrail_NoRegister) \
	DECLARE_SERIALIZER(ASolarTrail)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_132_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ASolarTrail(ASolarTrail&&) = delete; \
	ASolarTrail(const ASolarTrail&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ASolarTrail); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ASolarTrail); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ASolarTrail) \
	NO_API virtual ~ASolarTrail();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_129_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_132_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_132_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_132_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ASolarTrail;

// ********** End Class ASolarTrail ****************************************************************

// ********** Begin Class AAxisTrail ***************************************************************
AURACRON_API UClass* Z_Construct_UClass_AAxisTrail_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_159_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAxisTrail(); \
	friend struct Z_Construct_UClass_AAxisTrail_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAxisTrail_NoRegister(); \
public: \
	DECLARE_CLASS2(AAxisTrail, ATrailBase, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAxisTrail_NoRegister) \
	DECLARE_SERIALIZER(AAxisTrail)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_159_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAxisTrail(AAxisTrail&&) = delete; \
	AAxisTrail(const AAxisTrail&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAxisTrail); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAxisTrail); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAxisTrail) \
	NO_API virtual ~AAxisTrail();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_156_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_159_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_159_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_159_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAxisTrail;

// ********** End Class AAxisTrail *****************************************************************

// ********** Begin Class ALunarTrail **************************************************************
AURACRON_API UClass* Z_Construct_UClass_ALunarTrail_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_190_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesALunarTrail(); \
	friend struct Z_Construct_UClass_ALunarTrail_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_ALunarTrail_NoRegister(); \
public: \
	DECLARE_CLASS2(ALunarTrail, ATrailBase, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_ALunarTrail_NoRegister) \
	DECLARE_SERIALIZER(ALunarTrail)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_190_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ALunarTrail(ALunarTrail&&) = delete; \
	ALunarTrail(const ALunarTrail&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ALunarTrail); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ALunarTrail); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ALunarTrail) \
	NO_API virtual ~ALunarTrail();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_187_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_190_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_190_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_190_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ALunarTrail;

// ********** End Class ALunarTrail ****************************************************************

// ********** Begin Delegate FOnAxisTransitionAvailable ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_542_DELEGATE \
static void FOnAxisTransitionAvailable_DelegateWrapper(const FMulticastScriptDelegate& OnAxisTransitionAvailable, ACharacter* Character, FVector Destination, int32 ConnectionPoint);


// ********** End Delegate FOnAxisTransitionAvailable **********************************************

// ********** Begin Delegate FOnSolarHealthRegeneration ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_543_DELEGATE \
static void FOnSolarHealthRegeneration_DelegateWrapper(const FMulticastScriptDelegate& OnSolarHealthRegeneration, AActor* Player, float HealthRegen, float SpeedBoost);


// ********** End Delegate FOnSolarHealthRegeneration **********************************************

// ********** Begin Delegate FOnLunarVisionEffect **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_544_DELEGATE \
static void FOnLunarVisionEffect_DelegateWrapper(const FMulticastScriptDelegate& OnLunarVisionEffect, APawn* Pawn, float NightVisionFactor, float StealthFactor);


// ********** End Delegate FOnLunarVisionEffect ****************************************************

// ********** Begin Class AAURACRONPCGTrail ********************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnMapContraction); \
	DECLARE_FUNCTION(execUpdateObjectiveConnections); \
	DECLARE_FUNCTION(execGetPCGComponent); \
	DECLARE_FUNCTION(execGetPlayerPositionAlongTrail); \
	DECLARE_FUNCTION(execIsPlayerInTrail); \
	DECLARE_FUNCTION(execApplyTrailEffectsToPlayer); \
	DECLARE_FUNCTION(execHandlePlayerEndOverlap); \
	DECLARE_FUNCTION(execHandlePlayerOverlap); \
	DECLARE_FUNCTION(execClearControlPoints); \
	DECLARE_FUNCTION(execAddControlPoint); \
	DECLARE_FUNCTION(execIsVisible); \
	DECLARE_FUNCTION(execGetActivityScale); \
	DECLARE_FUNCTION(execAssociateWithDataLayer); \
	DECLARE_FUNCTION(execConfigureWorldPartitionStreaming); \
	DECLARE_FUNCTION(execSetTrailEndpoints); \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execSetTrailVisibility); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateTrail); \
	DECLARE_FUNCTION(execGetTrailType); \
	DECLARE_FUNCTION(execSetTrailType);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGTrail(); \
	friend struct Z_Construct_UClass_AAURACRONPCGTrail_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGTrail, ATrailBase, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGTrail_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGTrail)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGTrail(AAURACRONPCGTrail&&) = delete; \
	AAURACRONPCGTrail(const AAURACRONPCGTrail&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGTrail); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGTrail); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGTrail) \
	NO_API virtual ~AAURACRONPCGTrail();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_220_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_223_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGTrail;

// ********** End Class AAURACRONPCGTrail **********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
