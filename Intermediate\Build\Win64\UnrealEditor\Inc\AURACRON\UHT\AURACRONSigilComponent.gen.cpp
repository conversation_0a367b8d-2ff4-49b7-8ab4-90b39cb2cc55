// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Components/AURACRONSigilComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONSigilComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UAURACRONSigilComponent();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONSigilComponent_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONSigilType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONEquippedSigilInfo ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo;
class UScriptStruct* FAURACRONEquippedSigilInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONEquippedSigilInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de um S\xc3\xadgilo equipado\n */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de um S\xc3\xadgilo equipado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilType_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do S\xc3\xadgilo */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do S\xc3\xadgilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
		{ "ClampMax", "5" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel do S\xc3\xadgilo (1-5) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel do S\xc3\xadgilo (1-5)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rarity_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade do S\xc3\xadgilo (afeta efici\xc3\xaancia) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade do S\xc3\xadgilo (afeta efici\xc3\xaancia)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipTime_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de equipamento (para cooldowns) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de equipamento (para cooldowns)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o S\xc3\xadgilo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o S\xc3\xadgilo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AppliedEffects_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos aplicados por este S\xc3\xadgilo */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos aplicados por este S\xc3\xadgilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrantedAbilities_MetaData[] = {
		{ "Category", "S\xc3\xadgilo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades concedidas por este S\xc3\xadgilo */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades concedidas por este S\xc3\xadgilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EquipTime;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AppliedEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AppliedEffects;
	static const UECodeGen_Private::FClassPropertyParams NewProp_GrantedAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GrantedAbilities;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONEquippedSigilInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEquippedSigilInfo, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilType_MetaData), NewProp_SigilType_MetaData) }; // 1798462891
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEquippedSigilInfo, Level), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Level_MetaData), NewProp_Level_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEquippedSigilInfo, Rarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rarity_MetaData), NewProp_Rarity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_EquipTime = { "EquipTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEquippedSigilInfo, EquipTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipTime_MetaData), NewProp_EquipTime_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONEquippedSigilInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONEquippedSigilInfo), &Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_AppliedEffects_Inner = { "AppliedEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_AppliedEffects = { "AppliedEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEquippedSigilInfo, AppliedEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AppliedEffects_MetaData), NewProp_AppliedEffects_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_GrantedAbilities_Inner = { "GrantedAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_GrantedAbilities = { "GrantedAbilities", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEquippedSigilInfo, GrantedAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrantedAbilities_MetaData), NewProp_GrantedAbilities_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_EquipTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_AppliedEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_AppliedEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_GrantedAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewProp_GrantedAbilities,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONEquippedSigilInfo",
	Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::PropPointers),
	sizeof(FAURACRONEquippedSigilInfo),
	alignof(FAURACRONEquippedSigilInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONEquippedSigilInfo ******************************************

// ********** Begin Class UAURACRONSigilComponent Function ActivateSigilFusion *********************
static FName NAME_UAURACRONSigilComponent_ActivateSigilFusion = FName(TEXT("ActivateSigilFusion"));
void UAURACRONSigilComponent::ActivateSigilFusion()
{
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_ActivateSigilFusion);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativa a fus\xc3\xa3o de S\xc3\xadgilos (habilidade especial) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa a fus\xc3\xa3o de S\xc3\xadgilos (habilidade especial)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "ActivateSigilFusion", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execActivateSigilFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateSigilFusion_Implementation();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function ActivateSigilFusion ***********************

// ********** Begin Class UAURACRONSigilComponent Function ActivateSigilSpecificAbility ************
struct AURACRONSigilComponent_eventActivateSigilSpecificAbility_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_UAURACRONSigilComponent_ActivateSigilSpecificAbility = FName(TEXT("ActivateSigilSpecificAbility"));
void UAURACRONSigilComponent::ActivateSigilSpecificAbility(EAURACRONSigilType SigilType)
{
	AURACRONSigilComponent_eventActivateSigilSpecificAbility_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_ActivateSigilSpecificAbility);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Habilidades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativa habilidade espec\xc3\xad""fica do S\xc3\xadgilo durante fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa habilidade espec\xc3\xad""fica do S\xc3\xadgilo durante fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventActivateSigilSpecificAbility_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "ActivateSigilSpecificAbility", Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventActivateSigilSpecificAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventActivateSigilSpecificAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execActivateSigilSpecificAbility)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateSigilSpecificAbility_Implementation(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function ActivateSigilSpecificAbility **************

// ********** Begin Class UAURACRONSigilComponent Function CanEquipMoreSigils **********************
struct Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics
{
	struct AURACRONSigilComponent_eventCanEquipMoreSigils_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se pode equipar mais S\xc3\xadgilos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se pode equipar mais S\xc3\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONSigilComponent_eventCanEquipMoreSigils_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONSigilComponent_eventCanEquipMoreSigils_Parms), &Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "CanEquipMoreSigils", Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::AURACRONSigilComponent_eventCanEquipMoreSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::AURACRONSigilComponent_eventCanEquipMoreSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execCanEquipMoreSigils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanEquipMoreSigils();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function CanEquipMoreSigils ************************

// ********** Begin Class UAURACRONSigilComponent Function CanReforge ******************************
struct Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics
{
	struct AURACRONSigilComponent_eventCanReforge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Reforjamento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se o jogador pode re-forjar S\xc3\xadgilos (no Nexus) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se o jogador pode re-forjar S\xc3\xadgilos (no Nexus)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONSigilComponent_eventCanReforge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONSigilComponent_eventCanReforge_Parms), &Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "CanReforge", Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::AURACRONSigilComponent_eventCanReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::AURACRONSigilComponent_eventCanReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execCanReforge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanReforge();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function CanReforge ********************************

// ********** Begin Class UAURACRONSigilComponent Function EquipSigil ******************************
struct AURACRONSigilComponent_eventEquipSigil_Parms
{
	EAURACRONSigilType SigilType;
	int32 Level;
	int32 Rarity;
};
static FName NAME_UAURACRONSigilComponent_EquipSigil = FName(TEXT("EquipSigil"));
void UAURACRONSigilComponent::EquipSigil(EAURACRONSigilType SigilType, int32 Level, int32 Rarity)
{
	AURACRONSigilComponent_eventEquipSigil_Parms Parms;
	Parms.SigilType=SigilType;
	Parms.Level=Level;
	Parms.Rarity=Rarity;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_EquipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipa um S\xc3\xadgilo espec\xc3\xad""fico */" },
#endif
		{ "CPP_Default_Level", "1" },
		{ "CPP_Default_Rarity", "1" },
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipa um S\xc3\xadgilo espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventEquipSigil_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventEquipSigil_Parms, Level), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventEquipSigil_Parms, Rarity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::NewProp_Rarity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "EquipSigil", Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execEquipSigil)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_GET_PROPERTY(FIntProperty,Z_Param_Level);
	P_GET_PROPERTY(FIntProperty,Z_Param_Rarity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EquipSigil_Implementation(EAURACRONSigilType(Z_Param_SigilType),Z_Param_Level,Z_Param_Rarity);
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function EquipSigil ********************************

// ********** Begin Class UAURACRONSigilComponent Function GetAllEquippedSigils ********************
struct Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics
{
	struct AURACRONSigilComponent_eventGetAllEquippedSigils_Parms
	{
		TArray<FAURACRONEquippedSigilInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m todos os S\xc3\xadgilos equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m todos os S\xc3\xadgilos equipados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo, METADATA_PARAMS(0, nullptr) }; // 1576455384
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventGetAllEquippedSigils_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1576455384
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "GetAllEquippedSigils", Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::AURACRONSigilComponent_eventGetAllEquippedSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::AURACRONSigilComponent_eventGetAllEquippedSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execGetAllEquippedSigils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONEquippedSigilInfo>*)Z_Param__Result=P_THIS->GetAllEquippedSigils();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function GetAllEquippedSigils **********************

// ********** Begin Class UAURACRONSigilComponent Function GetEquippedSigilCount *******************
struct Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics
{
	struct AURACRONSigilComponent_eventGetEquippedSigilCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m o n\xc3\xbamero de S\xc3\xadgilos equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o n\xc3\xbamero de S\xc3\xadgilos equipados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventGetEquippedSigilCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "GetEquippedSigilCount", Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::AURACRONSigilComponent_eventGetEquippedSigilCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::AURACRONSigilComponent_eventGetEquippedSigilCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execGetEquippedSigilCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetEquippedSigilCount();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function GetEquippedSigilCount *********************

// ********** Begin Class UAURACRONSigilComponent Function GetFusionCooldownRemaining **************
struct Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics
{
	struct AURACRONSigilComponent_eventGetFusionCooldownRemaining_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m o tempo restante de cooldown da fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o tempo restante de cooldown da fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventGetFusionCooldownRemaining_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "GetFusionCooldownRemaining", Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::AURACRONSigilComponent_eventGetFusionCooldownRemaining_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::AURACRONSigilComponent_eventGetFusionCooldownRemaining_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execGetFusionCooldownRemaining)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFusionCooldownRemaining();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function GetFusionCooldownRemaining ****************

// ********** Begin Class UAURACRONSigilComponent Function GetMaxSigilSlots ************************
struct Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics
{
	struct AURACRONSigilComponent_eventGetMaxSigilSlots_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m o n\xc3\xbamero m\xc3\xa1ximo de S\xc3\xadgilos que podem ser equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o n\xc3\xbamero m\xc3\xa1ximo de S\xc3\xadgilos que podem ser equipados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventGetMaxSigilSlots_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "GetMaxSigilSlots", Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::AURACRONSigilComponent_eventGetMaxSigilSlots_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::AURACRONSigilComponent_eventGetMaxSigilSlots_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execGetMaxSigilSlots)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMaxSigilSlots();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function GetMaxSigilSlots **************************

// ********** Begin Class UAURACRONSigilComponent Function GetSigilInfo ****************************
struct Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics
{
	struct AURACRONSigilComponent_eventGetSigilInfo_Parms
	{
		EAURACRONSigilType SigilType;
		FAURACRONEquippedSigilInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obt\xc3\xa9m informa\xc3\xa7\xc3\xb5""es de um S\xc3\xadgilo equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m informa\xc3\xa7\xc3\xb5""es de um S\xc3\xadgilo equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventGetSigilInfo_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventGetSigilInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo, METADATA_PARAMS(0, nullptr) }; // 1576455384
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "GetSigilInfo", Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::AURACRONSigilComponent_eventGetSigilInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::AURACRONSigilComponent_eventGetSigilInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execGetSigilInfo)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONEquippedSigilInfo*)Z_Param__Result=P_THIS->GetSigilInfo(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function GetSigilInfo ******************************

// ********** Begin Class UAURACRONSigilComponent Function InitializeWithAbilitySystem *************
struct Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics
{
	struct AURACRONSigilComponent_eventInitializeWithAbilitySystem_Parms
	{
		UAbilitySystemComponent* InAbilitySystemComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializa o componente com o sistema de habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializa o componente com o sistema de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InAbilitySystemComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InAbilitySystemComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::NewProp_InAbilitySystemComponent = { "InAbilitySystemComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventInitializeWithAbilitySystem_Parms, InAbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InAbilitySystemComponent_MetaData), NewProp_InAbilitySystemComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::NewProp_InAbilitySystemComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "InitializeWithAbilitySystem", Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::AURACRONSigilComponent_eventInitializeWithAbilitySystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::AURACRONSigilComponent_eventInitializeWithAbilitySystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execInitializeWithAbilitySystem)
{
	P_GET_OBJECT(UAbilitySystemComponent,Z_Param_InAbilitySystemComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWithAbilitySystem(Z_Param_InAbilitySystemComponent);
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function InitializeWithAbilitySystem ***************

// ********** Begin Class UAURACRONSigilComponent Function IsFusionAvailable ***********************
struct Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics
{
	struct AURACRONSigilComponent_eventIsFusionAvailable_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se a fus\xc3\xa3o est\xc3\xa1 dispon\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se a fus\xc3\xa3o est\xc3\xa1 dispon\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONSigilComponent_eventIsFusionAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONSigilComponent_eventIsFusionAvailable_Parms), &Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "IsFusionAvailable", Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::AURACRONSigilComponent_eventIsFusionAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::AURACRONSigilComponent_eventIsFusionAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execIsFusionAvailable)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFusionAvailable();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function IsFusionAvailable *************************

// ********** Begin Class UAURACRONSigilComponent Function IsNearNexus *****************************
struct Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics
{
	struct AURACRONSigilComponent_eventIsNearNexus_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Reforjamento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se o jogador est\xc3\xa1 pr\xc3\xb3ximo ao Nexus */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se o jogador est\xc3\xa1 pr\xc3\xb3ximo ao Nexus" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONSigilComponent_eventIsNearNexus_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONSigilComponent_eventIsNearNexus_Parms), &Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "IsNearNexus", Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::AURACRONSigilComponent_eventIsNearNexus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::AURACRONSigilComponent_eventIsNearNexus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execIsNearNexus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsNearNexus();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function IsNearNexus *******************************

// ********** Begin Class UAURACRONSigilComponent Function IsSigilEquipped *************************
struct Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics
{
	struct AURACRONSigilComponent_eventIsSigilEquipped_Parms
	{
		EAURACRONSigilType SigilType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica se um S\xc3\xadgilo est\xc3\xa1 equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se um S\xc3\xadgilo est\xc3\xa1 equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventIsSigilEquipped_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
void Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONSigilComponent_eventIsSigilEquipped_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONSigilComponent_eventIsSigilEquipped_Parms), &Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "IsSigilEquipped", Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::AURACRONSigilComponent_eventIsSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::AURACRONSigilComponent_eventIsSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execIsSigilEquipped)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSigilEquipped(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function IsSigilEquipped ***************************

// ********** Begin Class UAURACRONSigilComponent Function OnAutoFusionTriggered *******************
static FName NAME_UAURACRONSigilComponent_OnAutoFusionTriggered = FName(TEXT("OnAutoFusionTriggered"));
void UAURACRONSigilComponent::OnAutoFusionTriggered()
{
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_OnAutoFusionTriggered);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando fus\xc3\xa3o autom\xc3\xa1tica \xc3\xa9 ativada aos 6 minutos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando fus\xc3\xa3o autom\xc3\xa1tica \xc3\xa9 ativada aos 6 minutos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnAutoFusionTriggered", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONSigilComponent Function OnAutoFusionTriggered *********************

// ********** Begin Class UAURACRONSigilComponent Function OnFusionActivated ***********************
static FName NAME_UAURACRONSigilComponent_OnFusionActivated = FName(TEXT("OnFusionActivated"));
void UAURACRONSigilComponent::OnFusionActivated()
{
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_OnFusionActivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando a fus\xc3\xa3o \xc3\xa9 ativada */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando a fus\xc3\xa3o \xc3\xa9 ativada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnFusionActivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONSigilComponent Function OnFusionActivated *************************

// ********** Begin Class UAURACRONSigilComponent Function OnRep_EquippedSigils ********************
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de Replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de Replica\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnRep_EquippedSigils", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execOnRep_EquippedSigils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_EquippedSigils();
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function OnRep_EquippedSigils **********************

// ********** Begin Class UAURACRONSigilComponent Function OnSigilEquipped *************************
struct AURACRONSigilComponent_eventOnSigilEquipped_Parms
{
	EAURACRONSigilType SigilType;
	int32 Level;
	int32 Rarity;
};
static FName NAME_UAURACRONSigilComponent_OnSigilEquipped = FName(TEXT("OnSigilEquipped"));
void UAURACRONSigilComponent::OnSigilEquipped(EAURACRONSigilType SigilType, int32 Level, int32 Rarity)
{
	AURACRONSigilComponent_eventOnSigilEquipped_Parms Parms;
	Parms.SigilType=SigilType;
	Parms.Level=Level;
	Parms.Rarity=Rarity;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_OnSigilEquipped);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando um S\xc3\xadgilo \xc3\xa9 equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando um S\xc3\xadgilo \xc3\xa9 equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilEquipped_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilEquipped_Parms, Level), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilEquipped_Parms, Rarity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::NewProp_Rarity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnSigilEquipped", Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventOnSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventOnSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONSigilComponent Function OnSigilEquipped ***************************

// ********** Begin Class UAURACRONSigilComponent Function OnSigilReforged *************************
struct AURACRONSigilComponent_eventOnSigilReforged_Parms
{
	EAURACRONSigilType OldSigilType;
	EAURACRONSigilType NewSigilType;
};
static FName NAME_UAURACRONSigilComponent_OnSigilReforged = FName(TEXT("OnSigilReforged"));
void UAURACRONSigilComponent::OnSigilReforged(EAURACRONSigilType OldSigilType, EAURACRONSigilType NewSigilType)
{
	AURACRONSigilComponent_eventOnSigilReforged_Parms Parms;
	Parms.OldSigilType=OldSigilType;
	Parms.NewSigilType=NewSigilType;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_OnSigilReforged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando um S\xc3\xadgilo \xc3\xa9 re-forjado */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando um S\xc3\xadgilo \xc3\xa9 re-forjado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldSigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldSigilType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewSigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewSigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_OldSigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_OldSigilType = { "OldSigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilReforged_Parms, OldSigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_NewSigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_NewSigilType = { "NewSigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilReforged_Parms, NewSigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_OldSigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_OldSigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_NewSigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::NewProp_NewSigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnSigilReforged", Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventOnSigilReforged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventOnSigilReforged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONSigilComponent Function OnSigilReforged ***************************

// ********** Begin Class UAURACRONSigilComponent Function OnSigilSpecificAbilityActivated *********
struct AURACRONSigilComponent_eventOnSigilSpecificAbilityActivated_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated = FName(TEXT("OnSigilSpecificAbilityActivated"));
void UAURACRONSigilComponent::OnSigilSpecificAbilityActivated(EAURACRONSigilType SigilType)
{
	AURACRONSigilComponent_eventOnSigilSpecificAbilityActivated_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando habilidade espec\xc3\xad""fica de S\xc3\xadgilo \xc3\xa9 ativada */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando habilidade espec\xc3\xad""fica de S\xc3\xadgilo \xc3\xa9 ativada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilSpecificAbilityActivated_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnSigilSpecificAbilityActivated", Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventOnSigilSpecificAbilityActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventOnSigilSpecificAbilityActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONSigilComponent Function OnSigilSpecificAbilityActivated ***********

// ********** Begin Class UAURACRONSigilComponent Function OnSigilUnequipped ***********************
struct AURACRONSigilComponent_eventOnSigilUnequipped_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_UAURACRONSigilComponent_OnSigilUnequipped = FName(TEXT("OnSigilUnequipped"));
void UAURACRONSigilComponent::OnSigilUnequipped(EAURACRONSigilType SigilType)
{
	AURACRONSigilComponent_eventOnSigilUnequipped_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_OnSigilUnequipped);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Eventos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento chamado quando um S\xc3\xadgilo \xc3\xa9 removido */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento chamado quando um S\xc3\xadgilo \xc3\xa9 removido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventOnSigilUnequipped_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "OnSigilUnequipped", Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventOnSigilUnequipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventOnSigilUnequipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAURACRONSigilComponent Function OnSigilUnequipped *************************

// ********** Begin Class UAURACRONSigilComponent Function ReforgeSigil ****************************
struct AURACRONSigilComponent_eventReforgeSigil_Parms
{
	EAURACRONSigilType OldSigilType;
	EAURACRONSigilType NewSigilType;
};
static FName NAME_UAURACRONSigilComponent_ReforgeSigil = FName(TEXT("ReforgeSigil"));
void UAURACRONSigilComponent::ReforgeSigil(EAURACRONSigilType OldSigilType, EAURACRONSigilType NewSigilType)
{
	AURACRONSigilComponent_eventReforgeSigil_Parms Parms;
	Parms.OldSigilType=OldSigilType;
	Parms.NewSigilType=NewSigilType;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_ReforgeSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Reforjamento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Re-forja um S\xc3\xadgilo (troca por outro tipo) - apenas no Nexus */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Re-forja um S\xc3\xadgilo (troca por outro tipo) - apenas no Nexus" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldSigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldSigilType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewSigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewSigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_OldSigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_OldSigilType = { "OldSigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventReforgeSigil_Parms, OldSigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_NewSigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_NewSigilType = { "NewSigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventReforgeSigil_Parms, NewSigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_OldSigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_OldSigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_NewSigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::NewProp_NewSigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "ReforgeSigil", Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventReforgeSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventReforgeSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execReforgeSigil)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_OldSigilType);
	P_GET_ENUM(EAURACRONSigilType,Z_Param_NewSigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReforgeSigil_Implementation(EAURACRONSigilType(Z_Param_OldSigilType),EAURACRONSigilType(Z_Param_NewSigilType));
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function ReforgeSigil ******************************

// ********** Begin Class UAURACRONSigilComponent Function UnequipSigil ****************************
struct AURACRONSigilComponent_eventUnequipSigil_Parms
{
	EAURACRONSigilType SigilType;
};
static FName NAME_UAURACRONSigilComponent_UnequipSigil = FName(TEXT("UnequipSigil"));
void UAURACRONSigilComponent::UnequipSigil(EAURACRONSigilType SigilType)
{
	AURACRONSigilComponent_eventUnequipSigil_Parms Parms;
	Parms.SigilType=SigilType;
	UFunction* Func = FindFunctionChecked(NAME_UAURACRONSigilComponent_UnequipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove um S\xc3\xadgilo equipado */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove um S\xc3\xadgilo equipado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONSigilComponent_eventUnequipSigil_Parms, SigilType), Z_Construct_UEnum_AURACRON_EAURACRONSigilType, METADATA_PARAMS(0, nullptr) }; // 1798462891
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::NewProp_SigilType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONSigilComponent, nullptr, "UnequipSigil", Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::PropPointers), sizeof(AURACRONSigilComponent_eventUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONSigilComponent_eventUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONSigilComponent::execUnequipSigil)
{
	P_GET_ENUM(EAURACRONSigilType,Z_Param_SigilType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnequipSigil_Implementation(EAURACRONSigilType(Z_Param_SigilType));
	P_NATIVE_END;
}
// ********** End Class UAURACRONSigilComponent Function UnequipSigil ******************************

// ********** Begin Class UAURACRONSigilComponent **************************************************
void UAURACRONSigilComponent::StaticRegisterNativesUAURACRONSigilComponent()
{
	UClass* Class = UAURACRONSigilComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateSigilFusion", &UAURACRONSigilComponent::execActivateSigilFusion },
		{ "ActivateSigilSpecificAbility", &UAURACRONSigilComponent::execActivateSigilSpecificAbility },
		{ "CanEquipMoreSigils", &UAURACRONSigilComponent::execCanEquipMoreSigils },
		{ "CanReforge", &UAURACRONSigilComponent::execCanReforge },
		{ "EquipSigil", &UAURACRONSigilComponent::execEquipSigil },
		{ "GetAllEquippedSigils", &UAURACRONSigilComponent::execGetAllEquippedSigils },
		{ "GetEquippedSigilCount", &UAURACRONSigilComponent::execGetEquippedSigilCount },
		{ "GetFusionCooldownRemaining", &UAURACRONSigilComponent::execGetFusionCooldownRemaining },
		{ "GetMaxSigilSlots", &UAURACRONSigilComponent::execGetMaxSigilSlots },
		{ "GetSigilInfo", &UAURACRONSigilComponent::execGetSigilInfo },
		{ "InitializeWithAbilitySystem", &UAURACRONSigilComponent::execInitializeWithAbilitySystem },
		{ "IsFusionAvailable", &UAURACRONSigilComponent::execIsFusionAvailable },
		{ "IsNearNexus", &UAURACRONSigilComponent::execIsNearNexus },
		{ "IsSigilEquipped", &UAURACRONSigilComponent::execIsSigilEquipped },
		{ "OnRep_EquippedSigils", &UAURACRONSigilComponent::execOnRep_EquippedSigils },
		{ "ReforgeSigil", &UAURACRONSigilComponent::execReforgeSigil },
		{ "UnequipSigil", &UAURACRONSigilComponent::execUnequipSigil },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONSigilComponent;
UClass* UAURACRONSigilComponent::GetPrivateStaticClass()
{
	using TClass = UAURACRONSigilComponent;
	if (!Z_Registration_Info_UClass_UAURACRONSigilComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONSigilComponent"),
			Z_Registration_Info_UClass_UAURACRONSigilComponent.InnerSingleton,
			StaticRegisterNativesUAURACRONSigilComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONSigilComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONSigilComponent_NoRegister()
{
	return UAURACRONSigilComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONSigilComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente respons\xc3\xa1vel por gerenciar os S\xc3\xadgilos equipados no personagem\n * Integra com o Gameplay Ability System para aplicar efeitos e habilidades\n */" },
#endif
		{ "IncludePath", "Components/AURACRONSigilComponent.h" },
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente respons\xc3\xa1vel por gerenciar os S\xc3\xadgilos equipados no personagem\nIntegra com o Gameplay Ability System para aplicar efeitos e habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de habilidades do propriet\xc3\xa1rio */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de habilidades do propriet\xc3\xa1rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigils_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** S\xc3\xadgilos atualmente equipados */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "S\xc3\xadgilos atualmente equipados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSigilSlots_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos" },
		{ "ClampMax", "6" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de slots de S\xc3\xadgilos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de slots de S\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionCooldown_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown da fus\xc3\xa3o em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown da fus\xc3\xa3o em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionDuration_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o da fus\xc3\xa3o em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da fus\xc3\xa3o em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionCooldownRemaining_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante de cooldown da fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante de cooldown da fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFusionActive_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a fus\xc3\xa3o est\xc3\xa1 atualmente ativa */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a fus\xc3\xa3o est\xc3\xa1 atualmente ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTimeRemaining_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante da fus\xc3\xa3o ativa */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante da fus\xc3\xa3o ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameStartTime_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de in\xc3\xad""cio da partida para fus\xc3\xa3o autom\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de in\xc3\xad""cio da partida para fus\xc3\xa3o autom\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoFusionTriggered_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a fus\xc3\xa3o autom\xc3\xa1tica j\xc3\xa1 foi ativada */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a fus\xc3\xa3o autom\xc3\xa1tica j\xc3\xa1 foi ativada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeCount_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Reforjamento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de re-forjamentos realizados nesta partida */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de re-forjamentos realizados nesta partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReforgeTime_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Reforjamento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo do \xc3\xbaltimo re-forjamento */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo do \xc3\xbaltimo re-forjamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NexusProximityDistance_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Reforjamento" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xa1xima do Nexus para re-forjamento */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa1xima do Nexus para re-forjamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AegisBaseEffects_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos base para Aegis */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos base para Aegis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuinBaseEffects_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos base para Ruin */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos base para Ruin" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VesperBaseEffects_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos base para Vesper */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos base para Vesper" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AegisBaseAbilities_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades base para Aegis */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades base para Aegis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuinBaseAbilities_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades base para Ruin */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades base para Ruin" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VesperBaseAbilities_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Configura\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades base para Vesper */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades base para Vesper" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionEffect_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Fus\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito aplicado durante a fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito aplicado durante a fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AegisMurallionAbility_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Habilidades Espec\xc3\xad""ficas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade espec\xc3\xad""fica do Aegis - Murallion (barreira circular) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade espec\xc3\xad""fica do Aegis - Murallion (barreira circular)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuinFracassoPrismalAbility_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Habilidades Espec\xc3\xad""ficas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade espec\xc3\xad""fica do Ruin - Fracasso Prismal (reset parcial de recarga) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade espec\xc3\xad""fica do Ruin - Fracasso Prismal (reset parcial de recarga)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VesperSoproDeFluxoAbility_MetaData[] = {
		{ "Category", "AURACRON|S\xc3\xadgilos|Habilidades Espec\xc3\xad""ficas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade espec\xc3\xad""fica do Vesper - Sopro de Fluxo (dash aliado + escudo) */" },
#endif
		{ "ModuleRelativePath", "Public/Components/AURACRONSigilComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade espec\xc3\xad""fica do Vesper - Sopro de Fluxo (dash aliado + escudo)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EquippedSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EquippedSigils;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSigilSlots;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionCooldownRemaining;
	static void NewProp_bIsFusionActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFusionActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionTimeRemaining;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameStartTime;
	static void NewProp_bAutoFusionTriggered_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoFusionTriggered;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReforgeCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReforgeTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NexusProximityDistance;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AegisBaseEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AegisBaseEffects;
	static const UECodeGen_Private::FClassPropertyParams NewProp_RuinBaseEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RuinBaseEffects;
	static const UECodeGen_Private::FClassPropertyParams NewProp_VesperBaseEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VesperBaseEffects;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AegisBaseAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AegisBaseAbilities;
	static const UECodeGen_Private::FClassPropertyParams NewProp_RuinBaseAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RuinBaseAbilities;
	static const UECodeGen_Private::FClassPropertyParams NewProp_VesperBaseAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VesperBaseAbilities;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FusionEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AegisMurallionAbility;
	static const UECodeGen_Private::FClassPropertyParams NewProp_RuinFracassoPrismalAbility;
	static const UECodeGen_Private::FClassPropertyParams NewProp_VesperSoproDeFluxoAbility;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilFusion, "ActivateSigilFusion" }, // 2932705912
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_ActivateSigilSpecificAbility, "ActivateSigilSpecificAbility" }, // 2718845159
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_CanEquipMoreSigils, "CanEquipMoreSigils" }, // 557968719
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_CanReforge, "CanReforge" }, // 1270083931
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_EquipSigil, "EquipSigil" }, // 4198769517
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_GetAllEquippedSigils, "GetAllEquippedSigils" }, // 1806769189
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_GetEquippedSigilCount, "GetEquippedSigilCount" }, // 4244724107
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_GetFusionCooldownRemaining, "GetFusionCooldownRemaining" }, // 1216161809
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_GetMaxSigilSlots, "GetMaxSigilSlots" }, // 3053120219
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_GetSigilInfo, "GetSigilInfo" }, // 1735559667
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_InitializeWithAbilitySystem, "InitializeWithAbilitySystem" }, // 2358782402
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_IsFusionAvailable, "IsFusionAvailable" }, // 2864600446
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_IsNearNexus, "IsNearNexus" }, // 2657207156
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_IsSigilEquipped, "IsSigilEquipped" }, // 1119290173
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnAutoFusionTriggered, "OnAutoFusionTriggered" }, // 3143454109
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnFusionActivated, "OnFusionActivated" }, // 3733305009
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnRep_EquippedSigils, "OnRep_EquippedSigils" }, // 59889166
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilEquipped, "OnSigilEquipped" }, // 2855671503
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilReforged, "OnSigilReforged" }, // 3888057954
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilSpecificAbilityActivated, "OnSigilSpecificAbilityActivated" }, // 2323450302
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_OnSigilUnequipped, "OnSigilUnequipped" }, // 2569360029
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_ReforgeSigil, "ReforgeSigil" }, // 52450119
		{ &Z_Construct_UFunction_UAURACRONSigilComponent_UnequipSigil, "UnequipSigil" }, // 1803028112
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONSigilComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x0124080000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_EquippedSigils_Inner = { "EquippedSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo, METADATA_PARAMS(0, nullptr) }; // 1576455384
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_EquippedSigils = { "EquippedSigils", "OnRep_EquippedSigils", (EPropertyFlags)0x0020080100000034, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, EquippedSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigils_MetaData), NewProp_EquippedSigils_MetaData) }; // 1576455384
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_MaxSigilSlots = { "MaxSigilSlots", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, MaxSigilSlots), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSigilSlots_MetaData), NewProp_MaxSigilSlots_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionCooldown = { "FusionCooldown", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, FusionCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionCooldown_MetaData), NewProp_FusionCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionDuration = { "FusionDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, FusionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionDuration_MetaData), NewProp_FusionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionCooldownRemaining = { "FusionCooldownRemaining", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, FusionCooldownRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionCooldownRemaining_MetaData), NewProp_FusionCooldownRemaining_MetaData) };
void Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bIsFusionActive_SetBit(void* Obj)
{
	((UAURACRONSigilComponent*)Obj)->bIsFusionActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bIsFusionActive = { "bIsFusionActive", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAURACRONSigilComponent), &Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bIsFusionActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFusionActive_MetaData), NewProp_bIsFusionActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionTimeRemaining = { "FusionTimeRemaining", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, FusionTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTimeRemaining_MetaData), NewProp_FusionTimeRemaining_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_GameStartTime = { "GameStartTime", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, GameStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameStartTime_MetaData), NewProp_GameStartTime_MetaData) };
void Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bAutoFusionTriggered_SetBit(void* Obj)
{
	((UAURACRONSigilComponent*)Obj)->bAutoFusionTriggered = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bAutoFusionTriggered = { "bAutoFusionTriggered", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAURACRONSigilComponent), &Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bAutoFusionTriggered_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoFusionTriggered_MetaData), NewProp_bAutoFusionTriggered_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_ReforgeCount = { "ReforgeCount", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, ReforgeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeCount_MetaData), NewProp_ReforgeCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_LastReforgeTime = { "LastReforgeTime", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, LastReforgeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReforgeTime_MetaData), NewProp_LastReforgeTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_NexusProximityDistance = { "NexusProximityDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, NexusProximityDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NexusProximityDistance_MetaData), NewProp_NexusProximityDistance_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseEffects_Inner = { "AegisBaseEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseEffects = { "AegisBaseEffects", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, AegisBaseEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AegisBaseEffects_MetaData), NewProp_AegisBaseEffects_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseEffects_Inner = { "RuinBaseEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseEffects = { "RuinBaseEffects", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, RuinBaseEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuinBaseEffects_MetaData), NewProp_RuinBaseEffects_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseEffects_Inner = { "VesperBaseEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseEffects = { "VesperBaseEffects", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, VesperBaseEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VesperBaseEffects_MetaData), NewProp_VesperBaseEffects_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseAbilities_Inner = { "AegisBaseAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseAbilities = { "AegisBaseAbilities", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, AegisBaseAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AegisBaseAbilities_MetaData), NewProp_AegisBaseAbilities_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseAbilities_Inner = { "RuinBaseAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseAbilities = { "RuinBaseAbilities", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, RuinBaseAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuinBaseAbilities_MetaData), NewProp_RuinBaseAbilities_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseAbilities_Inner = { "VesperBaseAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseAbilities = { "VesperBaseAbilities", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, VesperBaseAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VesperBaseAbilities_MetaData), NewProp_VesperBaseAbilities_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionEffect = { "FusionEffect", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, FusionEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionEffect_MetaData), NewProp_FusionEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisMurallionAbility = { "AegisMurallionAbility", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, AegisMurallionAbility), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AegisMurallionAbility_MetaData), NewProp_AegisMurallionAbility_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinFracassoPrismalAbility = { "RuinFracassoPrismalAbility", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, RuinFracassoPrismalAbility), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuinFracassoPrismalAbility_MetaData), NewProp_RuinFracassoPrismalAbility_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperSoproDeFluxoAbility = { "VesperSoproDeFluxoAbility", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONSigilComponent, VesperSoproDeFluxoAbility), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VesperSoproDeFluxoAbility_MetaData), NewProp_VesperSoproDeFluxoAbility_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAURACRONSigilComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_EquippedSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_EquippedSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_MaxSigilSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionCooldownRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bIsFusionActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionTimeRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_GameStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_bAutoFusionTriggered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_ReforgeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_LastReforgeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_NexusProximityDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisBaseAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinBaseAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperBaseAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_FusionEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_AegisMurallionAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_RuinFracassoPrismalAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONSigilComponent_Statics::NewProp_VesperSoproDeFluxoAbility,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONSigilComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAURACRONSigilComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONSigilComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONSigilComponent_Statics::ClassParams = {
	&UAURACRONSigilComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAURACRONSigilComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONSigilComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONSigilComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONSigilComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONSigilComponent()
{
	if (!Z_Registration_Info_UClass_UAURACRONSigilComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONSigilComponent.OuterSingleton, Z_Construct_UClass_UAURACRONSigilComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONSigilComponent.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAURACRONSigilComponent::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_EquippedSigils(TEXT("EquippedSigils"));
	static FName Name_FusionCooldownRemaining(TEXT("FusionCooldownRemaining"));
	static FName Name_bIsFusionActive(TEXT("bIsFusionActive"));
	static FName Name_FusionTimeRemaining(TEXT("FusionTimeRemaining"));
	static FName Name_bAutoFusionTriggered(TEXT("bAutoFusionTriggered"));
	static FName Name_ReforgeCount(TEXT("ReforgeCount"));
	const bool bIsValid = true
		&& Name_EquippedSigils == ClassReps[(int32)ENetFields_Private::EquippedSigils].Property->GetFName()
		&& Name_FusionCooldownRemaining == ClassReps[(int32)ENetFields_Private::FusionCooldownRemaining].Property->GetFName()
		&& Name_bIsFusionActive == ClassReps[(int32)ENetFields_Private::bIsFusionActive].Property->GetFName()
		&& Name_FusionTimeRemaining == ClassReps[(int32)ENetFields_Private::FusionTimeRemaining].Property->GetFName()
		&& Name_bAutoFusionTriggered == ClassReps[(int32)ENetFields_Private::bAutoFusionTriggered].Property->GetFName()
		&& Name_ReforgeCount == ClassReps[(int32)ENetFields_Private::ReforgeCount].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAURACRONSigilComponent"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONSigilComponent);
UAURACRONSigilComponent::~UAURACRONSigilComponent() {}
// ********** End Class UAURACRONSigilComponent ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONEquippedSigilInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONEquippedSigilInfo_Statics::NewStructOps, TEXT("AURACRONEquippedSigilInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONEquippedSigilInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONEquippedSigilInfo), 1576455384U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONSigilComponent, UAURACRONSigilComponent::StaticClass, TEXT("UAURACRONSigilComponent"), &Z_Registration_Info_UClass_UAURACRONSigilComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONSigilComponent), 3654718401U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h__Script_AURACRON_359642520(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_AURACRONSigilComponent_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
