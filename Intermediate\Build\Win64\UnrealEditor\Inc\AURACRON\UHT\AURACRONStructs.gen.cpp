// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AURACRONStructs.h"
#include "GameplayAbilitySpecHandle.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONStructs() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONTemporalState();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPrismalFlowSegment ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPrismalFlowSegment;
class UScriptStruct* FPrismalFlowSegment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrismalFlowSegment, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PrismalFlowSegment"));
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Segmento do Fluxo Prismal - O n\xc3\xba""cleo serpentino do mapa\n * Representa uma se\xc3\xa7\xc3\xa3o do rio de energia que serpenteia pelos ambientes\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segmento do Fluxo Prismal - O n\xc3\xba""cleo serpentino do mapa\nRepresenta uma se\xc3\xa7\xc3\xa3o do rio de energia que serpenteia pelos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o mundial do segmento */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o mundial do segmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowDirection_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dire\xc3\xa7\xc3\xa3o do fluxo de energia */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dire\xc3\xa7\xc3\xa3o do fluxo de energia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
		{ "ClampMax", "50.0" },
		{ "ClampMin", "20.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Largura do segmento (20-50 unidades) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura do segmento (20-50 unidades)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do fluxo - afeta movimento e habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do fluxo - afeta movimento e habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyIntensity_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da energia prism\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da energia prism\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentColor_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor atual baseada na equipe controladora */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor atual baseada na equipe controladora" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipe que controla este segmento (-1 = neutro, 0 = equipe A, 1 = equipe B) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipe que controla este segmento (-1 = neutro, 0 = equipe A, 1 = equipe B)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCalmFlow_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se este segmento est\xc3\xa1 em estado calmo (para estrat\xc3\xa9gias) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se este segmento est\xc3\xa1 em estado calmo (para estrat\xc3\xa9gias)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlTimeRemaining_MetaData[] = {
		{ "Category", "Fluxo Prismal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante de controle de equipe */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante de controle de equipe" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentColor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static void NewProp_bIsCalmFlow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCalmFlow;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ControlTimeRemaining;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrismalFlowSegment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowDirection_MetaData), NewProp_FlowDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnergyIntensity = { "EnergyIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, EnergyIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyIntensity_MetaData), NewProp_EnergyIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_CurrentColor = { "CurrentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, CurrentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentColor_MetaData), NewProp_CurrentColor_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
void Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow_SetBit(void* Obj)
{
	((FPrismalFlowSegment*)Obj)->bIsCalmFlow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow = { "bIsCalmFlow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPrismalFlowSegment), &Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCalmFlow_MetaData), NewProp_bIsCalmFlow_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControlTimeRemaining = { "ControlTimeRemaining", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, ControlTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlTimeRemaining_MetaData), NewProp_ControlTimeRemaining_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnergyIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_CurrentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_bIsCalmFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControlTimeRemaining,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PrismalFlowSegment",
	Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers),
	sizeof(FPrismalFlowSegment),
	alignof(FPrismalFlowSegment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton;
}
// ********** End ScriptStruct FPrismalFlowSegment *************************************************

// ********** Begin ScriptStruct FAURACRONProceduralObjective **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective;
class UScriptStruct* FAURACRONProceduralObjective::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONProceduralObjective, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONProceduralObjective"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Objetivo Procedural - Sistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica de objetivos\n * Baseado no estado da partida para balanceamento autom\xc3\xa1tico\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetivo Procedural - Sistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica de objetivos\nBaseado no estado da partida para balanceamento autom\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveType_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do objetivo procedural */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do objetivo procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o mundial do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o mundial do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Environment_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente onde o objetivo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente onde o objetivo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de ambiente (alias para compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Equipe que controla o objetivo (-1 = neutro) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipe que controla o objetivo (-1 = neutro)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do objetivo (alias para compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do objetivo (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamOwner_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Propriet\xc3\xa1rio da equipe (alias para compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriet\xc3\xa1rio da equipe (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedValue_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor acumulado para objetivos que requerem progresso */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor acumulado para objetivos que requerem progresso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor m\xc3\xa1ximo necess\xc3\xa1rio para completar o objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor m\xc3\xa1ximo necess\xc3\xa1rio para completar o objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifeTime_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de vida do objetivo (0 = permanente) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de vida do objetivo (0 = permanente)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RemainingLifeTime_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante de vida */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante de vida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldReward_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensa em ouro para a equipe */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensa em ouro para a equipe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceReward_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensa em experi\xc3\xaancia para a equipe */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensa em experi\xc3\xaancia para a equipe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffType_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de buff concedido ao completar */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de buff concedido ao completar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffMagnitude_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Magnitude do buff */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Magnitude do buff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffDuration_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do buff em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do buff em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo foi completado */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo foi completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade do objetivo (maior = mais importante) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade do objetivo (maior = mais importante)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicValue_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor estrat\xc3\xa9gico do objetivo (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor estrat\xc3\xa9gico do objetivo (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCategory_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Categoria do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de respawn em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de respawn em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeAlive_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo que o objetivo est\xc3\xa1 vivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo que o objetivo est\xc3\xa1 vivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveID_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\xbanico do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\xbanico do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveName_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveDescription_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\xa7\xc3\xa3o do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\xa7\xc3\xa3o do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardPoints_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de recompensa */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de recompensa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do objetivo em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do objetivo em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredPlayers_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de jogadores necess\xc3\xa1rios */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de jogadores necess\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vida m\xc3\xa1xima do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vida m\xc3\xa1xima do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vida atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vida atual do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeUntilRespawn_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo at\xc3\xa9 respawn */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo at\xc3\xa9 respawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Objetivo Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de cria\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de cria\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamOwner;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifeTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RemainingLifeTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldReward;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceReward;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BuffType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BuffType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffMagnitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffDuration;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrategicValue;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveCategory_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveCategory;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeAlive;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveDescription;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RewardPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredPlayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeUntilRespawn;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONProceduralObjective>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveType_MetaData), NewProp_ObjectiveType_MetaData) }; // 2271266485
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Environment_MetaData), NewProp_Environment_MetaData) }; // 2509470107
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2509470107
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, State), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 1111886678
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TeamOwner = { "TeamOwner", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, TeamOwner), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamOwner_MetaData), NewProp_TeamOwner_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_AccumulatedValue = { "AccumulatedValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, AccumulatedValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedValue_MetaData), NewProp_AccumulatedValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_LifeTime = { "LifeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, LifeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifeTime_MetaData), NewProp_LifeTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RemainingLifeTime = { "RemainingLifeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, RemainingLifeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RemainingLifeTime_MetaData), NewProp_RemainingLifeTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_GoldReward = { "GoldReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, GoldReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldReward_MetaData), NewProp_GoldReward_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ExperienceReward = { "ExperienceReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ExperienceReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceReward_MetaData), NewProp_ExperienceReward_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType = { "BuffType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, BuffType), Z_Construct_UEnum_AURACRON_EAURACRONBuffType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffType_MetaData), NewProp_BuffType_MetaData) }; // 362549284
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffMagnitude = { "BuffMagnitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, BuffMagnitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffMagnitude_MetaData), NewProp_BuffMagnitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffDuration = { "BuffDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, BuffDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffDuration_MetaData), NewProp_BuffDuration_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONProceduralObjective*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONProceduralObjective), &Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((FAURACRONProceduralObjective*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONProceduralObjective), &Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_StrategicValue = { "StrategicValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, StrategicValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicValue_MetaData), NewProp_StrategicValue_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory = { "ObjectiveCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveCategory), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCategory_MetaData), NewProp_ObjectiveCategory_MetaData) }; // 842757909
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CurrentState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1111886678
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeAlive = { "TimeAlive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, TimeAlive), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeAlive_MetaData), NewProp_TimeAlive_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveID = { "ObjectiveID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveID_MetaData), NewProp_ObjectiveID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveName = { "ObjectiveName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveName_MetaData), NewProp_ObjectiveName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveDescription = { "ObjectiveDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveDescription_MetaData), NewProp_ObjectiveDescription_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RewardPoints = { "RewardPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, RewardPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardPoints_MetaData), NewProp_RewardPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RequiredPlayers = { "RequiredPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, RequiredPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredPlayers_MetaData), NewProp_RequiredPlayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeUntilRespawn = { "TimeUntilRespawn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, TimeUntilRespawn), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeUntilRespawn_MetaData), NewProp_TimeUntilRespawn_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CreationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Environment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TeamOwner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_AccumulatedValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_LifeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RemainingLifeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_GoldReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ExperienceReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffMagnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_BuffDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_StrategicValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeAlive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RewardPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_RequiredPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeUntilRespawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CreationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONProceduralObjective",
	Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers),
	sizeof(FAURACRONProceduralObjective),
	alignof(FAURACRONProceduralObjective),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONProceduralObjective ****************************************

// ********** Begin ScriptStruct FAURACRONObjectiveGenerationConfig ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig;
class UScriptStruct* FAURACRONObjectiveGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONObjectiveGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o de objetivos procedurais\n * Par\xc3\xa2metros para o sistema de balanceamento din\xc3\xa2mico\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o de objetivos procedurais\nPar\xc3\xa2metros para o sistema de balanceamento din\xc3\xa2mico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinActiveObjectives_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xadnimo de objetivos ativos simultaneamente */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xadnimo de objetivos ativos simultaneamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveObjectives_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de objetivos ativos simultaneamente */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de objetivos ativos simultaneamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSpawnInterval_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo m\xc3\xadnimo entre spawns de objetivos (segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo m\xc3\xadnimo entre spawns de objetivos (segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpawnInterval_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "600.0" },
		{ "ClampMin", "30.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo m\xc3\xa1ximo entre spawns de objetivos (segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo m\xc3\xa1ximo entre spawns de objetivos (segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CatchupSpawnMultiplier_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de spawn quando uma equipe est\xc3\xa1 atr\xc3\xa1s */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de spawn quando uma equipe est\xc3\xa1 atr\xc3\xa1s" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CatchupGoldThreshold_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "5000" },
		{ "ClampMin", "500" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Diferen\xc3\xa7""a de ouro necess\xc3\xa1ria para ativar catch-up */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Diferen\xc3\xa7""a de ouro necess\xc3\xa1ria para ativar catch-up" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CatchupKillThreshold_MetaData[] = {
		{ "Category", "Gera\xc3\xa7\xc3\xa3o" },
		{ "ClampMax", "15" },
		{ "ClampMin", "3" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Diferen\xc3\xa7""a de kills necess\xc3\xa1ria para ativar catch-up */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Diferen\xc3\xa7""a de kills necess\xc3\xa1ria para ativar catch-up" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinActiveObjectives;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveObjectives;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSpawnInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSpawnInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CatchupSpawnMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CatchupGoldThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CatchupKillThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONObjectiveGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinActiveObjectives = { "MinActiveObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MinActiveObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinActiveObjectives_MetaData), NewProp_MinActiveObjectives_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxActiveObjectives = { "MaxActiveObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MaxActiveObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveObjectives_MetaData), NewProp_MaxActiveObjectives_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinSpawnInterval = { "MinSpawnInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MinSpawnInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSpawnInterval_MetaData), NewProp_MinSpawnInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxSpawnInterval = { "MaxSpawnInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MaxSpawnInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpawnInterval_MetaData), NewProp_MaxSpawnInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupSpawnMultiplier = { "CatchupSpawnMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, CatchupSpawnMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CatchupSpawnMultiplier_MetaData), NewProp_CatchupSpawnMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupGoldThreshold = { "CatchupGoldThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, CatchupGoldThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CatchupGoldThreshold_MetaData), NewProp_CatchupGoldThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupKillThreshold = { "CatchupKillThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, CatchupKillThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CatchupKillThreshold_MetaData), NewProp_CatchupKillThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinSpawnInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxSpawnInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupSpawnMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupGoldThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_CatchupKillThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONObjectiveGenerationConfig",
	Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers),
	sizeof(FAURACRONObjectiveGenerationConfig),
	alignof(FAURACRONObjectiveGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONObjectiveGenerationConfig **********************************

// ********** Begin ScriptStruct FAURACRONBasicStreamingConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig;
class UScriptStruct* FAURACRONBasicStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONBasicStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o b\xc3\xa1sica de streaming para sistemas gerais\n * Para World Partition espec\xc3\xad""fico, use FAURACRONPCGWorldPartitionStreamingConfig\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o b\xc3\xa1sica de streaming para sistemas gerais\nPara World Partition espec\xc3\xad""fico, use FAURACRONPCGWorldPartitionStreamingConfig" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeScale_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Forma de streaming - usando FVector para definir escala da caixa */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Forma de streaming - usando FVector para definir escala da caixa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationDistance_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePerformanceBasedStreaming_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar streaming baseado em performance */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar streaming baseado em performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShapeScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationDistance;
	static void NewProp_bUsePerformanceBasedStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePerformanceBasedStreaming;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONBasicStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ShapeScale = { "ShapeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONBasicStreamingConfig, ShapeScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeScale_MetaData), NewProp_ShapeScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONBasicStreamingConfig, StreamingPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ActivationDistance = { "ActivationDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONBasicStreamingConfig, ActivationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationDistance_MetaData), NewProp_ActivationDistance_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming_SetBit(void* Obj)
{
	((FAURACRONBasicStreamingConfig*)Obj)->bUsePerformanceBasedStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming = { "bUsePerformanceBasedStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONBasicStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePerformanceBasedStreaming_MetaData), NewProp_bUsePerformanceBasedStreaming_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ShapeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_StreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_ActivationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewProp_bUsePerformanceBasedStreaming,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONBasicStreamingConfig",
	Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::PropPointers),
	sizeof(FAURACRONBasicStreamingConfig),
	alignof(FAURACRONBasicStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONBasicStreamingConfig ***************************************

// ********** Begin ScriptStruct FAURACRONMapTacticalAdvantages ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages;
class UScriptStruct* FAURACRONMapTacticalAdvantages::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONMapTacticalAdvantages"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Vantagens t\xc3\xa1ticas do mapa\n * Estrutura para gerenciar buffs e debuffs baseados no ambiente e fase\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vantagens t\xc3\xa1ticas do mapa\nEstrutura para gerenciar buffs e debuffs baseados no ambiente e fase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedMultiplier_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de velocidade de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de velocidade de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRangeMultiplier_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de alcance de vis\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de alcance de vis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageReductionPercentage_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Percentual de redu\xc3\xa7\xc3\xa3o de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Percentual de redu\xc3\xa7\xc3\xa3o de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingEffectivenessMultiplier_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de efetividade de cura */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de efetividade de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceGenerationMultiplier_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de gera\xc3\xa7\xc3\xa3o de recursos */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de gera\xc3\xa7\xc3\xa3o de recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReductionPercentage_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Percentual de redu\xc3\xa7\xc3\xa3o de cooldown */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Percentual de redu\xc3\xa7\xc3\xa3o de cooldown" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasSpecialEventBonus_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se tem b\xc3\xb4nus de evento especial */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se tem b\xc3\xb4nus de evento especial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasEnvironmentalAdvantages_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se tem vantagens ambientais */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se tem vantagens ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalEffectStrength_MetaData[] = {
		{ "Category", "Vantagens T\xc3\xa1ticas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""a dos efeitos ambientais */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a dos efeitos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionRangeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageReductionPercentage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingEffectivenessMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResourceGenerationMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReductionPercentage;
	static void NewProp_bHasSpecialEventBonus_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasSpecialEventBonus;
	static void NewProp_bHasEnvironmentalAdvantages_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasEnvironmentalAdvantages;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalEffectStrength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONMapTacticalAdvantages>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_MovementSpeedMultiplier = { "MovementSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, MovementSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedMultiplier_MetaData), NewProp_MovementSpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_VisionRangeMultiplier = { "VisionRangeMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, VisionRangeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRangeMultiplier_MetaData), NewProp_VisionRangeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_DamageReductionPercentage = { "DamageReductionPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, DamageReductionPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageReductionPercentage_MetaData), NewProp_DamageReductionPercentage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_HealingEffectivenessMultiplier = { "HealingEffectivenessMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, HealingEffectivenessMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingEffectivenessMultiplier_MetaData), NewProp_HealingEffectivenessMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_ResourceGenerationMultiplier = { "ResourceGenerationMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, ResourceGenerationMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceGenerationMultiplier_MetaData), NewProp_ResourceGenerationMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_CooldownReductionPercentage = { "CooldownReductionPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, CooldownReductionPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReductionPercentage_MetaData), NewProp_CooldownReductionPercentage_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasSpecialEventBonus_SetBit(void* Obj)
{
	((FAURACRONMapTacticalAdvantages*)Obj)->bHasSpecialEventBonus = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasSpecialEventBonus = { "bHasSpecialEventBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONMapTacticalAdvantages), &Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasSpecialEventBonus_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasSpecialEventBonus_MetaData), NewProp_bHasSpecialEventBonus_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasEnvironmentalAdvantages_SetBit(void* Obj)
{
	((FAURACRONMapTacticalAdvantages*)Obj)->bHasEnvironmentalAdvantages = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasEnvironmentalAdvantages = { "bHasEnvironmentalAdvantages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONMapTacticalAdvantages), &Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasEnvironmentalAdvantages_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasEnvironmentalAdvantages_MetaData), NewProp_bHasEnvironmentalAdvantages_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_EnvironmentalEffectStrength = { "EnvironmentalEffectStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMapTacticalAdvantages, EnvironmentalEffectStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalEffectStrength_MetaData), NewProp_EnvironmentalEffectStrength_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_MovementSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_VisionRangeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_DamageReductionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_HealingEffectivenessMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_ResourceGenerationMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_CooldownReductionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasSpecialEventBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_bHasEnvironmentalAdvantages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewProp_EnvironmentalEffectStrength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONMapTacticalAdvantages",
	Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::PropPointers),
	sizeof(FAURACRONMapTacticalAdvantages),
	alignof(FAURACRONMapTacticalAdvantages),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONMapTacticalAdvantages **************************************

// ********** Begin ScriptStruct FAURACRONTemporalState ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONTemporalState;
class UScriptStruct* FAURACRONTemporalState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONTemporalState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONTemporalState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONTemporalState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONTemporalState"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONTemporalState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estado temporal para sistema de rewind e loop\n * Armazena snapshot completo do estado do personagem em um momento espec\xc3\xad""fico\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado temporal para sistema de rewind e loop\nArmazena snapshot completo do estado do personagem em um momento espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp quando o estado foi capturado */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp quando o estado foi capturado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\xa7\xc3\xa3o do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\xa7\xc3\xa3o do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do personagem */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do personagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vida atual */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vida atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mana_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana atual */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityCooldowns_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldowns das habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldowns das habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveGameplayTags_MetaData[] = {
		{ "Category", "Estado Temporal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags de gameplay ativas */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONStructs.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags de gameplay ativas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mana;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityCooldowns_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AbilityCooldowns_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AbilityCooldowns;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveGameplayTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONTemporalState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Mana = { "Mana", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, Mana), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mana_MetaData), NewProp_Mana_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_AbilityCooldowns_ValueProp = { "AbilityCooldowns", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_AbilityCooldowns_Key_KeyProp = { "AbilityCooldowns_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle, METADATA_PARAMS(0, nullptr) }; // 417001783
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_AbilityCooldowns = { "AbilityCooldowns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, AbilityCooldowns), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityCooldowns_MetaData), NewProp_AbilityCooldowns_MetaData) }; // 417001783
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_ActiveGameplayTags = { "ActiveGameplayTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTemporalState, ActiveGameplayTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveGameplayTags_MetaData), NewProp_ActiveGameplayTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_Mana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_AbilityCooldowns_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_AbilityCooldowns_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_AbilityCooldowns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewProp_ActiveGameplayTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONTemporalState",
	Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::PropPointers),
	sizeof(FAURACRONTemporalState),
	alignof(FAURACRONTemporalState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONTemporalState()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONTemporalState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONTemporalState.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONTemporalState.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONTemporalState **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPrismalFlowSegment::StaticStruct, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewStructOps, TEXT("PrismalFlowSegment"), &Z_Registration_Info_UScriptStruct_FPrismalFlowSegment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrismalFlowSegment), 757446967U) },
		{ FAURACRONProceduralObjective::StaticStruct, Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewStructOps, TEXT("AURACRONProceduralObjective"), &Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONProceduralObjective), 3242810244U) },
		{ FAURACRONObjectiveGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewStructOps, TEXT("AURACRONObjectiveGenerationConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONObjectiveGenerationConfig), 1527385679U) },
		{ FAURACRONBasicStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONBasicStreamingConfig_Statics::NewStructOps, TEXT("AURACRONBasicStreamingConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONBasicStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONBasicStreamingConfig), 3378310114U) },
		{ FAURACRONMapTacticalAdvantages::StaticStruct, Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages_Statics::NewStructOps, TEXT("AURACRONMapTacticalAdvantages"), &Z_Registration_Info_UScriptStruct_FAURACRONMapTacticalAdvantages, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONMapTacticalAdvantages), 1083727124U) },
		{ FAURACRONTemporalState::StaticStruct, Z_Construct_UScriptStruct_FAURACRONTemporalState_Statics::NewStructOps, TEXT("AURACRONTemporalState"), &Z_Registration_Info_UScriptStruct_FAURACRONTemporalState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONTemporalState), 2469578611U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_1305705819(TEXT("/Script/AURACRON"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONStructs_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
