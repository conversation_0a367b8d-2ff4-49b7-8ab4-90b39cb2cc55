// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Components/DamageZoneComponent.h"

#ifdef AURACRON_DamageZoneComponent_generated_h
#error "DamageZoneComponent.generated.h already included, missing '#pragma once' in DamageZoneComponent.h"
#endif
#define AURACRON_DamageZoneComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;

// ********** Begin Delegate FOnPlayerDamagedByZone ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_18_DELEGATE \
AURACRON_API void FOnPlayerDamagedByZone_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDamagedByZone, AActor* Player, float DamageAmount, float DamageMultiplier);


// ********** End Delegate FOnPlayerDamagedByZone **************************************************

// ********** Begin Delegate FOnPlayerInWarningZone ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_19_DELEGATE \
AURACRON_API void FOnPlayerInWarningZone_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerInWarningZone, AActor* Player, float Distance);


// ********** End Delegate FOnPlayerInWarningZone **************************************************

// ********** Begin Delegate FOnDamageZoneActivated ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_20_DELEGATE \
AURACRON_API void FOnDamageZoneActivated_DelegateWrapper(const FMulticastScriptDelegate& OnDamageZoneActivated);


// ********** End Delegate FOnDamageZoneActivated **************************************************

// ********** Begin Delegate FOnDamageZoneDeactivated **********************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_21_DELEGATE \
AURACRON_API void FOnDamageZoneDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnDamageZoneDeactivated);


// ********** End Delegate FOnDamageZoneDeactivated ************************************************

// ********** Begin Class UDamageZoneComponent *****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsDamageZoneActive); \
	DECLARE_FUNCTION(execGetCurrentWarningRadius); \
	DECLARE_FUNCTION(execGetCurrentSafeRadius); \
	DECLARE_FUNCTION(execGetZoneCenter); \
	DECLARE_FUNCTION(execSetVisualEffectsIntensity); \
	DECLARE_FUNCTION(execSetVisualEffectsEnabled); \
	DECLARE_FUNCTION(execSetDamageZoneActive); \
	DECLARE_FUNCTION(execSetWarningRadius); \
	DECLARE_FUNCTION(execSetDamageScalingFactor); \
	DECLARE_FUNCTION(execSetDamagePerSecond); \
	DECLARE_FUNCTION(execSetSafeRadius);


AURACRON_API UClass* Z_Construct_UClass_UDamageZoneComponent_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUDamageZoneComponent(); \
	friend struct Z_Construct_UClass_UDamageZoneComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UDamageZoneComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(UDamageZoneComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UDamageZoneComponent_NoRegister) \
	DECLARE_SERIALIZER(UDamageZoneComponent)


#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UDamageZoneComponent(UDamageZoneComponent&&) = delete; \
	UDamageZoneComponent(const UDamageZoneComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UDamageZoneComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UDamageZoneComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UDamageZoneComponent) \
	NO_API virtual ~UDamageZoneComponent();


#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_27_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h_30_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UDamageZoneComponent;

// ********** End Class UDamageZoneComponent *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
