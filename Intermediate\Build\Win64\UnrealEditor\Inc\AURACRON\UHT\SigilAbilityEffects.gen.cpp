// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilAbilityEffects.h"
#include "AttributeSet.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilAbilityEffects() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction();
AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff();
AURACRON_API UClass* Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierProtection();
AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierProtection_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierRegeneration();
AURACRON_API UClass* Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility();
AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_Shield();
AURACRON_API UClass* Z_Construct_UClass_UGE_SoproDeFluxo_Shield_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityAttributeSet();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityAttributeSet_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectBase();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectBase_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectFactory();
AURACRON_API UClass* Z_Construct_UClass_USigilAbilityEffectFactory_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilBarrierProtectionCalculation();
AURACRON_API UClass* Z_Construct_UClass_USigilBarrierProtectionCalculation_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilCooldownReductionCalculation();
AURACRON_API UClass* Z_Construct_UClass_USigilCooldownReductionCalculation_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilDamageBuffCalculation();
AURACRON_API UClass* Z_Construct_UClass_USigilDamageBuffCalculation_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilShieldCalculation();
AURACRON_API UClass* Z_Construct_UClass_USigilShieldCalculation_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAttributeSet();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffectExecutionCalculation();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAttributeData();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class USigilAbilityAttributeSet Function OnRep_BarrierProtection ***************
struct Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics
{
	struct SigilAbilityAttributeSet_eventOnRep_BarrierProtection_Parms
	{
		FGameplayAttributeData OldBarrierProtection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rep notifies\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rep notifies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldBarrierProtection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldBarrierProtection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::NewProp_OldBarrierProtection = { "OldBarrierProtection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityAttributeSet_eventOnRep_BarrierProtection_Parms, OldBarrierProtection), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldBarrierProtection_MetaData), NewProp_OldBarrierProtection_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::NewProp_OldBarrierProtection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityAttributeSet, nullptr, "OnRep_BarrierProtection", Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::SigilAbilityAttributeSet_eventOnRep_BarrierProtection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::SigilAbilityAttributeSet_eventOnRep_BarrierProtection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityAttributeSet::execOnRep_BarrierProtection)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldBarrierProtection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_BarrierProtection(Z_Param_Out_OldBarrierProtection);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityAttributeSet Function OnRep_BarrierProtection *****************

// ********** Begin Class USigilAbilityAttributeSet Function OnRep_DamageMultiplier ****************
struct Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics
{
	struct SigilAbilityAttributeSet_eventOnRep_DamageMultiplier_Parms
	{
		FGameplayAttributeData OldDamageMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldDamageMultiplier_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldDamageMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::NewProp_OldDamageMultiplier = { "OldDamageMultiplier", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityAttributeSet_eventOnRep_DamageMultiplier_Parms, OldDamageMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldDamageMultiplier_MetaData), NewProp_OldDamageMultiplier_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::NewProp_OldDamageMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityAttributeSet, nullptr, "OnRep_DamageMultiplier", Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::SigilAbilityAttributeSet_eventOnRep_DamageMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::SigilAbilityAttributeSet_eventOnRep_DamageMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityAttributeSet::execOnRep_DamageMultiplier)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldDamageMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_DamageMultiplier(Z_Param_Out_OldDamageMultiplier);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityAttributeSet Function OnRep_DamageMultiplier ******************

// ********** Begin Class USigilAbilityAttributeSet Function OnRep_IncomingDamage ******************
struct Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics
{
	struct SigilAbilityAttributeSet_eventOnRep_IncomingDamage_Parms
	{
		FGameplayAttributeData OldIncomingDamage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldIncomingDamage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldIncomingDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::NewProp_OldIncomingDamage = { "OldIncomingDamage", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityAttributeSet_eventOnRep_IncomingDamage_Parms, OldIncomingDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldIncomingDamage_MetaData), NewProp_OldIncomingDamage_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::NewProp_OldIncomingDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityAttributeSet, nullptr, "OnRep_IncomingDamage", Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::SigilAbilityAttributeSet_eventOnRep_IncomingDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::SigilAbilityAttributeSet_eventOnRep_IncomingDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityAttributeSet::execOnRep_IncomingDamage)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldIncomingDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_IncomingDamage(Z_Param_Out_OldIncomingDamage);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityAttributeSet Function OnRep_IncomingDamage ********************

// ********** Begin Class USigilAbilityAttributeSet Function OnRep_MaxShieldAmount *****************
struct Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics
{
	struct SigilAbilityAttributeSet_eventOnRep_MaxShieldAmount_Parms
	{
		FGameplayAttributeData OldMaxShieldAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMaxShieldAmount_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMaxShieldAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::NewProp_OldMaxShieldAmount = { "OldMaxShieldAmount", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityAttributeSet_eventOnRep_MaxShieldAmount_Parms, OldMaxShieldAmount), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMaxShieldAmount_MetaData), NewProp_OldMaxShieldAmount_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::NewProp_OldMaxShieldAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityAttributeSet, nullptr, "OnRep_MaxShieldAmount", Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::SigilAbilityAttributeSet_eventOnRep_MaxShieldAmount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::SigilAbilityAttributeSet_eventOnRep_MaxShieldAmount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityAttributeSet::execOnRep_MaxShieldAmount)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMaxShieldAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MaxShieldAmount(Z_Param_Out_OldMaxShieldAmount);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityAttributeSet Function OnRep_MaxShieldAmount *******************

// ********** Begin Class USigilAbilityAttributeSet Function OnRep_OutgoingDamage ******************
struct Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics
{
	struct SigilAbilityAttributeSet_eventOnRep_OutgoingDamage_Parms
	{
		FGameplayAttributeData OldOutgoingDamage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldOutgoingDamage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldOutgoingDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::NewProp_OldOutgoingDamage = { "OldOutgoingDamage", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityAttributeSet_eventOnRep_OutgoingDamage_Parms, OldOutgoingDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldOutgoingDamage_MetaData), NewProp_OldOutgoingDamage_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::NewProp_OldOutgoingDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityAttributeSet, nullptr, "OnRep_OutgoingDamage", Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::SigilAbilityAttributeSet_eventOnRep_OutgoingDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::SigilAbilityAttributeSet_eventOnRep_OutgoingDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityAttributeSet::execOnRep_OutgoingDamage)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldOutgoingDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_OutgoingDamage(Z_Param_Out_OldOutgoingDamage);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityAttributeSet Function OnRep_OutgoingDamage ********************

// ********** Begin Class USigilAbilityAttributeSet Function OnRep_ShieldAmount ********************
struct Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics
{
	struct SigilAbilityAttributeSet_eventOnRep_ShieldAmount_Parms
	{
		FGameplayAttributeData OldShieldAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldShieldAmount_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldShieldAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::NewProp_OldShieldAmount = { "OldShieldAmount", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityAttributeSet_eventOnRep_ShieldAmount_Parms, OldShieldAmount), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldShieldAmount_MetaData), NewProp_OldShieldAmount_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::NewProp_OldShieldAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityAttributeSet, nullptr, "OnRep_ShieldAmount", Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::SigilAbilityAttributeSet_eventOnRep_ShieldAmount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::SigilAbilityAttributeSet_eventOnRep_ShieldAmount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityAttributeSet::execOnRep_ShieldAmount)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldShieldAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ShieldAmount(Z_Param_Out_OldShieldAmount);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityAttributeSet Function OnRep_ShieldAmount **********************

// ********** Begin Class USigilAbilityAttributeSet ************************************************
void USigilAbilityAttributeSet::StaticRegisterNativesUSigilAbilityAttributeSet()
{
	UClass* Class = USigilAbilityAttributeSet::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "OnRep_BarrierProtection", &USigilAbilityAttributeSet::execOnRep_BarrierProtection },
		{ "OnRep_DamageMultiplier", &USigilAbilityAttributeSet::execOnRep_DamageMultiplier },
		{ "OnRep_IncomingDamage", &USigilAbilityAttributeSet::execOnRep_IncomingDamage },
		{ "OnRep_MaxShieldAmount", &USigilAbilityAttributeSet::execOnRep_MaxShieldAmount },
		{ "OnRep_OutgoingDamage", &USigilAbilityAttributeSet::execOnRep_OutgoingDamage },
		{ "OnRep_ShieldAmount", &USigilAbilityAttributeSet::execOnRep_ShieldAmount },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbilityAttributeSet;
UClass* USigilAbilityAttributeSet::GetPrivateStaticClass()
{
	using TClass = USigilAbilityAttributeSet;
	if (!Z_Registration_Info_UClass_USigilAbilityAttributeSet.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbilityAttributeSet"),
			Z_Registration_Info_UClass_USigilAbilityAttributeSet.InnerSingleton,
			StaticRegisterNativesUSigilAbilityAttributeSet,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbilityAttributeSet.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbilityAttributeSet_NoRegister()
{
	return USigilAbilityAttributeSet::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbilityAttributeSet_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// ATTRIBUTE SET PARA HABILIDADES DOS S\xc3\x8dGILOS\n// ========================================\n" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ATTRIBUTE SET PARA HABILIDADES DOS S\xc3\x8dGILOS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BarrierProtection_MetaData[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de prote\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de prote\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultiplier_MetaData[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de dano\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldAmount_MetaData[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de escudo\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de escudo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxShieldAmount_MetaData[] = {
		{ "Category", "Sigil Abilities" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IncomingDamage_MetaData[] = {
		{ "Category", "Sigil Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de dano para UE 5.6\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de dano para UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutgoingDamage_MetaData[] = {
		{ "Category", "Sigil Abilities" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BarrierProtection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShieldAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaxShieldAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IncomingDamage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutgoingDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_BarrierProtection, "OnRep_BarrierProtection" }, // 2171410560
		{ &Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_DamageMultiplier, "OnRep_DamageMultiplier" }, // 2341076171
		{ &Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_IncomingDamage, "OnRep_IncomingDamage" }, // 654460768
		{ &Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_MaxShieldAmount, "OnRep_MaxShieldAmount" }, // 3747877751
		{ &Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_OutgoingDamage, "OnRep_OutgoingDamage" }, // 824246605
		{ &Z_Construct_UFunction_USigilAbilityAttributeSet_OnRep_ShieldAmount, "OnRep_ShieldAmount" }, // 814670147
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbilityAttributeSet>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_BarrierProtection = { "BarrierProtection", "OnRep_BarrierProtection", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityAttributeSet, BarrierProtection), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BarrierProtection_MetaData), NewProp_BarrierProtection_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", "OnRep_DamageMultiplier", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityAttributeSet, DamageMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultiplier_MetaData), NewProp_DamageMultiplier_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_ShieldAmount = { "ShieldAmount", "OnRep_ShieldAmount", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityAttributeSet, ShieldAmount), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldAmount_MetaData), NewProp_ShieldAmount_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_MaxShieldAmount = { "MaxShieldAmount", "OnRep_MaxShieldAmount", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityAttributeSet, MaxShieldAmount), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxShieldAmount_MetaData), NewProp_MaxShieldAmount_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_IncomingDamage = { "IncomingDamage", "OnRep_IncomingDamage", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityAttributeSet, IncomingDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IncomingDamage_MetaData), NewProp_IncomingDamage_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_OutgoingDamage = { "OutgoingDamage", "OnRep_OutgoingDamage", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityAttributeSet, OutgoingDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutgoingDamage_MetaData), NewProp_OutgoingDamage_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbilityAttributeSet_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_BarrierProtection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_ShieldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_MaxShieldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_IncomingDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityAttributeSet_Statics::NewProp_OutgoingDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityAttributeSet_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbilityAttributeSet_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAttributeSet,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityAttributeSet_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbilityAttributeSet_Statics::ClassParams = {
	&USigilAbilityAttributeSet::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAbilityAttributeSet_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityAttributeSet_Statics::PropPointers),
	0,
	0x003000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityAttributeSet_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbilityAttributeSet_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbilityAttributeSet()
{
	if (!Z_Registration_Info_UClass_USigilAbilityAttributeSet.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbilityAttributeSet.OuterSingleton, Z_Construct_UClass_USigilAbilityAttributeSet_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbilityAttributeSet.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void USigilAbilityAttributeSet::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_BarrierProtection(TEXT("BarrierProtection"));
	static FName Name_DamageMultiplier(TEXT("DamageMultiplier"));
	static FName Name_ShieldAmount(TEXT("ShieldAmount"));
	static FName Name_MaxShieldAmount(TEXT("MaxShieldAmount"));
	static FName Name_IncomingDamage(TEXT("IncomingDamage"));
	static FName Name_OutgoingDamage(TEXT("OutgoingDamage"));
	const bool bIsValid = true
		&& Name_BarrierProtection == ClassReps[(int32)ENetFields_Private::BarrierProtection].Property->GetFName()
		&& Name_DamageMultiplier == ClassReps[(int32)ENetFields_Private::DamageMultiplier].Property->GetFName()
		&& Name_ShieldAmount == ClassReps[(int32)ENetFields_Private::ShieldAmount].Property->GetFName()
		&& Name_MaxShieldAmount == ClassReps[(int32)ENetFields_Private::MaxShieldAmount].Property->GetFName()
		&& Name_IncomingDamage == ClassReps[(int32)ENetFields_Private::IncomingDamage].Property->GetFName()
		&& Name_OutgoingDamage == ClassReps[(int32)ENetFields_Private::OutgoingDamage].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in USigilAbilityAttributeSet"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbilityAttributeSet);
USigilAbilityAttributeSet::~USigilAbilityAttributeSet() {}
// ********** End Class USigilAbilityAttributeSet **************************************************

// ********** Begin Class USigilBarrierProtectionCalculation ***************************************
void USigilBarrierProtectionCalculation::StaticRegisterNativesUSigilBarrierProtectionCalculation()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilBarrierProtectionCalculation;
UClass* USigilBarrierProtectionCalculation::GetPrivateStaticClass()
{
	using TClass = USigilBarrierProtectionCalculation;
	if (!Z_Registration_Info_UClass_USigilBarrierProtectionCalculation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilBarrierProtectionCalculation"),
			Z_Registration_Info_UClass_USigilBarrierProtectionCalculation.InnerSingleton,
			StaticRegisterNativesUSigilBarrierProtectionCalculation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilBarrierProtectionCalculation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilBarrierProtectionCalculation_NoRegister()
{
	return USigilBarrierProtectionCalculation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para prote\xc3\xa7\xc3\xa3o da barreira Murallion\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para prote\xc3\xa7\xc3\xa3o da barreira Murallion" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilBarrierProtectionCalculation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffectExecutionCalculation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics::ClassParams = {
	&USigilBarrierProtectionCalculation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilBarrierProtectionCalculation()
{
	if (!Z_Registration_Info_UClass_USigilBarrierProtectionCalculation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilBarrierProtectionCalculation.OuterSingleton, Z_Construct_UClass_USigilBarrierProtectionCalculation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilBarrierProtectionCalculation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilBarrierProtectionCalculation);
USigilBarrierProtectionCalculation::~USigilBarrierProtectionCalculation() {}
// ********** End Class USigilBarrierProtectionCalculation *****************************************

// ********** Begin Class USigilDamageBuffCalculation **********************************************
void USigilDamageBuffCalculation::StaticRegisterNativesUSigilDamageBuffCalculation()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilDamageBuffCalculation;
UClass* USigilDamageBuffCalculation::GetPrivateStaticClass()
{
	using TClass = USigilDamageBuffCalculation;
	if (!Z_Registration_Info_UClass_USigilDamageBuffCalculation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilDamageBuffCalculation"),
			Z_Registration_Info_UClass_USigilDamageBuffCalculation.InnerSingleton,
			StaticRegisterNativesUSigilDamageBuffCalculation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilDamageBuffCalculation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilDamageBuffCalculation_NoRegister()
{
	return USigilDamageBuffCalculation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilDamageBuffCalculation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para buff de dano do Fracasso Prismal\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para buff de dano do Fracasso Prismal" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilDamageBuffCalculation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilDamageBuffCalculation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffectExecutionCalculation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDamageBuffCalculation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilDamageBuffCalculation_Statics::ClassParams = {
	&USigilDamageBuffCalculation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDamageBuffCalculation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilDamageBuffCalculation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilDamageBuffCalculation()
{
	if (!Z_Registration_Info_UClass_USigilDamageBuffCalculation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilDamageBuffCalculation.OuterSingleton, Z_Construct_UClass_USigilDamageBuffCalculation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilDamageBuffCalculation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilDamageBuffCalculation);
USigilDamageBuffCalculation::~USigilDamageBuffCalculation() {}
// ********** End Class USigilDamageBuffCalculation ************************************************

// ********** Begin Class USigilShieldCalculation **************************************************
void USigilShieldCalculation::StaticRegisterNativesUSigilShieldCalculation()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilShieldCalculation;
UClass* USigilShieldCalculation::GetPrivateStaticClass()
{
	using TClass = USigilShieldCalculation;
	if (!Z_Registration_Info_UClass_USigilShieldCalculation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilShieldCalculation"),
			Z_Registration_Info_UClass_USigilShieldCalculation.InnerSingleton,
			StaticRegisterNativesUSigilShieldCalculation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilShieldCalculation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilShieldCalculation_NoRegister()
{
	return USigilShieldCalculation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilShieldCalculation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para escudo do Sopro de Fluxo\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para escudo do Sopro de Fluxo" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilShieldCalculation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilShieldCalculation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffectExecutionCalculation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilShieldCalculation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilShieldCalculation_Statics::ClassParams = {
	&USigilShieldCalculation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilShieldCalculation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilShieldCalculation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilShieldCalculation()
{
	if (!Z_Registration_Info_UClass_USigilShieldCalculation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilShieldCalculation.OuterSingleton, Z_Construct_UClass_USigilShieldCalculation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilShieldCalculation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilShieldCalculation);
USigilShieldCalculation::~USigilShieldCalculation() {}
// ********** End Class USigilShieldCalculation ****************************************************

// ********** Begin Class USigilCooldownReductionCalculation ***************************************
void USigilCooldownReductionCalculation::StaticRegisterNativesUSigilCooldownReductionCalculation()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilCooldownReductionCalculation;
UClass* USigilCooldownReductionCalculation::GetPrivateStaticClass()
{
	using TClass = USigilCooldownReductionCalculation;
	if (!Z_Registration_Info_UClass_USigilCooldownReductionCalculation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilCooldownReductionCalculation"),
			Z_Registration_Info_UClass_USigilCooldownReductionCalculation.InnerSingleton,
			StaticRegisterNativesUSigilCooldownReductionCalculation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilCooldownReductionCalculation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilCooldownReductionCalculation_NoRegister()
{
	return USigilCooldownReductionCalculation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilCooldownReductionCalculation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para redu\xc3\xa7\xc3\xa3o de cooldown do Fracasso Prismal\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para redu\xc3\xa7\xc3\xa3o de cooldown do Fracasso Prismal" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilCooldownReductionCalculation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilCooldownReductionCalculation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffectExecutionCalculation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilCooldownReductionCalculation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilCooldownReductionCalculation_Statics::ClassParams = {
	&USigilCooldownReductionCalculation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilCooldownReductionCalculation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilCooldownReductionCalculation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilCooldownReductionCalculation()
{
	if (!Z_Registration_Info_UClass_USigilCooldownReductionCalculation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilCooldownReductionCalculation.OuterSingleton, Z_Construct_UClass_USigilCooldownReductionCalculation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilCooldownReductionCalculation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilCooldownReductionCalculation);
USigilCooldownReductionCalculation::~USigilCooldownReductionCalculation() {}
// ********** End Class USigilCooldownReductionCalculation *****************************************

// ********** Begin Class USigilAbilityEffectBase **************************************************
void USigilAbilityEffectBase::StaticRegisterNativesUSigilAbilityEffectBase()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbilityEffectBase;
UClass* USigilAbilityEffectBase::GetPrivateStaticClass()
{
	using TClass = USigilAbilityEffectBase;
	if (!Z_Registration_Info_UClass_USigilAbilityEffectBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbilityEffectBase"),
			Z_Registration_Info_UClass_USigilAbilityEffectBase.InnerSingleton,
			StaticRegisterNativesUSigilAbilityEffectBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbilityEffectBase.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbilityEffectBase_NoRegister()
{
	return USigilAbilityEffectBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbilityEffectBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe base para todos os GameplayEffects das habilidades dos s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe base para todos os GameplayEffects das habilidades dos s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseEffectMagnitude_MetaData[] = {
		{ "Category", "Sigil Ability Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es base\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es base" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Sigil Ability Effect" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlockedTags_MetaData[] = {
		{ "Category", "Sigil Ability Effect" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseEffectMagnitude;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlockedTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbilityEffectBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAbilityEffectBase_Statics::NewProp_BaseEffectMagnitude = { "BaseEffectMagnitude", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectBase, BaseEffectMagnitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseEffectMagnitude_MetaData), NewProp_BaseEffectMagnitude_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityEffectBase_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectBase, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAbilityEffectBase_Statics::NewProp_BlockedTags = { "BlockedTags", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectBase, BlockedTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlockedTags_MetaData), NewProp_BlockedTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbilityEffectBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectBase_Statics::NewProp_BaseEffectMagnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectBase_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectBase_Statics::NewProp_BlockedTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbilityEffectBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffect,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbilityEffectBase_Statics::ClassParams = {
	&USigilAbilityEffectBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilAbilityEffectBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectBase_Statics::PropPointers),
	0,
	0x009000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectBase_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbilityEffectBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbilityEffectBase()
{
	if (!Z_Registration_Info_UClass_USigilAbilityEffectBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbilityEffectBase.OuterSingleton, Z_Construct_UClass_USigilAbilityEffectBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbilityEffectBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbilityEffectBase);
USigilAbilityEffectBase::~USigilAbilityEffectBase() {}
// ********** End Class USigilAbilityEffectBase ****************************************************

// ********** Begin Class UGE_Murallion_BarrierProtection ******************************************
void UGE_Murallion_BarrierProtection::StaticRegisterNativesUGE_Murallion_BarrierProtection()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection;
UClass* UGE_Murallion_BarrierProtection::GetPrivateStaticClass()
{
	using TClass = UGE_Murallion_BarrierProtection;
	if (!Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GE_Murallion_BarrierProtection"),
			Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection.InnerSingleton,
			StaticRegisterNativesUGE_Murallion_BarrierProtection,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection.InnerSingleton;
}
UClass* Z_Construct_UClass_UGE_Murallion_BarrierProtection_NoRegister()
{
	return UGE_Murallion_BarrierProtection::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect para prote\xc3\xa7\xc3\xa3o da barreira Murallion\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect para prote\xc3\xa7\xc3\xa3o da barreira Murallion" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageReductionPercent_MetaData[] = {
		{ "Category", "Barrier Protection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas da barreira\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBlocksDebuffs_MetaData[] = {
		{ "Category", "Barrier Protection" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImmunityTags_MetaData[] = {
		{ "Category", "Barrier Protection" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageReductionPercent;
	static void NewProp_bBlocksDebuffs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBlocksDebuffs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImmunityTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UGE_Murallion_BarrierProtection>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_DamageReductionPercent = { "DamageReductionPercent", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_Murallion_BarrierProtection, DamageReductionPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageReductionPercent_MetaData), NewProp_DamageReductionPercent_MetaData) };
void Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_bBlocksDebuffs_SetBit(void* Obj)
{
	((UGE_Murallion_BarrierProtection*)Obj)->bBlocksDebuffs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_bBlocksDebuffs = { "bBlocksDebuffs", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_Murallion_BarrierProtection), &Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_bBlocksDebuffs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBlocksDebuffs_MetaData), NewProp_bBlocksDebuffs_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_ImmunityTags = { "ImmunityTags", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_Murallion_BarrierProtection, ImmunityTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImmunityTags_MetaData), NewProp_ImmunityTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_DamageReductionPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_bBlocksDebuffs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::NewProp_ImmunityTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::ClassParams = {
	&UGE_Murallion_BarrierProtection::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::Class_MetaDataParams), Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UGE_Murallion_BarrierProtection()
{
	if (!Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection.OuterSingleton, Z_Construct_UClass_UGE_Murallion_BarrierProtection_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UGE_Murallion_BarrierProtection);
UGE_Murallion_BarrierProtection::~UGE_Murallion_BarrierProtection() {}
// ********** End Class UGE_Murallion_BarrierProtection ********************************************

// ********** Begin Class UGE_Murallion_BarrierRegeneration ****************************************
void UGE_Murallion_BarrierRegeneration::StaticRegisterNativesUGE_Murallion_BarrierRegeneration()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration;
UClass* UGE_Murallion_BarrierRegeneration::GetPrivateStaticClass()
{
	using TClass = UGE_Murallion_BarrierRegeneration;
	if (!Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GE_Murallion_BarrierRegeneration"),
			Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration.InnerSingleton,
			StaticRegisterNativesUGE_Murallion_BarrierRegeneration,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration.InnerSingleton;
}
UClass* Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_NoRegister()
{
	return UGE_Murallion_BarrierRegeneration::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect para regenera\xc3\xa7\xc3\xa3o dentro da barreira\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect para regenera\xc3\xa7\xc3\xa3o dentro da barreira" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegenPerSecond_MetaData[] = {
		{ "Category", "Barrier Regeneration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de regenera\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de regenera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegenPerSecond_MetaData[] = {
		{ "Category", "Barrier Regeneration" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegenPerSecond;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaRegenPerSecond;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UGE_Murallion_BarrierRegeneration>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::NewProp_HealthRegenPerSecond = { "HealthRegenPerSecond", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_Murallion_BarrierRegeneration, HealthRegenPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegenPerSecond_MetaData), NewProp_HealthRegenPerSecond_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::NewProp_ManaRegenPerSecond = { "ManaRegenPerSecond", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_Murallion_BarrierRegeneration, ManaRegenPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegenPerSecond_MetaData), NewProp_ManaRegenPerSecond_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::NewProp_HealthRegenPerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::NewProp_ManaRegenPerSecond,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::ClassParams = {
	&UGE_Murallion_BarrierRegeneration::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::Class_MetaDataParams), Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UGE_Murallion_BarrierRegeneration()
{
	if (!Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration.OuterSingleton, Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UGE_Murallion_BarrierRegeneration);
UGE_Murallion_BarrierRegeneration::~UGE_Murallion_BarrierRegeneration() {}
// ********** End Class UGE_Murallion_BarrierRegeneration ******************************************

// ********** Begin Class UGE_FracassoPrismal_DamageBuff *******************************************
void UGE_FracassoPrismal_DamageBuff::StaticRegisterNativesUGE_FracassoPrismal_DamageBuff()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff;
UClass* UGE_FracassoPrismal_DamageBuff::GetPrivateStaticClass()
{
	using TClass = UGE_FracassoPrismal_DamageBuff;
	if (!Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GE_FracassoPrismal_DamageBuff"),
			Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff.InnerSingleton,
			StaticRegisterNativesUGE_FracassoPrismal_DamageBuff,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff.InnerSingleton;
}
UClass* Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_NoRegister()
{
	return UGE_FracassoPrismal_DamageBuff::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect para buff de dano do Fracasso Prismal\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect para buff de dano do Fracasso Prismal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultiplier_MetaData[] = {
		{ "Category", "Damage Buff" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es do buff de dano\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do buff de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsAbilities_MetaData[] = {
		{ "Category", "Damage Buff" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsBasicAttacks_MetaData[] = {
		{ "Category", "Damage Buff" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedDamageTypes_MetaData[] = {
		{ "Category", "Damage Buff" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static void NewProp_bAffectsAbilities_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsAbilities;
	static void NewProp_bAffectsBasicAttacks_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsBasicAttacks;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AffectedDamageTypes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UGE_FracassoPrismal_DamageBuff>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_FracassoPrismal_DamageBuff, DamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultiplier_MetaData), NewProp_DamageMultiplier_MetaData) };
void Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsAbilities_SetBit(void* Obj)
{
	((UGE_FracassoPrismal_DamageBuff*)Obj)->bAffectsAbilities = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsAbilities = { "bAffectsAbilities", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_FracassoPrismal_DamageBuff), &Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsAbilities_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsAbilities_MetaData), NewProp_bAffectsAbilities_MetaData) };
void Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsBasicAttacks_SetBit(void* Obj)
{
	((UGE_FracassoPrismal_DamageBuff*)Obj)->bAffectsBasicAttacks = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsBasicAttacks = { "bAffectsBasicAttacks", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_FracassoPrismal_DamageBuff), &Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsBasicAttacks_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsBasicAttacks_MetaData), NewProp_bAffectsBasicAttacks_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_AffectedDamageTypes = { "AffectedDamageTypes", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_FracassoPrismal_DamageBuff, AffectedDamageTypes), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedDamageTypes_MetaData), NewProp_AffectedDamageTypes_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_bAffectsBasicAttacks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::NewProp_AffectedDamageTypes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::ClassParams = {
	&UGE_FracassoPrismal_DamageBuff::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::Class_MetaDataParams), Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff()
{
	if (!Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff.OuterSingleton, Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UGE_FracassoPrismal_DamageBuff);
UGE_FracassoPrismal_DamageBuff::~UGE_FracassoPrismal_DamageBuff() {}
// ********** End Class UGE_FracassoPrismal_DamageBuff *********************************************

// ********** Begin Class UGE_FracassoPrismal_CooldownReduction ************************************
void UGE_FracassoPrismal_CooldownReduction::StaticRegisterNativesUGE_FracassoPrismal_CooldownReduction()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction;
UClass* UGE_FracassoPrismal_CooldownReduction::GetPrivateStaticClass()
{
	using TClass = UGE_FracassoPrismal_CooldownReduction;
	if (!Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GE_FracassoPrismal_CooldownReduction"),
			Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction.InnerSingleton,
			StaticRegisterNativesUGE_FracassoPrismal_CooldownReduction,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction.InnerSingleton;
}
UClass* Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_NoRegister()
{
	return UGE_FracassoPrismal_CooldownReduction::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect para redu\xc3\xa7\xc3\xa3o de cooldown do Fracasso Prismal\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect para redu\xc3\xa7\xc3\xa3o de cooldown do Fracasso Prismal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReductionPercent_MetaData[] = {
		{ "Category", "Cooldown Reduction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de redu\xc3\xa7\xc3\xa3o de cooldown\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de redu\xc3\xa7\xc3\xa3o de cooldown" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedAbilities_MetaData[] = {
		{ "Category", "Cooldown Reduction" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsAllAbilities_MetaData[] = {
		{ "Category", "Cooldown Reduction" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReductionPercent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AffectedAbilities;
	static void NewProp_bAffectsAllAbilities_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsAllAbilities;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UGE_FracassoPrismal_CooldownReduction>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_CooldownReductionPercent = { "CooldownReductionPercent", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_FracassoPrismal_CooldownReduction, CooldownReductionPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReductionPercent_MetaData), NewProp_CooldownReductionPercent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_AffectedAbilities = { "AffectedAbilities", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_FracassoPrismal_CooldownReduction, AffectedAbilities), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedAbilities_MetaData), NewProp_AffectedAbilities_MetaData) }; // 2104890724
void Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_bAffectsAllAbilities_SetBit(void* Obj)
{
	((UGE_FracassoPrismal_CooldownReduction*)Obj)->bAffectsAllAbilities = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_bAffectsAllAbilities = { "bAffectsAllAbilities", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_FracassoPrismal_CooldownReduction), &Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_bAffectsAllAbilities_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsAllAbilities_MetaData), NewProp_bAffectsAllAbilities_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_CooldownReductionPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_AffectedAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::NewProp_bAffectsAllAbilities,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::ClassParams = {
	&UGE_FracassoPrismal_CooldownReduction::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::Class_MetaDataParams), Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction()
{
	if (!Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction.OuterSingleton, Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UGE_FracassoPrismal_CooldownReduction);
UGE_FracassoPrismal_CooldownReduction::~UGE_FracassoPrismal_CooldownReduction() {}
// ********** End Class UGE_FracassoPrismal_CooldownReduction **************************************

// ********** Begin Class UGE_SoproDeFluxo_Shield **************************************************
void UGE_SoproDeFluxo_Shield::StaticRegisterNativesUGE_SoproDeFluxo_Shield()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield;
UClass* UGE_SoproDeFluxo_Shield::GetPrivateStaticClass()
{
	using TClass = UGE_SoproDeFluxo_Shield;
	if (!Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GE_SoproDeFluxo_Shield"),
			Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield.InnerSingleton,
			StaticRegisterNativesUGE_SoproDeFluxo_Shield,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield.InnerSingleton;
}
UClass* Z_Construct_UClass_UGE_SoproDeFluxo_Shield_NoRegister()
{
	return UGE_SoproDeFluxo_Shield::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect para escudo do Sopro de Fluxo\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect para escudo do Sopro de Fluxo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldAmount_MetaData[] = {
		{ "Category", "Shield" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es do escudo\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do escudo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBlocksMagicalDamage_MetaData[] = {
		{ "Category", "Shield" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBlocksPhysicalDamage_MetaData[] = {
		{ "Category", "Shield" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldDecayRate_MetaData[] = {
		{ "Category", "Shield" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShieldAmount;
	static void NewProp_bBlocksMagicalDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBlocksMagicalDamage;
	static void NewProp_bBlocksPhysicalDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBlocksPhysicalDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShieldDecayRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UGE_SoproDeFluxo_Shield>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_ShieldAmount = { "ShieldAmount", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_SoproDeFluxo_Shield, ShieldAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldAmount_MetaData), NewProp_ShieldAmount_MetaData) };
void Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksMagicalDamage_SetBit(void* Obj)
{
	((UGE_SoproDeFluxo_Shield*)Obj)->bBlocksMagicalDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksMagicalDamage = { "bBlocksMagicalDamage", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_SoproDeFluxo_Shield), &Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksMagicalDamage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBlocksMagicalDamage_MetaData), NewProp_bBlocksMagicalDamage_MetaData) };
void Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksPhysicalDamage_SetBit(void* Obj)
{
	((UGE_SoproDeFluxo_Shield*)Obj)->bBlocksPhysicalDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksPhysicalDamage = { "bBlocksPhysicalDamage", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_SoproDeFluxo_Shield), &Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksPhysicalDamage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBlocksPhysicalDamage_MetaData), NewProp_bBlocksPhysicalDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_ShieldDecayRate = { "ShieldDecayRate", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_SoproDeFluxo_Shield, ShieldDecayRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldDecayRate_MetaData), NewProp_ShieldDecayRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_ShieldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksMagicalDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_bBlocksPhysicalDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::NewProp_ShieldDecayRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::ClassParams = {
	&UGE_SoproDeFluxo_Shield::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::Class_MetaDataParams), Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UGE_SoproDeFluxo_Shield()
{
	if (!Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield.OuterSingleton, Z_Construct_UClass_UGE_SoproDeFluxo_Shield_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UGE_SoproDeFluxo_Shield);
UGE_SoproDeFluxo_Shield::~UGE_SoproDeFluxo_Shield() {}
// ********** End Class UGE_SoproDeFluxo_Shield ****************************************************

// ********** Begin Class UGE_SoproDeFluxo_DashMobility ********************************************
void UGE_SoproDeFluxo_DashMobility::StaticRegisterNativesUGE_SoproDeFluxo_DashMobility()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility;
UClass* UGE_SoproDeFluxo_DashMobility::GetPrivateStaticClass()
{
	using TClass = UGE_SoproDeFluxo_DashMobility;
	if (!Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GE_SoproDeFluxo_DashMobility"),
			Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility.InnerSingleton,
			StaticRegisterNativesUGE_SoproDeFluxo_DashMobility,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility.InnerSingleton;
}
UClass* Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_NoRegister()
{
	return UGE_SoproDeFluxo_DashMobility::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect para mobilidade durante o dash\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect para mobilidade durante o dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedMultiplier_MetaData[] = {
		{ "Category", "Dash Mobility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de mobilidade\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de mobilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresCollision_MetaData[] = {
		{ "Category", "Dash Mobility" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bImmuneToSlows_MetaData[] = {
		{ "Category", "Dash Mobility" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImmunityTags_MetaData[] = {
		{ "Category", "Dash Mobility" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedMultiplier;
	static void NewProp_bIgnoresCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresCollision;
	static void NewProp_bImmuneToSlows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bImmuneToSlows;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImmunityTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UGE_SoproDeFluxo_DashMobility>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_MovementSpeedMultiplier = { "MovementSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_SoproDeFluxo_DashMobility, MovementSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedMultiplier_MetaData), NewProp_MovementSpeedMultiplier_MetaData) };
void Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bIgnoresCollision_SetBit(void* Obj)
{
	((UGE_SoproDeFluxo_DashMobility*)Obj)->bIgnoresCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bIgnoresCollision = { "bIgnoresCollision", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_SoproDeFluxo_DashMobility), &Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bIgnoresCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresCollision_MetaData), NewProp_bIgnoresCollision_MetaData) };
void Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bImmuneToSlows_SetBit(void* Obj)
{
	((UGE_SoproDeFluxo_DashMobility*)Obj)->bImmuneToSlows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bImmuneToSlows = { "bImmuneToSlows", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UGE_SoproDeFluxo_DashMobility), &Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bImmuneToSlows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bImmuneToSlows_MetaData), NewProp_bImmuneToSlows_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_ImmunityTags = { "ImmunityTags", nullptr, (EPropertyFlags)0x0010000000010015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UGE_SoproDeFluxo_DashMobility, ImmunityTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImmunityTags_MetaData), NewProp_ImmunityTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_MovementSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bIgnoresCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_bImmuneToSlows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::NewProp_ImmunityTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilAbilityEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::ClassParams = {
	&UGE_SoproDeFluxo_DashMobility::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::Class_MetaDataParams), Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility()
{
	if (!Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility.OuterSingleton, Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UGE_SoproDeFluxo_DashMobility);
UGE_SoproDeFluxo_DashMobility::~UGE_SoproDeFluxo_DashMobility() {}
// ********** End Class UGE_SoproDeFluxo_DashMobility **********************************************

// ********** Begin Class USigilAbilityEffectFactory Function CreateFracassoPrismalCooldownReduction 
struct Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics
{
	struct SigilAbilityEffectFactory_eventCreateFracassoPrismalCooldownReduction_Parms
	{
		float ReductionPercent;
		UGameplayEffect* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability Effects" },
		{ "CPP_Default_ReductionPercent", "0.500000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReductionPercent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::NewProp_ReductionPercent = { "ReductionPercent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateFracassoPrismalCooldownReduction_Parms, ReductionPercent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateFracassoPrismalCooldownReduction_Parms, ReturnValue), Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::NewProp_ReductionPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityEffectFactory, nullptr, "CreateFracassoPrismalCooldownReduction", Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::SigilAbilityEffectFactory_eventCreateFracassoPrismalCooldownReduction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::SigilAbilityEffectFactory_eventCreateFracassoPrismalCooldownReduction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityEffectFactory::execCreateFracassoPrismalCooldownReduction)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ReductionPercent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UGameplayEffect**)Z_Param__Result=USigilAbilityEffectFactory::CreateFracassoPrismalCooldownReduction(Z_Param_ReductionPercent);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityEffectFactory Function CreateFracassoPrismalCooldownReduction *

// ********** Begin Class USigilAbilityEffectFactory Function CreateFracassoPrismalDamageBuff ******
struct Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics
{
	struct SigilAbilityEffectFactory_eventCreateFracassoPrismalDamageBuff_Parms
	{
		float DamageMultiplier;
		float Duration;
		UGameplayEffect* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability Effects" },
		{ "CPP_Default_DamageMultiplier", "1.250000" },
		{ "CPP_Default_Duration", "8.000000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateFracassoPrismalDamageBuff_Parms, DamageMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateFracassoPrismalDamageBuff_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateFracassoPrismalDamageBuff_Parms, ReturnValue), Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityEffectFactory, nullptr, "CreateFracassoPrismalDamageBuff", Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::SigilAbilityEffectFactory_eventCreateFracassoPrismalDamageBuff_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::SigilAbilityEffectFactory_eventCreateFracassoPrismalDamageBuff_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityEffectFactory::execCreateFracassoPrismalDamageBuff)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageMultiplier);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UGameplayEffect**)Z_Param__Result=USigilAbilityEffectFactory::CreateFracassoPrismalDamageBuff(Z_Param_DamageMultiplier,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityEffectFactory Function CreateFracassoPrismalDamageBuff ********

// ********** Begin Class USigilAbilityEffectFactory Function CreateMurallionBarrierProtection *****
struct Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics
{
	struct SigilAbilityEffectFactory_eventCreateMurallionBarrierProtection_Parms
	{
		float DamageReduction;
		float Duration;
		UGameplayEffect* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// M\xc3\xa9todos de cria\xc3\xa7\xc3\xa3o de efeitos\n" },
#endif
		{ "CPP_Default_DamageReduction", "0.500000" },
		{ "CPP_Default_Duration", "3.000000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa9todos de cria\xc3\xa7\xc3\xa3o de efeitos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageReduction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::NewProp_DamageReduction = { "DamageReduction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierProtection_Parms, DamageReduction), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierProtection_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierProtection_Parms, ReturnValue), Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::NewProp_DamageReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityEffectFactory, nullptr, "CreateMurallionBarrierProtection", Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::SigilAbilityEffectFactory_eventCreateMurallionBarrierProtection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::SigilAbilityEffectFactory_eventCreateMurallionBarrierProtection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityEffectFactory::execCreateMurallionBarrierProtection)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageReduction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UGameplayEffect**)Z_Param__Result=USigilAbilityEffectFactory::CreateMurallionBarrierProtection(Z_Param_DamageReduction,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityEffectFactory Function CreateMurallionBarrierProtection *******

// ********** Begin Class USigilAbilityEffectFactory Function CreateMurallionBarrierRegeneration ***
struct Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics
{
	struct SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms
	{
		float HealthRegen;
		float ManaRegen;
		float Duration;
		UGameplayEffect* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability Effects" },
		{ "CPP_Default_Duration", "3.000000" },
		{ "CPP_Default_HealthRegen", "10.000000" },
		{ "CPP_Default_ManaRegen", "5.000000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegen;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaRegen;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_HealthRegen = { "HealthRegen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms, HealthRegen), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_ManaRegen = { "ManaRegen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms, ManaRegen), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms, ReturnValue), Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_HealthRegen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_ManaRegen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityEffectFactory, nullptr, "CreateMurallionBarrierRegeneration", Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::SigilAbilityEffectFactory_eventCreateMurallionBarrierRegeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityEffectFactory::execCreateMurallionBarrierRegeneration)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_HealthRegen);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ManaRegen);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UGameplayEffect**)Z_Param__Result=USigilAbilityEffectFactory::CreateMurallionBarrierRegeneration(Z_Param_HealthRegen,Z_Param_ManaRegen,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityEffectFactory Function CreateMurallionBarrierRegeneration *****

// ********** Begin Class USigilAbilityEffectFactory Function CreateSoproDeFluxoDashMobility *******
struct Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics
{
	struct SigilAbilityEffectFactory_eventCreateSoproDeFluxoDashMobility_Parms
	{
		float SpeedMultiplier;
		float Duration;
		UGameplayEffect* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability Effects" },
		{ "CPP_Default_Duration", "2.000000" },
		{ "CPP_Default_SpeedMultiplier", "2.000000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateSoproDeFluxoDashMobility_Parms, SpeedMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateSoproDeFluxoDashMobility_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateSoproDeFluxoDashMobility_Parms, ReturnValue), Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::NewProp_SpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityEffectFactory, nullptr, "CreateSoproDeFluxoDashMobility", Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::SigilAbilityEffectFactory_eventCreateSoproDeFluxoDashMobility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::SigilAbilityEffectFactory_eventCreateSoproDeFluxoDashMobility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityEffectFactory::execCreateSoproDeFluxoDashMobility)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SpeedMultiplier);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UGameplayEffect**)Z_Param__Result=USigilAbilityEffectFactory::CreateSoproDeFluxoDashMobility(Z_Param_SpeedMultiplier,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityEffectFactory Function CreateSoproDeFluxoDashMobility *********

// ********** Begin Class USigilAbilityEffectFactory Function CreateSoproDeFluxoShield *************
struct Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics
{
	struct SigilAbilityEffectFactory_eventCreateSoproDeFluxoShield_Parms
	{
		float ShieldAmount;
		float Duration;
		UGameplayEffect* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability Effects" },
		{ "CPP_Default_Duration", "5.000000" },
		{ "CPP_Default_ShieldAmount", "200.000000" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ShieldAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::NewProp_ShieldAmount = { "ShieldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateSoproDeFluxoShield_Parms, ShieldAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateSoproDeFluxoShield_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAbilityEffectFactory_eventCreateSoproDeFluxoShield_Parms, ReturnValue), Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::NewProp_ShieldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAbilityEffectFactory, nullptr, "CreateSoproDeFluxoShield", Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::SigilAbilityEffectFactory_eventCreateSoproDeFluxoShield_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::SigilAbilityEffectFactory_eventCreateSoproDeFluxoShield_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAbilityEffectFactory::execCreateSoproDeFluxoShield)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ShieldAmount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UGameplayEffect**)Z_Param__Result=USigilAbilityEffectFactory::CreateSoproDeFluxoShield(Z_Param_ShieldAmount,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class USigilAbilityEffectFactory Function CreateSoproDeFluxoShield ***************

// ********** Begin Class USigilAbilityEffectFactory ***********************************************
void USigilAbilityEffectFactory::StaticRegisterNativesUSigilAbilityEffectFactory()
{
	UClass* Class = USigilAbilityEffectFactory::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateFracassoPrismalCooldownReduction", &USigilAbilityEffectFactory::execCreateFracassoPrismalCooldownReduction },
		{ "CreateFracassoPrismalDamageBuff", &USigilAbilityEffectFactory::execCreateFracassoPrismalDamageBuff },
		{ "CreateMurallionBarrierProtection", &USigilAbilityEffectFactory::execCreateMurallionBarrierProtection },
		{ "CreateMurallionBarrierRegeneration", &USigilAbilityEffectFactory::execCreateMurallionBarrierRegeneration },
		{ "CreateSoproDeFluxoDashMobility", &USigilAbilityEffectFactory::execCreateSoproDeFluxoDashMobility },
		{ "CreateSoproDeFluxoShield", &USigilAbilityEffectFactory::execCreateSoproDeFluxoShield },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAbilityEffectFactory;
UClass* USigilAbilityEffectFactory::GetPrivateStaticClass()
{
	using TClass = USigilAbilityEffectFactory;
	if (!Z_Registration_Info_UClass_USigilAbilityEffectFactory.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAbilityEffectFactory"),
			Z_Registration_Info_UClass_USigilAbilityEffectFactory.InnerSingleton,
			StaticRegisterNativesUSigilAbilityEffectFactory,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAbilityEffectFactory.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAbilityEffectFactory_NoRegister()
{
	return USigilAbilityEffectFactory::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAbilityEffectFactory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Factory para criar GameplayEffects das habilidades dos s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAbilityEffects.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory para criar GameplayEffects das habilidades dos s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MurallionBarrierProtectionClass_MetaData[] = {
		{ "Category", "Effect Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Templates de efeitos\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Templates de efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MurallionBarrierRegenerationClass_MetaData[] = {
		{ "Category", "Effect Templates" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FracassoPrismalDamageBuffClass_MetaData[] = {
		{ "Category", "Effect Templates" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FracassoPrismalCooldownReductionClass_MetaData[] = {
		{ "Category", "Effect Templates" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoproDeFluxoShieldClass_MetaData[] = {
		{ "Category", "Effect Templates" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoproDeFluxoDashMobilityClass_MetaData[] = {
		{ "Category", "Effect Templates" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAbilityEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_MurallionBarrierProtectionClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_MurallionBarrierRegenerationClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FracassoPrismalDamageBuffClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FracassoPrismalCooldownReductionClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_SoproDeFluxoShieldClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_SoproDeFluxoDashMobilityClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalCooldownReduction, "CreateFracassoPrismalCooldownReduction" }, // 3265820283
		{ &Z_Construct_UFunction_USigilAbilityEffectFactory_CreateFracassoPrismalDamageBuff, "CreateFracassoPrismalDamageBuff" }, // 1212760538
		{ &Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierProtection, "CreateMurallionBarrierProtection" }, // 2840035972
		{ &Z_Construct_UFunction_USigilAbilityEffectFactory_CreateMurallionBarrierRegeneration, "CreateMurallionBarrierRegeneration" }, // 2590995352
		{ &Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoDashMobility, "CreateSoproDeFluxoDashMobility" }, // 64756242
		{ &Z_Construct_UFunction_USigilAbilityEffectFactory_CreateSoproDeFluxoShield, "CreateSoproDeFluxoShield" }, // 3149189715
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAbilityEffectFactory>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_MurallionBarrierProtectionClass = { "MurallionBarrierProtectionClass", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectFactory, MurallionBarrierProtectionClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UGE_Murallion_BarrierProtection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MurallionBarrierProtectionClass_MetaData), NewProp_MurallionBarrierProtectionClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_MurallionBarrierRegenerationClass = { "MurallionBarrierRegenerationClass", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectFactory, MurallionBarrierRegenerationClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UGE_Murallion_BarrierRegeneration_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MurallionBarrierRegenerationClass_MetaData), NewProp_MurallionBarrierRegenerationClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_FracassoPrismalDamageBuffClass = { "FracassoPrismalDamageBuffClass", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectFactory, FracassoPrismalDamageBuffClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FracassoPrismalDamageBuffClass_MetaData), NewProp_FracassoPrismalDamageBuffClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_FracassoPrismalCooldownReductionClass = { "FracassoPrismalCooldownReductionClass", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectFactory, FracassoPrismalCooldownReductionClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FracassoPrismalCooldownReductionClass_MetaData), NewProp_FracassoPrismalCooldownReductionClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_SoproDeFluxoShieldClass = { "SoproDeFluxoShieldClass", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectFactory, SoproDeFluxoShieldClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UGE_SoproDeFluxo_Shield_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoproDeFluxoShieldClass_MetaData), NewProp_SoproDeFluxoShieldClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_SoproDeFluxoDashMobilityClass = { "SoproDeFluxoDashMobilityClass", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAbilityEffectFactory, SoproDeFluxoDashMobilityClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoproDeFluxoDashMobilityClass_MetaData), NewProp_SoproDeFluxoDashMobilityClass_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAbilityEffectFactory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_MurallionBarrierProtectionClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_MurallionBarrierRegenerationClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_FracassoPrismalDamageBuffClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_FracassoPrismalCooldownReductionClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_SoproDeFluxoShieldClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAbilityEffectFactory_Statics::NewProp_SoproDeFluxoDashMobilityClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectFactory_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAbilityEffectFactory_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectFactory_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAbilityEffectFactory_Statics::ClassParams = {
	&USigilAbilityEffectFactory::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAbilityEffectFactory_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectFactory_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAbilityEffectFactory_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAbilityEffectFactory_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAbilityEffectFactory()
{
	if (!Z_Registration_Info_UClass_USigilAbilityEffectFactory.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAbilityEffectFactory.OuterSingleton, Z_Construct_UClass_USigilAbilityEffectFactory_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAbilityEffectFactory.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAbilityEffectFactory);
USigilAbilityEffectFactory::~USigilAbilityEffectFactory() {}
// ********** End Class USigilAbilityEffectFactory *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilAbilityAttributeSet, USigilAbilityAttributeSet::StaticClass, TEXT("USigilAbilityAttributeSet"), &Z_Registration_Info_UClass_USigilAbilityAttributeSet, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbilityAttributeSet), 3104719474U) },
		{ Z_Construct_UClass_USigilBarrierProtectionCalculation, USigilBarrierProtectionCalculation::StaticClass, TEXT("USigilBarrierProtectionCalculation"), &Z_Registration_Info_UClass_USigilBarrierProtectionCalculation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilBarrierProtectionCalculation), 3702001866U) },
		{ Z_Construct_UClass_USigilDamageBuffCalculation, USigilDamageBuffCalculation::StaticClass, TEXT("USigilDamageBuffCalculation"), &Z_Registration_Info_UClass_USigilDamageBuffCalculation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilDamageBuffCalculation), 2557755940U) },
		{ Z_Construct_UClass_USigilShieldCalculation, USigilShieldCalculation::StaticClass, TEXT("USigilShieldCalculation"), &Z_Registration_Info_UClass_USigilShieldCalculation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilShieldCalculation), 3236649286U) },
		{ Z_Construct_UClass_USigilCooldownReductionCalculation, USigilCooldownReductionCalculation::StaticClass, TEXT("USigilCooldownReductionCalculation"), &Z_Registration_Info_UClass_USigilCooldownReductionCalculation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilCooldownReductionCalculation), 1767854781U) },
		{ Z_Construct_UClass_USigilAbilityEffectBase, USigilAbilityEffectBase::StaticClass, TEXT("USigilAbilityEffectBase"), &Z_Registration_Info_UClass_USigilAbilityEffectBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbilityEffectBase), 3028874601U) },
		{ Z_Construct_UClass_UGE_Murallion_BarrierProtection, UGE_Murallion_BarrierProtection::StaticClass, TEXT("UGE_Murallion_BarrierProtection"), &Z_Registration_Info_UClass_UGE_Murallion_BarrierProtection, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UGE_Murallion_BarrierProtection), 1267420643U) },
		{ Z_Construct_UClass_UGE_Murallion_BarrierRegeneration, UGE_Murallion_BarrierRegeneration::StaticClass, TEXT("UGE_Murallion_BarrierRegeneration"), &Z_Registration_Info_UClass_UGE_Murallion_BarrierRegeneration, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UGE_Murallion_BarrierRegeneration), 911855578U) },
		{ Z_Construct_UClass_UGE_FracassoPrismal_DamageBuff, UGE_FracassoPrismal_DamageBuff::StaticClass, TEXT("UGE_FracassoPrismal_DamageBuff"), &Z_Registration_Info_UClass_UGE_FracassoPrismal_DamageBuff, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UGE_FracassoPrismal_DamageBuff), 556759775U) },
		{ Z_Construct_UClass_UGE_FracassoPrismal_CooldownReduction, UGE_FracassoPrismal_CooldownReduction::StaticClass, TEXT("UGE_FracassoPrismal_CooldownReduction"), &Z_Registration_Info_UClass_UGE_FracassoPrismal_CooldownReduction, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UGE_FracassoPrismal_CooldownReduction), 2325584900U) },
		{ Z_Construct_UClass_UGE_SoproDeFluxo_Shield, UGE_SoproDeFluxo_Shield::StaticClass, TEXT("UGE_SoproDeFluxo_Shield"), &Z_Registration_Info_UClass_UGE_SoproDeFluxo_Shield, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UGE_SoproDeFluxo_Shield), 594810952U) },
		{ Z_Construct_UClass_UGE_SoproDeFluxo_DashMobility, UGE_SoproDeFluxo_DashMobility::StaticClass, TEXT("UGE_SoproDeFluxo_DashMobility"), &Z_Registration_Info_UClass_UGE_SoproDeFluxo_DashMobility, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UGE_SoproDeFluxo_DashMobility), 2843544121U) },
		{ Z_Construct_UClass_USigilAbilityEffectFactory, USigilAbilityEffectFactory::StaticClass, TEXT("USigilAbilityEffectFactory"), &Z_Registration_Info_UClass_USigilAbilityEffectFactory, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAbilityEffectFactory), 2449006552U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h__Script_AURACRON_2840698064(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilityEffects_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
