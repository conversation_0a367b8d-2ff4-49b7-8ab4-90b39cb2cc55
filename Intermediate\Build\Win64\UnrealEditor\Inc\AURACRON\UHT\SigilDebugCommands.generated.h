// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Debug/SigilDebugCommands.h"

#ifdef AURACRON_SigilDebugCommands_generated_h
#error "SigilDebugCommands.generated.h already included, missing '#pragma once' in SigilDebugCommands.h"
#endif
#define AURACRON_SigilDebugCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class USigilDebugSettings ******************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilDebugSettings_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilDebugSettings(); \
	friend struct Z_Construct_UClass_USigilDebugSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilDebugSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilDebugSettings, UDeveloperSettings, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilDebugSettings_NoRegister) \
	DECLARE_SERIALIZER(USigilDebugSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_24_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilDebugSettings(USigilDebugSettings&&) = delete; \
	USigilDebugSettings(const USigilDebugSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilDebugSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilDebugSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilDebugSettings) \
	NO_API virtual ~USigilDebugSettings();


#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_21_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_24_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilDebugSettings;

// ********** End Class USigilDebugSettings ********************************************************

// ********** Begin Class USigilDebugCommands ******************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilDebugCommands_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_84_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilDebugCommands(); \
	friend struct Z_Construct_UClass_USigilDebugCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilDebugCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilDebugCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilDebugCommands_NoRegister) \
	DECLARE_SERIALIZER(USigilDebugCommands)


#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_84_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilDebugCommands(USigilDebugCommands&&) = delete; \
	USigilDebugCommands(const USigilDebugCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilDebugCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilDebugCommands); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilDebugCommands) \
	NO_API virtual ~USigilDebugCommands();


#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_81_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_84_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_84_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_84_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilDebugCommands;

// ********** End Class USigilDebugCommands ********************************************************

// ********** Begin ScriptStruct FSigilTestResult **************************************************
#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_235_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilTestResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilTestResult;
// ********** End ScriptStruct FSigilTestResult ****************************************************

// ********** Begin ScriptStruct FMOBAScenarioConfig ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h_265_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMOBAScenarioConfig;
// ********** End ScriptStruct FMOBAScenarioConfig *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
