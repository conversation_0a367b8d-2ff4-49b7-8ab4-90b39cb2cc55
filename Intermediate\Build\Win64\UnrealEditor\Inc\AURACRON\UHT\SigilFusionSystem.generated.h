// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Fusion/SigilFusionSystem.h"

#ifdef AURACRON_SigilFusionSystem_generated_h
#error "SigilFusionSystem.generated.h already included, missing '#pragma once' in SigilFusionSystem.h"
#endif
#define AURACRON_SigilFusionSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ASigilItem;
enum class ESigilFusionResult : uint8;
struct FGameplayTag;
struct FGuid;
struct FSigilFusionInstance;
struct FSigilFusionNotification;
struct FSigilFusionRecipe;

// ********** Begin ScriptStruct FSigilFusionRecipe ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_52_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFusionRecipe;
// ********** End ScriptStruct FSigilFusionRecipe **************************************************

// ********** Begin ScriptStruct FSigilFusionInstance **********************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_87_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFusionInstance_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFusionInstance;
// ********** End ScriptStruct FSigilFusionInstance ************************************************

// ********** Begin ScriptStruct FSigilFusionNotification ******************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_127_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFusionNotification_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFusionNotification;
// ********** End ScriptStruct FSigilFusionNotification ********************************************

// ********** Begin Delegate FOnSigilFusionStarted *************************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_153_DELEGATE \
AURACRON_API void FOnSigilFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionStarted, FSigilFusionInstance const& FusionInstance, AActor* Owner);


// ********** End Delegate FOnSigilFusionStarted ***************************************************

// ********** Begin Delegate FOnSigilFusionCompleted ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_154_DELEGATE \
AURACRON_API void FOnSigilFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionCompleted, FSigilFusionInstance const& FusionInstance, ESigilFusionResult Result, ASigilItem* ResultSigil);


// ********** End Delegate FOnSigilFusionCompleted *************************************************

// ********** Begin Delegate FOnSigilFusionProgress ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_155_DELEGATE \
AURACRON_API void FOnSigilFusionProgress_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionProgress, FSigilFusionInstance const& FusionInstance, float ProgressPercent);


// ********** End Delegate FOnSigilFusionProgress **************************************************

// ********** Begin Delegate FOnSigilFusionCancelled ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_156_DELEGATE \
AURACRON_API void FOnSigilFusionCancelled_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionCancelled, FSigilFusionInstance const& FusionInstance, AActor* Owner);


// ********** End Delegate FOnSigilFusionCancelled *************************************************

// ********** Begin Delegate FOnSigilFusionNotification ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_157_DELEGATE \
AURACRON_API void FOnSigilFusionNotification_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionNotification, FSigilFusionNotification const& Notification, AActor* Owner);


// ********** End Delegate FOnSigilFusionNotification **********************************************

// ********** Begin Delegate FOnSigilCreated *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_158_DELEGATE \
AURACRON_API void FOnSigilCreated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilCreated, ASigilItem* CreatedSigil, AActor* Owner, FSigilFusionRecipe const& Recipe);


// ********** End Delegate FOnSigilCreated *********************************************************

// ********** Begin Delegate FOnSigilConsumed ******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_159_DELEGATE \
AURACRON_API void FOnSigilConsumed_DelegateWrapper(const FMulticastScriptDelegate& OnSigilConsumed, ASigilItem* ConsumedSigil, AActor* Owner);


// ********** End Delegate FOnSigilConsumed ********************************************************

// ********** Begin Class USigilFusionSystem *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetDebugMode); \
	DECLARE_FUNCTION(execDebugCancelAllFusions); \
	DECLARE_FUNCTION(execDebugStartTestFusion); \
	DECLARE_FUNCTION(execDebugPrintActiveFusions); \
	DECLARE_FUNCTION(execSetMaxSimultaneousFusions); \
	DECLARE_FUNCTION(execSetDefaultFusionTime); \
	DECLARE_FUNCTION(execSetAutomaticFusionEnabled); \
	DECLARE_FUNCTION(execHideAllNotifications); \
	DECLARE_FUNCTION(execShowFusionNotification); \
	DECLARE_FUNCTION(execGetAvailableRecipes); \
	DECLARE_FUNCTION(execLoadDefaultRecipes); \
	DECLARE_FUNCTION(execRemoveFusionRecipe); \
	DECLARE_FUNCTION(execAddFusionRecipe); \
	DECLARE_FUNCTION(execGetActiveFusionCount); \
	DECLARE_FUNCTION(execGetFusionProgress); \
	DECLARE_FUNCTION(execGetFusionByID); \
	DECLARE_FUNCTION(execGetActiveFusions); \
	DECLARE_FUNCTION(execFindBestRecipe); \
	DECLARE_FUNCTION(execCanStartFusion); \
	DECLARE_FUNCTION(execCancelAllFusions); \
	DECLARE_FUNCTION(execCancelFusion); \
	DECLARE_FUNCTION(execStartManualFusion); \
	DECLARE_FUNCTION(execStartAutomaticFusion);


AURACRON_API UClass* Z_Construct_UClass_USigilFusionSystem_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilFusionSystem(); \
	friend struct Z_Construct_UClass_USigilFusionSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilFusionSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilFusionSystem, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilFusionSystem_NoRegister) \
	DECLARE_SERIALIZER(USigilFusionSystem)


#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilFusionSystem(USigilFusionSystem&&) = delete; \
	USigilFusionSystem(const USigilFusionSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilFusionSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilFusionSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilFusionSystem) \
	NO_API virtual ~USigilFusionSystem();


#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_165_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h_168_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilFusionSystem;

// ********** End Class USigilFusionSystem *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h

// ********** Begin Enum ESigilFusionState *********************************************************
#define FOREACH_ENUM_ESIGILFUSIONSTATE(op) \
	op(ESigilFusionState::None) \
	op(ESigilFusionState::Preparing) \
	op(ESigilFusionState::InProgress) \
	op(ESigilFusionState::Completed) \
	op(ESigilFusionState::Failed) \
	op(ESigilFusionState::Cancelled) 

enum class ESigilFusionState : uint8;
template<> struct TIsUEnumClass<ESigilFusionState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilFusionState>();
// ********** End Enum ESigilFusionState ***********************************************************

// ********** Begin Enum ESigilFusionType **********************************************************
#define FOREACH_ENUM_ESIGILFUSIONTYPE(op) \
	op(ESigilFusionType::Basic) \
	op(ESigilFusionType::Advanced) \
	op(ESigilFusionType::Legendary) \
	op(ESigilFusionType::Spectral) 

enum class ESigilFusionType : uint8;
template<> struct TIsUEnumClass<ESigilFusionType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilFusionType>();
// ********** End Enum ESigilFusionType ************************************************************

// ********** Begin Enum ESigilFusionResult ********************************************************
#define FOREACH_ENUM_ESIGILFUSIONRESULT(op) \
	op(ESigilFusionResult::Success) \
	op(ESigilFusionResult::Failed) \
	op(ESigilFusionResult::CriticalSuccess) \
	op(ESigilFusionResult::Cancelled) 

enum class ESigilFusionResult : uint8;
template<> struct TIsUEnumClass<ESigilFusionResult> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilFusionResult>();
// ********** End Enum ESigilFusionResult **********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
