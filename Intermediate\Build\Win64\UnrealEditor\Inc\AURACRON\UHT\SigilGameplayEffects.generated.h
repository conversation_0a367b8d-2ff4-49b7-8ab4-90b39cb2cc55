// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Sigils/SigilGameplayEffects.h"

#ifdef AURACRON_SigilGameplayEffects_generated_h
#error "SigilGameplayEffects.generated.h already included, missing '#pragma once' in SigilGameplayEffects.h"
#endif
#define AURACRON_SigilGameplayEffects_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UGameplayEffect;
enum class ESigilRarity : uint8;
enum class ESigilType : uint8;
enum class ESpectralEffectType : uint8;
struct FSpectralEffectConfig;

// ********** Begin ScriptStruct FSpectralEffectConfig *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_45_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSpectralEffectConfig;
// ********** End ScriptStruct FSpectralEffectConfig ***********************************************

// ********** Begin Class USigilGameplayEffectBase *************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilGameplayEffectBase_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_95_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilGameplayEffectBase(); \
	friend struct Z_Construct_UClass_USigilGameplayEffectBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilGameplayEffectBase_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilGameplayEffectBase, UGameplayEffect, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilGameplayEffectBase_NoRegister) \
	DECLARE_SERIALIZER(USigilGameplayEffectBase)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_95_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilGameplayEffectBase(USigilGameplayEffectBase&&) = delete; \
	USigilGameplayEffectBase(const USigilGameplayEffectBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilGameplayEffectBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilGameplayEffectBase); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilGameplayEffectBase) \
	NO_API virtual ~USigilGameplayEffectBase();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_92_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_95_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_95_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_95_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilGameplayEffectBase;

// ********** End Class USigilGameplayEffectBase ***************************************************

// ********** Begin Class USigilAutoFusionEffect ***************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilAutoFusionEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_138_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAutoFusionEffect(); \
	friend struct Z_Construct_UClass_USigilAutoFusionEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAutoFusionEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAutoFusionEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAutoFusionEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilAutoFusionEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_138_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAutoFusionEffect(USigilAutoFusionEffect&&) = delete; \
	USigilAutoFusionEffect(const USigilAutoFusionEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAutoFusionEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAutoFusionEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAutoFusionEffect) \
	NO_API virtual ~USigilAutoFusionEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_135_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_138_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_138_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_138_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAutoFusionEffect;

// ********** End Class USigilAutoFusionEffect *****************************************************

// ********** Begin Class USigilForceFusionEffect **************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilForceFusionEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_169_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilForceFusionEffect(); \
	friend struct Z_Construct_UClass_USigilForceFusionEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilForceFusionEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilForceFusionEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilForceFusionEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilForceFusionEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_169_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilForceFusionEffect(USigilForceFusionEffect&&) = delete; \
	USigilForceFusionEffect(const USigilForceFusionEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilForceFusionEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilForceFusionEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilForceFusionEffect) \
	NO_API virtual ~USigilForceFusionEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_166_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_169_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_169_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_169_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilForceFusionEffect;

// ********** End Class USigilForceFusionEffect ****************************************************

// ********** Begin Class USigilSpectralPowerEffect ************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralPowerEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_197_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilSpectralPowerEffect(); \
	friend struct Z_Construct_UClass_USigilSpectralPowerEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilSpectralPowerEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilSpectralPowerEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilSpectralPowerEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilSpectralPowerEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_197_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilSpectralPowerEffect(USigilSpectralPowerEffect&&) = delete; \
	USigilSpectralPowerEffect(const USigilSpectralPowerEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilSpectralPowerEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilSpectralPowerEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilSpectralPowerEffect) \
	NO_API virtual ~USigilSpectralPowerEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_194_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_197_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_197_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_197_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilSpectralPowerEffect;

// ********** End Class USigilSpectralPowerEffect **************************************************

// ********** Begin Class USigilSpectralResilienceEffect *******************************************
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralResilienceEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_217_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilSpectralResilienceEffect(); \
	friend struct Z_Construct_UClass_USigilSpectralResilienceEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilSpectralResilienceEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilSpectralResilienceEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilSpectralResilienceEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilSpectralResilienceEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_217_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilSpectralResilienceEffect(USigilSpectralResilienceEffect&&) = delete; \
	USigilSpectralResilienceEffect(const USigilSpectralResilienceEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilSpectralResilienceEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilSpectralResilienceEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilSpectralResilienceEffect) \
	NO_API virtual ~USigilSpectralResilienceEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_214_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_217_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_217_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_217_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilSpectralResilienceEffect;

// ********** End Class USigilSpectralResilienceEffect *********************************************

// ********** Begin Class USigilSpectralVelocityEffect *********************************************
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralVelocityEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_237_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilSpectralVelocityEffect(); \
	friend struct Z_Construct_UClass_USigilSpectralVelocityEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilSpectralVelocityEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilSpectralVelocityEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilSpectralVelocityEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilSpectralVelocityEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_237_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilSpectralVelocityEffect(USigilSpectralVelocityEffect&&) = delete; \
	USigilSpectralVelocityEffect(const USigilSpectralVelocityEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilSpectralVelocityEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilSpectralVelocityEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilSpectralVelocityEffect) \
	NO_API virtual ~USigilSpectralVelocityEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_234_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_237_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_237_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_237_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilSpectralVelocityEffect;

// ********** End Class USigilSpectralVelocityEffect ***********************************************

// ********** Begin Class USigilSpectralFocusEffect ************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralFocusEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_261_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilSpectralFocusEffect(); \
	friend struct Z_Construct_UClass_USigilSpectralFocusEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilSpectralFocusEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilSpectralFocusEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilSpectralFocusEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilSpectralFocusEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_261_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilSpectralFocusEffect(USigilSpectralFocusEffect&&) = delete; \
	USigilSpectralFocusEffect(const USigilSpectralFocusEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilSpectralFocusEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilSpectralFocusEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilSpectralFocusEffect) \
	NO_API virtual ~USigilSpectralFocusEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_258_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_261_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_261_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_261_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilSpectralFocusEffect;

// ********** End Class USigilSpectralFocusEffect **************************************************

// ********** Begin Class USigilTeamFightEffect ****************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilTeamFightEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_289_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilTeamFightEffect(); \
	friend struct Z_Construct_UClass_USigilTeamFightEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilTeamFightEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilTeamFightEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilTeamFightEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilTeamFightEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_289_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilTeamFightEffect(USigilTeamFightEffect&&) = delete; \
	USigilTeamFightEffect(const USigilTeamFightEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilTeamFightEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilTeamFightEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilTeamFightEffect) \
	NO_API virtual ~USigilTeamFightEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_286_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_289_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_289_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_289_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilTeamFightEffect;

// ********** End Class USigilTeamFightEffect ******************************************************

// ********** Begin Class USigilObjectiveEffect ****************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilObjectiveEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_317_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilObjectiveEffect(); \
	friend struct Z_Construct_UClass_USigilObjectiveEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilObjectiveEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilObjectiveEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilObjectiveEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilObjectiveEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_317_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilObjectiveEffect(USigilObjectiveEffect&&) = delete; \
	USigilObjectiveEffect(const USigilObjectiveEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilObjectiveEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilObjectiveEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilObjectiveEffect) \
	NO_API virtual ~USigilObjectiveEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_314_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_317_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_317_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_317_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilObjectiveEffect;

// ********** End Class USigilObjectiveEffect ******************************************************

// ********** Begin Class USigilReforgeEffect ******************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilReforgeEffect_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_349_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilReforgeEffect(); \
	friend struct Z_Construct_UClass_USigilReforgeEffect_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilReforgeEffect_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilReforgeEffect, USigilGameplayEffectBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilReforgeEffect_NoRegister) \
	DECLARE_SERIALIZER(USigilReforgeEffect)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_349_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilReforgeEffect(USigilReforgeEffect&&) = delete; \
	USigilReforgeEffect(const USigilReforgeEffect&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilReforgeEffect); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilReforgeEffect); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilReforgeEffect) \
	NO_API virtual ~USigilReforgeEffect();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_346_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_349_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_349_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_349_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilReforgeEffect;

// ********** End Class USigilReforgeEffect ********************************************************

// ********** Begin Class USigilFusionExecutionCalculation *****************************************
AURACRON_API UClass* Z_Construct_UClass_USigilFusionExecutionCalculation_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_381_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilFusionExecutionCalculation(); \
	friend struct Z_Construct_UClass_USigilFusionExecutionCalculation_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilFusionExecutionCalculation_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilFusionExecutionCalculation, UGameplayEffectExecutionCalculation, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilFusionExecutionCalculation_NoRegister) \
	DECLARE_SERIALIZER(USigilFusionExecutionCalculation)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_381_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilFusionExecutionCalculation(USigilFusionExecutionCalculation&&) = delete; \
	USigilFusionExecutionCalculation(const USigilFusionExecutionCalculation&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilFusionExecutionCalculation); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilFusionExecutionCalculation); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilFusionExecutionCalculation) \
	NO_API virtual ~USigilFusionExecutionCalculation();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_378_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_381_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_381_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_381_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilFusionExecutionCalculation;

// ********** End Class USigilFusionExecutionCalculation *******************************************

// ********** Begin Class USigilSpectralExecutionCalculation ***************************************
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralExecutionCalculation_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_412_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilSpectralExecutionCalculation(); \
	friend struct Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilSpectralExecutionCalculation_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilSpectralExecutionCalculation, UGameplayEffectExecutionCalculation, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilSpectralExecutionCalculation_NoRegister) \
	DECLARE_SERIALIZER(USigilSpectralExecutionCalculation)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_412_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilSpectralExecutionCalculation(USigilSpectralExecutionCalculation&&) = delete; \
	USigilSpectralExecutionCalculation(const USigilSpectralExecutionCalculation&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilSpectralExecutionCalculation); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilSpectralExecutionCalculation); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilSpectralExecutionCalculation) \
	NO_API virtual ~USigilSpectralExecutionCalculation();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_409_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_412_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_412_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_412_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilSpectralExecutionCalculation;

// ********** End Class USigilSpectralExecutionCalculation *****************************************

// ********** Begin Class USigilEffectFactory ******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidateEffectConfig); \
	DECLARE_FUNCTION(execGetDefaultFusionDuration); \
	DECLARE_FUNCTION(execGetDefaultRarityMultiplier); \
	DECLARE_FUNCTION(execCreateObjectiveEffect); \
	DECLARE_FUNCTION(execCreateTeamFightEffect); \
	DECLARE_FUNCTION(execCreateSpectralEffect); \
	DECLARE_FUNCTION(execCreateAutoFusionEffect);


AURACRON_API UClass* Z_Construct_UClass_USigilEffectFactory_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilEffectFactory(); \
	friend struct Z_Construct_UClass_USigilEffectFactory_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilEffectFactory_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilEffectFactory, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilEffectFactory_NoRegister) \
	DECLARE_SERIALIZER(USigilEffectFactory)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API USigilEffectFactory(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilEffectFactory(USigilEffectFactory&&) = delete; \
	USigilEffectFactory(const USigilEffectFactory&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilEffectFactory); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilEffectFactory); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(USigilEffectFactory) \
	NO_API virtual ~USigilEffectFactory();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_439_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h_442_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilEffectFactory;

// ********** End Class USigilEffectFactory ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h

// ********** Begin Enum ESpectralEffectType *******************************************************
#define FOREACH_ENUM_ESPECTRALEFFECTTYPE(op) \
	op(ESpectralEffectType::None) \
	op(ESpectralEffectType::PowerBoost) \
	op(ESpectralEffectType::ResilienceBoost) \
	op(ESpectralEffectType::VelocityBoost) \
	op(ESpectralEffectType::FocusBoost) \
	op(ESpectralEffectType::CombatBonus) \
	op(ESpectralEffectType::MobilityBonus) \
	op(ESpectralEffectType::ResourceBonus) \
	op(ESpectralEffectType::TeamFightBonus) \
	op(ESpectralEffectType::ObjectiveBonus) \
	op(ESpectralEffectType::FusionMultiplier) 

enum class ESpectralEffectType : uint8;
template<> struct TIsUEnumClass<ESpectralEffectType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESpectralEffectType>();
// ********** End Enum ESpectralEffectType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
