// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Multiplayer/SigilReplicationManager.h"
#include "GameplayTagContainer.h"
#include "Net/Serialization/FastArraySerializerImplementation.h"
#include "UObject/CoreNet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilReplicationManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager();
AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EPredictionType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ERollbackType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAdvancedInterestSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAntiCheatEvent();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAntiCheatSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONReplicationSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FClientPredictionData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FClientPredictionSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FDynamicObjectSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FDynamicObjectState();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FEnvironmentState();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerValidationData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPredictionEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPredictionSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FReplicationMetrics();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FRollbackEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FRollbackSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FRollbackState();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FRollbackStateArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionReplicationData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationStats();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSpatialHashGrid();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FTeamState();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FTelemetrySettings();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NETCORE_API UScriptStruct* Z_Construct_UScriptStruct_FFastArraySerializer();
NETCORE_API UScriptStruct* Z_Construct_UScriptStruct_FFastArraySerializerItem();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FSigilReplicationData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilReplicationData;
class UScriptStruct* FSigilReplicationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilReplicationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilReplicationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilReplicationData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilReplicationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para replica\xc3\xa7\xc3\xa3o de dados de s\xc3\xadgilo\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para replica\xc3\xa7\xc3\xa3o de dados de s\xc3\xadgilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilType_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rarity_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotIndex_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEquipped_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilTags_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionProgress;
	static void NewProp_bIsEquipped_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEquipped;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilReplicationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SigilType), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilType_MetaData), NewProp_SigilType_MetaData) }; // 3758400079
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rarity_MetaData), NewProp_Rarity_MetaData) }; // 3544987888
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SlotIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotIndex_MetaData), NewProp_SlotIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_FusionProgress = { "FusionProgress", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, FusionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionProgress_MetaData), NewProp_FusionProgress_MetaData) };
void Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped_SetBit(void* Obj)
{
	((FSigilReplicationData*)Obj)->bIsEquipped = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped = { "bIsEquipped", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilReplicationData), &Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEquipped_MetaData), NewProp_bIsEquipped_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilTags = { "SigilTags", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationData, SigilTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilTags_MetaData), NewProp_SigilTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_FusionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_bIsEquipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewProp_SigilTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilReplicationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilReplicationData",
	Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationData_Statics::PropPointers),
	sizeof(FSigilReplicationData),
	alignof(FSigilReplicationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilReplicationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilReplicationData.InnerSingleton, Z_Construct_UScriptStruct_FSigilReplicationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationData.InnerSingleton;
}
// ********** End ScriptStruct FSigilReplicationData ***********************************************

// ********** Begin ScriptStruct FSigilReplicationStats ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilReplicationStats;
class UScriptStruct* FSigilReplicationStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilReplicationStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilReplicationStats, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilReplicationStats"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilReplicationStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas do sistema\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas do sistema" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalSigils_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigils_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedSlots_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReforgeTimestamp_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSystemActive_MetaData[] = {
		{ "Category", "SigilReplicationStats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalSigils;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EquippedSigils;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnlockedSlots;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReforgeTimestamp;
	static void NewProp_bSystemActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSystemActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilReplicationStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_TotalSigils = { "TotalSigils", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, TotalSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalSigils_MetaData), NewProp_TotalSigils_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_EquippedSigils = { "EquippedSigils", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, EquippedSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigils_MetaData), NewProp_EquippedSigils_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_UnlockedSlots = { "UnlockedSlots", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, UnlockedSlots), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedSlots_MetaData), NewProp_UnlockedSlots_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_LastReforgeTimestamp = { "LastReforgeTimestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilReplicationStats, LastReforgeTimestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReforgeTimestamp_MetaData), NewProp_LastReforgeTimestamp_MetaData) };
void Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive_SetBit(void* Obj)
{
	((FSigilReplicationStats*)Obj)->bSystemActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive = { "bSystemActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilReplicationStats), &Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSystemActive_MetaData), NewProp_bSystemActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_TotalSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_EquippedSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_UnlockedSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_LastReforgeTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewProp_bSystemActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilReplicationStats",
	Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::PropPointers),
	sizeof(FSigilReplicationStats),
	alignof(FSigilReplicationStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilReplicationStats()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilReplicationStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilReplicationStats.InnerSingleton, Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilReplicationStats.InnerSingleton;
}
// ********** End ScriptStruct FSigilReplicationStats **********************************************

// ********** Begin ScriptStruct FSigilFusionReplicationData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData;
class UScriptStruct* FSigilFusionReplicationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFusionReplicationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFusionReplicationData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para dados de fus\xc3\xa3o replicados\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de fus\xc3\xa3o replicados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionStartTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFusing_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRarity_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionStartTime;
	static void NewProp_bIsFusing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFusing;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetRarity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFusionReplicationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionProgress = { "FusionProgress", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, FusionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionProgress_MetaData), NewProp_FusionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionStartTime = { "FusionStartTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, FusionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionStartTime_MetaData), NewProp_FusionStartTime_MetaData) };
void Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing_SetBit(void* Obj)
{
	((FSigilFusionReplicationData*)Obj)->bIsFusing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing = { "bIsFusing", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilFusionReplicationData), &Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFusing_MetaData), NewProp_bIsFusing_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity = { "TargetRarity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionReplicationData, TargetRarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRarity_MetaData), NewProp_TargetRarity_MetaData) }; // 3544987888
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_FusionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_bIsFusing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewProp_TargetRarity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFusionReplicationData",
	Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::PropPointers),
	sizeof(FSigilFusionReplicationData),
	alignof(FSigilFusionReplicationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionReplicationData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.InnerSingleton, Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData.InnerSingleton;
}
// ********** End ScriptStruct FSigilFusionReplicationData *****************************************

// ********** Begin ScriptStruct FSigilPlayerDataEntry *********************************************
static_assert(std::is_polymorphic<FSigilPlayerDataEntry>() == std::is_polymorphic<FFastArraySerializerItem>(), "USTRUCT FSigilPlayerDataEntry cannot be polymorphic unless super FFastArraySerializerItem is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry;
class UScriptStruct* FSigilPlayerDataEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerDataEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerDataEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Item para replica\xc3\xa7\xc3\xa3o de dados de Sigil por jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Item para replica\xc3\xa7\xc3\xa3o de dados de Sigil por jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerDataEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerDataEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData_Inner = { "SigilData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData = { "SigilData", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerDataEntry, SigilData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewProp_SigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializerItem,
	&NewStructOps,
	"SigilPlayerDataEntry",
	Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::PropPointers),
	sizeof(FSigilPlayerDataEntry),
	alignof(FSigilPlayerDataEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerDataEntry ***********************************************

// ********** Begin ScriptStruct FSigilPlayerDataArray *********************************************
static_assert(std::is_polymorphic<FSigilPlayerDataArray>() == std::is_polymorphic<FFastArraySerializer>(), "USTRUCT FSigilPlayerDataArray cannot be polymorphic unless super FFastArraySerializer is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray;
class UScriptStruct* FSigilPlayerDataArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerDataArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerDataArray"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.OuterSingleton;
}
#if defined(UE_NET_HAS_IRIS_FASTARRAY_BINDING) && UE_NET_HAS_IRIS_FASTARRAY_BINDING
UE_NET_IMPLEMENT_FASTARRAY(FSigilPlayerDataArray);
#else
UE_NET_IMPLEMENT_FASTARRAY_STUB(FSigilPlayerDataArray);
#endif
struct Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Serializer para dados de Sigil por jogador - UE 5.6 PRODUCTION READY\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Serializer para dados de Sigil por jogador - UE 5.6 PRODUCTION READY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Items_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Items_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Items;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerDataArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items_Inner = { "Items", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilPlayerDataEntry, METADATA_PARAMS(0, nullptr) }; // 2914415567
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items = { "Items", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerDataArray, Items), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Items_MetaData), NewProp_Items_MetaData) }; // 2914415567
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewProp_Items,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializer,
	&NewStructOps,
	"SigilPlayerDataArray",
	Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::PropPointers),
	sizeof(FSigilPlayerDataArray),
	alignof(FSigilPlayerDataArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerDataArray()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerDataArray ***********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsEntry ********************************************
static_assert(std::is_polymorphic<FSigilPlayerStatsEntry>() == std::is_polymorphic<FFastArraySerializerItem>(), "USTRUCT FSigilPlayerStatsEntry cannot be polymorphic unless super FFastArraySerializerItem is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry;
class UScriptStruct* FSigilPlayerStatsEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerStatsEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Item para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas por jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Item para replica\xc3\xa7\xc3\xa3o de estat\xc3\xadsticas por jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerStatsEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerStatsEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerStatsEntry, Stats), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializerItem,
	&NewStructOps,
	"SigilPlayerStatsEntry",
	Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::PropPointers),
	sizeof(FSigilPlayerStatsEntry),
	alignof(FSigilPlayerStatsEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerStatsEntry **********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsArray ********************************************
static_assert(std::is_polymorphic<FSigilPlayerStatsArray>() == std::is_polymorphic<FFastArraySerializer>(), "USTRUCT FSigilPlayerStatsArray cannot be polymorphic unless super FFastArraySerializer is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray;
class UScriptStruct* FSigilPlayerStatsArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilPlayerStatsArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilPlayerStatsArray"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.OuterSingleton;
}
#if defined(UE_NET_HAS_IRIS_FASTARRAY_BINDING) && UE_NET_HAS_IRIS_FASTARRAY_BINDING
UE_NET_IMPLEMENT_FASTARRAY(FSigilPlayerStatsArray);
#else
UE_NET_IMPLEMENT_FASTARRAY_STUB(FSigilPlayerStatsArray);
#endif
struct Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Serializer para estat\xc3\xadsticas por jogador - UE 5.6 PRODUCTION READY\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Serializer para estat\xc3\xadsticas por jogador - UE 5.6 PRODUCTION READY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Items_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Items_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Items;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilPlayerStatsArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items_Inner = { "Items", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilPlayerStatsEntry, METADATA_PARAMS(0, nullptr) }; // 3618960821
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items = { "Items", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilPlayerStatsArray, Items), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Items_MetaData), NewProp_Items_MetaData) }; // 3618960821
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewProp_Items,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializer,
	&NewStructOps,
	"SigilPlayerStatsArray",
	Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::PropPointers),
	sizeof(FSigilPlayerStatsArray),
	alignof(FSigilPlayerStatsArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilPlayerStatsArray()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.InnerSingleton, Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray.InnerSingleton;
}
// ********** End ScriptStruct FSigilPlayerStatsArray **********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsEntry ******************************************
static_assert(std::is_polymorphic<FSigilActiveFusionsEntry>() == std::is_polymorphic<FFastArraySerializerItem>(), "USTRUCT FSigilActiveFusionsEntry cannot be polymorphic unless super FFastArraySerializerItem is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry;
class UScriptStruct* FSigilActiveFusionsEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilActiveFusionsEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Item para replica\xc3\xa7\xc3\xa3o de fus\xc3\xb5""es ativas por jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Item para replica\xc3\xa7\xc3\xa3o de fus\xc3\xb5""es ativas por jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionData_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FusionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FusionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilActiveFusionsEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionID = { "FusionID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsEntry, FusionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionID_MetaData), NewProp_FusionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData_Inner = { "FusionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(0, nullptr) }; // 4074950016
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData = { "FusionData", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsEntry, FusionData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionData_MetaData), NewProp_FusionData_MetaData) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewProp_FusionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializerItem,
	&NewStructOps,
	"SigilActiveFusionsEntry",
	Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::PropPointers),
	sizeof(FSigilActiveFusionsEntry),
	alignof(FSigilActiveFusionsEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.InnerSingleton, Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry.InnerSingleton;
}
// ********** End ScriptStruct FSigilActiveFusionsEntry ********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsArray ******************************************
static_assert(std::is_polymorphic<FSigilActiveFusionsArray>() == std::is_polymorphic<FFastArraySerializer>(), "USTRUCT FSigilActiveFusionsArray cannot be polymorphic unless super FFastArraySerializer is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray;
class UScriptStruct* FSigilActiveFusionsArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilActiveFusionsArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilActiveFusionsArray"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.OuterSingleton;
}
#if defined(UE_NET_HAS_IRIS_FASTARRAY_BINDING) && UE_NET_HAS_IRIS_FASTARRAY_BINDING
UE_NET_IMPLEMENT_FASTARRAY(FSigilActiveFusionsArray);
#else
UE_NET_IMPLEMENT_FASTARRAY_STUB(FSigilActiveFusionsArray);
#endif
struct Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// FastArray Serializer para fus\xc3\xb5""es ativas por jogador - UE 5.6 PRODUCTION READY\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FastArray Serializer para fus\xc3\xb5""es ativas por jogador - UE 5.6 PRODUCTION READY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Items_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Items_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Items;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilActiveFusionsArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items_Inner = { "Items", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilActiveFusionsEntry, METADATA_PARAMS(0, nullptr) }; // 2854786014
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items = { "Items", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilActiveFusionsArray, Items), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Items_MetaData), NewProp_Items_MetaData) }; // 2854786014
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewProp_Items,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	Z_Construct_UScriptStruct_FFastArraySerializer,
	&NewStructOps,
	"SigilActiveFusionsArray",
	Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::PropPointers),
	sizeof(FSigilActiveFusionsArray),
	alignof(FSigilActiveFusionsArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilActiveFusionsArray()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.InnerSingleton, Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray.InnerSingleton;
}
// ********** End ScriptStruct FSigilActiveFusionsArray ********************************************

// ********** Begin Enum EPredictionType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPredictionType;
static UEnum* EPredictionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPredictionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPredictionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EPredictionType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EPredictionType"));
	}
	return Z_Registration_Info_UEnum_EPredictionType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EPredictionType>()
{
	return EPredictionType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EPredictionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ability.Name", "EPredictionType::Ability" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para sistemas avan\xc3\xa7""ados\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
		{ "Movement.Name", "EPredictionType::Movement" },
		{ "Sigil.Name", "EPredictionType::Sigil" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para sistemas avan\xc3\xa7""ados" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPredictionType::Movement", (int64)EPredictionType::Movement },
		{ "EPredictionType::Ability", (int64)EPredictionType::Ability },
		{ "EPredictionType::Sigil", (int64)EPredictionType::Sigil },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EPredictionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EPredictionType",
	"EPredictionType",
	Z_Construct_UEnum_AURACRON_EPredictionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EPredictionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EPredictionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EPredictionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EPredictionType()
{
	if (!Z_Registration_Info_UEnum_EPredictionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPredictionType.InnerSingleton, Z_Construct_UEnum_AURACRON_EPredictionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPredictionType.InnerSingleton;
}
// ********** End Enum EPredictionType *************************************************************

// ********** Begin Enum ERollbackType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERollbackType;
static UEnum* ERollbackType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERollbackType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERollbackType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ERollbackType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ERollbackType"));
	}
	return Z_Registration_Info_UEnum_ERollbackType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ERollbackType>()
{
	return ERollbackType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ERollbackType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ability.Name", "ERollbackType::Ability" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
		{ "Movement.Name", "ERollbackType::Movement" },
		{ "Sigil.Name", "ERollbackType::Sigil" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERollbackType::Movement", (int64)ERollbackType::Movement },
		{ "ERollbackType::Ability", (int64)ERollbackType::Ability },
		{ "ERollbackType::Sigil", (int64)ERollbackType::Sigil },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ERollbackType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ERollbackType",
	"ERollbackType",
	Z_Construct_UEnum_AURACRON_ERollbackType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ERollbackType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ERollbackType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ERollbackType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ERollbackType()
{
	if (!Z_Registration_Info_UEnum_ERollbackType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERollbackType.InnerSingleton, Z_Construct_UEnum_AURACRON_ERollbackType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERollbackType.InnerSingleton;
}
// ********** End Enum ERollbackType ***************************************************************

// ********** Begin ScriptStruct FPredictionEntry **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPredictionEntry;
class UScriptStruct* FPredictionEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPredictionEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPredictionEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPredictionEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PredictionEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FPredictionEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPredictionEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para sistemas avan\xc3\xa7""ados\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para sistemas avan\xc3\xa7""ados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictedLocation_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictedVelocity_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityID_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionType_MetaData[] = {
		{ "Category", "PredictionEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PredictedLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PredictedVelocity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AbilityID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PredictionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PredictionType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPredictionEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictedLocation = { "PredictedLocation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, PredictedLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictedLocation_MetaData), NewProp_PredictedLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictedVelocity = { "PredictedVelocity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, PredictedVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictedVelocity_MetaData), NewProp_PredictedVelocity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_AbilityID = { "AbilityID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, AbilityID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityID_MetaData), NewProp_AbilityID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictionType = { "PredictionType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionEntry, PredictionType), Z_Construct_UEnum_AURACRON_EPredictionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionType_MetaData), NewProp_PredictionType_MetaData) }; // 3939455507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPredictionEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictedLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictedVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_AbilityID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewProp_PredictionType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPredictionEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPredictionEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PredictionEntry",
	Z_Construct_UScriptStruct_FPredictionEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPredictionEntry_Statics::PropPointers),
	sizeof(FPredictionEntry),
	alignof(FPredictionEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPredictionEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPredictionEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPredictionEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FPredictionEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPredictionEntry.InnerSingleton, Z_Construct_UScriptStruct_FPredictionEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPredictionEntry.InnerSingleton;
}
// ********** End ScriptStruct FPredictionEntry ****************************************************

// ********** Begin ScriptStruct FRollbackEntry ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRollbackEntry;
class UScriptStruct* FRollbackEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRollbackEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRollbackEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("RollbackEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FRollbackEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRollbackEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorrectedLocation_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorrectedVelocity_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityID_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilID_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShouldCancelAbility_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackType_MetaData[] = {
		{ "Category", "RollbackEntry" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CorrectedLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CorrectedVelocity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AbilityID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static void NewProp_bShouldCancelAbility_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShouldCancelAbility;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RollbackType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RollbackType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRollbackEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_CorrectedLocation = { "CorrectedLocation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, CorrectedLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorrectedLocation_MetaData), NewProp_CorrectedLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_CorrectedVelocity = { "CorrectedVelocity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, CorrectedVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorrectedVelocity_MetaData), NewProp_CorrectedVelocity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_AbilityID = { "AbilityID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, AbilityID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityID_MetaData), NewProp_AbilityID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, SigilID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilID_MetaData), NewProp_SigilID_MetaData) };
void Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_bShouldCancelAbility_SetBit(void* Obj)
{
	((FRollbackEntry*)Obj)->bShouldCancelAbility = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_bShouldCancelAbility = { "bShouldCancelAbility", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRollbackEntry), &Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_bShouldCancelAbility_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShouldCancelAbility_MetaData), NewProp_bShouldCancelAbility_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_RollbackType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_RollbackType = { "RollbackType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackEntry, RollbackType), Z_Construct_UEnum_AURACRON_ERollbackType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackType_MetaData), NewProp_RollbackType_MetaData) }; // 2703372478
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRollbackEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_CorrectedLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_CorrectedVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_AbilityID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_bShouldCancelAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_RollbackType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewProp_RollbackType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRollbackEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"RollbackEntry",
	Z_Construct_UScriptStruct_FRollbackEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackEntry_Statics::PropPointers),
	sizeof(FRollbackEntry),
	alignof(FRollbackEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRollbackEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRollbackEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRollbackEntry.InnerSingleton, Z_Construct_UScriptStruct_FRollbackEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRollbackEntry.InnerSingleton;
}
// ********** End ScriptStruct FRollbackEntry ******************************************************

// ********** Begin ScriptStruct FClientPredictionSettings *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClientPredictionSettings;
class UScriptStruct* FClientPredictionSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClientPredictionSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClientPredictionSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClientPredictionSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ClientPredictionSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FClientPredictionSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClientPredictionSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de predi\xc3\xa7\xc3\xa3o de cliente\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de predi\xc3\xa7\xc3\xa3o de cliente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMovementPrediction_MetaData[] = {
		{ "Category", "ClientPredictionSettings" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAbilityPrediction_MetaData[] = {
		{ "Category", "ClientPredictionSettings" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSigilPrediction_MetaData[] = {
		{ "Category", "ClientPredictionSettings" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPredictionTime_MetaData[] = {
		{ "Category", "ClientPredictionSettings" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionTolerance_MetaData[] = {
		{ "Category", "ClientPredictionSettings" },
		{ "ClampMax", "0.5" },
		{ "ClampMin", "0.01" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionBufferSize_MetaData[] = {
		{ "Category", "ClientPredictionSettings" },
		{ "ClampMax", "120" },
		{ "ClampMin", "30" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableMovementPrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMovementPrediction;
	static void NewProp_bEnableAbilityPrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAbilityPrediction;
	static void NewProp_bEnableSigilPrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSigilPrediction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxPredictionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PredictionTolerance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PredictionBufferSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClientPredictionSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableMovementPrediction_SetBit(void* Obj)
{
	((FClientPredictionSettings*)Obj)->bEnableMovementPrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableMovementPrediction = { "bEnableMovementPrediction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClientPredictionSettings), &Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableMovementPrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMovementPrediction_MetaData), NewProp_bEnableMovementPrediction_MetaData) };
void Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableAbilityPrediction_SetBit(void* Obj)
{
	((FClientPredictionSettings*)Obj)->bEnableAbilityPrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableAbilityPrediction = { "bEnableAbilityPrediction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClientPredictionSettings), &Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableAbilityPrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAbilityPrediction_MetaData), NewProp_bEnableAbilityPrediction_MetaData) };
void Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableSigilPrediction_SetBit(void* Obj)
{
	((FClientPredictionSettings*)Obj)->bEnableSigilPrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableSigilPrediction = { "bEnableSigilPrediction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FClientPredictionSettings), &Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableSigilPrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSigilPrediction_MetaData), NewProp_bEnableSigilPrediction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_MaxPredictionTime = { "MaxPredictionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClientPredictionSettings, MaxPredictionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPredictionTime_MetaData), NewProp_MaxPredictionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_PredictionTolerance = { "PredictionTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClientPredictionSettings, PredictionTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionTolerance_MetaData), NewProp_PredictionTolerance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_PredictionBufferSize = { "PredictionBufferSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClientPredictionSettings, PredictionBufferSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionBufferSize_MetaData), NewProp_PredictionBufferSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableMovementPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableAbilityPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_bEnableSigilPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_MaxPredictionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_PredictionTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewProp_PredictionBufferSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"ClientPredictionSettings",
	Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::PropPointers),
	sizeof(FClientPredictionSettings),
	alignof(FClientPredictionSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClientPredictionSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FClientPredictionSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClientPredictionSettings.InnerSingleton, Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClientPredictionSettings.InnerSingleton;
}
// ********** End ScriptStruct FClientPredictionSettings *******************************************

// ********** Begin ScriptStruct FRollbackSettings *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRollbackSettings;
class UScriptStruct* FRollbackSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRollbackSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRollbackSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("RollbackSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FRollbackSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRollbackSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de rollback networking\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de rollback networking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRollbackFrames_MetaData[] = {
		{ "Category", "RollbackSettings" },
		{ "ClampMax", "120" },
		{ "ClampMin", "30" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackTolerance_MetaData[] = {
		{ "Category", "RollbackSettings" },
		{ "ClampMax", "0.2" },
		{ "ClampMin", "0.01" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRollbackLogging_MetaData[] = {
		{ "Category", "RollbackSettings" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackBufferSize_MetaData[] = {
		{ "Category", "RollbackSettings" },
		{ "ClampMax", "240" },
		{ "ClampMin", "60" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRollbackFrames;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RollbackTolerance;
	static void NewProp_bEnableRollbackLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRollbackLogging;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RollbackBufferSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRollbackSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_MaxRollbackFrames = { "MaxRollbackFrames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackSettings, MaxRollbackFrames), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRollbackFrames_MetaData), NewProp_MaxRollbackFrames_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_RollbackTolerance = { "RollbackTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackSettings, RollbackTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackTolerance_MetaData), NewProp_RollbackTolerance_MetaData) };
void Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_bEnableRollbackLogging_SetBit(void* Obj)
{
	((FRollbackSettings*)Obj)->bEnableRollbackLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_bEnableRollbackLogging = { "bEnableRollbackLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRollbackSettings), &Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_bEnableRollbackLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRollbackLogging_MetaData), NewProp_bEnableRollbackLogging_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_RollbackBufferSize = { "RollbackBufferSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackSettings, RollbackBufferSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackBufferSize_MetaData), NewProp_RollbackBufferSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRollbackSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_MaxRollbackFrames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_RollbackTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_bEnableRollbackLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewProp_RollbackBufferSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRollbackSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"RollbackSettings",
	Z_Construct_UScriptStruct_FRollbackSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackSettings_Statics::PropPointers),
	sizeof(FRollbackSettings),
	alignof(FRollbackSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRollbackSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRollbackSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRollbackSettings.InnerSingleton, Z_Construct_UScriptStruct_FRollbackSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRollbackSettings.InnerSingleton;
}
// ********** End ScriptStruct FRollbackSettings ***************************************************

// ********** Begin Delegate FOnSigilEquipped ******************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilEquipped_Parms
	{
		int32 PlayerID;
		FSigilReplicationData SigilData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegados para eventos de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegados para eventos de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEquipped_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_SigilData = { "SigilData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilEquipped_Parms, SigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::NewProp_SigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilEquipped__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEquipped, int32 PlayerID, FSigilReplicationData const& SigilData)
{
	struct _Script_AURACRON_eventOnSigilEquipped_Parms
	{
		int32 PlayerID;
		FSigilReplicationData SigilData;
	};
	_Script_AURACRON_eventOnSigilEquipped_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilData=SigilData;
	OnSigilEquipped.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilEquipped ********************************************************

// ********** Begin Delegate FOnSigilUnequipped ****************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilUnequipped_Parms
	{
		int32 PlayerID;
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilUnequipped_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilUnequipped_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilUnequipped__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilUnequipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilUnequipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilUnequipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilUnequipped, int32 PlayerID, int32 SlotIndex)
{
	struct _Script_AURACRON_eventOnSigilUnequipped_Parms
	{
		int32 PlayerID;
		int32 SlotIndex;
	};
	_Script_AURACRON_eventOnSigilUnequipped_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SlotIndex=SlotIndex;
	OnSigilUnequipped.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilUnequipped ******************************************************

// ********** Begin Delegate FOnSigilReplicationFusionStarted **************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms
	{
		int32 PlayerID;
		FSigilFusionReplicationData FusionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_FusionData = { "FusionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms, FusionData), Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionData_MetaData), NewProp_FusionData_MetaData) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::NewProp_FusionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilReplicationFusionStarted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilReplicationFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionStarted, int32 PlayerID, FSigilFusionReplicationData const& FusionData)
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms
	{
		int32 PlayerID;
		FSigilFusionReplicationData FusionData;
	};
	_Script_AURACRON_eventOnSigilReplicationFusionStarted_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.FusionData=FusionData;
	OnSigilReplicationFusionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilReplicationFusionStarted ****************************************

// ********** Begin Delegate FOnSigilReplicationFusionCompleted ************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms
	{
		int32 PlayerID;
		FSigilReplicationData NewSigilData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_NewSigilData = { "NewSigilData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms, NewSigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSigilData_MetaData), NewProp_NewSigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::NewProp_NewSigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilReplicationFusionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilReplicationFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionCompleted, int32 PlayerID, FSigilReplicationData const& NewSigilData)
{
	struct _Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms
	{
		int32 PlayerID;
		FSigilReplicationData NewSigilData;
	};
	_Script_AURACRON_eventOnSigilReplicationFusionCompleted_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.NewSigilData=NewSigilData;
	OnSigilReplicationFusionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilReplicationFusionCompleted **************************************

// ********** Begin Delegate FOnSigilSystemStatsUpdated ********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms
	{
		FSigilReplicationStats Stats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms, Stats), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilSystemStatsUpdated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilSystemStatsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilSystemStatsUpdated, FSigilReplicationStats const& Stats)
{
	struct _Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms
	{
		FSigilReplicationStats Stats;
	};
	_Script_AURACRON_eventOnSigilSystemStatsUpdated_Parms Parms;
	Parms.Stats=Stats;
	OnSigilSystemStatsUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilSystemStatsUpdated **********************************************

// ********** Begin ScriptStruct FPlayerValidationData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPlayerValidationData;
class UScriptStruct* FPlayerValidationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerValidationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPlayerValidationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerValidationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PlayerValidationData"));
	}
	return Z_Registration_Info_UScriptStruct_FPlayerValidationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPlayerValidationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas para sistemas avan\xc3\xa7""ados de replica\xc3\xa7\xc3\xa3o - UE 5.6 APIs modernas\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas para sistemas avan\xc3\xa7""ados de replica\xc3\xa7\xc3\xa3o - UE 5.6 APIs modernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastMovementValidation_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastValidPosition_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAbilityValidation_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationFailures_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValidated_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastMovementValidation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastValidPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAbilityValidation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ValidationFailures;
	static void NewProp_bIsValidated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValidated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerValidationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_LastMovementValidation = { "LastMovementValidation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerValidationData, LastMovementValidation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastMovementValidation_MetaData), NewProp_LastMovementValidation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_LastValidPosition = { "LastValidPosition", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerValidationData, LastValidPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastValidPosition_MetaData), NewProp_LastValidPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_LastAbilityValidation = { "LastAbilityValidation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerValidationData, LastAbilityValidation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAbilityValidation_MetaData), NewProp_LastAbilityValidation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_ValidationFailures = { "ValidationFailures", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerValidationData, ValidationFailures), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationFailures_MetaData), NewProp_ValidationFailures_MetaData) };
void Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_bIsValidated_SetBit(void* Obj)
{
	((FPlayerValidationData*)Obj)->bIsValidated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_bIsValidated = { "bIsValidated", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlayerValidationData), &Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_bIsValidated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValidated_MetaData), NewProp_bIsValidated_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerValidationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_LastMovementValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_LastValidPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_LastAbilityValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_ValidationFailures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewProp_bIsValidated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerValidationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerValidationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PlayerValidationData",
	Z_Construct_UScriptStruct_FPlayerValidationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerValidationData_Statics::PropPointers),
	sizeof(FPlayerValidationData),
	alignof(FPlayerValidationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerValidationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerValidationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerValidationData()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerValidationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPlayerValidationData.InnerSingleton, Z_Construct_UScriptStruct_FPlayerValidationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPlayerValidationData.InnerSingleton;
}
// ********** End ScriptStruct FPlayerValidationData ***********************************************

// ********** Begin ScriptStruct FDynamicObjectState ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDynamicObjectState;
class UScriptStruct* FDynamicObjectState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDynamicObjectState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDynamicObjectState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDynamicObjectState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("DynamicObjectState"));
	}
	return Z_Registration_Info_UScriptStruct_FDynamicObjectState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDynamicObjectState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDynamicObjectState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_ObjectID = { "ObjectID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectState, ObjectID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectID_MetaData), NewProp_ObjectID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectState, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectState, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectState, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectState, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FDynamicObjectState*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectState), &Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDynamicObjectState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_ObjectID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicObjectState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDynamicObjectState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"DynamicObjectState",
	Z_Construct_UScriptStruct_FDynamicObjectState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicObjectState_Statics::PropPointers),
	sizeof(FDynamicObjectState),
	alignof(FDynamicObjectState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicObjectState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDynamicObjectState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDynamicObjectState()
{
	if (!Z_Registration_Info_UScriptStruct_FDynamicObjectState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDynamicObjectState.InnerSingleton, Z_Construct_UScriptStruct_FDynamicObjectState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDynamicObjectState.InnerSingleton;
}
// ********** End ScriptStruct FDynamicObjectState *************************************************

// ********** Begin ScriptStruct FEnvironmentState *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEnvironmentState;
class UScriptStruct* FEnvironmentState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEnvironmentState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEnvironmentState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEnvironmentState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EnvironmentState"));
	}
	return Z_Registration_Info_UScriptStruct_FEnvironmentState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEnvironmentState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentName_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivePortals_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentTags_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_EnvironmentID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnvironmentName;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActivePortals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActivePortals;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnvironmentTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEnvironmentState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_EnvironmentID = { "EnvironmentID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEnvironmentState, EnvironmentID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentID_MetaData), NewProp_EnvironmentID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_EnvironmentName = { "EnvironmentName", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEnvironmentState, EnvironmentName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentName_MetaData), NewProp_EnvironmentName_MetaData) };
void Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FEnvironmentState*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEnvironmentState), &Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEnvironmentState, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_ActivePortals_Inner = { "ActivePortals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_ActivePortals = { "ActivePortals", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEnvironmentState, ActivePortals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivePortals_MetaData), NewProp_ActivePortals_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_EnvironmentTags = { "EnvironmentTags", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEnvironmentState, EnvironmentTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentTags_MetaData), NewProp_EnvironmentTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEnvironmentState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_EnvironmentID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_EnvironmentName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_ActivePortals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_ActivePortals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewProp_EnvironmentTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEnvironmentState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEnvironmentState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"EnvironmentState",
	Z_Construct_UScriptStruct_FEnvironmentState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEnvironmentState_Statics::PropPointers),
	sizeof(FEnvironmentState),
	alignof(FEnvironmentState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEnvironmentState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEnvironmentState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEnvironmentState()
{
	if (!Z_Registration_Info_UScriptStruct_FEnvironmentState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEnvironmentState.InnerSingleton, Z_Construct_UScriptStruct_FEnvironmentState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEnvironmentState.InnerSingleton;
}
// ********** End ScriptStruct FEnvironmentState ***************************************************

// ********** Begin ScriptStruct FTeamState ********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTeamState;
class UScriptStruct* FTeamState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTeamState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTeamState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTeamState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("TeamState"));
	}
	return Z_Registration_Info_UScriptStruct_FTeamState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTeamState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerIDs_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamScore_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamGold_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectivesControlled_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsWinning_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayerIDs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeamGold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectivesControlled;
	static void NewProp_bIsWinning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsWinning;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTeamState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTeamState, TeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamID_MetaData), NewProp_TeamID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_PlayerIDs_Inner = { "PlayerIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_PlayerIDs = { "PlayerIDs", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTeamState, PlayerIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerIDs_MetaData), NewProp_PlayerIDs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_TeamScore = { "TeamScore", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTeamState, TeamScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamScore_MetaData), NewProp_TeamScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_TeamGold = { "TeamGold", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTeamState, TeamGold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamGold_MetaData), NewProp_TeamGold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_ObjectivesControlled = { "ObjectivesControlled", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTeamState, ObjectivesControlled), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectivesControlled_MetaData), NewProp_ObjectivesControlled_MetaData) };
void Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_bIsWinning_SetBit(void* Obj)
{
	((FTeamState*)Obj)->bIsWinning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_bIsWinning = { "bIsWinning", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTeamState), &Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_bIsWinning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsWinning_MetaData), NewProp_bIsWinning_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTeamState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_PlayerIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_PlayerIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_TeamScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_TeamGold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_ObjectivesControlled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTeamState_Statics::NewProp_bIsWinning,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTeamState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTeamState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"TeamState",
	Z_Construct_UScriptStruct_FTeamState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTeamState_Statics::PropPointers),
	sizeof(FTeamState),
	alignof(FTeamState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTeamState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTeamState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTeamState()
{
	if (!Z_Registration_Info_UScriptStruct_FTeamState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTeamState.InnerSingleton, Z_Construct_UScriptStruct_FTeamState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTeamState.InnerSingleton;
}
// ********** End ScriptStruct FTeamState **********************************************************

// ********** Begin ScriptStruct FAntiCheatEvent ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAntiCheatEvent;
class UScriptStruct* FAntiCheatEvent::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAntiCheatEvent.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAntiCheatEvent.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAntiCheatEvent, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AntiCheatEvent"));
	}
	return Z_Registration_Info_UScriptStruct_FAntiCheatEvent.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAntiCheatEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Details_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Details;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAntiCheatEvent>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatEvent, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatEvent, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatEvent, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_Details = { "Details", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatEvent, Details), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Details_MetaData), NewProp_Details_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatEvent, Severity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_Details,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewProp_Severity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AntiCheatEvent",
	Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::PropPointers),
	sizeof(FAntiCheatEvent),
	alignof(FAntiCheatEvent),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAntiCheatEvent()
{
	if (!Z_Registration_Info_UScriptStruct_FAntiCheatEvent.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAntiCheatEvent.InnerSingleton, Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAntiCheatEvent.InnerSingleton;
}
// ********** End ScriptStruct FAntiCheatEvent *****************************************************

// ********** Begin ScriptStruct FRollbackState ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRollbackState;
class UScriptStruct* FRollbackState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRollbackState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRollbackState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("RollbackState"));
	}
	return Z_Registration_Info_UScriptStruct_FRollbackState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRollbackState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structs para sistemas avan\xc3\xa7""ados - UE 5.6 APIs modernas\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structs para sistemas avan\xc3\xa7""ados - UE 5.6 APIs modernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameNumber_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FrameNumber;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRollbackState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackState, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackState, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackState, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_FrameNumber = { "FrameNumber", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackState, FrameNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameNumber_MetaData), NewProp_FrameNumber_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRollbackState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackState_Statics::NewProp_FrameNumber,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRollbackState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"RollbackState",
	Z_Construct_UScriptStruct_FRollbackState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackState_Statics::PropPointers),
	sizeof(FRollbackState),
	alignof(FRollbackState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRollbackState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRollbackState()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRollbackState.InnerSingleton, Z_Construct_UScriptStruct_FRollbackState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRollbackState.InnerSingleton;
}
// ********** End ScriptStruct FRollbackState ******************************************************

// ********** Begin ScriptStruct FPredictionSettings ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPredictionSettings;
class UScriptStruct* FPredictionSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPredictionSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPredictionSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPredictionSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PredictionSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FPredictionSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPredictionSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPredictionTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackTolerance_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPredictionFrames_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxPredictionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RollbackTolerance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPredictionFrames;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPredictionSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewProp_MaxPredictionTime = { "MaxPredictionTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionSettings, MaxPredictionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPredictionTime_MetaData), NewProp_MaxPredictionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewProp_RollbackTolerance = { "RollbackTolerance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionSettings, RollbackTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackTolerance_MetaData), NewProp_RollbackTolerance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewProp_MaxPredictionFrames = { "MaxPredictionFrames", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPredictionSettings, MaxPredictionFrames), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPredictionFrames_MetaData), NewProp_MaxPredictionFrames_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPredictionSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewProp_MaxPredictionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewProp_RollbackTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewProp_MaxPredictionFrames,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPredictionSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPredictionSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PredictionSettings",
	Z_Construct_UScriptStruct_FPredictionSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPredictionSettings_Statics::PropPointers),
	sizeof(FPredictionSettings),
	alignof(FPredictionSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPredictionSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPredictionSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPredictionSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FPredictionSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPredictionSettings.InnerSingleton, Z_Construct_UScriptStruct_FPredictionSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPredictionSettings.InnerSingleton;
}
// ********** End ScriptStruct FPredictionSettings *************************************************

// ********** Begin ScriptStruct FRollbackStateArray ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRollbackStateArray;
class UScriptStruct* FRollbackStateArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackStateArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRollbackStateArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRollbackStateArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("RollbackStateArray"));
	}
	return Z_Registration_Info_UScriptStruct_FRollbackStateArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRollbackStateArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Struct wrapper para TArray em TMap - UE 5.6 compat\xc3\xadvel\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct wrapper para TArray em TMap - UE 5.6 compat\xc3\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_States_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_States_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_States;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRollbackStateArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRollbackStateArray_Statics::NewProp_States_Inner = { "States", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRollbackState, METADATA_PARAMS(0, nullptr) }; // 2737844484
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRollbackStateArray_Statics::NewProp_States = { "States", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRollbackStateArray, States), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_States_MetaData), NewProp_States_MetaData) }; // 2737844484
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRollbackStateArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackStateArray_Statics::NewProp_States_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRollbackStateArray_Statics::NewProp_States,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackStateArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRollbackStateArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"RollbackStateArray",
	Z_Construct_UScriptStruct_FRollbackStateArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackStateArray_Statics::PropPointers),
	sizeof(FRollbackStateArray),
	alignof(FRollbackStateArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRollbackStateArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRollbackStateArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRollbackStateArray()
{
	if (!Z_Registration_Info_UScriptStruct_FRollbackStateArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRollbackStateArray.InnerSingleton, Z_Construct_UScriptStruct_FRollbackStateArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRollbackStateArray.InnerSingleton;
}
// ********** End ScriptStruct FRollbackStateArray *************************************************

// ********** Begin ScriptStruct FClientPredictionData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FClientPredictionData;
class UScriptStruct* FClientPredictionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FClientPredictionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FClientPredictionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FClientPredictionData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ClientPredictionData"));
	}
	return Z_Registration_Info_UScriptStruct_FClientPredictionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FClientPredictionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Struct para dados de predi\xc3\xa7\xc3\xa3o do cliente - UE 5.6 APIs modernas\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct para dados de predi\xc3\xa7\xc3\xa3o do cliente - UE 5.6 APIs modernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionHistory_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackStates_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastPredictionTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PredictionHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PredictionHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RollbackStates_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RollbackStates;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastPredictionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FClientPredictionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_PredictionHistory_Inner = { "PredictionHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_PredictionHistory = { "PredictionHistory", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClientPredictionData, PredictionHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionHistory_MetaData), NewProp_PredictionHistory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_RollbackStates_Inner = { "RollbackStates", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRollbackState, METADATA_PARAMS(0, nullptr) }; // 2737844484
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_RollbackStates = { "RollbackStates", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClientPredictionData, RollbackStates), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackStates_MetaData), NewProp_RollbackStates_MetaData) }; // 2737844484
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_LastPredictionTime = { "LastPredictionTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FClientPredictionData, LastPredictionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastPredictionTime_MetaData), NewProp_LastPredictionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FClientPredictionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_PredictionHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_PredictionHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_RollbackStates_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_RollbackStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewProp_LastPredictionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClientPredictionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FClientPredictionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"ClientPredictionData",
	Z_Construct_UScriptStruct_FClientPredictionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClientPredictionData_Statics::PropPointers),
	sizeof(FClientPredictionData),
	alignof(FClientPredictionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FClientPredictionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FClientPredictionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FClientPredictionData()
{
	if (!Z_Registration_Info_UScriptStruct_FClientPredictionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FClientPredictionData.InnerSingleton, Z_Construct_UScriptStruct_FClientPredictionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FClientPredictionData.InnerSingleton;
}
// ********** End ScriptStruct FClientPredictionData ***********************************************

// ********** Begin ScriptStruct FReplicationMetrics ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FReplicationMetrics;
class UScriptStruct* FReplicationMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FReplicationMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FReplicationMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FReplicationMetrics, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ReplicationMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FReplicationMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FReplicationMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBandwidth_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLatency_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationRate_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DroppedPackets_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionRatio_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalReplications_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalBandwidth_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatEvents_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackEvents_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentBandwidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLatency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReplicationRate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DroppedPackets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionRatio;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalReplications;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalBandwidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AntiCheatEvents;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RollbackEvents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FReplicationMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_CurrentBandwidth = { "CurrentBandwidth", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, CurrentBandwidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBandwidth_MetaData), NewProp_CurrentBandwidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_AverageLatency = { "AverageLatency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, AverageLatency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLatency_MetaData), NewProp_AverageLatency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_ReplicationRate = { "ReplicationRate", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, ReplicationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationRate_MetaData), NewProp_ReplicationRate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_DroppedPackets = { "DroppedPackets", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, DroppedPackets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DroppedPackets_MetaData), NewProp_DroppedPackets_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_CompressionRatio = { "CompressionRatio", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, CompressionRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionRatio_MetaData), NewProp_CompressionRatio_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, StartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_TotalReplications = { "TotalReplications", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, TotalReplications), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalReplications_MetaData), NewProp_TotalReplications_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_TotalBandwidth = { "TotalBandwidth", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, TotalBandwidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalBandwidth_MetaData), NewProp_TotalBandwidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_AntiCheatEvents = { "AntiCheatEvents", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, AntiCheatEvents), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatEvents_MetaData), NewProp_AntiCheatEvents_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_RollbackEvents = { "RollbackEvents", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FReplicationMetrics, RollbackEvents), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackEvents_MetaData), NewProp_RollbackEvents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FReplicationMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_CurrentBandwidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_AverageLatency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_ReplicationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_DroppedPackets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_CompressionRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_TotalReplications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_TotalBandwidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_AntiCheatEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewProp_RollbackEvents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FReplicationMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FReplicationMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"ReplicationMetrics",
	Z_Construct_UScriptStruct_FReplicationMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FReplicationMetrics_Statics::PropPointers),
	sizeof(FReplicationMetrics),
	alignof(FReplicationMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FReplicationMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FReplicationMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FReplicationMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FReplicationMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FReplicationMetrics.InnerSingleton, Z_Construct_UScriptStruct_FReplicationMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FReplicationMetrics.InnerSingleton;
}
// ********** End ScriptStruct FReplicationMetrics *************************************************

// ********** Begin ScriptStruct FAdvancedInterestSettings *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings;
class UScriptStruct* FAdvancedInterestSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAdvancedInterestSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AdvancedInterestSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de configura\xc3\xa7\xc3\xa3o para sistemas avan\xc3\xa7""ados - UE 5.6 APIs modernas\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de configura\xc3\xa7\xc3\xa3o para sistemas avan\xc3\xa7""ados - UE 5.6 APIs modernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRelevantActors_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelevanceUpdateFrequency_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialHashGridSize_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceCulling_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFrustumCulling_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGameplayRelevance_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePriorityScaling_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRelevantActors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RelevanceUpdateFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpatialHashGridSize;
	static void NewProp_bUseDistanceCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceCulling;
	static void NewProp_bUseFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFrustumCulling;
	static void NewProp_bUseGameplayRelevance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGameplayRelevance;
	static void NewProp_bUsePriorityScaling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePriorityScaling;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAdvancedInterestSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_MaxRelevantActors = { "MaxRelevantActors", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdvancedInterestSettings, MaxRelevantActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRelevantActors_MetaData), NewProp_MaxRelevantActors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_RelevanceUpdateFrequency = { "RelevanceUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdvancedInterestSettings, RelevanceUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelevanceUpdateFrequency_MetaData), NewProp_RelevanceUpdateFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_SpatialHashGridSize = { "SpatialHashGridSize", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdvancedInterestSettings, SpatialHashGridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialHashGridSize_MetaData), NewProp_SpatialHashGridSize_MetaData) };
void Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseDistanceCulling_SetBit(void* Obj)
{
	((FAdvancedInterestSettings*)Obj)->bUseDistanceCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseDistanceCulling = { "bUseDistanceCulling", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAdvancedInterestSettings), &Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseDistanceCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceCulling_MetaData), NewProp_bUseDistanceCulling_MetaData) };
void Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseFrustumCulling_SetBit(void* Obj)
{
	((FAdvancedInterestSettings*)Obj)->bUseFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseFrustumCulling = { "bUseFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAdvancedInterestSettings), &Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFrustumCulling_MetaData), NewProp_bUseFrustumCulling_MetaData) };
void Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseGameplayRelevance_SetBit(void* Obj)
{
	((FAdvancedInterestSettings*)Obj)->bUseGameplayRelevance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseGameplayRelevance = { "bUseGameplayRelevance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAdvancedInterestSettings), &Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseGameplayRelevance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGameplayRelevance_MetaData), NewProp_bUseGameplayRelevance_MetaData) };
void Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUsePriorityScaling_SetBit(void* Obj)
{
	((FAdvancedInterestSettings*)Obj)->bUsePriorityScaling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUsePriorityScaling = { "bUsePriorityScaling", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAdvancedInterestSettings), &Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUsePriorityScaling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePriorityScaling_MetaData), NewProp_bUsePriorityScaling_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_MaxRelevantActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_RelevanceUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_SpatialHashGridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseDistanceCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUseGameplayRelevance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewProp_bUsePriorityScaling,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AdvancedInterestSettings",
	Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::PropPointers),
	sizeof(FAdvancedInterestSettings),
	alignof(FAdvancedInterestSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAdvancedInterestSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings.InnerSingleton, Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings.InnerSingleton;
}
// ********** End ScriptStruct FAdvancedInterestSettings *******************************************

// ********** Begin ScriptStruct FAntiCheatSettings ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAntiCheatSettings;
class UScriptStruct* FAntiCheatSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAntiCheatSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAntiCheatSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAntiCheatSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AntiCheatSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAntiCheatSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAntiCheatSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateMovement_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateAbilities_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateResources_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateSigilActions_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementTolerance_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityTolerance_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMovementSpeedTolerance_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationFrequency_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValidationFailures_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuspiciousActionThreshold_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatLogLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bValidateMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateMovement;
	static void NewProp_bValidateAbilities_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateAbilities;
	static void NewProp_bValidateResources_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateResources;
	static void NewProp_bValidateSigilActions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateSigilActions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMovementSpeedTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationFrequency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxValidationFailures;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SuspiciousActionThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AntiCheatLogLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAntiCheatSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateMovement_SetBit(void* Obj)
{
	((FAntiCheatSettings*)Obj)->bValidateMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateMovement = { "bValidateMovement", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAntiCheatSettings), &Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateMovement_MetaData), NewProp_bValidateMovement_MetaData) };
void Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateAbilities_SetBit(void* Obj)
{
	((FAntiCheatSettings*)Obj)->bValidateAbilities = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateAbilities = { "bValidateAbilities", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAntiCheatSettings), &Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateAbilities_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateAbilities_MetaData), NewProp_bValidateAbilities_MetaData) };
void Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateResources_SetBit(void* Obj)
{
	((FAntiCheatSettings*)Obj)->bValidateResources = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateResources = { "bValidateResources", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAntiCheatSettings), &Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateResources_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateResources_MetaData), NewProp_bValidateResources_MetaData) };
void Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateSigilActions_SetBit(void* Obj)
{
	((FAntiCheatSettings*)Obj)->bValidateSigilActions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateSigilActions = { "bValidateSigilActions", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAntiCheatSettings), &Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateSigilActions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateSigilActions_MetaData), NewProp_bValidateSigilActions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_MovementTolerance = { "MovementTolerance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, MovementTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementTolerance_MetaData), NewProp_MovementTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_AbilityTolerance = { "AbilityTolerance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, AbilityTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityTolerance_MetaData), NewProp_AbilityTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_MaxMovementSpeedTolerance = { "MaxMovementSpeedTolerance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, MaxMovementSpeedTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMovementSpeedTolerance_MetaData), NewProp_MaxMovementSpeedTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_ValidationFrequency = { "ValidationFrequency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, ValidationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationFrequency_MetaData), NewProp_ValidationFrequency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_MaxValidationFailures = { "MaxValidationFailures", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, MaxValidationFailures), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValidationFailures_MetaData), NewProp_MaxValidationFailures_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_SuspiciousActionThreshold = { "SuspiciousActionThreshold", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, SuspiciousActionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuspiciousActionThreshold_MetaData), NewProp_SuspiciousActionThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_AntiCheatLogLevel = { "AntiCheatLogLevel", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAntiCheatSettings, AntiCheatLogLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatLogLevel_MetaData), NewProp_AntiCheatLogLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateResources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_bValidateSigilActions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_MovementTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_AbilityTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_MaxMovementSpeedTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_ValidationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_MaxValidationFailures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_SuspiciousActionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewProp_AntiCheatLogLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AntiCheatSettings",
	Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::PropPointers),
	sizeof(FAntiCheatSettings),
	alignof(FAntiCheatSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAntiCheatSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAntiCheatSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAntiCheatSettings.InnerSingleton, Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAntiCheatSettings.InnerSingleton;
}
// ********** End ScriptStruct FAntiCheatSettings **************************************************

// ********** Begin ScriptStruct FDynamicObjectSettings ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDynamicObjectSettings;
class UScriptStruct* FDynamicObjectSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDynamicObjectSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDynamicObjectSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDynamicObjectSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("DynamicObjectSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FDynamicObjectSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDynamicObjects_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateFrequency_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseInterpolation_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePrediction_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateFluxoPrismal_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateTrilhoEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateEnvironmentTransitions_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateTerrainChanges_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FluxoPrismalUpdateFrequency_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrilhoEffectRadius_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentTransitionRadius_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainChangeRadius_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxDynamicObjects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PredictionTime;
	static void NewProp_bUseInterpolation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInterpolation;
	static void NewProp_bUsePrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePrediction;
	static void NewProp_bReplicateFluxoPrismal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateFluxoPrismal;
	static void NewProp_bReplicateTrilhoEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateTrilhoEffects;
	static void NewProp_bReplicateEnvironmentTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateEnvironmentTransitions;
	static void NewProp_bReplicateTerrainChanges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateTerrainChanges;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FluxoPrismalUpdateFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TrilhoEffectRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentTransitionRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainChangeRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDynamicObjectSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_MaxDynamicObjects = { "MaxDynamicObjects", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, MaxDynamicObjects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDynamicObjects_MetaData), NewProp_MaxDynamicObjects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_UpdateFrequency = { "UpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, UpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateFrequency_MetaData), NewProp_UpdateFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_PredictionTime = { "PredictionTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, PredictionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionTime_MetaData), NewProp_PredictionTime_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUseInterpolation_SetBit(void* Obj)
{
	((FDynamicObjectSettings*)Obj)->bUseInterpolation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUseInterpolation = { "bUseInterpolation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectSettings), &Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUseInterpolation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseInterpolation_MetaData), NewProp_bUseInterpolation_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUsePrediction_SetBit(void* Obj)
{
	((FDynamicObjectSettings*)Obj)->bUsePrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUsePrediction = { "bUsePrediction", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectSettings), &Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUsePrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePrediction_MetaData), NewProp_bUsePrediction_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateFluxoPrismal_SetBit(void* Obj)
{
	((FDynamicObjectSettings*)Obj)->bReplicateFluxoPrismal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateFluxoPrismal = { "bReplicateFluxoPrismal", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectSettings), &Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateFluxoPrismal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateFluxoPrismal_MetaData), NewProp_bReplicateFluxoPrismal_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTrilhoEffects_SetBit(void* Obj)
{
	((FDynamicObjectSettings*)Obj)->bReplicateTrilhoEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTrilhoEffects = { "bReplicateTrilhoEffects", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectSettings), &Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTrilhoEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateTrilhoEffects_MetaData), NewProp_bReplicateTrilhoEffects_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateEnvironmentTransitions_SetBit(void* Obj)
{
	((FDynamicObjectSettings*)Obj)->bReplicateEnvironmentTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateEnvironmentTransitions = { "bReplicateEnvironmentTransitions", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectSettings), &Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateEnvironmentTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateEnvironmentTransitions_MetaData), NewProp_bReplicateEnvironmentTransitions_MetaData) };
void Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTerrainChanges_SetBit(void* Obj)
{
	((FDynamicObjectSettings*)Obj)->bReplicateTerrainChanges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTerrainChanges = { "bReplicateTerrainChanges", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDynamicObjectSettings), &Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTerrainChanges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateTerrainChanges_MetaData), NewProp_bReplicateTerrainChanges_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_FluxoPrismalUpdateFrequency = { "FluxoPrismalUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, FluxoPrismalUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FluxoPrismalUpdateFrequency_MetaData), NewProp_FluxoPrismalUpdateFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_TrilhoEffectRadius = { "TrilhoEffectRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, TrilhoEffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrilhoEffectRadius_MetaData), NewProp_TrilhoEffectRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_EnvironmentTransitionRadius = { "EnvironmentTransitionRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, EnvironmentTransitionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentTransitionRadius_MetaData), NewProp_EnvironmentTransitionRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_TerrainChangeRadius = { "TerrainChangeRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicObjectSettings, TerrainChangeRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainChangeRadius_MetaData), NewProp_TerrainChangeRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_MaxDynamicObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_UpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_PredictionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUseInterpolation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bUsePrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateFluxoPrismal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTrilhoEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateEnvironmentTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_bReplicateTerrainChanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_FluxoPrismalUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_TrilhoEffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_EnvironmentTransitionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewProp_TerrainChangeRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"DynamicObjectSettings",
	Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::PropPointers),
	sizeof(FDynamicObjectSettings),
	alignof(FDynamicObjectSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDynamicObjectSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FDynamicObjectSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDynamicObjectSettings.InnerSingleton, Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDynamicObjectSettings.InnerSingleton;
}
// ********** End ScriptStruct FDynamicObjectSettings **********************************************

// ********** Begin ScriptStruct FTelemetrySettings ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTelemetrySettings;
class UScriptStruct* FTelemetrySettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTelemetrySettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTelemetrySettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTelemetrySettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("TelemetrySettings"));
	}
	return Z_Registration_Info_UScriptStruct_FTelemetrySettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTelemetrySettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTelemetry_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectionInterval_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHistorySize_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSendToServer_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectReplicationStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectBandwidthStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectLatencyStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCollectAntiCheatStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TelemetryUpdateFrequency_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TelemetryHistorySize_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableTelemetry_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTelemetry;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollectionInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxHistorySize;
	static void NewProp_bSendToServer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSendToServer;
	static void NewProp_bCollectReplicationStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectReplicationStats;
	static void NewProp_bCollectBandwidthStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectBandwidthStats;
	static void NewProp_bCollectLatencyStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectLatencyStats;
	static void NewProp_bCollectAntiCheatStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCollectAntiCheatStats;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TelemetryUpdateFrequency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TelemetryHistorySize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTelemetrySettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bEnableTelemetry_SetBit(void* Obj)
{
	((FTelemetrySettings*)Obj)->bEnableTelemetry = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bEnableTelemetry = { "bEnableTelemetry", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTelemetrySettings), &Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bEnableTelemetry_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTelemetry_MetaData), NewProp_bEnableTelemetry_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_CollectionInterval = { "CollectionInterval", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTelemetrySettings, CollectionInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectionInterval_MetaData), NewProp_CollectionInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_MaxHistorySize = { "MaxHistorySize", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTelemetrySettings, MaxHistorySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHistorySize_MetaData), NewProp_MaxHistorySize_MetaData) };
void Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bSendToServer_SetBit(void* Obj)
{
	((FTelemetrySettings*)Obj)->bSendToServer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bSendToServer = { "bSendToServer", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTelemetrySettings), &Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bSendToServer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSendToServer_MetaData), NewProp_bSendToServer_MetaData) };
void Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectReplicationStats_SetBit(void* Obj)
{
	((FTelemetrySettings*)Obj)->bCollectReplicationStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectReplicationStats = { "bCollectReplicationStats", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTelemetrySettings), &Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectReplicationStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectReplicationStats_MetaData), NewProp_bCollectReplicationStats_MetaData) };
void Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectBandwidthStats_SetBit(void* Obj)
{
	((FTelemetrySettings*)Obj)->bCollectBandwidthStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectBandwidthStats = { "bCollectBandwidthStats", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTelemetrySettings), &Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectBandwidthStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectBandwidthStats_MetaData), NewProp_bCollectBandwidthStats_MetaData) };
void Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectLatencyStats_SetBit(void* Obj)
{
	((FTelemetrySettings*)Obj)->bCollectLatencyStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectLatencyStats = { "bCollectLatencyStats", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTelemetrySettings), &Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectLatencyStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectLatencyStats_MetaData), NewProp_bCollectLatencyStats_MetaData) };
void Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectAntiCheatStats_SetBit(void* Obj)
{
	((FTelemetrySettings*)Obj)->bCollectAntiCheatStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectAntiCheatStats = { "bCollectAntiCheatStats", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTelemetrySettings), &Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectAntiCheatStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCollectAntiCheatStats_MetaData), NewProp_bCollectAntiCheatStats_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_TelemetryUpdateFrequency = { "TelemetryUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTelemetrySettings, TelemetryUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TelemetryUpdateFrequency_MetaData), NewProp_TelemetryUpdateFrequency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_TelemetryHistorySize = { "TelemetryHistorySize", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTelemetrySettings, TelemetryHistorySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TelemetryHistorySize_MetaData), NewProp_TelemetryHistorySize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTelemetrySettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bEnableTelemetry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_CollectionInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_MaxHistorySize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bSendToServer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectReplicationStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectBandwidthStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectLatencyStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_bCollectAntiCheatStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_TelemetryUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewProp_TelemetryHistorySize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTelemetrySettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTelemetrySettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"TelemetrySettings",
	Z_Construct_UScriptStruct_FTelemetrySettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTelemetrySettings_Statics::PropPointers),
	sizeof(FTelemetrySettings),
	alignof(FTelemetrySettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTelemetrySettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTelemetrySettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTelemetrySettings()
{
	if (!Z_Registration_Info_UScriptStruct_FTelemetrySettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTelemetrySettings.InnerSingleton, Z_Construct_UScriptStruct_FTelemetrySettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTelemetrySettings.InnerSingleton;
}
// ********** End ScriptStruct FTelemetrySettings **************************************************

// ********** Begin ScriptStruct FAURACRONReplicationSettings **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings;
class UScriptStruct* FAURACRONReplicationSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONReplicationSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONReplicationSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilReplicationRate_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCompression_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDeltaCompression_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSigilsPerPlayer_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateAegisEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateRuinEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateVesperEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateEnvironmentStates_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateTrilhoStates_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateFluxoPrismalFlow_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilEffectReplicationPriority_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentReplicationPriority_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrilhoReplicationPriority_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FluxoPrismalReplicationPriority_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SigilReplicationRate;
	static void NewProp_bUseCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCompression;
	static void NewProp_bUseDeltaCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDeltaCompression;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSigilsPerPlayer;
	static void NewProp_bReplicateAegisEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateAegisEffects;
	static void NewProp_bReplicateRuinEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateRuinEffects;
	static void NewProp_bReplicateVesperEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateVesperEffects;
	static void NewProp_bReplicateEnvironmentStates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateEnvironmentStates;
	static void NewProp_bReplicateTrilhoStates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateTrilhoStates;
	static void NewProp_bReplicateFluxoPrismalFlow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateFluxoPrismalFlow;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SigilEffectReplicationPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentReplicationPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TrilhoReplicationPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FluxoPrismalReplicationPriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONReplicationSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_SigilReplicationRate = { "SigilReplicationRate", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONReplicationSettings, SigilReplicationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilReplicationRate_MetaData), NewProp_SigilReplicationRate_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseCompression_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bUseCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseCompression = { "bUseCompression", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCompression_MetaData), NewProp_bUseCompression_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseDeltaCompression_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bUseDeltaCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseDeltaCompression = { "bUseDeltaCompression", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseDeltaCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDeltaCompression_MetaData), NewProp_bUseDeltaCompression_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_MaxSigilsPerPlayer = { "MaxSigilsPerPlayer", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONReplicationSettings, MaxSigilsPerPlayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSigilsPerPlayer_MetaData), NewProp_MaxSigilsPerPlayer_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateAegisEffects_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bReplicateAegisEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateAegisEffects = { "bReplicateAegisEffects", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateAegisEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateAegisEffects_MetaData), NewProp_bReplicateAegisEffects_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateRuinEffects_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bReplicateRuinEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateRuinEffects = { "bReplicateRuinEffects", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateRuinEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateRuinEffects_MetaData), NewProp_bReplicateRuinEffects_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateVesperEffects_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bReplicateVesperEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateVesperEffects = { "bReplicateVesperEffects", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateVesperEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateVesperEffects_MetaData), NewProp_bReplicateVesperEffects_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateEnvironmentStates_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bReplicateEnvironmentStates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateEnvironmentStates = { "bReplicateEnvironmentStates", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateEnvironmentStates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateEnvironmentStates_MetaData), NewProp_bReplicateEnvironmentStates_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateTrilhoStates_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bReplicateTrilhoStates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateTrilhoStates = { "bReplicateTrilhoStates", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateTrilhoStates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateTrilhoStates_MetaData), NewProp_bReplicateTrilhoStates_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateFluxoPrismalFlow_SetBit(void* Obj)
{
	((FAURACRONReplicationSettings*)Obj)->bReplicateFluxoPrismalFlow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateFluxoPrismalFlow = { "bReplicateFluxoPrismalFlow", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONReplicationSettings), &Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateFluxoPrismalFlow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateFluxoPrismalFlow_MetaData), NewProp_bReplicateFluxoPrismalFlow_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_SigilEffectReplicationPriority = { "SigilEffectReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONReplicationSettings, SigilEffectReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilEffectReplicationPriority_MetaData), NewProp_SigilEffectReplicationPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_EnvironmentReplicationPriority = { "EnvironmentReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONReplicationSettings, EnvironmentReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentReplicationPriority_MetaData), NewProp_EnvironmentReplicationPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_TrilhoReplicationPriority = { "TrilhoReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONReplicationSettings, TrilhoReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrilhoReplicationPriority_MetaData), NewProp_TrilhoReplicationPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_FluxoPrismalReplicationPriority = { "FluxoPrismalReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONReplicationSettings, FluxoPrismalReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FluxoPrismalReplicationPriority_MetaData), NewProp_FluxoPrismalReplicationPriority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_SigilReplicationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bUseDeltaCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_MaxSigilsPerPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateAegisEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateRuinEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateVesperEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateEnvironmentStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateTrilhoStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_bReplicateFluxoPrismalFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_SigilEffectReplicationPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_EnvironmentReplicationPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_TrilhoReplicationPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewProp_FluxoPrismalReplicationPriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONReplicationSettings",
	Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::PropPointers),
	sizeof(FAURACRONReplicationSettings),
	alignof(FAURACRONReplicationSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONReplicationSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONReplicationSettings ****************************************

// ********** Begin ScriptStruct FSpatialHashGrid **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSpatialHashGrid;
class UScriptStruct* FSpatialHashGrid::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSpatialHashGrid.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSpatialHashGrid.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSpatialHashGrid, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SpatialHashGrid"));
	}
	return Z_Registration_Info_UScriptStruct_FSpatialHashGrid.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSpatialHashGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSize_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridWidth_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridHeight_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCells_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridCells_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GridCells;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSpatialHashGrid>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpatialHashGrid, GridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSize_MetaData), NewProp_GridSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridWidth = { "GridWidth", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpatialHashGrid, GridWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridWidth_MetaData), NewProp_GridWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridHeight = { "GridHeight", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpatialHashGrid, GridHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridHeight_MetaData), NewProp_GridHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridCells_Inner = { "GridCells", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridCells = { "GridCells", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpatialHashGrid, GridCells), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCells_MetaData), NewProp_GridCells_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridCells_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewProp_GridCells,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SpatialHashGrid",
	Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::PropPointers),
	sizeof(FSpatialHashGrid),
	alignof(FSpatialHashGrid),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSpatialHashGrid()
{
	if (!Z_Registration_Info_UScriptStruct_FSpatialHashGrid.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSpatialHashGrid.InnerSingleton, Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSpatialHashGrid.InnerSingleton;
}
// ********** End ScriptStruct FSpatialHashGrid ****************************************************

// ********** Begin Class USigilReplicationManager Function DebugForceFullReplication **************
struct Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "DebugForceFullReplication", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execDebugForceFullReplication)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugForceFullReplication();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function DebugForceFullReplication ****************

// ********** Begin Class USigilReplicationManager Function DebugPrintReplicationStats *************
struct Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "DebugPrintReplicationStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execDebugPrintReplicationStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugPrintReplicationStats();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function DebugPrintReplicationStats ***************

// ********** Begin Class USigilReplicationManager Function DebugSimulateNetworkLag ****************
struct Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics
{
	struct SigilReplicationManager_eventDebugSimulateNetworkLag_Parms
	{
		float LagSeconds;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LagSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::NewProp_LagSeconds = { "LagSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventDebugSimulateNetworkLag_Parms, LagSeconds), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::NewProp_LagSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "DebugSimulateNetworkLag", Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::SigilReplicationManager_eventDebugSimulateNetworkLag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::SigilReplicationManager_eventDebugSimulateNetworkLag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execDebugSimulateNetworkLag)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_LagSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugSimulateNetworkLag(Z_Param_LagSeconds);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function DebugSimulateNetworkLag ******************

// ********** Begin Class USigilReplicationManager Function EnableMOBAOptimizations ****************
struct Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics
{
	struct SigilReplicationManager_eventEnableMOBAOptimizations_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((SigilReplicationManager_eventEnableMOBAOptimizations_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilReplicationManager_eventEnableMOBAOptimizations_Parms), &Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "EnableMOBAOptimizations", Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::SigilReplicationManager_eventEnableMOBAOptimizations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::SigilReplicationManager_eventEnableMOBAOptimizations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execEnableMOBAOptimizations)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableMOBAOptimizations(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function EnableMOBAOptimizations ******************

// ********** Begin Class USigilReplicationManager Function GetPlayerActiveFusions *****************
struct Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics
{
	struct SigilReplicationManager_eventGetPlayerActiveFusions_Parms
	{
		int32 PlayerID;
		TArray<FSigilFusionReplicationData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerActiveFusions_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(0, nullptr) }; // 4074950016
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerActiveFusions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetPlayerActiveFusions", Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::SigilReplicationManager_eventGetPlayerActiveFusions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::SigilReplicationManager_eventGetPlayerActiveFusions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetPlayerActiveFusions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilFusionReplicationData>*)Z_Param__Result=P_THIS->GetPlayerActiveFusions(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetPlayerActiveFusions *******************

// ********** Begin Class USigilReplicationManager Function GetPlayerSigils ************************
struct Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics
{
	struct SigilReplicationManager_eventGetPlayerSigils_Parms
	{
		int32 PlayerID;
		TArray<FSigilReplicationData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de consulta\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de consulta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerSigils_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerSigils_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetPlayerSigils", Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::SigilReplicationManager_eventGetPlayerSigils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::SigilReplicationManager_eventGetPlayerSigils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetPlayerSigils)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilReplicationData>*)Z_Param__Result=P_THIS->GetPlayerSigils(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetPlayerSigils **************************

// ********** Begin Class USigilReplicationManager Function GetPlayerStats *************************
struct Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics
{
	struct SigilReplicationManager_eventGetPlayerStats_Parms
	{
		int32 PlayerID;
		FSigilReplicationStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerStats_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetPlayerStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(0, nullptr) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetPlayerStats", Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::SigilReplicationManager_eventGetPlayerStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::SigilReplicationManager_eventGetPlayerStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetPlayerStats)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilReplicationStats*)Z_Param__Result=P_THIS->GetPlayerStats(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetPlayerStats ***************************

// ********** Begin Class USigilReplicationManager Function GetRegisteredPlayers *******************
struct Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics
{
	struct SigilReplicationManager_eventGetRegisteredPlayers_Parms
	{
		TArray<int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventGetRegisteredPlayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "GetRegisteredPlayers", Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::SigilReplicationManager_eventGetRegisteredPlayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::SigilReplicationManager_eventGetRegisteredPlayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execGetRegisteredPlayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<int32>*)Z_Param__Result=P_THIS->GetRegisteredPlayers();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function GetRegisteredPlayers *********************

// ********** Begin Class USigilReplicationManager Function IsPlayerRegistered *********************
struct Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics
{
	struct SigilReplicationManager_eventIsPlayerRegistered_Parms
	{
		int32 PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventIsPlayerRegistered_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilReplicationManager_eventIsPlayerRegistered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilReplicationManager_eventIsPlayerRegistered_Parms), &Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "IsPlayerRegistered", Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::SigilReplicationManager_eventIsPlayerRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::SigilReplicationManager_eventIsPlayerRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execIsPlayerRegistered)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerRegistered(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function IsPlayerRegistered ***********************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyEquip *******************
struct SigilReplicationManager_eventMulticastNotifyEquip_Parms
{
	int32 PlayerID;
	FSigilReplicationData SigilData;
};
static FName NAME_USigilReplicationManager_MulticastNotifyEquip = FName(TEXT("MulticastNotifyEquip"));
void USigilReplicationManager::MulticastNotifyEquip(int32 PlayerID, FSigilReplicationData const& SigilData)
{
	SigilReplicationManager_eventMulticastNotifyEquip_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilData=SigilData;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyEquip);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multicast RPCs para notifica\xc3\xa7\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multicast RPCs para notifica\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyEquip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_SigilData = { "SigilData", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyEquip_Parms, SigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilData_MetaData), NewProp_SigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::NewProp_SigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyEquip", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyEquip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyEquip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyEquip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT(FSigilReplicationData,Z_Param_SigilData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyEquip_Implementation(Z_Param_PlayerID,Z_Param_SigilData);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyEquip *********************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyFusionComplete **********
struct SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms
{
	int32 PlayerID;
	FSigilReplicationData NewSigilData;
};
static FName NAME_USigilReplicationManager_MulticastNotifyFusionComplete = FName(TEXT("MulticastNotifyFusionComplete"));
void USigilReplicationManager::MulticastNotifyFusionComplete(int32 PlayerID, FSigilReplicationData const& NewSigilData)
{
	SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.NewSigilData=NewSigilData;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyFusionComplete);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSigilData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSigilData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_NewSigilData = { "NewSigilData", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms, NewSigilData), Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSigilData_MetaData), NewProp_NewSigilData_MetaData) }; // 1135390972
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::NewProp_NewSigilData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyFusionComplete", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyFusionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyFusionComplete)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT(FSigilReplicationData,Z_Param_NewSigilData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyFusionComplete_Implementation(Z_Param_PlayerID,Z_Param_NewSigilData);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyFusionComplete ************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyFusionStart *************
struct SigilReplicationManager_eventMulticastNotifyFusionStart_Parms
{
	int32 PlayerID;
	FSigilFusionReplicationData FusionData;
};
static FName NAME_USigilReplicationManager_MulticastNotifyFusionStart = FName(TEXT("MulticastNotifyFusionStart"));
void USigilReplicationManager::MulticastNotifyFusionStart(int32 PlayerID, FSigilFusionReplicationData const& FusionData)
{
	SigilReplicationManager_eventMulticastNotifyFusionStart_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.FusionData=FusionData;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyFusionStart);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_FusionData = { "FusionData", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms, FusionData), Z_Construct_UScriptStruct_FSigilFusionReplicationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionData_MetaData), NewProp_FusionData_MetaData) }; // 4074950016
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::NewProp_FusionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyFusionStart", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyFusionStart_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyFusionStart)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT(FSigilFusionReplicationData,Z_Param_FusionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyFusionStart_Implementation(Z_Param_PlayerID,Z_Param_FusionData);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyFusionStart ***************

// ********** Begin Class USigilReplicationManager Function MulticastNotifyUnequip *****************
struct SigilReplicationManager_eventMulticastNotifyUnequip_Parms
{
	int32 PlayerID;
	int32 SlotIndex;
};
static FName NAME_USigilReplicationManager_MulticastNotifyUnequip = FName(TEXT("MulticastNotifyUnequip"));
void USigilReplicationManager::MulticastNotifyUnequip(int32 PlayerID, int32 SlotIndex)
{
	SigilReplicationManager_eventMulticastNotifyUnequip_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_MulticastNotifyUnequip);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyUnequip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventMulticastNotifyUnequip_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "MulticastNotifyUnequip", Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::PropPointers), sizeof(SigilReplicationManager_eventMulticastNotifyUnequip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00024CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventMulticastNotifyUnequip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execMulticastNotifyUnequip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastNotifyUnequip_Implementation(Z_Param_PlayerID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function MulticastNotifyUnequip *******************

// ********** Begin Class USigilReplicationManager Function OnRep_ActiveFusions ********************
struct Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OnRep_ActiveFusions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOnRep_ActiveFusions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ActiveFusions();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OnRep_ActiveFusions **********************

// ********** Begin Class USigilReplicationManager Function OnRep_PlayerSigilData ******************
struct Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de callback de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de callback de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OnRep_PlayerSigilData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOnRep_PlayerSigilData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_PlayerSigilData();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OnRep_PlayerSigilData ********************

// ********** Begin Class USigilReplicationManager Function OnRep_PlayerSystemStats ****************
struct Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OnRep_PlayerSystemStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOnRep_PlayerSystemStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_PlayerSystemStats();
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OnRep_PlayerSystemStats ******************

// ********** Begin Class USigilReplicationManager Function OptimizeReplicationForDistance *********
struct Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics
{
	struct SigilReplicationManager_eventOptimizeReplicationForDistance_Parms
	{
		AActor* ViewerActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ViewerActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::NewProp_ViewerActor = { "ViewerActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventOptimizeReplicationForDistance_Parms, ViewerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::NewProp_ViewerActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "OptimizeReplicationForDistance", Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::SigilReplicationManager_eventOptimizeReplicationForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::SigilReplicationManager_eventOptimizeReplicationForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execOptimizeReplicationForDistance)
{
	P_GET_OBJECT(AActor,Z_Param_ViewerActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeReplicationForDistance(Z_Param_ViewerActor);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function OptimizeReplicationForDistance ***********

// ********** Begin Class USigilReplicationManager Function RegisterPlayer *************************
struct Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics
{
	struct SigilReplicationManager_eventRegisterPlayer_Parms
	{
		int32 PlayerID;
		USigilManagerComponent* SigilManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es principais de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es principais de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilManager_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventRegisterPlayer_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_SigilManager = { "SigilManager", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventRegisterPlayer_Parms, SigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilManager_MetaData), NewProp_SigilManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::NewProp_SigilManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "RegisterPlayer", Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::SigilReplicationManager_eventRegisterPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::SigilReplicationManager_eventRegisterPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execRegisterPlayer)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(USigilManagerComponent,Z_Param_SigilManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPlayer(Z_Param_PlayerID,Z_Param_SigilManager);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function RegisterPlayer ***************************

// ********** Begin Class USigilReplicationManager Function ReplicateFusionComplete ****************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics
{
	struct SigilReplicationManager_eventReplicateFusionComplete_Parms
	{
		int32 PlayerID;
		ASigilItem* OldSigil;
		ASigilItem* NewSigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OldSigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewSigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionComplete_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_OldSigil = { "OldSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionComplete_Parms, OldSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_NewSigil = { "NewSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionComplete_Parms, NewSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_OldSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::NewProp_NewSigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateFusionComplete", Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::SigilReplicationManager_eventReplicateFusionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::SigilReplicationManager_eventReplicateFusionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateFusionComplete)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(ASigilItem,Z_Param_OldSigil);
	P_GET_OBJECT(ASigilItem,Z_Param_NewSigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateFusionComplete(Z_Param_PlayerID,Z_Param_OldSigil,Z_Param_NewSigil);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateFusionComplete ******************

// ********** Begin Class USigilReplicationManager Function ReplicateFusionStart *******************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics
{
	struct SigilReplicationManager_eventReplicateFusionStart_Parms
	{
		int32 PlayerID;
		ASigilItem* Sigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionStart_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateFusionStart_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateFusionStart", Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::SigilReplicationManager_eventReplicateFusionStart_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::SigilReplicationManager_eventReplicateFusionStart_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateFusionStart)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateFusionStart(Z_Param_PlayerID,Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateFusionStart *********************

// ********** Begin Class USigilReplicationManager Function ReplicateSigilEquip ********************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics
{
	struct SigilReplicationManager_eventReplicateSigilEquip_Parms
	{
		int32 PlayerID;
		ASigilItem* Sigil;
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilEquip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilEquip_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilEquip_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateSigilEquip", Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::SigilReplicationManager_eventReplicateSigilEquip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::SigilReplicationManager_eventReplicateSigilEquip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateSigilEquip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateSigilEquip(Z_Param_PlayerID,Z_Param_Sigil,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateSigilEquip **********************

// ********** Begin Class USigilReplicationManager Function ReplicateSigilUnequip ******************
struct Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics
{
	struct SigilReplicationManager_eventReplicateSigilUnequip_Parms
	{
		int32 PlayerID;
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilUnequip_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventReplicateSigilUnequip_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ReplicateSigilUnequip", Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::SigilReplicationManager_eventReplicateSigilUnequip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::SigilReplicationManager_eventReplicateSigilUnequip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execReplicateSigilUnequip)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReplicateSigilUnequip(Z_Param_PlayerID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ReplicateSigilUnequip ********************

// ********** Begin Class USigilReplicationManager Function ServerEquipSigil ***********************
struct SigilReplicationManager_eventServerEquipSigil_Parms
{
	int32 PlayerID;
	int32 SigilID;
	int32 SlotIndex;
};
static FName NAME_USigilReplicationManager_ServerEquipSigil = FName(TEXT("ServerEquipSigil"));
void USigilReplicationManager::ServerEquipSigil(int32 PlayerID, int32 SigilID, int32 SlotIndex)
{
	SigilReplicationManager_eventServerEquipSigil_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilID=SigilID;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerEquipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// RPCs para comunica\xc3\xa7\xc3\xa3o cliente-servidor\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPCs para comunica\xc3\xa7\xc3\xa3o cliente-servidor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerEquipSigil_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerEquipSigil_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerEquipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SigilID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerEquipSigil", Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerEquipSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerEquipSigil_Validate(Z_Param_PlayerID,Z_Param_SigilID,Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerEquipSigil_Validate"));
		return;
	}
	P_THIS->ServerEquipSigil_Implementation(Z_Param_PlayerID,Z_Param_SigilID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerEquipSigil *************************

// ********** Begin Class USigilReplicationManager Function ServerForceFusion **********************
struct SigilReplicationManager_eventServerForceFusion_Parms
{
	int32 PlayerID;
	int32 SigilID;
};
static FName NAME_USigilReplicationManager_ServerForceFusion = FName(TEXT("ServerForceFusion"));
void USigilReplicationManager::ServerForceFusion(int32 PlayerID, int32 SigilID)
{
	SigilReplicationManager_eventServerForceFusion_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilID=SigilID;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerForceFusion);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerForceFusion_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerForceFusion_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::NewProp_SigilID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerForceFusion", Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerForceFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerForceFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerForceFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerForceFusion_Validate(Z_Param_PlayerID,Z_Param_SigilID))
	{
		RPC_ValidateFailed(TEXT("ServerForceFusion_Validate"));
		return;
	}
	P_THIS->ServerForceFusion_Implementation(Z_Param_PlayerID,Z_Param_SigilID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerForceFusion ************************

// ********** Begin Class USigilReplicationManager Function ServerReforge **************************
struct SigilReplicationManager_eventServerReforge_Parms
{
	int32 PlayerID;
};
static FName NAME_USigilReplicationManager_ServerReforge = FName(TEXT("ServerReforge"));
void USigilReplicationManager::ServerReforge(int32 PlayerID)
{
	SigilReplicationManager_eventServerReforge_Parms Parms;
	Parms.PlayerID=PlayerID;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerReforge);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerReforge_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerReforge", Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerReforge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerReforge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerReforge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerReforge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerReforge)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerReforge_Validate(Z_Param_PlayerID))
	{
		RPC_ValidateFailed(TEXT("ServerReforge_Validate"));
		return;
	}
	P_THIS->ServerReforge_Implementation(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerReforge ****************************

// ********** Begin Class USigilReplicationManager Function ServerStartFusion **********************
struct SigilReplicationManager_eventServerStartFusion_Parms
{
	int32 PlayerID;
	int32 SigilID;
};
static FName NAME_USigilReplicationManager_ServerStartFusion = FName(TEXT("ServerStartFusion"));
void USigilReplicationManager::ServerStartFusion(int32 PlayerID, int32 SigilID)
{
	SigilReplicationManager_eventServerStartFusion_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SigilID=SigilID;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerStartFusion);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerStartFusion_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_SigilID = { "SigilID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerStartFusion_Parms, SigilID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::NewProp_SigilID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerStartFusion", Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerStartFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerStartFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerStartFusion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SigilID);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerStartFusion_Validate(Z_Param_PlayerID,Z_Param_SigilID))
	{
		RPC_ValidateFailed(TEXT("ServerStartFusion_Validate"));
		return;
	}
	P_THIS->ServerStartFusion_Implementation(Z_Param_PlayerID,Z_Param_SigilID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerStartFusion ************************

// ********** Begin Class USigilReplicationManager Function ServerUnequipSigil *********************
struct SigilReplicationManager_eventServerUnequipSigil_Parms
{
	int32 PlayerID;
	int32 SlotIndex;
};
static FName NAME_USigilReplicationManager_ServerUnequipSigil = FName(TEXT("ServerUnequipSigil"));
void USigilReplicationManager::ServerUnequipSigil(int32 PlayerID, int32 SlotIndex)
{
	SigilReplicationManager_eventServerUnequipSigil_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilReplicationManager_ServerUnequipSigil);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerUnequipSigil_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventServerUnequipSigil_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "ServerUnequipSigil", Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::PropPointers), sizeof(SigilReplicationManager_eventServerUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilReplicationManager_eventServerUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execServerUnequipSigil)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerUnequipSigil_Validate(Z_Param_PlayerID,Z_Param_SlotIndex))
	{
		RPC_ValidateFailed(TEXT("ServerUnequipSigil_Validate"));
		return;
	}
	P_THIS->ServerUnequipSigil_Implementation(Z_Param_PlayerID,Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function ServerUnequipSigil ***********************

// ********** Begin Class USigilReplicationManager Function SetReplicationPriority *****************
struct Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics
{
	struct SigilReplicationManager_eventSetReplicationPriority_Parms
	{
		int32 PlayerID;
		float Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Otimiza\xc3\xa7\xc3\xb5""es de rede\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimiza\xc3\xa7\xc3\xb5""es de rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventSetReplicationPriority_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventSetReplicationPriority_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "SetReplicationPriority", Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::SigilReplicationManager_eventSetReplicationPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::SigilReplicationManager_eventSetReplicationPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execSetReplicationPriority)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetReplicationPriority(Z_Param_PlayerID,Z_Param_Priority);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function SetReplicationPriority *******************

// ********** Begin Class USigilReplicationManager Function UnregisterPlayer ***********************
struct Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics
{
	struct SigilReplicationManager_eventUnregisterPlayer_Parms
	{
		int32 PlayerID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventUnregisterPlayer_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "UnregisterPlayer", Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::SigilReplicationManager_eventUnregisterPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::SigilReplicationManager_eventUnregisterPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execUnregisterPlayer)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterPlayer(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function UnregisterPlayer *************************

// ********** Begin Class USigilReplicationManager Function UpdatePlayerStats **********************
struct Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics
{
	struct SigilReplicationManager_eventUpdatePlayerStats_Parms
	{
		int32 PlayerID;
		FSigilReplicationStats Stats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventUpdatePlayerStats_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilReplicationManager_eventUpdatePlayerStats_Parms, Stats), Z_Construct_UScriptStruct_FSigilReplicationStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 2192231724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilReplicationManager, nullptr, "UpdatePlayerStats", Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::SigilReplicationManager_eventUpdatePlayerStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::SigilReplicationManager_eventUpdatePlayerStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilReplicationManager::execUpdatePlayerStats)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FSigilReplicationStats,Z_Param_Out_Stats);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePlayerStats(Z_Param_PlayerID,Z_Param_Out_Stats);
	P_NATIVE_END;
}
// ********** End Class USigilReplicationManager Function UpdatePlayerStats ************************

// ********** Begin Class USigilReplicationManager *************************************************
void USigilReplicationManager::StaticRegisterNativesUSigilReplicationManager()
{
	UClass* Class = USigilReplicationManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DebugForceFullReplication", &USigilReplicationManager::execDebugForceFullReplication },
		{ "DebugPrintReplicationStats", &USigilReplicationManager::execDebugPrintReplicationStats },
		{ "DebugSimulateNetworkLag", &USigilReplicationManager::execDebugSimulateNetworkLag },
		{ "EnableMOBAOptimizations", &USigilReplicationManager::execEnableMOBAOptimizations },
		{ "GetPlayerActiveFusions", &USigilReplicationManager::execGetPlayerActiveFusions },
		{ "GetPlayerSigils", &USigilReplicationManager::execGetPlayerSigils },
		{ "GetPlayerStats", &USigilReplicationManager::execGetPlayerStats },
		{ "GetRegisteredPlayers", &USigilReplicationManager::execGetRegisteredPlayers },
		{ "IsPlayerRegistered", &USigilReplicationManager::execIsPlayerRegistered },
		{ "MulticastNotifyEquip", &USigilReplicationManager::execMulticastNotifyEquip },
		{ "MulticastNotifyFusionComplete", &USigilReplicationManager::execMulticastNotifyFusionComplete },
		{ "MulticastNotifyFusionStart", &USigilReplicationManager::execMulticastNotifyFusionStart },
		{ "MulticastNotifyUnequip", &USigilReplicationManager::execMulticastNotifyUnequip },
		{ "OnRep_ActiveFusions", &USigilReplicationManager::execOnRep_ActiveFusions },
		{ "OnRep_PlayerSigilData", &USigilReplicationManager::execOnRep_PlayerSigilData },
		{ "OnRep_PlayerSystemStats", &USigilReplicationManager::execOnRep_PlayerSystemStats },
		{ "OptimizeReplicationForDistance", &USigilReplicationManager::execOptimizeReplicationForDistance },
		{ "RegisterPlayer", &USigilReplicationManager::execRegisterPlayer },
		{ "ReplicateFusionComplete", &USigilReplicationManager::execReplicateFusionComplete },
		{ "ReplicateFusionStart", &USigilReplicationManager::execReplicateFusionStart },
		{ "ReplicateSigilEquip", &USigilReplicationManager::execReplicateSigilEquip },
		{ "ReplicateSigilUnequip", &USigilReplicationManager::execReplicateSigilUnequip },
		{ "ServerEquipSigil", &USigilReplicationManager::execServerEquipSigil },
		{ "ServerForceFusion", &USigilReplicationManager::execServerForceFusion },
		{ "ServerReforge", &USigilReplicationManager::execServerReforge },
		{ "ServerStartFusion", &USigilReplicationManager::execServerStartFusion },
		{ "ServerUnequipSigil", &USigilReplicationManager::execServerUnequipSigil },
		{ "SetReplicationPriority", &USigilReplicationManager::execSetReplicationPriority },
		{ "UnregisterPlayer", &USigilReplicationManager::execUnregisterPlayer },
		{ "UpdatePlayerStats", &USigilReplicationManager::execUpdatePlayerStats },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilReplicationManager;
UClass* USigilReplicationManager::GetPrivateStaticClass()
{
	using TClass = USigilReplicationManager;
	if (!Z_Registration_Info_UClass_USigilReplicationManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilReplicationManager"),
			Z_Registration_Info_UClass_USigilReplicationManager.InnerSingleton,
			StaticRegisterNativesUSigilReplicationManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilReplicationManager.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister()
{
	return USigilReplicationManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilReplicationManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador de replica\xc3\xa7\xc3\xa3o para o sistema de s\xc3\xadgilos em ambiente MOBA 5x5\n * Suporta at\xc3\xa9 10 jogadores simult\xc3\xa2neos com otimiza\xc3\xa7\xc3\xb5""es de rede\n */" },
#endif
		{ "IncludePath", "Multiplayer/SigilReplicationManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador de replica\xc3\xa7\xc3\xa3o para o sistema de s\xc3\xadgilos em ambiente MOBA 5x5\nSuporta at\xc3\xa9 10 jogadores simult\xc3\xa2neos com otimiza\xc3\xa7\xc3\xb5""es de rede" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationFrequency_MetaData[] = {
		{ "Category", "Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForMOBA_MetaData[] = {
		{ "Category", "Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Hz\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Hz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReplicationDistance_MetaData[] = {
		{ "Category", "Replication" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerSigilDataArray_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados replicados usando FFastArraySerializer\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados replicados usando FFastArraySerializer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerSystemStatsArray_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveFusionsArray_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilEquipped_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de replica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilUnequipped_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilFusionStarted_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilFusionCompleted_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilSystemStatsUpdated_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredManagers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados internos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados internos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerReplicationPriorities_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingReplications_MetaData[] = {
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalReplicationsSent_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estat\xc3\xadsticas de rede\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas de rede" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalReplicationsReceived_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageReplicationSize_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkBandwidthUsed_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableClientPrediction_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es avan\xc3\xa7""adas do UE 5.6\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es avan\xc3\xa7""adas do UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRollbackNetworking_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedDeltaCompression_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedInterestManagement_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAntiCheatValidation_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEOSIntegration_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNetworkTelemetry_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDynamicObjectReplication_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEnvironmentReplication_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTeamStateReplication_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClientPredictionSettings_MetaData[] = {
		{ "Category", "Client Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas\n" },
#endif
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RollbackSettings_MetaData[] = {
		{ "Category", "Rollback" },
		{ "ModuleRelativePath", "Public/Multiplayer/SigilReplicationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReplicationFrequency;
	static void NewProp_bOptimizeForMOBA_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForMOBA;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxReplicationDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerSigilDataArray;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerSystemStatsArray;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveFusionsArray;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilEquipped;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilUnequipped;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilFusionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilFusionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilSystemStatsUpdated;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RegisteredManagers_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RegisteredManagers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredManagers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerReplicationPriorities_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerReplicationPriorities_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerReplicationPriorities;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PendingReplications_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PendingReplications;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalReplicationsSent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalReplicationsReceived;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageReplicationSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkBandwidthUsed;
	static void NewProp_bEnableClientPrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableClientPrediction;
	static void NewProp_bEnableRollbackNetworking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRollbackNetworking;
	static void NewProp_bEnableAdvancedDeltaCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedDeltaCompression;
	static void NewProp_bEnableAdvancedInterestManagement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedInterestManagement;
	static void NewProp_bEnableAntiCheatValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAntiCheatValidation;
	static void NewProp_bEnableEOSIntegration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEOSIntegration;
	static void NewProp_bEnableNetworkTelemetry_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNetworkTelemetry;
	static void NewProp_bEnableDynamicObjectReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDynamicObjectReplication;
	static void NewProp_bEnableEnvironmentReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEnvironmentReplication;
	static void NewProp_bEnableTeamStateReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTeamStateReplication;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ClientPredictionSettings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RollbackSettings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilReplicationManager_DebugForceFullReplication, "DebugForceFullReplication" }, // 2980608089
		{ &Z_Construct_UFunction_USigilReplicationManager_DebugPrintReplicationStats, "DebugPrintReplicationStats" }, // 236304037
		{ &Z_Construct_UFunction_USigilReplicationManager_DebugSimulateNetworkLag, "DebugSimulateNetworkLag" }, // 1343363922
		{ &Z_Construct_UFunction_USigilReplicationManager_EnableMOBAOptimizations, "EnableMOBAOptimizations" }, // 2955673951
		{ &Z_Construct_UFunction_USigilReplicationManager_GetPlayerActiveFusions, "GetPlayerActiveFusions" }, // 548125989
		{ &Z_Construct_UFunction_USigilReplicationManager_GetPlayerSigils, "GetPlayerSigils" }, // 2447439334
		{ &Z_Construct_UFunction_USigilReplicationManager_GetPlayerStats, "GetPlayerStats" }, // 1709379116
		{ &Z_Construct_UFunction_USigilReplicationManager_GetRegisteredPlayers, "GetRegisteredPlayers" }, // 4086896788
		{ &Z_Construct_UFunction_USigilReplicationManager_IsPlayerRegistered, "IsPlayerRegistered" }, // 969777628
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyEquip, "MulticastNotifyEquip" }, // 984738762
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionComplete, "MulticastNotifyFusionComplete" }, // 3525060545
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyFusionStart, "MulticastNotifyFusionStart" }, // 3391583346
		{ &Z_Construct_UFunction_USigilReplicationManager_MulticastNotifyUnequip, "MulticastNotifyUnequip" }, // 129305045
		{ &Z_Construct_UFunction_USigilReplicationManager_OnRep_ActiveFusions, "OnRep_ActiveFusions" }, // 1289251947
		{ &Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSigilData, "OnRep_PlayerSigilData" }, // 3838066646
		{ &Z_Construct_UFunction_USigilReplicationManager_OnRep_PlayerSystemStats, "OnRep_PlayerSystemStats" }, // 3124009867
		{ &Z_Construct_UFunction_USigilReplicationManager_OptimizeReplicationForDistance, "OptimizeReplicationForDistance" }, // 2758966223
		{ &Z_Construct_UFunction_USigilReplicationManager_RegisterPlayer, "RegisterPlayer" }, // 414353555
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionComplete, "ReplicateFusionComplete" }, // 1898802866
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateFusionStart, "ReplicateFusionStart" }, // 2503874245
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilEquip, "ReplicateSigilEquip" }, // 436117084
		{ &Z_Construct_UFunction_USigilReplicationManager_ReplicateSigilUnequip, "ReplicateSigilUnequip" }, // 4098669783
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerEquipSigil, "ServerEquipSigil" }, // 969334517
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerForceFusion, "ServerForceFusion" }, // 1209176791
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerReforge, "ServerReforge" }, // 3936180501
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerStartFusion, "ServerStartFusion" }, // 2356067191
		{ &Z_Construct_UFunction_USigilReplicationManager_ServerUnequipSigil, "ServerUnequipSigil" }, // 3804628561
		{ &Z_Construct_UFunction_USigilReplicationManager_SetReplicationPriority, "SetReplicationPriority" }, // 3409658185
		{ &Z_Construct_UFunction_USigilReplicationManager_UnregisterPlayer, "UnregisterPlayer" }, // 1492838579
		{ &Z_Construct_UFunction_USigilReplicationManager_UpdatePlayerStats, "UpdatePlayerStats" }, // 3703334491
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilReplicationManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ReplicationFrequency = { "ReplicationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, ReplicationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationFrequency_MetaData), NewProp_ReplicationFrequency_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bOptimizeForMOBA = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA = { "bOptimizeForMOBA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForMOBA_MetaData), NewProp_bOptimizeForMOBA_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxReplicationDistance = { "MaxReplicationDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, MaxReplicationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReplicationDistance_MetaData), NewProp_MaxReplicationDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSigilDataArray = { "PlayerSigilDataArray", nullptr, (EPropertyFlags)0x0010000000000020, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PlayerSigilDataArray), Z_Construct_UScriptStruct_FSigilPlayerDataArray, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerSigilDataArray_MetaData), NewProp_PlayerSigilDataArray_MetaData) }; // 180768312
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSystemStatsArray = { "PlayerSystemStatsArray", nullptr, (EPropertyFlags)0x0010000000000020, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PlayerSystemStatsArray), Z_Construct_UScriptStruct_FSigilPlayerStatsArray, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerSystemStatsArray_MetaData), NewProp_PlayerSystemStatsArray_MetaData) }; // 1239708595
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ActiveFusionsArray = { "ActiveFusionsArray", nullptr, (EPropertyFlags)0x0010000000000020, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, ActiveFusionsArray), Z_Construct_UScriptStruct_FSigilActiveFusionsArray, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveFusionsArray_MetaData), NewProp_ActiveFusionsArray_MetaData) }; // 1547376004
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilEquipped = { "OnSigilEquipped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilEquipped), Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilEquipped_MetaData), NewProp_OnSigilEquipped_MetaData) }; // 2670181724
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilUnequipped = { "OnSigilUnequipped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilUnequipped), Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilUnequipped_MetaData), NewProp_OnSigilUnequipped_MetaData) }; // 3950600408
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionStarted = { "OnSigilFusionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilFusionStarted), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilFusionStarted_MetaData), NewProp_OnSigilFusionStarted_MetaData) }; // 3381686676
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionCompleted = { "OnSigilFusionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilFusionCompleted), Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilFusionCompleted_MetaData), NewProp_OnSigilFusionCompleted_MetaData) }; // 2230623330
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilSystemStatsUpdated = { "OnSigilSystemStatsUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, OnSigilSystemStatsUpdated), Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilSystemStatsUpdated_MetaData), NewProp_OnSigilSystemStatsUpdated_MetaData) }; // 3107523442
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_ValueProp = { "RegisteredManagers", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_Key_KeyProp = { "RegisteredManagers_Key", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers = { "RegisteredManagers", nullptr, (EPropertyFlags)0x0020088000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, RegisteredManagers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredManagers_MetaData), NewProp_RegisteredManagers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_ValueProp = { "PlayerReplicationPriorities", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_Key_KeyProp = { "PlayerReplicationPriorities_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities = { "PlayerReplicationPriorities", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PlayerReplicationPriorities), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerReplicationPriorities_MetaData), NewProp_PlayerReplicationPriorities_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications_Inner = { "PendingReplications", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilReplicationData, METADATA_PARAMS(0, nullptr) }; // 1135390972
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications = { "PendingReplications", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, PendingReplications), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingReplications_MetaData), NewProp_PendingReplications_MetaData) }; // 1135390972
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsSent = { "TotalReplicationsSent", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, TotalReplicationsSent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalReplicationsSent_MetaData), NewProp_TotalReplicationsSent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsReceived = { "TotalReplicationsReceived", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, TotalReplicationsReceived), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalReplicationsReceived_MetaData), NewProp_TotalReplicationsReceived_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_AverageReplicationSize = { "AverageReplicationSize", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, AverageReplicationSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageReplicationSize_MetaData), NewProp_AverageReplicationSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_NetworkBandwidthUsed = { "NetworkBandwidthUsed", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, NetworkBandwidthUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkBandwidthUsed_MetaData), NewProp_NetworkBandwidthUsed_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableClientPrediction_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableClientPrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableClientPrediction = { "bEnableClientPrediction", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableClientPrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableClientPrediction_MetaData), NewProp_bEnableClientPrediction_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableRollbackNetworking_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableRollbackNetworking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableRollbackNetworking = { "bEnableRollbackNetworking", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableRollbackNetworking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRollbackNetworking_MetaData), NewProp_bEnableRollbackNetworking_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedDeltaCompression_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableAdvancedDeltaCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedDeltaCompression = { "bEnableAdvancedDeltaCompression", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedDeltaCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedDeltaCompression_MetaData), NewProp_bEnableAdvancedDeltaCompression_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedInterestManagement_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableAdvancedInterestManagement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedInterestManagement = { "bEnableAdvancedInterestManagement", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedInterestManagement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedInterestManagement_MetaData), NewProp_bEnableAdvancedInterestManagement_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAntiCheatValidation_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableAntiCheatValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAntiCheatValidation = { "bEnableAntiCheatValidation", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAntiCheatValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAntiCheatValidation_MetaData), NewProp_bEnableAntiCheatValidation_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEOSIntegration_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableEOSIntegration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEOSIntegration = { "bEnableEOSIntegration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEOSIntegration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEOSIntegration_MetaData), NewProp_bEnableEOSIntegration_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableNetworkTelemetry_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableNetworkTelemetry = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableNetworkTelemetry = { "bEnableNetworkTelemetry", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableNetworkTelemetry_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNetworkTelemetry_MetaData), NewProp_bEnableNetworkTelemetry_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableDynamicObjectReplication_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableDynamicObjectReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableDynamicObjectReplication = { "bEnableDynamicObjectReplication", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableDynamicObjectReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDynamicObjectReplication_MetaData), NewProp_bEnableDynamicObjectReplication_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEnvironmentReplication_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableEnvironmentReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEnvironmentReplication = { "bEnableEnvironmentReplication", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEnvironmentReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEnvironmentReplication_MetaData), NewProp_bEnableEnvironmentReplication_MetaData) };
void Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableTeamStateReplication_SetBit(void* Obj)
{
	((USigilReplicationManager*)Obj)->bEnableTeamStateReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableTeamStateReplication = { "bEnableTeamStateReplication", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilReplicationManager), &Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableTeamStateReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTeamStateReplication_MetaData), NewProp_bEnableTeamStateReplication_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ClientPredictionSettings = { "ClientPredictionSettings", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, ClientPredictionSettings), Z_Construct_UScriptStruct_FClientPredictionSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClientPredictionSettings_MetaData), NewProp_ClientPredictionSettings_MetaData) }; // 1311621667
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RollbackSettings = { "RollbackSettings", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReplicationManager, RollbackSettings), Z_Construct_UScriptStruct_FRollbackSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RollbackSettings_MetaData), NewProp_RollbackSettings_MetaData) }; // 1921083708
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ReplicationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bOptimizeForMOBA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_MaxReplicationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSigilDataArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerSystemStatsArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ActiveFusionsArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilEquipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilUnequipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilFusionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_OnSigilSystemStatsUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RegisteredManagers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PlayerReplicationPriorities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_PendingReplications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsSent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_TotalReplicationsReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_AverageReplicationSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_NetworkBandwidthUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableClientPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableRollbackNetworking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedDeltaCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAdvancedInterestManagement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableAntiCheatValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEOSIntegration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableNetworkTelemetry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableDynamicObjectReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableEnvironmentReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_bEnableTeamStateReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_ClientPredictionSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReplicationManager_Statics::NewProp_RollbackSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilReplicationManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilReplicationManager_Statics::ClassParams = {
	&USigilReplicationManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReplicationManager_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilReplicationManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilReplicationManager()
{
	if (!Z_Registration_Info_UClass_USigilReplicationManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilReplicationManager.OuterSingleton, Z_Construct_UClass_USigilReplicationManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilReplicationManager.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void USigilReplicationManager::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_PlayerSigilDataArray(TEXT("PlayerSigilDataArray"));
	static FName Name_PlayerSystemStatsArray(TEXT("PlayerSystemStatsArray"));
	static FName Name_ActiveFusionsArray(TEXT("ActiveFusionsArray"));
	const bool bIsValid = true
		&& Name_PlayerSigilDataArray == ClassReps[(int32)ENetFields_Private::PlayerSigilDataArray].Property->GetFName()
		&& Name_PlayerSystemStatsArray == ClassReps[(int32)ENetFields_Private::PlayerSystemStatsArray].Property->GetFName()
		&& Name_ActiveFusionsArray == ClassReps[(int32)ENetFields_Private::ActiveFusionsArray].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in USigilReplicationManager"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilReplicationManager);
USigilReplicationManager::~USigilReplicationManager() {}
// ********** End Class USigilReplicationManager ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPredictionType_StaticEnum, TEXT("EPredictionType"), &Z_Registration_Info_UEnum_EPredictionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3939455507U) },
		{ ERollbackType_StaticEnum, TEXT("ERollbackType"), &Z_Registration_Info_UEnum_ERollbackType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2703372478U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilReplicationData::StaticStruct, Z_Construct_UScriptStruct_FSigilReplicationData_Statics::NewStructOps, TEXT("SigilReplicationData"), &Z_Registration_Info_UScriptStruct_FSigilReplicationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilReplicationData), 1135390972U) },
		{ FSigilReplicationStats::StaticStruct, Z_Construct_UScriptStruct_FSigilReplicationStats_Statics::NewStructOps, TEXT("SigilReplicationStats"), &Z_Registration_Info_UScriptStruct_FSigilReplicationStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilReplicationStats), 2192231724U) },
		{ FSigilFusionReplicationData::StaticStruct, Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics::NewStructOps, TEXT("SigilFusionReplicationData"), &Z_Registration_Info_UScriptStruct_FSigilFusionReplicationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFusionReplicationData), 4074950016U) },
		{ FSigilPlayerDataEntry::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics::NewStructOps, TEXT("SigilPlayerDataEntry"), &Z_Registration_Info_UScriptStruct_FSigilPlayerDataEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerDataEntry), 2914415567U) },
		{ FSigilPlayerDataArray::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics::NewStructOps, TEXT("SigilPlayerDataArray"), &Z_Registration_Info_UScriptStruct_FSigilPlayerDataArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerDataArray), 180768312U) },
		{ FSigilPlayerStatsEntry::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics::NewStructOps, TEXT("SigilPlayerStatsEntry"), &Z_Registration_Info_UScriptStruct_FSigilPlayerStatsEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerStatsEntry), 3618960821U) },
		{ FSigilPlayerStatsArray::StaticStruct, Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics::NewStructOps, TEXT("SigilPlayerStatsArray"), &Z_Registration_Info_UScriptStruct_FSigilPlayerStatsArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilPlayerStatsArray), 1239708595U) },
		{ FSigilActiveFusionsEntry::StaticStruct, Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics::NewStructOps, TEXT("SigilActiveFusionsEntry"), &Z_Registration_Info_UScriptStruct_FSigilActiveFusionsEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilActiveFusionsEntry), 2854786014U) },
		{ FSigilActiveFusionsArray::StaticStruct, Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics::NewStructOps, TEXT("SigilActiveFusionsArray"), &Z_Registration_Info_UScriptStruct_FSigilActiveFusionsArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilActiveFusionsArray), 1547376004U) },
		{ FPredictionEntry::StaticStruct, Z_Construct_UScriptStruct_FPredictionEntry_Statics::NewStructOps, TEXT("PredictionEntry"), &Z_Registration_Info_UScriptStruct_FPredictionEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPredictionEntry), 3000424761U) },
		{ FRollbackEntry::StaticStruct, Z_Construct_UScriptStruct_FRollbackEntry_Statics::NewStructOps, TEXT("RollbackEntry"), &Z_Registration_Info_UScriptStruct_FRollbackEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRollbackEntry), 3237839783U) },
		{ FClientPredictionSettings::StaticStruct, Z_Construct_UScriptStruct_FClientPredictionSettings_Statics::NewStructOps, TEXT("ClientPredictionSettings"), &Z_Registration_Info_UScriptStruct_FClientPredictionSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClientPredictionSettings), 1311621667U) },
		{ FRollbackSettings::StaticStruct, Z_Construct_UScriptStruct_FRollbackSettings_Statics::NewStructOps, TEXT("RollbackSettings"), &Z_Registration_Info_UScriptStruct_FRollbackSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRollbackSettings), 1921083708U) },
		{ FPlayerValidationData::StaticStruct, Z_Construct_UScriptStruct_FPlayerValidationData_Statics::NewStructOps, TEXT("PlayerValidationData"), &Z_Registration_Info_UScriptStruct_FPlayerValidationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerValidationData), 1347618915U) },
		{ FDynamicObjectState::StaticStruct, Z_Construct_UScriptStruct_FDynamicObjectState_Statics::NewStructOps, TEXT("DynamicObjectState"), &Z_Registration_Info_UScriptStruct_FDynamicObjectState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDynamicObjectState), 3338692335U) },
		{ FEnvironmentState::StaticStruct, Z_Construct_UScriptStruct_FEnvironmentState_Statics::NewStructOps, TEXT("EnvironmentState"), &Z_Registration_Info_UScriptStruct_FEnvironmentState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEnvironmentState), 1034763423U) },
		{ FTeamState::StaticStruct, Z_Construct_UScriptStruct_FTeamState_Statics::NewStructOps, TEXT("TeamState"), &Z_Registration_Info_UScriptStruct_FTeamState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTeamState), 2105655454U) },
		{ FAntiCheatEvent::StaticStruct, Z_Construct_UScriptStruct_FAntiCheatEvent_Statics::NewStructOps, TEXT("AntiCheatEvent"), &Z_Registration_Info_UScriptStruct_FAntiCheatEvent, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAntiCheatEvent), 2490044360U) },
		{ FRollbackState::StaticStruct, Z_Construct_UScriptStruct_FRollbackState_Statics::NewStructOps, TEXT("RollbackState"), &Z_Registration_Info_UScriptStruct_FRollbackState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRollbackState), 2737844484U) },
		{ FPredictionSettings::StaticStruct, Z_Construct_UScriptStruct_FPredictionSettings_Statics::NewStructOps, TEXT("PredictionSettings"), &Z_Registration_Info_UScriptStruct_FPredictionSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPredictionSettings), 3364867041U) },
		{ FRollbackStateArray::StaticStruct, Z_Construct_UScriptStruct_FRollbackStateArray_Statics::NewStructOps, TEXT("RollbackStateArray"), &Z_Registration_Info_UScriptStruct_FRollbackStateArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRollbackStateArray), 4191613111U) },
		{ FClientPredictionData::StaticStruct, Z_Construct_UScriptStruct_FClientPredictionData_Statics::NewStructOps, TEXT("ClientPredictionData"), &Z_Registration_Info_UScriptStruct_FClientPredictionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FClientPredictionData), 1306842652U) },
		{ FReplicationMetrics::StaticStruct, Z_Construct_UScriptStruct_FReplicationMetrics_Statics::NewStructOps, TEXT("ReplicationMetrics"), &Z_Registration_Info_UScriptStruct_FReplicationMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FReplicationMetrics), 3275842509U) },
		{ FAdvancedInterestSettings::StaticStruct, Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics::NewStructOps, TEXT("AdvancedInterestSettings"), &Z_Registration_Info_UScriptStruct_FAdvancedInterestSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAdvancedInterestSettings), 3515761136U) },
		{ FAntiCheatSettings::StaticStruct, Z_Construct_UScriptStruct_FAntiCheatSettings_Statics::NewStructOps, TEXT("AntiCheatSettings"), &Z_Registration_Info_UScriptStruct_FAntiCheatSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAntiCheatSettings), 2650344800U) },
		{ FDynamicObjectSettings::StaticStruct, Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics::NewStructOps, TEXT("DynamicObjectSettings"), &Z_Registration_Info_UScriptStruct_FDynamicObjectSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDynamicObjectSettings), 1557764310U) },
		{ FTelemetrySettings::StaticStruct, Z_Construct_UScriptStruct_FTelemetrySettings_Statics::NewStructOps, TEXT("TelemetrySettings"), &Z_Registration_Info_UScriptStruct_FTelemetrySettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTelemetrySettings), 1156304098U) },
		{ FAURACRONReplicationSettings::StaticStruct, Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics::NewStructOps, TEXT("AURACRONReplicationSettings"), &Z_Registration_Info_UScriptStruct_FAURACRONReplicationSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONReplicationSettings), 3104032931U) },
		{ FSpatialHashGrid::StaticStruct, Z_Construct_UScriptStruct_FSpatialHashGrid_Statics::NewStructOps, TEXT("SpatialHashGrid"), &Z_Registration_Info_UScriptStruct_FSpatialHashGrid, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSpatialHashGrid), 3427278659U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilReplicationManager, USigilReplicationManager::StaticClass, TEXT("USigilReplicationManager"), &Z_Registration_Info_UClass_USigilReplicationManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilReplicationManager), 2594905758U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_2904874027(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
