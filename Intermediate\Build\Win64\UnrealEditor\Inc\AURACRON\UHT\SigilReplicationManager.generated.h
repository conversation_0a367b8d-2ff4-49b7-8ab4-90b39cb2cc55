// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Multiplayer/SigilReplicationManager.h"

#ifdef AURACRON_SigilReplicationManager_generated_h
#error "SigilReplicationManager.generated.h already included, missing '#pragma once' in SigilReplicationManager.h"
#endif
#define AURACRON_SigilReplicationManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ASigilItem;
class USigilManagerComponent;
struct FSigilFusionReplicationData;
struct FSigilReplicationData;
struct FSigilReplicationStats;

// ********** Begin ScriptStruct FSigilReplicationData *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_29_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilReplicationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilReplicationData;
// ********** End ScriptStruct FSigilReplicationData ***********************************************

// ********** Begin ScriptStruct FSigilReplicationStats ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_80_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilReplicationStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilReplicationStats;
// ********** End ScriptStruct FSigilReplicationStats **********************************************

// ********** Begin ScriptStruct FSigilFusionReplicationData ***************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_111_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilFusionReplicationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilFusionReplicationData;
// ********** End ScriptStruct FSigilFusionReplicationData *****************************************

// ********** Begin ScriptStruct FSigilPlayerDataEntry *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_142_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerDataEntry_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializerItem Super;


struct FSigilPlayerDataEntry;
// ********** End ScriptStruct FSigilPlayerDataEntry ***********************************************

// ********** Begin ScriptStruct FSigilPlayerDataArray *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_169_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerDataArray_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializer Super; \
	UE_NET_DECLARE_FASTARRAY(FSigilPlayerDataArray, Items, );


struct FSigilPlayerDataArray;
// ********** End ScriptStruct FSigilPlayerDataArray ***********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsEntry ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_230_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerStatsEntry_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializerItem Super;


struct FSigilPlayerStatsEntry;
// ********** End ScriptStruct FSigilPlayerStatsEntry **********************************************

// ********** Begin ScriptStruct FSigilPlayerStatsArray ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_257_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilPlayerStatsArray_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializer Super; \
	UE_NET_DECLARE_FASTARRAY(FSigilPlayerStatsArray, Items, );


struct FSigilPlayerStatsArray;
// ********** End ScriptStruct FSigilPlayerStatsArray **********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsEntry ******************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_318_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilActiveFusionsEntry_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializerItem Super;


struct FSigilActiveFusionsEntry;
// ********** End ScriptStruct FSigilActiveFusionsEntry ********************************************

// ********** Begin ScriptStruct FSigilActiveFusionsArray ******************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_354_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilActiveFusionsArray_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FFastArraySerializer Super; \
	UE_NET_DECLARE_FASTARRAY(FSigilActiveFusionsArray, Items, );


struct FSigilActiveFusionsArray;
// ********** End ScriptStruct FSigilActiveFusionsArray ********************************************

// ********** Begin ScriptStruct FPredictionEntry **************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_422_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPredictionEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPredictionEntry;
// ********** End ScriptStruct FPredictionEntry ****************************************************

// ********** Begin ScriptStruct FRollbackEntry ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_449_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRollbackEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRollbackEntry;
// ********** End ScriptStruct FRollbackEntry ******************************************************

// ********** Begin ScriptStruct FClientPredictionSettings *****************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_480_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClientPredictionSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClientPredictionSettings;
// ********** End ScriptStruct FClientPredictionSettings *******************************************

// ********** Begin ScriptStruct FRollbackSettings *************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_505_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRollbackSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRollbackSettings;
// ********** End ScriptStruct FRollbackSettings ***************************************************

// ********** Begin Delegate FOnSigilEquipped ******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_521_DELEGATE \
AURACRON_API void FOnSigilEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEquipped, int32 PlayerID, FSigilReplicationData const& SigilData);


// ********** End Delegate FOnSigilEquipped ********************************************************

// ********** Begin Delegate FOnSigilUnequipped ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_522_DELEGATE \
AURACRON_API void FOnSigilUnequipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilUnequipped, int32 PlayerID, int32 SlotIndex);


// ********** End Delegate FOnSigilUnequipped ******************************************************

// ********** Begin Delegate FOnSigilReplicationFusionStarted **************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_523_DELEGATE \
AURACRON_API void FOnSigilReplicationFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionStarted, int32 PlayerID, FSigilFusionReplicationData const& FusionData);


// ********** End Delegate FOnSigilReplicationFusionStarted ****************************************

// ********** Begin Delegate FOnSigilReplicationFusionCompleted ************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_524_DELEGATE \
AURACRON_API void FOnSigilReplicationFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilReplicationFusionCompleted, int32 PlayerID, FSigilReplicationData const& NewSigilData);


// ********** End Delegate FOnSigilReplicationFusionCompleted **************************************

// ********** Begin Delegate FOnSigilSystemStatsUpdated ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_525_DELEGATE \
AURACRON_API void FOnSigilSystemStatsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilSystemStatsUpdated, FSigilReplicationStats const& Stats);


// ********** End Delegate FOnSigilSystemStatsUpdated **********************************************

// ********** Begin ScriptStruct FPlayerValidationData *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_531_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerValidationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPlayerValidationData;
// ********** End ScriptStruct FPlayerValidationData ***********************************************

// ********** Begin ScriptStruct FDynamicObjectState ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_561_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDynamicObjectState_Statics; \
	static class UScriptStruct* StaticStruct();


struct FDynamicObjectState;
// ********** End ScriptStruct FDynamicObjectState *************************************************

// ********** Begin ScriptStruct FEnvironmentState *************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_595_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEnvironmentState_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEnvironmentState;
// ********** End ScriptStruct FEnvironmentState ***************************************************

// ********** Begin ScriptStruct FTeamState ********************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_627_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTeamState_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTeamState;
// ********** End ScriptStruct FTeamState **********************************************************

// ********** Begin ScriptStruct FAntiCheatEvent ***************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_660_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAntiCheatEvent_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAntiCheatEvent;
// ********** End ScriptStruct FAntiCheatEvent *****************************************************

// ********** Begin ScriptStruct FRollbackState ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_691_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRollbackState_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRollbackState;
// ********** End ScriptStruct FRollbackState ******************************************************

// ********** Begin ScriptStruct FPredictionSettings ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_717_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPredictionSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPredictionSettings;
// ********** End ScriptStruct FPredictionSettings *************************************************

// ********** Begin ScriptStruct FRollbackStateArray ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_740_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRollbackStateArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRollbackStateArray;
// ********** End ScriptStruct FRollbackStateArray *************************************************

// ********** Begin ScriptStruct FClientPredictionData *********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_755_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClientPredictionData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClientPredictionData;
// ********** End ScriptStruct FClientPredictionData ***********************************************

// ********** Begin ScriptStruct FReplicationMetrics ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_775_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FReplicationMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FReplicationMetrics;
// ********** End ScriptStruct FReplicationMetrics *************************************************

// ********** Begin ScriptStruct FAdvancedInterestSettings *****************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_826_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAdvancedInterestSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAdvancedInterestSettings;
// ********** End ScriptStruct FAdvancedInterestSettings *******************************************

// ********** Begin ScriptStruct FAntiCheatSettings ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_864_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAntiCheatSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAntiCheatSettings;
// ********** End ScriptStruct FAntiCheatSettings **************************************************

// ********** Begin ScriptStruct FDynamicObjectSettings ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_918_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDynamicObjectSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FDynamicObjectSettings;
// ********** End ScriptStruct FDynamicObjectSettings **********************************************

// ********** Begin ScriptStruct FTelemetrySettings ************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_980_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTelemetrySettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTelemetrySettings;
// ********** End ScriptStruct FTelemetrySettings **************************************************

// ********** Begin ScriptStruct FAURACRONReplicationSettings **************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1030_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONReplicationSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONReplicationSettings;
// ********** End ScriptStruct FAURACRONReplicationSettings ****************************************

// ********** Begin ScriptStruct FSpatialHashGrid **************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1096_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSpatialHashGrid_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSpatialHashGrid;
// ********** End ScriptStruct FSpatialHashGrid ****************************************************

// ********** Begin Class USigilReplicationManager *************************************************
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void MulticastNotifyFusionComplete_Implementation(int32 PlayerID, FSigilReplicationData const& NewSigilData); \
	virtual void MulticastNotifyFusionStart_Implementation(int32 PlayerID, FSigilFusionReplicationData const& FusionData); \
	virtual void MulticastNotifyUnequip_Implementation(int32 PlayerID, int32 SlotIndex); \
	virtual void MulticastNotifyEquip_Implementation(int32 PlayerID, FSigilReplicationData const& SigilData); \
	virtual bool ServerReforge_Validate(int32 ); \
	virtual void ServerReforge_Implementation(int32 PlayerID); \
	virtual bool ServerForceFusion_Validate(int32 , int32 ); \
	virtual void ServerForceFusion_Implementation(int32 PlayerID, int32 SigilID); \
	virtual bool ServerStartFusion_Validate(int32 , int32 ); \
	virtual void ServerStartFusion_Implementation(int32 PlayerID, int32 SigilID); \
	virtual bool ServerUnequipSigil_Validate(int32 , int32 ); \
	virtual void ServerUnequipSigil_Implementation(int32 PlayerID, int32 SlotIndex); \
	virtual bool ServerEquipSigil_Validate(int32 , int32 , int32 ); \
	virtual void ServerEquipSigil_Implementation(int32 PlayerID, int32 SigilID, int32 SlotIndex); \
	DECLARE_FUNCTION(execOnRep_ActiveFusions); \
	DECLARE_FUNCTION(execOnRep_PlayerSystemStats); \
	DECLARE_FUNCTION(execOnRep_PlayerSigilData); \
	DECLARE_FUNCTION(execDebugForceFullReplication); \
	DECLARE_FUNCTION(execDebugSimulateNetworkLag); \
	DECLARE_FUNCTION(execDebugPrintReplicationStats); \
	DECLARE_FUNCTION(execMulticastNotifyFusionComplete); \
	DECLARE_FUNCTION(execMulticastNotifyFusionStart); \
	DECLARE_FUNCTION(execMulticastNotifyUnequip); \
	DECLARE_FUNCTION(execMulticastNotifyEquip); \
	DECLARE_FUNCTION(execServerReforge); \
	DECLARE_FUNCTION(execServerForceFusion); \
	DECLARE_FUNCTION(execServerStartFusion); \
	DECLARE_FUNCTION(execServerUnequipSigil); \
	DECLARE_FUNCTION(execServerEquipSigil); \
	DECLARE_FUNCTION(execEnableMOBAOptimizations); \
	DECLARE_FUNCTION(execOptimizeReplicationForDistance); \
	DECLARE_FUNCTION(execSetReplicationPriority); \
	DECLARE_FUNCTION(execGetRegisteredPlayers); \
	DECLARE_FUNCTION(execIsPlayerRegistered); \
	DECLARE_FUNCTION(execGetPlayerActiveFusions); \
	DECLARE_FUNCTION(execGetPlayerStats); \
	DECLARE_FUNCTION(execGetPlayerSigils); \
	DECLARE_FUNCTION(execUpdatePlayerStats); \
	DECLARE_FUNCTION(execReplicateFusionComplete); \
	DECLARE_FUNCTION(execReplicateFusionStart); \
	DECLARE_FUNCTION(execReplicateSigilUnequip); \
	DECLARE_FUNCTION(execReplicateSigilEquip); \
	DECLARE_FUNCTION(execUnregisterPlayer); \
	DECLARE_FUNCTION(execRegisterPlayer);


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilReplicationManager(); \
	friend struct Z_Construct_UClass_USigilReplicationManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilReplicationManager_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilReplicationManager, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilReplicationManager_NoRegister) \
	DECLARE_SERIALIZER(USigilReplicationManager) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		PlayerSigilDataArray=NETFIELD_REP_START, \
		PlayerSystemStatsArray, \
		ActiveFusionsArray, \
		NETFIELD_REP_END=ActiveFusionsArray	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilReplicationManager(USigilReplicationManager&&) = delete; \
	USigilReplicationManager(const USigilReplicationManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilReplicationManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilReplicationManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilReplicationManager) \
	NO_API virtual ~USigilReplicationManager();


#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1129_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h_1132_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilReplicationManager;

// ********** End Class USigilReplicationManager ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Multiplayer_SigilReplicationManager_h

// ********** Begin Enum EPredictionType ***********************************************************
#define FOREACH_ENUM_EPREDICTIONTYPE(op) \
	op(EPredictionType::Movement) \
	op(EPredictionType::Ability) \
	op(EPredictionType::Sigil) 

enum class EPredictionType : uint8;
template<> struct TIsUEnumClass<EPredictionType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EPredictionType>();
// ********** End Enum EPredictionType *************************************************************

// ********** Begin Enum ERollbackType *************************************************************
#define FOREACH_ENUM_EROLLBACKTYPE(op) \
	op(ERollbackType::Movement) \
	op(ERollbackType::Ability) \
	op(ERollbackType::Sigil) 

enum class ERollbackType : uint8;
template<> struct TIsUEnumClass<ERollbackType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ERollbackType>();
// ********** End Enum ERollbackType ***************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
