// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "VFX/SigilVFXManager.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilVFXManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilVFXManager();
AURACRON_API UClass* Z_Construct_UClass_USigilVFXManager_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilVFXPriority();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilVFXType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXInstance();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXPool();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXStats();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESigilVFXType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilVFXType;
static UEnum* ESigilVFXType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilVFXType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilVFXType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilVFXType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilVFXType"));
	}
	return Z_Registration_Info_UEnum_ESigilVFXType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilVFXType>()
{
	return ESigilVFXType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de efeitos VFX para s\xc3\xadgilos\n */" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "ESigilVFXType::Critical" },
		{ "Death.DisplayName", "Death" },
		{ "Death.Name", "ESigilVFXType::Death" },
		{ "Equip.DisplayName", "Equip" },
		{ "Equip.Name", "ESigilVFXType::Equip" },
		{ "FusionComplete.DisplayName", "Fusion Complete" },
		{ "FusionComplete.Name", "ESigilVFXType::FusionComplete" },
		{ "FusionProgress.DisplayName", "Fusion Progress" },
		{ "FusionProgress.Name", "ESigilVFXType::FusionProgress" },
		{ "FusionStart.DisplayName", "Fusion Start" },
		{ "FusionStart.Name", "ESigilVFXType::FusionStart" },
		{ "LevelUp.DisplayName", "Level Up" },
		{ "LevelUp.Name", "ESigilVFXType::LevelUp" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESigilVFXType::None" },
		{ "Objective.DisplayName", "Objective" },
		{ "Objective.Name", "ESigilVFXType::Objective" },
		{ "Reforge.DisplayName", "Reforge" },
		{ "Reforge.Name", "ESigilVFXType::Reforge" },
		{ "Respawn.DisplayName", "Respawn" },
		{ "Respawn.Name", "ESigilVFXType::Respawn" },
		{ "SpectralAura.DisplayName", "Spectral Aura" },
		{ "SpectralAura.Name", "ESigilVFXType::SpectralAura" },
		{ "SpectralPower.DisplayName", "Spectral Power" },
		{ "SpectralPower.Name", "ESigilVFXType::SpectralPower" },
		{ "TeamFight.DisplayName", "Team Fight" },
		{ "TeamFight.Name", "ESigilVFXType::TeamFight" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de efeitos VFX para s\xc3\xadgilos" },
#endif
		{ "Unequip.DisplayName", "Unequip" },
		{ "Unequip.Name", "ESigilVFXType::Unequip" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilVFXType::None", (int64)ESigilVFXType::None },
		{ "ESigilVFXType::Equip", (int64)ESigilVFXType::Equip },
		{ "ESigilVFXType::Unequip", (int64)ESigilVFXType::Unequip },
		{ "ESigilVFXType::FusionStart", (int64)ESigilVFXType::FusionStart },
		{ "ESigilVFXType::FusionProgress", (int64)ESigilVFXType::FusionProgress },
		{ "ESigilVFXType::FusionComplete", (int64)ESigilVFXType::FusionComplete },
		{ "ESigilVFXType::Reforge", (int64)ESigilVFXType::Reforge },
		{ "ESigilVFXType::LevelUp", (int64)ESigilVFXType::LevelUp },
		{ "ESigilVFXType::SpectralPower", (int64)ESigilVFXType::SpectralPower },
		{ "ESigilVFXType::SpectralAura", (int64)ESigilVFXType::SpectralAura },
		{ "ESigilVFXType::TeamFight", (int64)ESigilVFXType::TeamFight },
		{ "ESigilVFXType::Objective", (int64)ESigilVFXType::Objective },
		{ "ESigilVFXType::Critical", (int64)ESigilVFXType::Critical },
		{ "ESigilVFXType::Death", (int64)ESigilVFXType::Death },
		{ "ESigilVFXType::Respawn", (int64)ESigilVFXType::Respawn },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilVFXType",
	"ESigilVFXType",
	Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilVFXType()
{
	if (!Z_Registration_Info_UEnum_ESigilVFXType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilVFXType.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilVFXType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilVFXType.InnerSingleton;
}
// ********** End Enum ESigilVFXType ***************************************************************

// ********** Begin Enum ESigilVFXPriority *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilVFXPriority;
static UEnum* ESigilVFXPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilVFXPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilVFXPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilVFXPriority, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilVFXPriority"));
	}
	return Z_Registration_Info_UEnum_ESigilVFXPriority.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilVFXPriority>()
{
	return ESigilVFXPriority_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Prioridade dos efeitos VFX\n */" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "ESigilVFXPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "ESigilVFXPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "ESigilVFXPriority::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "ESigilVFXPriority::Medium" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade dos efeitos VFX" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilVFXPriority::Low", (int64)ESigilVFXPriority::Low },
		{ "ESigilVFXPriority::Medium", (int64)ESigilVFXPriority::Medium },
		{ "ESigilVFXPriority::High", (int64)ESigilVFXPriority::High },
		{ "ESigilVFXPriority::Critical", (int64)ESigilVFXPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilVFXPriority",
	"ESigilVFXPriority",
	Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilVFXPriority()
{
	if (!Z_Registration_Info_UEnum_ESigilVFXPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilVFXPriority.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilVFXPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilVFXPriority.InnerSingleton;
}
// ********** End Enum ESigilVFXPriority ***********************************************************

// ********** Begin ScriptStruct FSigilVFXConfig ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilVFXConfig;
class UScriptStruct* FSigilVFXConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilVFXConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilVFXConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilVFXConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilVFXConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o de um efeito VFX\n */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de um efeito VFX" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NiagaraSystem_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema Niagara para o efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema Niagara para o efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "VFX" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do efeito em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShouldLoop_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o efeito deve fazer loop */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o efeito deve fazer loop" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAttachToActor_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o efeito deve ser anexado ao ator */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o efeito deve ser anexado ao ator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttachSocketName_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do socket para anexar o efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do socket para anexar o efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeOffset_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Offset relativo para o efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Offset relativo para o efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameplayTags_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags de gameplay associadas */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags de gameplay associadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatParameters_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\xa2metros customizados do Niagara */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\xa2metros customizados do Niagara" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorParameters_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorParameters_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_NiagaraSystem;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bShouldLoop_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShouldLoop;
	static void NewProp_bAttachToActor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAttachToActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AttachSocketName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RelativeOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GameplayTags;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FloatParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_FloatParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VectorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VectorParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ColorParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilVFXConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_NiagaraSystem = { "NiagaraSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, NiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NiagaraSystem_MetaData), NewProp_NiagaraSystem_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, Priority), Z_Construct_UEnum_AURACRON_ESigilVFXPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 752846808
void Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bShouldLoop_SetBit(void* Obj)
{
	((FSigilVFXConfig*)Obj)->bShouldLoop = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bShouldLoop = { "bShouldLoop", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilVFXConfig), &Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bShouldLoop_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShouldLoop_MetaData), NewProp_bShouldLoop_MetaData) };
void Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bAttachToActor_SetBit(void* Obj)
{
	((FSigilVFXConfig*)Obj)->bAttachToActor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bAttachToActor = { "bAttachToActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilVFXConfig), &Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bAttachToActor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAttachToActor_MetaData), NewProp_bAttachToActor_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_AttachSocketName = { "AttachSocketName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, AttachSocketName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttachSocketName_MetaData), NewProp_AttachSocketName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_RelativeOffset = { "RelativeOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, RelativeOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeOffset_MetaData), NewProp_RelativeOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_GameplayTags = { "GameplayTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, GameplayTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameplayTags_MetaData), NewProp_GameplayTags_MetaData) }; // 2104890724
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_FloatParameters_ValueProp = { "FloatParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_FloatParameters_Key_KeyProp = { "FloatParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_FloatParameters = { "FloatParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, FloatParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatParameters_MetaData), NewProp_FloatParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_VectorParameters_ValueProp = { "VectorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_VectorParameters_Key_KeyProp = { "VectorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_VectorParameters = { "VectorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, VectorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorParameters_MetaData), NewProp_VectorParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_ColorParameters_ValueProp = { "ColorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_ColorParameters_Key_KeyProp = { "ColorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_ColorParameters = { "ColorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXConfig, ColorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorParameters_MetaData), NewProp_ColorParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_NiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bShouldLoop,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_bAttachToActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_AttachSocketName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_RelativeOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_GameplayTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_FloatParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_FloatParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_FloatParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_VectorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_VectorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_VectorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_ColorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_ColorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewProp_ColorParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilVFXConfig",
	Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::PropPointers),
	sizeof(FSigilVFXConfig),
	alignof(FSigilVFXConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilVFXConfig.InnerSingleton, Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXConfig.InnerSingleton;
}
// ********** End ScriptStruct FSigilVFXConfig *****************************************************

// ********** Begin ScriptStruct FSigilVFXInstance *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilVFXInstance;
class UScriptStruct* FSigilVFXInstance::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXInstance.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilVFXInstance.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilVFXInstance, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilVFXInstance"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXInstance.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilVFXInstance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Inst\xc3\xa2ncia ativa de um efeito VFX\n */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inst\xc3\xa2ncia ativa de um efeito VFX" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NiagaraComponent_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente Niagara ativo */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente Niagara ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXType_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerActor_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ator propriet\xc3\xa1rio do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator propriet\xc3\xa1rio do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RemainingTime_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceID_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\xbanico da inst\xc3\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\xbanico da inst\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a inst\xc3\xa2ncia est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a inst\xc3\xa2ncia est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp de cria\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp de cria\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NiagaraComponent;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_OwnerActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RemainingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceID;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilVFXInstance>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_NiagaraComponent = { "NiagaraComponent", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, NiagaraComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NiagaraComponent_MetaData), NewProp_NiagaraComponent_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXType_MetaData), NewProp_VFXType_MetaData) }; // 1051404279
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, Config), Z_Construct_UScriptStruct_FSigilVFXConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 809006763
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_OwnerActor = { "OwnerActor", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, OwnerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerActor_MetaData), NewProp_OwnerActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_RemainingTime = { "RemainingTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, RemainingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RemainingTime_MetaData), NewProp_RemainingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, InstanceID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceID_MetaData), NewProp_InstanceID_MetaData) };
void Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FSigilVFXInstance*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilVFXInstance), &Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXInstance, CreationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_NiagaraComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_OwnerActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_RemainingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_InstanceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewProp_CreationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilVFXInstance",
	Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::PropPointers),
	sizeof(FSigilVFXInstance),
	alignof(FSigilVFXInstance),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXInstance()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXInstance.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilVFXInstance.InnerSingleton, Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXInstance.InnerSingleton;
}
// ********** End ScriptStruct FSigilVFXInstance ***************************************************

// ********** Begin ScriptStruct FSigilVFXPool *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilVFXPool;
class UScriptStruct* FSigilVFXPool::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXPool.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilVFXPool.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilVFXPool, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilVFXPool"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXPool.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilVFXPool_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Pool de componentes Niagara para otimiza\xc3\xa7\xc3\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool de componentes Niagara para otimiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes dispon\xc3\xadveis no pool */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes dispon\xc3\xadveis no pool" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes atualmente em uso */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes atualmente em uso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPoolSize_MetaData[] = {
		{ "Category", "Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho m\xc3\xa1ximo do pool */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho m\xc3\xa1ximo do pool" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialPoolSize_MetaData[] = {
		{ "Category", "Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho inicial do pool */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho inicial do pool" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssociatedSystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema Niagara associado ao pool */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema Niagara associado ao pool" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AvailableComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AvailableComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveComponents;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPoolSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InitialPoolSize;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssociatedSystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilVFXPool>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_AvailableComponents_Inner = { "AvailableComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_AvailableComponents = { "AvailableComponents", nullptr, (EPropertyFlags)0x0114008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXPool, AvailableComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableComponents_MetaData), NewProp_AvailableComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_ActiveComponents_Inner = { "ActiveComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_ActiveComponents = { "ActiveComponents", nullptr, (EPropertyFlags)0x0114008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXPool, ActiveComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveComponents_MetaData), NewProp_ActiveComponents_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_MaxPoolSize = { "MaxPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXPool, MaxPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPoolSize_MetaData), NewProp_MaxPoolSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_InitialPoolSize = { "InitialPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXPool, InitialPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialPoolSize_MetaData), NewProp_InitialPoolSize_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_AssociatedSystem = { "AssociatedSystem", nullptr, (EPropertyFlags)0x0014000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXPool, AssociatedSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssociatedSystem_MetaData), NewProp_AssociatedSystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilVFXPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_AvailableComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_AvailableComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_ActiveComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_ActiveComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_MaxPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_InitialPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewProp_AssociatedSystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilVFXPool_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilVFXPool",
	Z_Construct_UScriptStruct_FSigilVFXPool_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXPool_Statics::PropPointers),
	sizeof(FSigilVFXPool),
	alignof(FSigilVFXPool),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXPool_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilVFXPool_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXPool()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXPool.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilVFXPool.InnerSingleton, Z_Construct_UScriptStruct_FSigilVFXPool_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXPool.InnerSingleton;
}
// ********** End ScriptStruct FSigilVFXPool *******************************************************

// ********** Begin ScriptStruct FSigilVFXStats ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilVFXStats;
class UScriptStruct* FSigilVFXStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilVFXStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilVFXStats, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilVFXStats"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilVFXStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estat\xc3\xadsticas do sistema VFX\n */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas do sistema VFX" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveEffects_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total de efeitos ativos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total de efeitos ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PooledComponents_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total de componentes no pool */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total de componentes no pool" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectsByType_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos por tipo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectsByPriority_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos por prioridade */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos por prioridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageEffectLifetime_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xa9""dio de vida dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xa9""dio de vida dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakSimultaneousEffects_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pico de efeitos simult\xc3\xa2neos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pico de efeitos simult\xc3\xa2neos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolEfficiency_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efici\xc3\xaancia do pool (%) */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efici\xc3\xaancia do pool (%)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveEffects;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PooledComponents;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EffectsByType_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectsByType_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectsByType_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EffectsByType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EffectsByPriority_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectsByPriority_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectsByPriority_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EffectsByPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageEffectLifetime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PeakSimultaneousEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PoolEfficiency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilVFXStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_ActiveEffects = { "ActiveEffects", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, ActiveEffects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveEffects_MetaData), NewProp_ActiveEffects_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_PooledComponents = { "PooledComponents", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, PooledComponents), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PooledComponents_MetaData), NewProp_PooledComponents_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType_ValueProp = { "EffectsByType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType_Key_KeyProp = { "EffectsByType_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType = { "EffectsByType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, EffectsByType), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectsByType_MetaData), NewProp_EffectsByType_MetaData) }; // 1051404279
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority_ValueProp = { "EffectsByPriority", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority_Key_KeyProp = { "EffectsByPriority_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilVFXPriority, METADATA_PARAMS(0, nullptr) }; // 752846808
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority = { "EffectsByPriority", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, EffectsByPriority), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectsByPriority_MetaData), NewProp_EffectsByPriority_MetaData) }; // 752846808
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_AverageEffectLifetime = { "AverageEffectLifetime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, AverageEffectLifetime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageEffectLifetime_MetaData), NewProp_AverageEffectLifetime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_PeakSimultaneousEffects = { "PeakSimultaneousEffects", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, PeakSimultaneousEffects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakSimultaneousEffects_MetaData), NewProp_PeakSimultaneousEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_PoolEfficiency = { "PoolEfficiency", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilVFXStats, PoolEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolEfficiency_MetaData), NewProp_PoolEfficiency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilVFXStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_ActiveEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_PooledComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_EffectsByPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_AverageEffectLifetime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_PeakSimultaneousEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewProp_PoolEfficiency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilVFXStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilVFXStats",
	Z_Construct_UScriptStruct_FSigilVFXStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXStats_Statics::PropPointers),
	sizeof(FSigilVFXStats),
	alignof(FSigilVFXStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilVFXStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilVFXStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilVFXStats()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilVFXStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilVFXStats.InnerSingleton, Z_Construct_UScriptStruct_FSigilVFXStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilVFXStats.InnerSingleton;
}
// ********** End ScriptStruct FSigilVFXStats ******************************************************

// ********** Begin Delegate FOnVFXStarted *********************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnVFXStarted_Parms
	{
		ESigilVFXType VFXType;
		int32 InstanceID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// DELEGATES\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DELEGATES" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnVFXStarted_Parms, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnVFXStarted_Parms, InstanceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::NewProp_InstanceID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnVFXStarted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnVFXStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnVFXStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnVFXStarted_DelegateWrapper(const FMulticastScriptDelegate& OnVFXStarted, ESigilVFXType VFXType, int32 InstanceID)
{
	struct _Script_AURACRON_eventOnVFXStarted_Parms
	{
		ESigilVFXType VFXType;
		int32 InstanceID;
	};
	_Script_AURACRON_eventOnVFXStarted_Parms Parms;
	Parms.VFXType=VFXType;
	Parms.InstanceID=InstanceID;
	OnVFXStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnVFXStarted ***********************************************************

// ********** Begin Delegate FOnVFXCompleted *******************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnVFXCompleted_Parms
	{
		ESigilVFXType VFXType;
		int32 InstanceID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnVFXCompleted_Parms, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnVFXCompleted_Parms, InstanceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::NewProp_InstanceID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnVFXCompleted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnVFXCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnVFXCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnVFXCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnVFXCompleted, ESigilVFXType VFXType, int32 InstanceID)
{
	struct _Script_AURACRON_eventOnVFXCompleted_Parms
	{
		ESigilVFXType VFXType;
		int32 InstanceID;
	};
	_Script_AURACRON_eventOnVFXCompleted_Parms Parms;
	Parms.VFXType=VFXType;
	Parms.InstanceID=InstanceID;
	OnVFXCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnVFXCompleted *********************************************************

// ********** Begin Delegate FOnVFXStatsChanged ****************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnVFXStatsChanged_Parms
	{
		FSigilVFXStats NewStats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewStats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::NewProp_NewStats = { "NewStats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnVFXStatsChanged_Parms, NewStats), Z_Construct_UScriptStruct_FSigilVFXStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewStats_MetaData), NewProp_NewStats_MetaData) }; // 2266338384
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::NewProp_NewStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnVFXStatsChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnVFXStatsChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnVFXStatsChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnVFXStatsChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVFXStatsChanged, FSigilVFXStats const& NewStats)
{
	struct _Script_AURACRON_eventOnVFXStatsChanged_Parms
	{
		FSigilVFXStats NewStats;
	};
	_Script_AURACRON_eventOnVFXStatsChanged_Parms Parms;
	Parms.NewStats=NewStats;
	OnVFXStatsChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnVFXStatsChanged ******************************************************

// ********** Begin Class USigilVFXManager Function CleanupExpiredVFX ******************************
struct Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limpar VFX expirados */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar VFX expirados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "CleanupExpiredVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execCleanupExpiredVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupExpiredVFX();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function CleanupExpiredVFX ********************************

// ********** Begin Class USigilVFXManager Function CleanupUnusedPools *****************************
struct Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limpar pools n\xc3\xa3o utilizados */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar pools n\xc3\xa3o utilizados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "CleanupUnusedPools", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execCleanupUnusedPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupUnusedPools();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function CleanupUnusedPools *******************************

// ********** Begin Class USigilVFXManager Function ClearAllVFX ************************************
struct Z_Construct_UFunction_USigilVFXManager_ClearAllVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limpar todos os efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar todos os efeitos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_ClearAllVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "ClearAllVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_ClearAllVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_ClearAllVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_ClearAllVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_ClearAllVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execClearAllVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllVFX();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function ClearAllVFX **************************************

// ********** Begin Class USigilVFXManager Function ForceCleanupAllPools ***************************
struct Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar limpeza de todos os pools */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar limpeza de todos os pools" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "ForceCleanupAllPools", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execForceCleanupAllPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceCleanupAllPools();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function ForceCleanupAllPools *****************************

// ********** Begin Class USigilVFXManager Function ForceCleanupAllVFX *****************************
struct Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar limpeza de todos os VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar limpeza de todos os VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "ForceCleanupAllVFX", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execForceCleanupAllVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceCleanupAllVFX();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function ForceCleanupAllVFX *******************************

// ********** Begin Class USigilVFXManager Function GetActiveVFXForActor ***************************
struct Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics
{
	struct SigilVFXManager_eventGetActiveVFXForActor_Parms
	{
		AActor* TargetActor;
		TArray<FSigilVFXInstance> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todos os efeitos ativos de um ator */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os efeitos ativos de um ator" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetActiveVFXForActor_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilVFXInstance, METADATA_PARAMS(0, nullptr) }; // 3397635331
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetActiveVFXForActor_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3397635331
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "GetActiveVFXForActor", Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::SigilVFXManager_eventGetActiveVFXForActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::SigilVFXManager_eventGetActiveVFXForActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execGetActiveVFXForActor)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilVFXInstance>*)Z_Param__Result=P_THIS->GetActiveVFXForActor(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function GetActiveVFXForActor *****************************

// ********** Begin Class USigilVFXManager Function GetDefaultVFXConfig ****************************
struct Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics
{
	struct SigilVFXManager_eventGetDefaultVFXConfig_Parms
	{
		ESigilVFXType VFXType;
		FSigilVFXConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter configura\xc3\xa7\xc3\xa3o padr\xc3\xa3o para um tipo de VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\xa7\xc3\xa3o padr\xc3\xa3o para um tipo de VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetDefaultVFXConfig_Parms, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetDefaultVFXConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilVFXConfig, METADATA_PARAMS(0, nullptr) }; // 809006763
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "GetDefaultVFXConfig", Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::SigilVFXManager_eventGetDefaultVFXConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::SigilVFXManager_eventGetDefaultVFXConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execGetDefaultVFXConfig)
{
	P_GET_ENUM(ESigilVFXType,Z_Param_VFXType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilVFXConfig*)Z_Param__Result=P_THIS->GetDefaultVFXConfig(ESigilVFXType(Z_Param_VFXType));
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function GetDefaultVFXConfig ******************************

// ********** Begin Class USigilVFXManager Function GetVFXInstance *********************************
struct Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics
{
	struct SigilVFXManager_eventGetVFXInstance_Parms
	{
		int32 InstanceID;
		FSigilVFXInstance ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter inst\xc3\xa2ncia de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter inst\xc3\xa2ncia de efeito" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetVFXInstance_Parms, InstanceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetVFXInstance_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilVFXInstance, METADATA_PARAMS(0, nullptr) }; // 3397635331
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::NewProp_InstanceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "GetVFXInstance", Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::SigilVFXManager_eventGetVFXInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::SigilVFXManager_eventGetVFXInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_GetVFXInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_GetVFXInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execGetVFXInstance)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilVFXInstance*)Z_Param__Result=P_THIS->GetVFXInstance(Z_Param_InstanceID);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function GetVFXInstance ***********************************

// ********** Begin Class USigilVFXManager Function GetVFXStats ************************************
struct Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics
{
	struct SigilVFXManager_eventGetVFXStats_Parms
	{
		FSigilVFXStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter estat\xc3\xadsticas do sistema VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\xadsticas do sistema VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventGetVFXStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilVFXStats, METADATA_PARAMS(0, nullptr) }; // 2266338384
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "GetVFXStats", Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::SigilVFXManager_eventGetVFXStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::SigilVFXManager_eventGetVFXStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_GetVFXStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_GetVFXStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execGetVFXStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilVFXStats*)Z_Param__Result=P_THIS->GetVFXStats();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function GetVFXStats **************************************

// ********** Begin Class USigilVFXManager Function InitializeVFXManager ***************************
struct Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar o manager VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar o manager VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "InitializeVFXManager", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execInitializeVFXManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeVFXManager();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function InitializeVFXManager *****************************

// ********** Begin Class USigilVFXManager Function IsVFXActive ************************************
struct Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics
{
	struct SigilVFXManager_eventIsVFXActive_Parms
	{
		int32 InstanceID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se um efeito est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um efeito est\xc3\xa1 ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventIsVFXActive_Parms, InstanceID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilVFXManager_eventIsVFXActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilVFXManager_eventIsVFXActive_Parms), &Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::NewProp_InstanceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "IsVFXActive", Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::SigilVFXManager_eventIsVFXActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::SigilVFXManager_eventIsVFXActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_IsVFXActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_IsVFXActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execIsVFXActive)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsVFXActive(Z_Param_InstanceID);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function IsVFXActive **************************************

// ********** Begin Class USigilVFXManager Function LoadDefaultVFXSystems **************************
struct Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Carregar sistemas VFX padr\xc3\xa3o assincronamente */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar sistemas VFX padr\xc3\xa3o assincronamente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "LoadDefaultVFXSystems", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execLoadDefaultVFXSystems)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadDefaultVFXSystems();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function LoadDefaultVFXSystems ****************************

// ********** Begin Class USigilVFXManager Function OptimizePools **********************************
struct Z_Construct_UFunction_USigilVFXManager_OptimizePools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Otimizar pools baseado no uso */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar pools baseado no uso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_OptimizePools_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "OptimizePools", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_OptimizePools_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_OptimizePools_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_OptimizePools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_OptimizePools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execOptimizePools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizePools();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function OptimizePools ************************************

// ********** Begin Class USigilVFXManager Function OptimizeVFXPools *******************************
struct Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Otimizar pools de VFX removendo componentes inv\xc3\xa1lidos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar pools de VFX removendo componentes inv\xc3\xa1lidos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "OptimizeVFXPools", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execOptimizeVFXPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeVFXPools();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function OptimizeVFXPools *********************************

// ********** Begin Class USigilVFXManager Function PlayCriticalVFX ********************************
struct Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics
{
	struct SigilVFXManager_eventPlayCriticalVFX_Parms
	{
		AActor* TargetActor;
		float Damage;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito cr\xc3\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito cr\xc3\xadtico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayCriticalVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayCriticalVFX_Parms, Damage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayCriticalVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlayCriticalVFX", Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::SigilVFXManager_eventPlayCriticalVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::SigilVFXManager_eventPlayCriticalVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlayCriticalVFX)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlayCriticalVFX(Z_Param_TargetActor,Z_Param_Damage);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlayCriticalVFX **********************************

// ********** Begin Class USigilVFXManager Function PlayObjectiveVFX *******************************
struct Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics
{
	struct SigilVFXManager_eventPlayObjectiveVFX_Parms
	{
		AActor* TargetActor;
		FGameplayTag ObjectiveTag;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveTag;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayObjectiveVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::NewProp_ObjectiveTag = { "ObjectiveTag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayObjectiveVFX_Parms, ObjectiveTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayObjectiveVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::NewProp_ObjectiveTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlayObjectiveVFX", Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::SigilVFXManager_eventPlayObjectiveVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::SigilVFXManager_eventPlayObjectiveVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlayObjectiveVFX)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT(FGameplayTag,Z_Param_ObjectiveTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlayObjectiveVFX(Z_Param_TargetActor,Z_Param_ObjectiveTag);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlayObjectiveVFX *********************************

// ********** Begin Class USigilVFXManager Function PlaySigilEquipVFX ******************************
struct Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics
{
	struct SigilVFXManager_eventPlaySigilEquipVFX_Parms
	{
		ASigilItem* Sigil;
		AActor* TargetActor;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de equipar sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de equipar sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilEquipVFX_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilEquipVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilEquipVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlaySigilEquipVFX", Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::SigilVFXManager_eventPlaySigilEquipVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::SigilVFXManager_eventPlaySigilEquipVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlaySigilEquipVFX)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlaySigilEquipVFX(Z_Param_Sigil,Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlaySigilEquipVFX ********************************

// ********** Begin Class USigilVFXManager Function PlaySigilFusionVFX *****************************
struct Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics
{
	struct SigilVFXManager_eventPlaySigilFusionVFX_Parms
	{
		ASigilItem* Sigil;
		AActor* TargetActor;
		bool bIsComplete;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de fus\xc3\xa3o de sigilo */" },
#endif
		{ "CPP_Default_bIsComplete", "false" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de fus\xc3\xa3o de sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static void NewProp_bIsComplete_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsComplete;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilFusionVFX_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilFusionVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_bIsComplete_SetBit(void* Obj)
{
	((SigilVFXManager_eventPlaySigilFusionVFX_Parms*)Obj)->bIsComplete = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_bIsComplete = { "bIsComplete", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilVFXManager_eventPlaySigilFusionVFX_Parms), &Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_bIsComplete_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilFusionVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_bIsComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlaySigilFusionVFX", Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::SigilVFXManager_eventPlaySigilFusionVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::SigilVFXManager_eventPlaySigilFusionVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlaySigilFusionVFX)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_UBOOL(Z_Param_bIsComplete);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlaySigilFusionVFX(Z_Param_Sigil,Z_Param_TargetActor,Z_Param_bIsComplete);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlaySigilFusionVFX *******************************

// ********** Begin Class USigilVFXManager Function PlaySigilReforgeVFX ****************************
struct Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics
{
	struct SigilVFXManager_eventPlaySigilReforgeVFX_Parms
	{
		ASigilItem* Sigil;
		AActor* TargetActor;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de reforge de sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de reforge de sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilReforgeVFX_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilReforgeVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilReforgeVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlaySigilReforgeVFX", Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::SigilVFXManager_eventPlaySigilReforgeVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::SigilVFXManager_eventPlaySigilReforgeVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlaySigilReforgeVFX)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlaySigilReforgeVFX(Z_Param_Sigil,Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlaySigilReforgeVFX ******************************

// ********** Begin Class USigilVFXManager Function PlaySigilUnequipVFX ****************************
struct Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics
{
	struct SigilVFXManager_eventPlaySigilUnequipVFX_Parms
	{
		ASigilItem* Sigil;
		AActor* TargetActor;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de desequipar sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de desequipar sigilo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilUnequipVFX_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilUnequipVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySigilUnequipVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlaySigilUnequipVFX", Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::SigilVFXManager_eventPlaySigilUnequipVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::SigilVFXManager_eventPlaySigilUnequipVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlaySigilUnequipVFX)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlaySigilUnequipVFX(Z_Param_Sigil,Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlaySigilUnequipVFX ******************************

// ********** Begin Class USigilVFXManager Function PlaySpectralAuraVFX ****************************
struct Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics
{
	struct SigilVFXManager_eventPlaySpectralAuraVFX_Parms
	{
		AActor* TargetActor;
		ESigilRarity Rarity;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de aura espectral */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de aura espectral" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySpectralAuraVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySpectralAuraVFX_Parms, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlaySpectralAuraVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlaySpectralAuraVFX", Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::SigilVFXManager_eventPlaySpectralAuraVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::SigilVFXManager_eventPlaySpectralAuraVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlaySpectralAuraVFX)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_ENUM(ESigilRarity,Z_Param_Rarity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlaySpectralAuraVFX(Z_Param_TargetActor,ESigilRarity(Z_Param_Rarity));
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlaySpectralAuraVFX ******************************

// ********** Begin Class USigilVFXManager Function PlayTeamFightVFX *******************************
struct Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics
{
	struct SigilVFXManager_eventPlayTeamFightVFX_Parms
	{
		AActor* TargetActor;
		int32 TeamID;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito de team fight */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito de team fight" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayTeamFightVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayTeamFightVFX_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayTeamFightVFX_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlayTeamFightVFX", Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::SigilVFXManager_eventPlayTeamFightVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::SigilVFXManager_eventPlayTeamFightVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlayTeamFightVFX)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlayTeamFightVFX(Z_Param_TargetActor,Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlayTeamFightVFX *********************************

// ********** Begin Class USigilVFXManager Function PlayVFXAtLocation ******************************
struct Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics
{
	struct SigilVFXManager_eventPlayVFXAtLocation_Parms
	{
		ESigilVFXType VFXType;
		FVector Location;
		FSigilVFXConfig Config;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito VFX em localiza\xc3\xa7\xc3\xa3o espec\xc3\xad""fica */" },
#endif
		{ "CPP_Default_Config", "()" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito VFX em localiza\xc3\xa7\xc3\xa3o espec\xc3\xad""fica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXAtLocation_Parms, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXAtLocation_Parms, Config), Z_Construct_UScriptStruct_FSigilVFXConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 809006763
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlayVFXAtLocation", Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::SigilVFXManager_eventPlayVFXAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C40401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::SigilVFXManager_eventPlayVFXAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlayVFXAtLocation)
{
	P_GET_ENUM(ESigilVFXType,Z_Param_VFXType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FSigilVFXConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlayVFXAtLocation(ESigilVFXType(Z_Param_VFXType),Z_Param_Out_Location,Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlayVFXAtLocation ********************************

// ********** Begin Class USigilVFXManager Function PlayVFXEffect **********************************
struct Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics
{
	struct SigilVFXManager_eventPlayVFXEffect_Parms
	{
		ESigilVFXType VFXType;
		AActor* TargetActor;
		FSigilVFXConfig Config;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tocar efeito VFX */" },
#endif
		{ "CPP_Default_Config", "()" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tocar efeito VFX" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXEffect_Parms, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXEffect_Parms, Config), Z_Construct_UScriptStruct_FSigilVFXConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 809006763
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPlayVFXEffect_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PlayVFXEffect", Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::SigilVFXManager_eventPlayVFXEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::SigilVFXManager_eventPlayVFXEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPlayVFXEffect)
{
	P_GET_ENUM(ESigilVFXType,Z_Param_VFXType);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FSigilVFXConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->PlayVFXEffect(ESigilVFXType(Z_Param_VFXType),Z_Param_TargetActor,Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PlayVFXEffect ************************************

// ********** Begin Class USigilVFXManager Function PreloadVFXPool *********************************
struct Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics
{
	struct SigilVFXManager_eventPreloadVFXPool_Parms
	{
		UNiagaraSystem* NiagaraSystem;
		int32 PoolSize;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pr\xc3\xa9-carregar pool de componentes */" },
#endif
		{ "CPP_Default_PoolSize", "10" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pr\xc3\xa9-carregar pool de componentes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NiagaraSystem;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::NewProp_NiagaraSystem = { "NiagaraSystem", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPreloadVFXPool_Parms, NiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::NewProp_PoolSize = { "PoolSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventPreloadVFXPool_Parms, PoolSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::NewProp_NiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::NewProp_PoolSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PreloadVFXPool", Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::SigilVFXManager_eventPreloadVFXPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::SigilVFXManager_eventPreloadVFXPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPreloadVFXPool)
{
	P_GET_OBJECT(UNiagaraSystem,Z_Param_NiagaraSystem);
	P_GET_PROPERTY(FIntProperty,Z_Param_PoolSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadVFXPool(Z_Param_NiagaraSystem,Z_Param_PoolSize);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PreloadVFXPool ***********************************

// ********** Begin Class USigilVFXManager Function PrintPoolInfo **********************************
struct Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Imprimir informa\xc3\xa7\xc3\xb5""es dos pools */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Imprimir informa\xc3\xa7\xc3\xb5""es dos pools" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PrintPoolInfo", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPrintPoolInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrintPoolInfo();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PrintPoolInfo ************************************

// ********** Begin Class USigilVFXManager Function PrintVFXStats **********************************
struct Z_Construct_UFunction_USigilVFXManager_PrintVFXStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Imprimir estat\xc3\xadsticas do VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Imprimir estat\xc3\xadsticas do VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_PrintVFXStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "PrintVFXStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_PrintVFXStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_PrintVFXStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_PrintVFXStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_PrintVFXStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execPrintVFXStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrintVFXStats();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function PrintVFXStats ************************************

// ********** Begin Class USigilVFXManager Function ResetVFXStats **********************************
struct Z_Construct_UFunction_USigilVFXManager_ResetVFXStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resetar estat\xc3\xadsticas */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resetar estat\xc3\xadsticas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_ResetVFXStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "ResetVFXStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_ResetVFXStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_ResetVFXStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_ResetVFXStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_ResetVFXStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execResetVFXStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetVFXStats();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function ResetVFXStats ************************************

// ********** Begin Class USigilVFXManager Function ShutdownVFXManager *****************************
struct Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Finalizar o manager VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Finalizar o manager VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "ShutdownVFXManager", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execShutdownVFXManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownVFXManager();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function ShutdownVFXManager *******************************

// ********** Begin Class USigilVFXManager Function StopAllVFXForActor *****************************
struct Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics
{
	struct SigilVFXManager_eventStopAllVFXForActor_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar todos os efeitos de um ator */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar todos os efeitos de um ator" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventStopAllVFXForActor_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "StopAllVFXForActor", Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::SigilVFXManager_eventStopAllVFXForActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::SigilVFXManager_eventStopAllVFXForActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execStopAllVFXForActor)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAllVFXForActor(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function StopAllVFXForActor *******************************

// ********** Begin Class USigilVFXManager Function StopAllVFXOfType *******************************
struct Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics
{
	struct SigilVFXManager_eventStopAllVFXOfType_Parms
	{
		ESigilVFXType VFXType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar todos os efeitos de um tipo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar todos os efeitos de um tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventStopAllVFXOfType_Parms, VFXType), Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::NewProp_VFXType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "StopAllVFXOfType", Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::SigilVFXManager_eventStopAllVFXOfType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::SigilVFXManager_eventStopAllVFXOfType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execStopAllVFXOfType)
{
	P_GET_ENUM(ESigilVFXType,Z_Param_VFXType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAllVFXOfType(ESigilVFXType(Z_Param_VFXType));
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function StopAllVFXOfType *********************************

// ********** Begin Class USigilVFXManager Function StopVFXEffect **********************************
struct Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics
{
	struct SigilVFXManager_eventStopVFXEffect_Parms
	{
		int32 InstanceID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar efeito VFX espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar efeito VFX espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilVFXManager_eventStopVFXEffect_Parms, InstanceID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilVFXManager_eventStopVFXEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilVFXManager_eventStopVFXEffect_Parms), &Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::NewProp_InstanceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "StopVFXEffect", Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::SigilVFXManager_eventStopVFXEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::SigilVFXManager_eventStopVFXEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilVFXManager_StopVFXEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_StopVFXEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execStopVFXEffect)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopVFXEffect(Z_Param_InstanceID);
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function StopVFXEffect ************************************

// ********** Begin Class USigilVFXManager Function ValidateAllVFXConfigs **************************
struct Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validar todas as configura\xc3\xa7\xc3\xb5""es VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar todas as configura\xc3\xa7\xc3\xb5""es VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilVFXManager, nullptr, "ValidateAllVFXConfigs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilVFXManager::execValidateAllVFXConfigs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateAllVFXConfigs();
	P_NATIVE_END;
}
// ********** End Class USigilVFXManager Function ValidateAllVFXConfigs ****************************

// ********** Begin Class USigilVFXManager *********************************************************
void USigilVFXManager::StaticRegisterNativesUSigilVFXManager()
{
	UClass* Class = USigilVFXManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CleanupExpiredVFX", &USigilVFXManager::execCleanupExpiredVFX },
		{ "CleanupUnusedPools", &USigilVFXManager::execCleanupUnusedPools },
		{ "ClearAllVFX", &USigilVFXManager::execClearAllVFX },
		{ "ForceCleanupAllPools", &USigilVFXManager::execForceCleanupAllPools },
		{ "ForceCleanupAllVFX", &USigilVFXManager::execForceCleanupAllVFX },
		{ "GetActiveVFXForActor", &USigilVFXManager::execGetActiveVFXForActor },
		{ "GetDefaultVFXConfig", &USigilVFXManager::execGetDefaultVFXConfig },
		{ "GetVFXInstance", &USigilVFXManager::execGetVFXInstance },
		{ "GetVFXStats", &USigilVFXManager::execGetVFXStats },
		{ "InitializeVFXManager", &USigilVFXManager::execInitializeVFXManager },
		{ "IsVFXActive", &USigilVFXManager::execIsVFXActive },
		{ "LoadDefaultVFXSystems", &USigilVFXManager::execLoadDefaultVFXSystems },
		{ "OptimizePools", &USigilVFXManager::execOptimizePools },
		{ "OptimizeVFXPools", &USigilVFXManager::execOptimizeVFXPools },
		{ "PlayCriticalVFX", &USigilVFXManager::execPlayCriticalVFX },
		{ "PlayObjectiveVFX", &USigilVFXManager::execPlayObjectiveVFX },
		{ "PlaySigilEquipVFX", &USigilVFXManager::execPlaySigilEquipVFX },
		{ "PlaySigilFusionVFX", &USigilVFXManager::execPlaySigilFusionVFX },
		{ "PlaySigilReforgeVFX", &USigilVFXManager::execPlaySigilReforgeVFX },
		{ "PlaySigilUnequipVFX", &USigilVFXManager::execPlaySigilUnequipVFX },
		{ "PlaySpectralAuraVFX", &USigilVFXManager::execPlaySpectralAuraVFX },
		{ "PlayTeamFightVFX", &USigilVFXManager::execPlayTeamFightVFX },
		{ "PlayVFXAtLocation", &USigilVFXManager::execPlayVFXAtLocation },
		{ "PlayVFXEffect", &USigilVFXManager::execPlayVFXEffect },
		{ "PreloadVFXPool", &USigilVFXManager::execPreloadVFXPool },
		{ "PrintPoolInfo", &USigilVFXManager::execPrintPoolInfo },
		{ "PrintVFXStats", &USigilVFXManager::execPrintVFXStats },
		{ "ResetVFXStats", &USigilVFXManager::execResetVFXStats },
		{ "ShutdownVFXManager", &USigilVFXManager::execShutdownVFXManager },
		{ "StopAllVFXForActor", &USigilVFXManager::execStopAllVFXForActor },
		{ "StopAllVFXOfType", &USigilVFXManager::execStopAllVFXOfType },
		{ "StopVFXEffect", &USigilVFXManager::execStopVFXEffect },
		{ "ValidateAllVFXConfigs", &USigilVFXManager::execValidateAllVFXConfigs },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilVFXManager;
UClass* USigilVFXManager::GetPrivateStaticClass()
{
	using TClass = USigilVFXManager;
	if (!Z_Registration_Info_UClass_USigilVFXManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilVFXManager"),
			Z_Registration_Info_UClass_USigilVFXManager.InnerSingleton,
			StaticRegisterNativesUSigilVFXManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilVFXManager.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilVFXManager_NoRegister()
{
	return USigilVFXManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilVFXManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "AURACRON" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente respons\xc3\xa1vel por gerenciar efeitos visuais Niagara para s\xc3\xadgilos\n * Otimizado para MOBA 5x5 com sistema de pooling\n */" },
#endif
		{ "IncludePath", "VFX/SigilVFXManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente respons\xc3\xa1vel por gerenciar efeitos visuais Niagara para s\xc3\xadgilos\nOtimizado para MOBA 5x5 com sistema de pooling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultVFXConfigs_MetaData[] = {
		{ "Category", "VFX Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o de VFX por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o de VFX por tipo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityVFXConfigs_MetaData[] = {
		{ "Category", "VFX Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de VFX por raridade de sigilo */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de VFX por raridade de sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSimultaneousEffects_MetaData[] = {
		{ "Category", "VFX Config" },
		{ "ClampMax", "200" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\xa1ximo de efeitos simult\xc3\xa2neos */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa1ximo de efeitos simult\xc3\xa2neos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxEffectsPerActor_MetaData[] = {
		{ "Category", "VFX Config" },
		{ "ClampMax", "50" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\xa1ximo de efeitos por ator */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa1ximo de efeitos por ator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CleanupInterval_MetaData[] = {
		{ "Category", "VFX Config" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de limpeza autom\xc3\xa1tica (segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de limpeza autom\xc3\xa1tica (segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePooling_MetaData[] = {
		{ "Category", "VFX Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar pooling */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar pooling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimize_MetaData[] = {
		{ "Category", "VFX Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve otimizar automaticamente */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve otimizar automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVFXStarted_MetaData[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento quando um VFX \xc3\xa9 iniciado */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento quando um VFX \xc3\xa9 iniciado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVFXCompleted_MetaData[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento quando um VFX \xc3\xa9 completado */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento quando um VFX \xc3\xa9 completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVFXStatsChanged_MetaData[] = {
		{ "Category", "Sigil VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento quando as estat\xc3\xadsticas mudam */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento quando as estat\xc3\xadsticas mudam" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveVFXInstances_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inst\xc3\xa2ncias ativas de VFX */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inst\xc3\xa2ncias ativas de VFX" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentPools_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pools de componentes Niagara */" },
#endif
		{ "ModuleRelativePath", "Public/VFX/SigilVFXManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pools de componentes Niagara" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultVFXConfigs_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultVFXConfigs_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultVFXConfigs_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DefaultVFXConfigs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RarityVFXConfigs_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RarityVFXConfigs_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RarityVFXConfigs_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RarityVFXConfigs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSimultaneousEffects;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxEffectsPerActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CleanupInterval;
	static void NewProp_bUsePooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePooling;
	static void NewProp_bAutoOptimize_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimize;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVFXStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVFXCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVFXStatsChanged;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveVFXInstances_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveVFXInstances_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveVFXInstances;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ComponentPools_ValueProp;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ComponentPools_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ComponentPools;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilVFXManager_CleanupExpiredVFX, "CleanupExpiredVFX" }, // 288164842
		{ &Z_Construct_UFunction_USigilVFXManager_CleanupUnusedPools, "CleanupUnusedPools" }, // 235590786
		{ &Z_Construct_UFunction_USigilVFXManager_ClearAllVFX, "ClearAllVFX" }, // 3984415884
		{ &Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllPools, "ForceCleanupAllPools" }, // 1144490254
		{ &Z_Construct_UFunction_USigilVFXManager_ForceCleanupAllVFX, "ForceCleanupAllVFX" }, // 658476343
		{ &Z_Construct_UFunction_USigilVFXManager_GetActiveVFXForActor, "GetActiveVFXForActor" }, // 833574318
		{ &Z_Construct_UFunction_USigilVFXManager_GetDefaultVFXConfig, "GetDefaultVFXConfig" }, // 2761288955
		{ &Z_Construct_UFunction_USigilVFXManager_GetVFXInstance, "GetVFXInstance" }, // 1216580661
		{ &Z_Construct_UFunction_USigilVFXManager_GetVFXStats, "GetVFXStats" }, // 875666366
		{ &Z_Construct_UFunction_USigilVFXManager_InitializeVFXManager, "InitializeVFXManager" }, // 956333285
		{ &Z_Construct_UFunction_USigilVFXManager_IsVFXActive, "IsVFXActive" }, // 1692617580
		{ &Z_Construct_UFunction_USigilVFXManager_LoadDefaultVFXSystems, "LoadDefaultVFXSystems" }, // 2461240593
		{ &Z_Construct_UFunction_USigilVFXManager_OptimizePools, "OptimizePools" }, // 1319254715
		{ &Z_Construct_UFunction_USigilVFXManager_OptimizeVFXPools, "OptimizeVFXPools" }, // 3633080197
		{ &Z_Construct_UFunction_USigilVFXManager_PlayCriticalVFX, "PlayCriticalVFX" }, // 723239601
		{ &Z_Construct_UFunction_USigilVFXManager_PlayObjectiveVFX, "PlayObjectiveVFX" }, // 3574898694
		{ &Z_Construct_UFunction_USigilVFXManager_PlaySigilEquipVFX, "PlaySigilEquipVFX" }, // 172164594
		{ &Z_Construct_UFunction_USigilVFXManager_PlaySigilFusionVFX, "PlaySigilFusionVFX" }, // 2806649408
		{ &Z_Construct_UFunction_USigilVFXManager_PlaySigilReforgeVFX, "PlaySigilReforgeVFX" }, // 4100095283
		{ &Z_Construct_UFunction_USigilVFXManager_PlaySigilUnequipVFX, "PlaySigilUnequipVFX" }, // 1662911063
		{ &Z_Construct_UFunction_USigilVFXManager_PlaySpectralAuraVFX, "PlaySpectralAuraVFX" }, // 1878309830
		{ &Z_Construct_UFunction_USigilVFXManager_PlayTeamFightVFX, "PlayTeamFightVFX" }, // 913022410
		{ &Z_Construct_UFunction_USigilVFXManager_PlayVFXAtLocation, "PlayVFXAtLocation" }, // 4129114630
		{ &Z_Construct_UFunction_USigilVFXManager_PlayVFXEffect, "PlayVFXEffect" }, // 2185404575
		{ &Z_Construct_UFunction_USigilVFXManager_PreloadVFXPool, "PreloadVFXPool" }, // 4244403468
		{ &Z_Construct_UFunction_USigilVFXManager_PrintPoolInfo, "PrintPoolInfo" }, // 1095885580
		{ &Z_Construct_UFunction_USigilVFXManager_PrintVFXStats, "PrintVFXStats" }, // 3646180061
		{ &Z_Construct_UFunction_USigilVFXManager_ResetVFXStats, "ResetVFXStats" }, // 948850543
		{ &Z_Construct_UFunction_USigilVFXManager_ShutdownVFXManager, "ShutdownVFXManager" }, // 3416293063
		{ &Z_Construct_UFunction_USigilVFXManager_StopAllVFXForActor, "StopAllVFXForActor" }, // 2466367313
		{ &Z_Construct_UFunction_USigilVFXManager_StopAllVFXOfType, "StopAllVFXOfType" }, // 55427029
		{ &Z_Construct_UFunction_USigilVFXManager_StopVFXEffect, "StopVFXEffect" }, // 2367884140
		{ &Z_Construct_UFunction_USigilVFXManager_ValidateAllVFXConfigs, "ValidateAllVFXConfigs" }, // 2745693324
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilVFXManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs_ValueProp = { "DefaultVFXConfigs", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FSigilVFXConfig, METADATA_PARAMS(0, nullptr) }; // 809006763
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs_Key_KeyProp = { "DefaultVFXConfigs_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilVFXType, METADATA_PARAMS(0, nullptr) }; // 1051404279
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs = { "DefaultVFXConfigs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, DefaultVFXConfigs), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultVFXConfigs_MetaData), NewProp_DefaultVFXConfigs_MetaData) }; // 1051404279 809006763
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs_ValueProp = { "RarityVFXConfigs", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FSigilVFXConfig, METADATA_PARAMS(0, nullptr) }; // 809006763
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs_Key_KeyProp = { "RarityVFXConfigs_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs = { "RarityVFXConfigs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, RarityVFXConfigs), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityVFXConfigs_MetaData), NewProp_RarityVFXConfigs_MetaData) }; // 3544987888 809006763
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_MaxSimultaneousEffects = { "MaxSimultaneousEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, MaxSimultaneousEffects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSimultaneousEffects_MetaData), NewProp_MaxSimultaneousEffects_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_MaxEffectsPerActor = { "MaxEffectsPerActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, MaxEffectsPerActor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxEffectsPerActor_MetaData), NewProp_MaxEffectsPerActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_CleanupInterval = { "CleanupInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, CleanupInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CleanupInterval_MetaData), NewProp_CleanupInterval_MetaData) };
void Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bUsePooling_SetBit(void* Obj)
{
	((USigilVFXManager*)Obj)->bUsePooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bUsePooling = { "bUsePooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilVFXManager), &Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bUsePooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePooling_MetaData), NewProp_bUsePooling_MetaData) };
void Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bAutoOptimize_SetBit(void* Obj)
{
	((USigilVFXManager*)Obj)->bAutoOptimize = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bAutoOptimize = { "bAutoOptimize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilVFXManager), &Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bAutoOptimize_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimize_MetaData), NewProp_bAutoOptimize_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_OnVFXStarted = { "OnVFXStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, OnVFXStarted), Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVFXStarted_MetaData), NewProp_OnVFXStarted_MetaData) }; // 2141561498
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_OnVFXCompleted = { "OnVFXCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, OnVFXCompleted), Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVFXCompleted_MetaData), NewProp_OnVFXCompleted_MetaData) }; // 1245464922
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_OnVFXStatsChanged = { "OnVFXStatsChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, OnVFXStatsChanged), Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVFXStatsChanged_MetaData), NewProp_OnVFXStatsChanged_MetaData) }; // 812129723
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ActiveVFXInstances_ValueProp = { "ActiveVFXInstances", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FSigilVFXInstance, METADATA_PARAMS(0, nullptr) }; // 3397635331
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ActiveVFXInstances_Key_KeyProp = { "ActiveVFXInstances_Key", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ActiveVFXInstances = { "ActiveVFXInstances", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, ActiveVFXInstances), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveVFXInstances_MetaData), NewProp_ActiveVFXInstances_MetaData) }; // 3397635331
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ComponentPools_ValueProp = { "ComponentPools", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FSigilVFXPool, METADATA_PARAMS(0, nullptr) }; // 1232841098
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ComponentPools_Key_KeyProp = { "ComponentPools_Key", nullptr, (EPropertyFlags)0x0004008000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ComponentPools = { "ComponentPools", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilVFXManager, ComponentPools), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentPools_MetaData), NewProp_ComponentPools_MetaData) }; // 1232841098
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilVFXManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_DefaultVFXConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_RarityVFXConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_MaxSimultaneousEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_MaxEffectsPerActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_CleanupInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bUsePooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_bAutoOptimize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_OnVFXStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_OnVFXCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_OnVFXStatsChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ActiveVFXInstances_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ActiveVFXInstances_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ActiveVFXInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ComponentPools_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ComponentPools_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilVFXManager_Statics::NewProp_ComponentPools,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilVFXManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilVFXManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilVFXManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilVFXManager_Statics::ClassParams = {
	&USigilVFXManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilVFXManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilVFXManager_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilVFXManager_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilVFXManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilVFXManager()
{
	if (!Z_Registration_Info_UClass_USigilVFXManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilVFXManager.OuterSingleton, Z_Construct_UClass_USigilVFXManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilVFXManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilVFXManager);
USigilVFXManager::~USigilVFXManager() {}
// ********** End Class USigilVFXManager ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESigilVFXType_StaticEnum, TEXT("ESigilVFXType"), &Z_Registration_Info_UEnum_ESigilVFXType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1051404279U) },
		{ ESigilVFXPriority_StaticEnum, TEXT("ESigilVFXPriority"), &Z_Registration_Info_UEnum_ESigilVFXPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 752846808U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilVFXConfig::StaticStruct, Z_Construct_UScriptStruct_FSigilVFXConfig_Statics::NewStructOps, TEXT("SigilVFXConfig"), &Z_Registration_Info_UScriptStruct_FSigilVFXConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilVFXConfig), 809006763U) },
		{ FSigilVFXInstance::StaticStruct, Z_Construct_UScriptStruct_FSigilVFXInstance_Statics::NewStructOps, TEXT("SigilVFXInstance"), &Z_Registration_Info_UScriptStruct_FSigilVFXInstance, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilVFXInstance), 3397635331U) },
		{ FSigilVFXPool::StaticStruct, Z_Construct_UScriptStruct_FSigilVFXPool_Statics::NewStructOps, TEXT("SigilVFXPool"), &Z_Registration_Info_UScriptStruct_FSigilVFXPool, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilVFXPool), 1232841098U) },
		{ FSigilVFXStats::StaticStruct, Z_Construct_UScriptStruct_FSigilVFXStats_Statics::NewStructOps, TEXT("SigilVFXStats"), &Z_Registration_Info_UScriptStruct_FSigilVFXStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilVFXStats), 2266338384U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilVFXManager, USigilVFXManager::StaticClass, TEXT("USigilVFXManager"), &Z_Registration_Info_UClass_USigilVFXManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilVFXManager), 1382110778U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_326921073(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
