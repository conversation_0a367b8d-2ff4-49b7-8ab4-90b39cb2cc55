// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "VFX/SigilVFXManager.h"

#ifdef AURACRON_SigilVFXManager_generated_h
#error "SigilVFXManager.generated.h already included, missing '#pragma once' in SigilVFXManager.h"
#endif
#define AURACRON_SigilVFXManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ASigilItem;
class UNiagaraSystem;
enum class ESigilRarity : uint8;
enum class ESigilVFXType : uint8;
struct FGameplayTag;
struct FSigilVFXConfig;
struct FSigilVFXInstance;
struct FSigilVFXStats;

// ********** Begin ScriptStruct FSigilVFXConfig ***************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_68_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilVFXConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilVFXConfig;
// ********** End ScriptStruct FSigilVFXConfig *****************************************************

// ********** Begin ScriptStruct FSigilVFXInstance *************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_139_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilVFXInstance_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilVFXInstance;
// ********** End ScriptStruct FSigilVFXInstance ***************************************************

// ********** Begin ScriptStruct FSigilVFXPool *****************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_191_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilVFXPool_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilVFXPool;
// ********** End ScriptStruct FSigilVFXPool *******************************************************

// ********** Begin ScriptStruct FSigilVFXStats ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_226_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilVFXStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilVFXStats;
// ********** End ScriptStruct FSigilVFXStats ******************************************************

// ********** Begin Delegate FOnVFXStarted *********************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_270_DELEGATE \
AURACRON_API void FOnVFXStarted_DelegateWrapper(const FMulticastScriptDelegate& OnVFXStarted, ESigilVFXType VFXType, int32 InstanceID);


// ********** End Delegate FOnVFXStarted ***********************************************************

// ********** Begin Delegate FOnVFXCompleted *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_271_DELEGATE \
AURACRON_API void FOnVFXCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnVFXCompleted, ESigilVFXType VFXType, int32 InstanceID);


// ********** End Delegate FOnVFXCompleted *********************************************************

// ********** Begin Delegate FOnVFXStatsChanged ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_272_DELEGATE \
AURACRON_API void FOnVFXStatsChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVFXStatsChanged, FSigilVFXStats const& NewStats);


// ********** End Delegate FOnVFXStatsChanged ******************************************************

// ********** Begin Class USigilVFXManager *********************************************************
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execForceCleanupAllVFX); \
	DECLARE_FUNCTION(execCleanupExpiredVFX); \
	DECLARE_FUNCTION(execOptimizeVFXPools); \
	DECLARE_FUNCTION(execValidateAllVFXConfigs); \
	DECLARE_FUNCTION(execLoadDefaultVFXSystems); \
	DECLARE_FUNCTION(execPlayVFXAtLocation); \
	DECLARE_FUNCTION(execResetVFXStats); \
	DECLARE_FUNCTION(execForceCleanupAllPools); \
	DECLARE_FUNCTION(execPrintPoolInfo); \
	DECLARE_FUNCTION(execPrintVFXStats); \
	DECLARE_FUNCTION(execOptimizePools); \
	DECLARE_FUNCTION(execCleanupUnusedPools); \
	DECLARE_FUNCTION(execPreloadVFXPool); \
	DECLARE_FUNCTION(execGetDefaultVFXConfig); \
	DECLARE_FUNCTION(execGetVFXStats); \
	DECLARE_FUNCTION(execGetActiveVFXForActor); \
	DECLARE_FUNCTION(execGetVFXInstance); \
	DECLARE_FUNCTION(execIsVFXActive); \
	DECLARE_FUNCTION(execPlayCriticalVFX); \
	DECLARE_FUNCTION(execPlayObjectiveVFX); \
	DECLARE_FUNCTION(execPlayTeamFightVFX); \
	DECLARE_FUNCTION(execPlaySpectralAuraVFX); \
	DECLARE_FUNCTION(execPlaySigilReforgeVFX); \
	DECLARE_FUNCTION(execPlaySigilFusionVFX); \
	DECLARE_FUNCTION(execPlaySigilUnequipVFX); \
	DECLARE_FUNCTION(execPlaySigilEquipVFX); \
	DECLARE_FUNCTION(execClearAllVFX); \
	DECLARE_FUNCTION(execStopAllVFXOfType); \
	DECLARE_FUNCTION(execStopAllVFXForActor); \
	DECLARE_FUNCTION(execStopVFXEffect); \
	DECLARE_FUNCTION(execPlayVFXEffect); \
	DECLARE_FUNCTION(execShutdownVFXManager); \
	DECLARE_FUNCTION(execInitializeVFXManager);


AURACRON_API UClass* Z_Construct_UClass_USigilVFXManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilVFXManager(); \
	friend struct Z_Construct_UClass_USigilVFXManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilVFXManager_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilVFXManager, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilVFXManager_NoRegister) \
	DECLARE_SERIALIZER(USigilVFXManager)


#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilVFXManager(USigilVFXManager&&) = delete; \
	USigilVFXManager(const USigilVFXManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilVFXManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilVFXManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilVFXManager) \
	NO_API virtual ~USigilVFXManager();


#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_282_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h_285_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilVFXManager;

// ********** End Class USigilVFXManager ***********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_VFX_SigilVFXManager_h

// ********** Begin Enum ESigilVFXType *************************************************************
#define FOREACH_ENUM_ESIGILVFXTYPE(op) \
	op(ESigilVFXType::None) \
	op(ESigilVFXType::Equip) \
	op(ESigilVFXType::Unequip) \
	op(ESigilVFXType::FusionStart) \
	op(ESigilVFXType::FusionProgress) \
	op(ESigilVFXType::FusionComplete) \
	op(ESigilVFXType::Reforge) \
	op(ESigilVFXType::LevelUp) \
	op(ESigilVFXType::SpectralPower) \
	op(ESigilVFXType::SpectralAura) \
	op(ESigilVFXType::TeamFight) \
	op(ESigilVFXType::Objective) \
	op(ESigilVFXType::Critical) \
	op(ESigilVFXType::Death) \
	op(ESigilVFXType::Respawn) 

enum class ESigilVFXType : uint8;
template<> struct TIsUEnumClass<ESigilVFXType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilVFXType>();
// ********** End Enum ESigilVFXType ***************************************************************

// ********** Begin Enum ESigilVFXPriority *********************************************************
#define FOREACH_ENUM_ESIGILVFXPRIORITY(op) \
	op(ESigilVFXPriority::Low) \
	op(ESigilVFXPriority::Medium) \
	op(ESigilVFXPriority::High) \
	op(ESigilVFXPriority::Critical) 

enum class ESigilVFXPriority : uint8;
template<> struct TIsUEnumClass<ESigilVFXPriority> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<ESigilVFXPriority>();
// ********** End Enum ESigilVFXPriority ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
