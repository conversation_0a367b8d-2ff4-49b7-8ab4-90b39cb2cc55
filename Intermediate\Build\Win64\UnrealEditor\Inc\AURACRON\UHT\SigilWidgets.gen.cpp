// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilWidgets.h"
#include "GameplayTagContainer.h"
#include "SigilManagerComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilWidgets() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilDragDropOperation();
AURACRON_API UClass* Z_Construct_UClass_USigilDragDropOperation_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilHUDWidget();
AURACRON_API UClass* Z_Construct_UClass_USigilHUDWidget_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilInventoryWidget();
AURACRON_API UClass* Z_Construct_UClass_USigilInventoryWidget_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilManagerComponent_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilNotificationWidget();
AURACRON_API UClass* Z_Construct_UClass_USigilNotificationWidget_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilSlotWidget();
AURACRON_API UClass* Z_Construct_UClass_USigilSlotWidget_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilSlotState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilSubType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilNotificationData();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilSlotVisualConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilSystemStats();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
UMG_API UClass* Z_Construct_UClass_UButton_NoRegister();
UMG_API UClass* Z_Construct_UClass_UCanvasPanel_NoRegister();
UMG_API UClass* Z_Construct_UClass_UDragDropOperation();
UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UOverlay_NoRegister();
UMG_API UClass* Z_Construct_UClass_UProgressBar_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UMG_API UClass* Z_Construct_UClass_UUserWidget_NoRegister();
UMG_API UClass* Z_Construct_UClass_UVerticalBox_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESigilSlotState ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilSlotState;
static UEnum* ESigilSlotState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilSlotState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilSlotState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilSlotState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilSlotState"));
	}
	return Z_Registration_Info_UEnum_ESigilSlotState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilSlotState>()
{
	return ESigilSlotState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados visuais do slot de sigilo\n */" },
#endif
		{ "DragTarget.DisplayName", "Drag Target" },
		{ "DragTarget.Name", "ESigilSlotState::DragTarget" },
		{ "Empty.DisplayName", "Empty" },
		{ "Empty.Name", "ESigilSlotState::Empty" },
		{ "Equipped.DisplayName", "Equipped" },
		{ "Equipped.Name", "ESigilSlotState::Equipped" },
		{ "Fusing.DisplayName", "Fusing" },
		{ "Fusing.Name", "ESigilSlotState::Fusing" },
		{ "FusionReady.DisplayName", "Fusion Ready" },
		{ "FusionReady.Name", "ESigilSlotState::FusionReady" },
		{ "Highlighted.DisplayName", "Highlighted" },
		{ "Highlighted.Name", "ESigilSlotState::Highlighted" },
		{ "Invalid.DisplayName", "Invalid" },
		{ "Invalid.Name", "ESigilSlotState::Invalid" },
		{ "Locked.DisplayName", "Locked" },
		{ "Locked.Name", "ESigilSlotState::Locked" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados visuais do slot de sigilo" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilSlotState::Empty", (int64)ESigilSlotState::Empty },
		{ "ESigilSlotState::Equipped", (int64)ESigilSlotState::Equipped },
		{ "ESigilSlotState::FusionReady", (int64)ESigilSlotState::FusionReady },
		{ "ESigilSlotState::Fusing", (int64)ESigilSlotState::Fusing },
		{ "ESigilSlotState::Locked", (int64)ESigilSlotState::Locked },
		{ "ESigilSlotState::Highlighted", (int64)ESigilSlotState::Highlighted },
		{ "ESigilSlotState::DragTarget", (int64)ESigilSlotState::DragTarget },
		{ "ESigilSlotState::Invalid", (int64)ESigilSlotState::Invalid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilSlotState",
	"ESigilSlotState",
	Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilSlotState()
{
	if (!Z_Registration_Info_UEnum_ESigilSlotState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilSlotState.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilSlotState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilSlotState.InnerSingleton;
}
// ********** End Enum ESigilSlotState *************************************************************

// ********** Begin ScriptStruct FSigilSlotVisualConfig ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig;
class UScriptStruct* FSigilSlotVisualConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilSlotVisualConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilSlotVisualConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o visual do slot\n */" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o visual do slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StateMaterials_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Materiais por estado\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Materiais por estado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityColors_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cores por raridade\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cores por raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StateVFX_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeitos VFX por estado\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos VFX por estado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipSound_MetaData[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sons por a\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons por a\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnequipSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvalidSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotAnimationDuration_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de anima\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de anima\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionAnimationDuration_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighlightPulseDuration_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StateMaterials_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StateMaterials_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StateMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StateMaterials;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RarityColors_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RarityColors_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RarityColors_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RarityColors;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StateVFX_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StateVFX_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StateVFX_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StateVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EquipSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UnequipSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FusionSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_InvalidSound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SlotAnimationDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionAnimationDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighlightPulseDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilSlotVisualConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials_ValueProp = { "StateMaterials", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials_Key_KeyProp = { "StateMaterials_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials = { "StateMaterials", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, StateMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StateMaterials_MetaData), NewProp_StateMaterials_MetaData) }; // 1671570546
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors_ValueProp = { "RarityColors", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors_Key_KeyProp = { "RarityColors_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors = { "RarityColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, RarityColors), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityColors_MetaData), NewProp_RarityColors_MetaData) }; // 3544987888
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX_ValueProp = { "StateVFX", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX_Key_KeyProp = { "StateVFX_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX = { "StateVFX", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, StateVFX), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StateVFX_MetaData), NewProp_StateVFX_MetaData) }; // 1671570546
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_EquipSound = { "EquipSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, EquipSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipSound_MetaData), NewProp_EquipSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_UnequipSound = { "UnequipSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, UnequipSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnequipSound_MetaData), NewProp_UnequipSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_FusionSound = { "FusionSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, FusionSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionSound_MetaData), NewProp_FusionSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_InvalidSound = { "InvalidSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, InvalidSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvalidSound_MetaData), NewProp_InvalidSound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_SlotAnimationDuration = { "SlotAnimationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, SlotAnimationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotAnimationDuration_MetaData), NewProp_SlotAnimationDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_FusionAnimationDuration = { "FusionAnimationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, FusionAnimationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionAnimationDuration_MetaData), NewProp_FusionAnimationDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_HighlightPulseDuration = { "HighlightPulseDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilSlotVisualConfig, HighlightPulseDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighlightPulseDuration_MetaData), NewProp_HighlightPulseDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_RarityColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_StateVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_EquipSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_UnequipSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_FusionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_InvalidSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_SlotAnimationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_FusionAnimationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewProp_HighlightPulseDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilSlotVisualConfig",
	Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::PropPointers),
	sizeof(FSigilSlotVisualConfig),
	alignof(FSigilSlotVisualConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilSlotVisualConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig.InnerSingleton, Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig.InnerSingleton;
}
// ********** End ScriptStruct FSigilSlotVisualConfig **********************************************

// ********** Begin ScriptStruct FSigilNotificationData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilNotificationData;
class UScriptStruct* FSigilNotificationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilNotificationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilNotificationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilNotificationData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilNotificationData"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilNotificationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilNotificationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Dados de notifica\xc3\xa7\xc3\xa3o do sistema\n */" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados de notifica\xc3\xa7\xc3\xa3o do sistema" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationRarity_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationTags_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Title;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NotificationRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NotificationRarity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NotificationTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilNotificationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_NotificationRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_NotificationRarity = { "NotificationRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, NotificationRarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationRarity_MetaData), NewProp_NotificationRarity_MetaData) }; // 3544987888
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_NotificationTags = { "NotificationTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilNotificationData, NotificationTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationTags_MetaData), NewProp_NotificationTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilNotificationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_NotificationRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_NotificationRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewProp_NotificationTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilNotificationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilNotificationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilNotificationData",
	Z_Construct_UScriptStruct_FSigilNotificationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilNotificationData_Statics::PropPointers),
	sizeof(FSigilNotificationData),
	alignof(FSigilNotificationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilNotificationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilNotificationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilNotificationData()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilNotificationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilNotificationData.InnerSingleton, Z_Construct_UScriptStruct_FSigilNotificationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilNotificationData.InnerSingleton;
}
// ********** End ScriptStruct FSigilNotificationData **********************************************

// ********** Begin Class USigilDragDropOperation Function CanDropOnSlot ***************************
struct Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics
{
	struct SigilDragDropOperation_eventCanDropOnSlot_Parms
	{
		USigilSlotWidget* TargetSlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetSlot_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetSlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::NewProp_TargetSlot = { "TargetSlot", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilDragDropOperation_eventCanDropOnSlot_Parms, TargetSlot), Z_Construct_UClass_USigilSlotWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetSlot_MetaData), NewProp_TargetSlot_MetaData) };
void Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilDragDropOperation_eventCanDropOnSlot_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilDragDropOperation_eventCanDropOnSlot_Parms), &Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::NewProp_TargetSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilDragDropOperation, nullptr, "CanDropOnSlot", Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::SigilDragDropOperation_eventCanDropOnSlot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::SigilDragDropOperation_eventCanDropOnSlot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilDragDropOperation::execCanDropOnSlot)
{
	P_GET_OBJECT(USigilSlotWidget,Z_Param_TargetSlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanDropOnSlot(Z_Param_TargetSlot);
	P_NATIVE_END;
}
// ********** End Class USigilDragDropOperation Function CanDropOnSlot *****************************

// ********** Begin Class USigilDragDropOperation Function IsValidDrop *****************************
struct Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics
{
	struct SigilDragDropOperation_eventIsValidDrop_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilDragDropOperation_eventIsValidDrop_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilDragDropOperation_eventIsValidDrop_Parms), &Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilDragDropOperation, nullptr, "IsValidDrop", Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::SigilDragDropOperation_eventIsValidDrop_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::SigilDragDropOperation_eventIsValidDrop_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilDragDropOperation::execIsValidDrop)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidDrop();
	P_NATIVE_END;
}
// ********** End Class USigilDragDropOperation Function IsValidDrop *******************************

// ********** Begin Class USigilDragDropOperation Function OnDragCancelled *************************
static FName NAME_USigilDragDropOperation_OnDragCancelled = FName(TEXT("OnDragCancelled"));
void USigilDragDropOperation::OnDragCancelled()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilDragDropOperation_OnDragCancelled);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilDragDropOperation, nullptr, "OnDragCancelled", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilDragDropOperation Function OnDragCancelled ***************************

// ********** Begin Class USigilDragDropOperation Function OnDragStarted ***************************
static FName NAME_USigilDragDropOperation_OnDragStarted = FName(TEXT("OnDragStarted"));
void USigilDragDropOperation::OnDragStarted()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilDragDropOperation_OnDragStarted);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilDragDropOperation, nullptr, "OnDragStarted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilDragDropOperation Function OnDragStarted *****************************

// ********** Begin Class USigilDragDropOperation Function OnDropCompleted *************************
struct SigilDragDropOperation_eventOnDropCompleted_Parms
{
	bool bSuccess;
};
static FName NAME_USigilDragDropOperation_OnDropCompleted = FName(TEXT("OnDropCompleted"));
void USigilDragDropOperation::OnDropCompleted(bool bSuccess)
{
	SigilDragDropOperation_eventOnDropCompleted_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_USigilDragDropOperation_OnDropCompleted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((SigilDragDropOperation_eventOnDropCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilDragDropOperation_eventOnDropCompleted_Parms), &Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilDragDropOperation, nullptr, "OnDropCompleted", Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::PropPointers), sizeof(SigilDragDropOperation_eventOnDropCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilDragDropOperation_eventOnDropCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilDragDropOperation Function OnDropCompleted ***************************

// ********** Begin Class USigilDragDropOperation **************************************************
void USigilDragDropOperation::StaticRegisterNativesUSigilDragDropOperation()
{
	UClass* Class = USigilDragDropOperation::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanDropOnSlot", &USigilDragDropOperation::execCanDropOnSlot },
		{ "IsValidDrop", &USigilDragDropOperation::execIsValidDrop },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilDragDropOperation;
UClass* USigilDragDropOperation::GetPrivateStaticClass()
{
	using TClass = USigilDragDropOperation;
	if (!Z_Registration_Info_UClass_USigilDragDropOperation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilDragDropOperation"),
			Z_Registration_Info_UClass_USigilDragDropOperation.InnerSingleton,
			StaticRegisterNativesUSigilDragDropOperation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilDragDropOperation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilDragDropOperation_NoRegister()
{
	return USigilDragDropOperation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilDragDropOperation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Opera\xc3\xa7\xc3\xa3o de drag & drop para s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "UI/SigilWidgets.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Opera\xc3\xa7\xc3\xa3o de drag & drop para s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DraggedSigil_MetaData[] = {
		{ "Category", "Drag Drop" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados do sigilo sendo arrastado\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados do sigilo sendo arrastado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceSlotIndex_MetaData[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceSlotWidget_MetaData[] = {
		{ "Category", "Drag Drop" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragVisual_MetaData[] = {
		{ "Category", "Drag Drop" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual do drag\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual do drag" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragOffset_MetaData[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanDropOnEmpty_MetaData[] = {
		{ "Category", "Drag Drop" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanSwapSigils_MetaData[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanDropOnSameSlot_MetaData[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DraggedSigil;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SourceSlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceSlotWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragVisual;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DragOffset;
	static void NewProp_bCanDropOnEmpty_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanDropOnEmpty;
	static void NewProp_bCanSwapSigils_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanSwapSigils;
	static void NewProp_bCanDropOnSameSlot_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanDropOnSameSlot;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilDragDropOperation_CanDropOnSlot, "CanDropOnSlot" }, // 707951694
		{ &Z_Construct_UFunction_USigilDragDropOperation_IsValidDrop, "IsValidDrop" }, // 1158905226
		{ &Z_Construct_UFunction_USigilDragDropOperation_OnDragCancelled, "OnDragCancelled" }, // 2258082063
		{ &Z_Construct_UFunction_USigilDragDropOperation_OnDragStarted, "OnDragStarted" }, // 1481688854
		{ &Z_Construct_UFunction_USigilDragDropOperation_OnDropCompleted, "OnDropCompleted" }, // 2808044120
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilDragDropOperation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_DraggedSigil = { "DraggedSigil", nullptr, (EPropertyFlags)0x0114000000000004, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDragDropOperation, DraggedSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DraggedSigil_MetaData), NewProp_DraggedSigil_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_SourceSlotIndex = { "SourceSlotIndex", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDragDropOperation, SourceSlotIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceSlotIndex_MetaData), NewProp_SourceSlotIndex_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_SourceSlotWidget = { "SourceSlotWidget", nullptr, (EPropertyFlags)0x011400000008000c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDragDropOperation, SourceSlotWidget), Z_Construct_UClass_USigilSlotWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceSlotWidget_MetaData), NewProp_SourceSlotWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_DragVisual = { "DragVisual", nullptr, (EPropertyFlags)0x011400000008000c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDragDropOperation, DragVisual), Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragVisual_MetaData), NewProp_DragVisual_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_DragOffset = { "DragOffset", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDragDropOperation, DragOffset), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragOffset_MetaData), NewProp_DragOffset_MetaData) };
void Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnEmpty_SetBit(void* Obj)
{
	((USigilDragDropOperation*)Obj)->bCanDropOnEmpty = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnEmpty = { "bCanDropOnEmpty", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDragDropOperation), &Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnEmpty_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanDropOnEmpty_MetaData), NewProp_bCanDropOnEmpty_MetaData) };
void Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanSwapSigils_SetBit(void* Obj)
{
	((USigilDragDropOperation*)Obj)->bCanSwapSigils = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanSwapSigils = { "bCanSwapSigils", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDragDropOperation), &Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanSwapSigils_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanSwapSigils_MetaData), NewProp_bCanSwapSigils_MetaData) };
void Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnSameSlot_SetBit(void* Obj)
{
	((USigilDragDropOperation*)Obj)->bCanDropOnSameSlot = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnSameSlot = { "bCanDropOnSameSlot", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDragDropOperation), &Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnSameSlot_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanDropOnSameSlot_MetaData), NewProp_bCanDropOnSameSlot_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilDragDropOperation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_DraggedSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_SourceSlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_SourceSlotWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_DragVisual,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_DragOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnEmpty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanSwapSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDragDropOperation_Statics::NewProp_bCanDropOnSameSlot,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDragDropOperation_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilDragDropOperation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDragDropOperation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDragDropOperation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilDragDropOperation_Statics::ClassParams = {
	&USigilDragDropOperation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilDragDropOperation_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilDragDropOperation_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDragDropOperation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilDragDropOperation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilDragDropOperation()
{
	if (!Z_Registration_Info_UClass_USigilDragDropOperation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilDragDropOperation.OuterSingleton, Z_Construct_UClass_USigilDragDropOperation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilDragDropOperation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilDragDropOperation);
USigilDragDropOperation::~USigilDragDropOperation() {}
// ********** End Class USigilDragDropOperation ****************************************************

// ********** Begin Class USigilSlotWidget Function CanAcceptSigil *********************************
struct Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics
{
	struct SigilSlotWidget_eventCanAcceptSigil_Parms
	{
		ASigilItem* Sigil;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventCanAcceptSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventCanAcceptSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventCanAcceptSigil_Parms), &Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "CanAcceptSigil", Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::SigilSlotWidget_eventCanAcceptSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::SigilSlotWidget_eventCanAcceptSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execCanAcceptSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAcceptSigil(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function CanAcceptSigil ***********************************

// ********** Begin Class USigilSlotWidget Function CreateDragDropOperation ************************
struct Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics
{
	struct SigilSlotWidget_eventCreateDragDropOperation_Parms
	{
		USigilDragDropOperation* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de drag & drop\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de drag & drop" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventCreateDragDropOperation_Parms, ReturnValue), Z_Construct_UClass_USigilDragDropOperation_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "CreateDragDropOperation", Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::SigilSlotWidget_eventCreateDragDropOperation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::SigilSlotWidget_eventCreateDragDropOperation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execCreateDragDropOperation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(USigilDragDropOperation**)Z_Param__Result=P_THIS->CreateDragDropOperation();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function CreateDragDropOperation **************************

// ********** Begin Class USigilSlotWidget Function EquipSigil *************************************
struct Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics
{
	struct SigilSlotWidget_eventEquipSigil_Parms
	{
		ASigilItem* Sigil;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Equipar/Desequipar\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipar/Desequipar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventEquipSigil_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventEquipSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventEquipSigil_Parms), &Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::NewProp_Sigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "EquipSigil", Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::SigilSlotWidget_eventEquipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::SigilSlotWidget_eventEquipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_EquipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_EquipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execEquipSigil)
{
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EquipSigil(Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function EquipSigil ***************************************

// ********** Begin Class USigilSlotWidget Function HandleDrop *************************************
struct Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics
{
	struct SigilSlotWidget_eventHandleDrop_Parms
	{
		USigilDragDropOperation* DropOperation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DropOperation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::NewProp_DropOperation = { "DropOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventHandleDrop_Parms, DropOperation), Z_Construct_UClass_USigilDragDropOperation_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventHandleDrop_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventHandleDrop_Parms), &Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::NewProp_DropOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "HandleDrop", Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::SigilSlotWidget_eventHandleDrop_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::SigilSlotWidget_eventHandleDrop_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_HandleDrop()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_HandleDrop_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execHandleDrop)
{
	P_GET_OBJECT(USigilDragDropOperation,Z_Param_DropOperation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HandleDrop(Z_Param_DropOperation);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function HandleDrop ***************************************

// ********** Begin Class USigilSlotWidget Function InitializeSlot *********************************
struct Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics
{
	struct SigilSlotWidget_eventInitializeSlot_Parms
	{
		int32 InSlotIndex;
		USigilManagerComponent* InSigilManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Inicializa\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InSigilManager_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_InSlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InSigilManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::NewProp_InSlotIndex = { "InSlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventInitializeSlot_Parms, InSlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::NewProp_InSigilManager = { "InSigilManager", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventInitializeSlot_Parms, InSigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InSigilManager_MetaData), NewProp_InSigilManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::NewProp_InSlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::NewProp_InSigilManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "InitializeSlot", Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::SigilSlotWidget_eventInitializeSlot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::SigilSlotWidget_eventInitializeSlot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_InitializeSlot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_InitializeSlot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execInitializeSlot)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_InSlotIndex);
	P_GET_OBJECT(USigilManagerComponent,Z_Param_InSigilManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeSlot(Z_Param_InSlotIndex,Z_Param_InSigilManager);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function InitializeSlot ***********************************

// ********** Begin Class USigilSlotWidget Function IsFusionReady **********************************
struct Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics
{
	struct SigilSlotWidget_eventIsFusionReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventIsFusionReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventIsFusionReady_Parms), &Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "IsFusionReady", Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::SigilSlotWidget_eventIsFusionReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::SigilSlotWidget_eventIsFusionReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_IsFusionReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_IsFusionReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execIsFusionReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFusionReady();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function IsFusionReady ************************************

// ********** Begin Class USigilSlotWidget Function IsSlotEmpty ************************************
struct Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics
{
	struct SigilSlotWidget_eventIsSlotEmpty_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventIsSlotEmpty_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventIsSlotEmpty_Parms), &Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "IsSlotEmpty", Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::SigilSlotWidget_eventIsSlotEmpty_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::SigilSlotWidget_eventIsSlotEmpty_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execIsSlotEmpty)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSlotEmpty();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function IsSlotEmpty **************************************

// ********** Begin Class USigilSlotWidget Function IsSlotLocked ***********************************
struct Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics
{
	struct SigilSlotWidget_eventIsSlotLocked_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventIsSlotLocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventIsSlotLocked_Parms), &Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "IsSlotLocked", Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::SigilSlotWidget_eventIsSlotLocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::SigilSlotWidget_eventIsSlotLocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execIsSlotLocked)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSlotLocked();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function IsSlotLocked *************************************

// ********** Begin Class USigilSlotWidget Function OnDragEntered **********************************
struct SigilSlotWidget_eventOnDragEntered_Parms
{
	USigilDragDropOperation* DragOperation;
};
static FName NAME_USigilSlotWidget_OnDragEntered = FName(TEXT("OnDragEntered"));
void USigilSlotWidget::OnDragEntered(USigilDragDropOperation* DragOperation)
{
	SigilSlotWidget_eventOnDragEntered_Parms Parms;
	Parms.DragOperation=DragOperation;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnDragEntered);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragOperation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::NewProp_DragOperation = { "DragOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventOnDragEntered_Parms, DragOperation), Z_Construct_UClass_USigilDragDropOperation_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::NewProp_DragOperation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnDragEntered", Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnDragEntered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnDragEntered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnDragEntered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnDragEntered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnDragEntered ************************************

// ********** Begin Class USigilSlotWidget Function OnDragLeft *************************************
struct SigilSlotWidget_eventOnDragLeft_Parms
{
	USigilDragDropOperation* DragOperation;
};
static FName NAME_USigilSlotWidget_OnDragLeft = FName(TEXT("OnDragLeft"));
void USigilSlotWidget::OnDragLeft(USigilDragDropOperation* DragOperation)
{
	SigilSlotWidget_eventOnDragLeft_Parms Parms;
	Parms.DragOperation=DragOperation;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnDragLeft);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragOperation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::NewProp_DragOperation = { "DragOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventOnDragLeft_Parms, DragOperation), Z_Construct_UClass_USigilDragDropOperation_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::NewProp_DragOperation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnDragLeft", Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnDragLeft_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnDragLeft_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnDragLeft()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnDragLeft_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnDragLeft ***************************************

// ********** Begin Class USigilSlotWidget Function OnDragStarted **********************************
struct SigilSlotWidget_eventOnDragStarted_Parms
{
	USigilDragDropOperation* DragOperation;
};
static FName NAME_USigilSlotWidget_OnDragStarted = FName(TEXT("OnDragStarted"));
void USigilSlotWidget::OnDragStarted(USigilDragDropOperation* DragOperation)
{
	SigilSlotWidget_eventOnDragStarted_Parms Parms;
	Parms.DragOperation=DragOperation;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnDragStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de drag & drop\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de drag & drop" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragOperation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::NewProp_DragOperation = { "DragOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventOnDragStarted_Parms, DragOperation), Z_Construct_UClass_USigilDragDropOperation_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::NewProp_DragOperation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnDragStarted", Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnDragStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnDragStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnDragStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnDragStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnDragStarted ************************************

// ********** Begin Class USigilSlotWidget Function OnDropReceived *********************************
struct SigilSlotWidget_eventOnDropReceived_Parms
{
	USigilDragDropOperation* DragOperation;
	bool bSuccess;
};
static FName NAME_USigilSlotWidget_OnDropReceived = FName(TEXT("OnDropReceived"));
void USigilSlotWidget::OnDropReceived(USigilDragDropOperation* DragOperation, bool bSuccess)
{
	SigilSlotWidget_eventOnDropReceived_Parms Parms;
	Parms.DragOperation=DragOperation;
	Parms.bSuccess=bSuccess ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnDropReceived);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragOperation;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::NewProp_DragOperation = { "DragOperation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventOnDropReceived_Parms, DragOperation), Z_Construct_UClass_USigilDragDropOperation_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((SigilSlotWidget_eventOnDropReceived_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventOnDropReceived_Parms), &Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::NewProp_DragOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnDropReceived", Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnDropReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnDropReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnDropReceived()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnDropReceived_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnDropReceived ***********************************

// ********** Begin Class USigilSlotWidget Function OnFusionCompleted ******************************
static FName NAME_USigilSlotWidget_OnFusionCompleted = FName(TEXT("OnFusionCompleted"));
void USigilSlotWidget::OnFusionCompleted()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnFusionCompleted);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnFusionCompleted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnFusionCompleted ********************************

// ********** Begin Class USigilSlotWidget Function OnFusionStarted ********************************
static FName NAME_USigilSlotWidget_OnFusionStarted = FName(TEXT("OnFusionStarted"));
void USigilSlotWidget::OnFusionStarted()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnFusionStarted);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnFusionStarted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnFusionStarted **********************************

// ********** Begin Class USigilSlotWidget Function OnInteractionButtonClicked *********************
struct Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bind de eventos\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bind de eventos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnInteractionButtonClicked", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execOnInteractionButtonClicked)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInteractionButtonClicked();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function OnInteractionButtonClicked ***********************

// ********** Begin Class USigilSlotWidget Function OnInteractionButtonHovered *********************
struct Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnInteractionButtonHovered", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execOnInteractionButtonHovered)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInteractionButtonHovered();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function OnInteractionButtonHovered ***********************

// ********** Begin Class USigilSlotWidget Function OnInteractionButtonUnhovered *******************
struct Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnInteractionButtonUnhovered", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execOnInteractionButtonUnhovered)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInteractionButtonUnhovered();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function OnInteractionButtonUnhovered *********************

// ********** Begin Class USigilSlotWidget Function OnSigilEquipped ********************************
struct SigilSlotWidget_eventOnSigilEquipped_Parms
{
	ASigilItem* Sigil;
};
static FName NAME_USigilSlotWidget_OnSigilEquipped = FName(TEXT("OnSigilEquipped"));
void USigilSlotWidget::OnSigilEquipped(ASigilItem* Sigil)
{
	SigilSlotWidget_eventOnSigilEquipped_Parms Parms;
	Parms.Sigil=Sigil;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnSigilEquipped);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de slot\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de slot" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventOnSigilEquipped_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnSigilEquipped", Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnSigilEquipped **********************************

// ********** Begin Class USigilSlotWidget Function OnSigilUnequipped ******************************
struct SigilSlotWidget_eventOnSigilUnequipped_Parms
{
	ASigilItem* Sigil;
};
static FName NAME_USigilSlotWidget_OnSigilUnequipped = FName(TEXT("OnSigilUnequipped"));
void USigilSlotWidget::OnSigilUnequipped(ASigilItem* Sigil)
{
	SigilSlotWidget_eventOnSigilUnequipped_Parms Parms;
	Parms.Sigil=Sigil;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnSigilUnequipped);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventOnSigilUnequipped_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnSigilUnequipped", Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnSigilUnequipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnSigilUnequipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnSigilUnequipped ********************************

// ********** Begin Class USigilSlotWidget Function OnSlotClicked **********************************
static FName NAME_USigilSlotWidget_OnSlotClicked = FName(TEXT("OnSlotClicked"));
void USigilSlotWidget::OnSlotClicked()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnSlotClicked);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnSlotClicked", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnSlotClicked ************************************

// ********** Begin Class USigilSlotWidget Function OnSlotHovered **********************************
struct SigilSlotWidget_eventOnSlotHovered_Parms
{
	bool bHovered;
};
static FName NAME_USigilSlotWidget_OnSlotHovered = FName(TEXT("OnSlotHovered"));
void USigilSlotWidget::OnSlotHovered(bool bHovered)
{
	SigilSlotWidget_eventOnSlotHovered_Parms Parms;
	Parms.bHovered=bHovered ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnSlotHovered);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bHovered_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHovered;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::NewProp_bHovered_SetBit(void* Obj)
{
	((SigilSlotWidget_eventOnSlotHovered_Parms*)Obj)->bHovered = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::NewProp_bHovered = { "bHovered", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventOnSlotHovered_Parms), &Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::NewProp_bHovered_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::NewProp_bHovered,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnSlotHovered", Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::PropPointers), sizeof(SigilSlotWidget_eventOnSlotHovered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilSlotWidget_eventOnSlotHovered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnSlotHovered ************************************

// ********** Begin Class USigilSlotWidget Function OnSlotUnlocked *********************************
static FName NAME_USigilSlotWidget_OnSlotUnlocked = FName(TEXT("OnSlotUnlocked"));
void USigilSlotWidget::OnSlotUnlocked()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilSlotWidget_OnSlotUnlocked);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "OnSlotUnlocked", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilSlotWidget Function OnSlotUnlocked ***********************************

// ********** Begin Class USigilSlotWidget Function PlaySlotAnimation ******************************
struct Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics
{
	struct SigilSlotWidget_eventPlaySlotAnimation_Parms
	{
		ESigilSlotState FromState;
		ESigilSlotState ToState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FromState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FromState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ToState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ToState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_FromState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_FromState = { "FromState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventPlaySlotAnimation_Parms, FromState), Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_ToState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_ToState = { "ToState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventPlaySlotAnimation_Parms, ToState), Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_FromState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_FromState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_ToState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::NewProp_ToState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "PlaySlotAnimation", Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::SigilSlotWidget_eventPlaySlotAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::SigilSlotWidget_eventPlaySlotAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execPlaySlotAnimation)
{
	P_GET_ENUM(ESigilSlotState,Z_Param_FromState);
	P_GET_ENUM(ESigilSlotState,Z_Param_ToState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlaySlotAnimation(ESigilSlotState(Z_Param_FromState),ESigilSlotState(Z_Param_ToState));
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function PlaySlotAnimation ********************************

// ********** Begin Class USigilSlotWidget Function PlaySlotSound **********************************
struct Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics
{
	struct SigilSlotWidget_eventPlaySlotSound_Parms
	{
		ESigilSlotState SoundState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SoundState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SoundState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::NewProp_SoundState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::NewProp_SoundState = { "SoundState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventPlaySlotSound_Parms, SoundState), Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::NewProp_SoundState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::NewProp_SoundState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "PlaySlotSound", Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::SigilSlotWidget_eventPlaySlotSound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::SigilSlotWidget_eventPlaySlotSound_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execPlaySlotSound)
{
	P_GET_ENUM(ESigilSlotState,Z_Param_SoundState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlaySlotSound(ESigilSlotState(Z_Param_SoundState));
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function PlaySlotSound ************************************

// ********** Begin Class USigilSlotWidget Function PlayVFXEffect **********************************
struct Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics
{
	struct SigilSlotWidget_eventPlayVFXEffect_Parms
	{
		ESigilSlotState EffectState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// VFX\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::NewProp_EffectState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::NewProp_EffectState = { "EffectState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventPlayVFXEffect_Parms, EffectState), Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::NewProp_EffectState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::NewProp_EffectState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "PlayVFXEffect", Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::SigilSlotWidget_eventPlayVFXEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::SigilSlotWidget_eventPlayVFXEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execPlayVFXEffect)
{
	P_GET_ENUM(ESigilSlotState,Z_Param_EffectState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayVFXEffect(ESigilSlotState(Z_Param_EffectState));
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function PlayVFXEffect ************************************

// ********** Begin Class USigilSlotWidget Function SetDragHighlight *******************************
struct Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics
{
	struct SigilSlotWidget_eventSetDragHighlight_Parms
	{
		bool bHighlighted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Drag Drop" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bHighlighted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHighlighted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::NewProp_bHighlighted_SetBit(void* Obj)
{
	((SigilSlotWidget_eventSetDragHighlight_Parms*)Obj)->bHighlighted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::NewProp_bHighlighted = { "bHighlighted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventSetDragHighlight_Parms), &Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::NewProp_bHighlighted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::NewProp_bHighlighted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "SetDragHighlight", Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::SigilSlotWidget_eventSetDragHighlight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::SigilSlotWidget_eventSetDragHighlight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execSetDragHighlight)
{
	P_GET_UBOOL(Z_Param_bHighlighted);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDragHighlight(Z_Param_bHighlighted);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function SetDragHighlight *********************************

// ********** Begin Class USigilSlotWidget Function StopVFXEffect **********************************
struct Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "StopVFXEffect", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execStopVFXEffect)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopVFXEffect();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function StopVFXEffect ************************************

// ********** Begin Class USigilSlotWidget Function UnequipSigil ***********************************
struct Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics
{
	struct SigilSlotWidget_eventUnequipSigil_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilSlotWidget_eventUnequipSigil_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilSlotWidget_eventUnequipSigil_Parms), &Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "UnequipSigil", Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::SigilSlotWidget_eventUnequipSigil_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::SigilSlotWidget_eventUnequipSigil_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_UnequipSigil()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_UnequipSigil_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execUnequipSigil)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnequipSigil();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function UnequipSigil *************************************

// ********** Begin Class USigilSlotWidget Function UpdateFusionProgress ***************************
struct Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics
{
	struct SigilSlotWidget_eventUpdateFusionProgress_Parms
	{
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventUpdateFusionProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "UpdateFusionProgress", Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::SigilSlotWidget_eventUpdateFusionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::SigilSlotWidget_eventUpdateFusionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execUpdateFusionProgress)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFusionProgress(Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function UpdateFusionProgress *****************************

// ********** Begin Class USigilSlotWidget Function UpdateRarityIndicator **************************
struct Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "UpdateRarityIndicator", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execUpdateRarityIndicator)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRarityIndicator();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function UpdateRarityIndicator ****************************

// ********** Begin Class USigilSlotWidget Function UpdateSlotState ********************************
struct Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics
{
	struct SigilSlotWidget_eventUpdateSlotState_Parms
	{
		ESigilSlotState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar estado\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar estado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventUpdateSlotState_Parms, NewState), Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(0, nullptr) }; // 1671570546
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "UpdateSlotState", Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::SigilSlotWidget_eventUpdateSlotState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::SigilSlotWidget_eventUpdateSlotState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execUpdateSlotState)
{
	P_GET_ENUM(ESigilSlotState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSlotState(ESigilSlotState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function UpdateSlotState **********************************

// ********** Begin Class USigilSlotWidget Function UpdateTimerDisplay *****************************
struct Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics
{
	struct SigilSlotWidget_eventUpdateTimerDisplay_Parms
	{
		float RemainingTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RemainingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::NewProp_RemainingTime = { "RemainingTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilSlotWidget_eventUpdateTimerDisplay_Parms, RemainingTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::NewProp_RemainingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "UpdateTimerDisplay", Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::SigilSlotWidget_eventUpdateTimerDisplay_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::SigilSlotWidget_eventUpdateTimerDisplay_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execUpdateTimerDisplay)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_RemainingTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTimerDisplay(Z_Param_RemainingTime);
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function UpdateTimerDisplay *******************************

// ********** Begin Class USigilSlotWidget Function UpdateVisualState ******************************
struct Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar visual\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar visual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilSlotWidget, nullptr, "UpdateVisualState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilSlotWidget::execUpdateVisualState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateVisualState();
	P_NATIVE_END;
}
// ********** End Class USigilSlotWidget Function UpdateVisualState ********************************

// ********** Begin Class USigilSlotWidget *********************************************************
void USigilSlotWidget::StaticRegisterNativesUSigilSlotWidget()
{
	UClass* Class = USigilSlotWidget::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanAcceptSigil", &USigilSlotWidget::execCanAcceptSigil },
		{ "CreateDragDropOperation", &USigilSlotWidget::execCreateDragDropOperation },
		{ "EquipSigil", &USigilSlotWidget::execEquipSigil },
		{ "HandleDrop", &USigilSlotWidget::execHandleDrop },
		{ "InitializeSlot", &USigilSlotWidget::execInitializeSlot },
		{ "IsFusionReady", &USigilSlotWidget::execIsFusionReady },
		{ "IsSlotEmpty", &USigilSlotWidget::execIsSlotEmpty },
		{ "IsSlotLocked", &USigilSlotWidget::execIsSlotLocked },
		{ "OnInteractionButtonClicked", &USigilSlotWidget::execOnInteractionButtonClicked },
		{ "OnInteractionButtonHovered", &USigilSlotWidget::execOnInteractionButtonHovered },
		{ "OnInteractionButtonUnhovered", &USigilSlotWidget::execOnInteractionButtonUnhovered },
		{ "PlaySlotAnimation", &USigilSlotWidget::execPlaySlotAnimation },
		{ "PlaySlotSound", &USigilSlotWidget::execPlaySlotSound },
		{ "PlayVFXEffect", &USigilSlotWidget::execPlayVFXEffect },
		{ "SetDragHighlight", &USigilSlotWidget::execSetDragHighlight },
		{ "StopVFXEffect", &USigilSlotWidget::execStopVFXEffect },
		{ "UnequipSigil", &USigilSlotWidget::execUnequipSigil },
		{ "UpdateFusionProgress", &USigilSlotWidget::execUpdateFusionProgress },
		{ "UpdateRarityIndicator", &USigilSlotWidget::execUpdateRarityIndicator },
		{ "UpdateSlotState", &USigilSlotWidget::execUpdateSlotState },
		{ "UpdateTimerDisplay", &USigilSlotWidget::execUpdateTimerDisplay },
		{ "UpdateVisualState", &USigilSlotWidget::execUpdateVisualState },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilSlotWidget;
UClass* USigilSlotWidget::GetPrivateStaticClass()
{
	using TClass = USigilSlotWidget;
	if (!Z_Registration_Info_UClass_USigilSlotWidget.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilSlotWidget"),
			Z_Registration_Info_UClass_USigilSlotWidget.InnerSingleton,
			StaticRegisterNativesUSigilSlotWidget,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilSlotWidget.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilSlotWidget_NoRegister()
{
	return USigilSlotWidget::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilSlotWidget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Widget individual para slot de sigilo\n */" },
#endif
		{ "IncludePath", "UI/SigilWidgets.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget individual para slot de sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente principal do slot\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente principal do slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Imagem do sigilo\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Imagem do sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectOverlay_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Overlay para efeitos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Overlay para efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionProgressBar_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Barra de progresso de fus\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Barra de progresso de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimerText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texto de timer\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texto de timer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityIndicator_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Indicador de raridade\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indicador de raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bot\xc3\xa3o invis\xc3\xadvel para intera\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bot\xc3\xa3o invis\xc3\xadvel para intera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LockIndicator_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Indicador de bloqueio\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indicador de bloqueio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotIndex_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xc3\x8dndice do slot\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x8dndice do slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigil_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sigilo equipado\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigilo equipado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estado atual do slot\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualConfig_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xa3o visual\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilManager_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Refer\xc3\xaancia ao manager\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao manager" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXComponent_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente VFX\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SlotBorder;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EffectOverlay;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionProgressBar;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TimerText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RarityIndicator;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LockIndicator;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EquippedSigil;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VFXComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilSlotWidget_CanAcceptSigil, "CanAcceptSigil" }, // 537151433
		{ &Z_Construct_UFunction_USigilSlotWidget_CreateDragDropOperation, "CreateDragDropOperation" }, // 2175372146
		{ &Z_Construct_UFunction_USigilSlotWidget_EquipSigil, "EquipSigil" }, // 1200612296
		{ &Z_Construct_UFunction_USigilSlotWidget_HandleDrop, "HandleDrop" }, // 856623909
		{ &Z_Construct_UFunction_USigilSlotWidget_InitializeSlot, "InitializeSlot" }, // 1690120007
		{ &Z_Construct_UFunction_USigilSlotWidget_IsFusionReady, "IsFusionReady" }, // 389552737
		{ &Z_Construct_UFunction_USigilSlotWidget_IsSlotEmpty, "IsSlotEmpty" }, // 2364025764
		{ &Z_Construct_UFunction_USigilSlotWidget_IsSlotLocked, "IsSlotLocked" }, // 2805929903
		{ &Z_Construct_UFunction_USigilSlotWidget_OnDragEntered, "OnDragEntered" }, // 1259626944
		{ &Z_Construct_UFunction_USigilSlotWidget_OnDragLeft, "OnDragLeft" }, // 3638254852
		{ &Z_Construct_UFunction_USigilSlotWidget_OnDragStarted, "OnDragStarted" }, // 1985868631
		{ &Z_Construct_UFunction_USigilSlotWidget_OnDropReceived, "OnDropReceived" }, // 957065845
		{ &Z_Construct_UFunction_USigilSlotWidget_OnFusionCompleted, "OnFusionCompleted" }, // 814469097
		{ &Z_Construct_UFunction_USigilSlotWidget_OnFusionStarted, "OnFusionStarted" }, // 2780783802
		{ &Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonClicked, "OnInteractionButtonClicked" }, // 546876797
		{ &Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonHovered, "OnInteractionButtonHovered" }, // 1539742320
		{ &Z_Construct_UFunction_USigilSlotWidget_OnInteractionButtonUnhovered, "OnInteractionButtonUnhovered" }, // 69170556
		{ &Z_Construct_UFunction_USigilSlotWidget_OnSigilEquipped, "OnSigilEquipped" }, // 1047116270
		{ &Z_Construct_UFunction_USigilSlotWidget_OnSigilUnequipped, "OnSigilUnequipped" }, // 2548464184
		{ &Z_Construct_UFunction_USigilSlotWidget_OnSlotClicked, "OnSlotClicked" }, // 934062663
		{ &Z_Construct_UFunction_USigilSlotWidget_OnSlotHovered, "OnSlotHovered" }, // 3092520456
		{ &Z_Construct_UFunction_USigilSlotWidget_OnSlotUnlocked, "OnSlotUnlocked" }, // 1994106265
		{ &Z_Construct_UFunction_USigilSlotWidget_PlaySlotAnimation, "PlaySlotAnimation" }, // 2839164196
		{ &Z_Construct_UFunction_USigilSlotWidget_PlaySlotSound, "PlaySlotSound" }, // 553086280
		{ &Z_Construct_UFunction_USigilSlotWidget_PlayVFXEffect, "PlayVFXEffect" }, // 2589588017
		{ &Z_Construct_UFunction_USigilSlotWidget_SetDragHighlight, "SetDragHighlight" }, // 3477948766
		{ &Z_Construct_UFunction_USigilSlotWidget_StopVFXEffect, "StopVFXEffect" }, // 4132140116
		{ &Z_Construct_UFunction_USigilSlotWidget_UnequipSigil, "UnequipSigil" }, // 3490813073
		{ &Z_Construct_UFunction_USigilSlotWidget_UpdateFusionProgress, "UpdateFusionProgress" }, // 2099665267
		{ &Z_Construct_UFunction_USigilSlotWidget_UpdateRarityIndicator, "UpdateRarityIndicator" }, // 3103695655
		{ &Z_Construct_UFunction_USigilSlotWidget_UpdateSlotState, "UpdateSlotState" }, // 3426119974
		{ &Z_Construct_UFunction_USigilSlotWidget_UpdateTimerDisplay, "UpdateTimerDisplay" }, // 1948010385
		{ &Z_Construct_UFunction_USigilSlotWidget_UpdateVisualState, "UpdateVisualState" }, // 2154980776
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilSlotWidget>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SlotBorder = { "SlotBorder", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, SlotBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotBorder_MetaData), NewProp_SlotBorder_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SigilImage = { "SigilImage", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, SigilImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilImage_MetaData), NewProp_SigilImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_EffectOverlay = { "EffectOverlay", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, EffectOverlay), Z_Construct_UClass_UOverlay_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectOverlay_MetaData), NewProp_EffectOverlay_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_FusionProgressBar = { "FusionProgressBar", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, FusionProgressBar), Z_Construct_UClass_UProgressBar_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionProgressBar_MetaData), NewProp_FusionProgressBar_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_TimerText = { "TimerText", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, TimerText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimerText_MetaData), NewProp_TimerText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_RarityIndicator = { "RarityIndicator", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, RarityIndicator), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityIndicator_MetaData), NewProp_RarityIndicator_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_InteractionButton = { "InteractionButton", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, InteractionButton), Z_Construct_UClass_UButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionButton_MetaData), NewProp_InteractionButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_LockIndicator = { "LockIndicator", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, LockIndicator), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LockIndicator_MetaData), NewProp_LockIndicator_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, SlotIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotIndex_MetaData), NewProp_SlotIndex_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_EquippedSigil = { "EquippedSigil", nullptr, (EPropertyFlags)0x0114000000000004, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, EquippedSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigil_MetaData), NewProp_EquippedSigil_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, CurrentState), Z_Construct_UEnum_AURACRON_ESigilSlotState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1671570546
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_VisualConfig = { "VisualConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, VisualConfig), Z_Construct_UScriptStruct_FSigilSlotVisualConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualConfig_MetaData), NewProp_VisualConfig_MetaData) }; // 3185679346
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SigilManager = { "SigilManager", nullptr, (EPropertyFlags)0x011400000008000c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, SigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilManager_MetaData), NewProp_SigilManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_VFXComponent = { "VFXComponent", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSlotWidget, VFXComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXComponent_MetaData), NewProp_VFXComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilSlotWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SlotBorder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SigilImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_EffectOverlay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_FusionProgressBar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_TimerText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_RarityIndicator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_InteractionButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_LockIndicator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_EquippedSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_VisualConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_SigilManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSlotWidget_Statics::NewProp_VFXComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSlotWidget_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilSlotWidget_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSlotWidget_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilSlotWidget_Statics::ClassParams = {
	&USigilSlotWidget::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilSlotWidget_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilSlotWidget_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSlotWidget_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilSlotWidget_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilSlotWidget()
{
	if (!Z_Registration_Info_UClass_USigilSlotWidget.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilSlotWidget.OuterSingleton, Z_Construct_UClass_USigilSlotWidget_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilSlotWidget.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilSlotWidget);
USigilSlotWidget::~USigilSlotWidget() {}
// ********** End Class USigilSlotWidget ***********************************************************

// ********** Begin Class USigilInventoryWidget Function CreateSlots *******************************
struct Z_Construct_UFunction_USigilInventoryWidget_CreateSlots_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerenciamento de slots\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciamento de slots" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_CreateSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "CreateSlots", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_CreateSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_CreateSlots_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilInventoryWidget_CreateSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_CreateSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execCreateSlots)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateSlots();
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function CreateSlots *********************************

// ********** Begin Class USigilInventoryWidget Function GetAllSlotWidgets *************************
struct Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics
{
	struct SigilInventoryWidget_eventGetAllSlotWidgets_Parms
	{
		TArray<USigilSlotWidget*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USigilSlotWidget_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000588, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventGetAllSlotWidgets_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "GetAllSlotWidgets", Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::SigilInventoryWidget_eventGetAllSlotWidgets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::SigilInventoryWidget_eventGetAllSlotWidgets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execGetAllSlotWidgets)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<USigilSlotWidget*>*)Z_Param__Result=P_THIS->GetAllSlotWidgets();
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function GetAllSlotWidgets ***************************

// ********** Begin Class USigilInventoryWidget Function GetSlotWidget *****************************
struct Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics
{
	struct SigilInventoryWidget_eventGetSlotWidget_Parms
	{
		int32 SlotIndex;
		USigilSlotWidget* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter slots\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter slots" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventGetSlotWidget_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventGetSlotWidget_Parms, ReturnValue), Z_Construct_UClass_USigilSlotWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "GetSlotWidget", Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::SigilInventoryWidget_eventGetSlotWidget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::SigilInventoryWidget_eventGetSlotWidget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execGetSlotWidget)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(USigilSlotWidget**)Z_Param__Result=P_THIS->GetSlotWidget(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function GetSlotWidget *******************************

// ********** Begin Class USigilInventoryWidget Function InitializeInventory ***********************
struct Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics
{
	struct SigilInventoryWidget_eventInitializeInventory_Parms
	{
		USigilManagerComponent* InSigilManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Inicializa\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InSigilManager_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InSigilManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::NewProp_InSigilManager = { "InSigilManager", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventInitializeInventory_Parms, InSigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InSigilManager_MetaData), NewProp_InSigilManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::NewProp_InSigilManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "InitializeInventory", Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::SigilInventoryWidget_eventInitializeInventory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::SigilInventoryWidget_eventInitializeInventory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execInitializeInventory)
{
	P_GET_OBJECT(USigilManagerComponent,Z_Param_InSigilManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeInventory(Z_Param_InSigilManager);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function InitializeInventory *************************

// ********** Begin Class USigilInventoryWidget Function OnFusionCompleted *************************
struct Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics
{
	struct SigilInventoryWidget_eventOnFusionCompleted_Parms
	{
		int32 SlotIndex;
		ASigilItem* FusedSigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusedSigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnFusionCompleted_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::NewProp_FusedSigil = { "FusedSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnFusionCompleted_Parms, FusedSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::NewProp_FusedSigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnFusionCompleted", Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::SigilInventoryWidget_eventOnFusionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::SigilInventoryWidget_eventOnFusionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnFusionCompleted)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_OBJECT(ASigilItem,Z_Param_FusedSigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnFusionCompleted(Z_Param_SlotIndex,Z_Param_FusedSigil);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnFusionCompleted ***************************

// ********** Begin Class USigilInventoryWidget Function OnInventoryInitialized ********************
static FName NAME_USigilInventoryWidget_OnInventoryInitialized = FName(TEXT("OnInventoryInitialized"));
void USigilInventoryWidget::OnInventoryInitialized()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilInventoryWidget_OnInventoryInitialized);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// EVENTOS BLUEPRINT\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "EVENTOS BLUEPRINT" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnInventoryInitialized", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilInventoryWidget Function OnInventoryInitialized **********************

// ********** Begin Class USigilInventoryWidget Function OnPlayerLevelChanged **********************
struct Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics
{
	struct SigilInventoryWidget_eventOnPlayerLevelChanged_Parms
	{
		int32 NewLevel;
		int32 OldLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Callbacks adicionais para sistema completo\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callbacks adicionais para sistema completo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OldLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnPlayerLevelChanged_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::NewProp_OldLevel = { "OldLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnPlayerLevelChanged_Parms, OldLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::NewProp_NewLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::NewProp_OldLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnPlayerLevelChanged", Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::SigilInventoryWidget_eventOnPlayerLevelChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::SigilInventoryWidget_eventOnPlayerLevelChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnPlayerLevelChanged)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewLevel);
	P_GET_PROPERTY(FIntProperty,Z_Param_OldLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerLevelChanged(Z_Param_NewLevel,Z_Param_OldLevel);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnPlayerLevelChanged ************************

// ********** Begin Class USigilInventoryWidget Function OnReforgeAvailable ************************
struct SigilInventoryWidget_eventOnReforgeAvailable_Parms
{
	bool bAvailable;
};
static FName NAME_USigilInventoryWidget_OnReforgeAvailable = FName(TEXT("OnReforgeAvailable"));
void USigilInventoryWidget::OnReforgeAvailable(bool bAvailable)
{
	SigilInventoryWidget_eventOnReforgeAvailable_Parms Parms;
	Parms.bAvailable=bAvailable ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_USigilInventoryWidget_OnReforgeAvailable);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bAvailable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvailable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::NewProp_bAvailable_SetBit(void* Obj)
{
	((SigilInventoryWidget_eventOnReforgeAvailable_Parms*)Obj)->bAvailable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::NewProp_bAvailable = { "bAvailable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilInventoryWidget_eventOnReforgeAvailable_Parms), &Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::NewProp_bAvailable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::NewProp_bAvailable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnReforgeAvailable", Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::PropPointers), sizeof(SigilInventoryWidget_eventOnReforgeAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilInventoryWidget_eventOnReforgeAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilInventoryWidget Function OnReforgeAvailable **************************

// ********** Begin Class USigilInventoryWidget Function OnReforgeButtonClicked ********************
struct Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bind de eventos\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bind de eventos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnReforgeButtonClicked", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnReforgeButtonClicked)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnReforgeButtonClicked();
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnReforgeButtonClicked **********************

// ********** Begin Class USigilInventoryWidget Function OnSigilEquipped ***************************
struct Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics
{
	struct SigilInventoryWidget_eventOnSigilEquipped_Parms
	{
		int32 SlotIndex;
		ASigilItem* Sigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Callbacks do manager\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callbacks do manager" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilEquipped_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilEquipped_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnSigilEquipped", Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::SigilInventoryWidget_eventOnSigilEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::SigilInventoryWidget_eventOnSigilEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnSigilEquipped)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilEquipped(Z_Param_SlotIndex,Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnSigilEquipped *****************************

// ********** Begin Class USigilInventoryWidget Function OnSigilExperienceGained *******************
struct Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics
{
	struct SigilInventoryWidget_eventOnSigilExperienceGained_Parms
	{
		int32 SlotIndex;
		float ExperienceGained;
		float TotalExperience;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperienceGained;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalExperience;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilExperienceGained_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::NewProp_ExperienceGained = { "ExperienceGained", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilExperienceGained_Parms, ExperienceGained), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::NewProp_TotalExperience = { "TotalExperience", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilExperienceGained_Parms, TotalExperience), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::NewProp_ExperienceGained,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::NewProp_TotalExperience,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnSigilExperienceGained", Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::SigilInventoryWidget_eventOnSigilExperienceGained_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::SigilInventoryWidget_eventOnSigilExperienceGained_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnSigilExperienceGained)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExperienceGained);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TotalExperience);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilExperienceGained(Z_Param_SlotIndex,Z_Param_ExperienceGained,Z_Param_TotalExperience);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnSigilExperienceGained *********************

// ********** Begin Class USigilInventoryWidget Function OnSigilSubtypeChanged *********************
struct Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics
{
	struct SigilInventoryWidget_eventOnSigilSubtypeChanged_Parms
	{
		int32 SlotIndex;
		ESigilSubType NewSubtype;
		ESigilSubType OldSubtype;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewSubtype_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewSubtype;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldSubtype_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldSubtype;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilSubtypeChanged_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_NewSubtype_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_NewSubtype = { "NewSubtype", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilSubtypeChanged_Parms, NewSubtype), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_OldSubtype_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_OldSubtype = { "OldSubtype", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilSubtypeChanged_Parms, OldSubtype), Z_Construct_UEnum_AURACRON_ESigilSubType, METADATA_PARAMS(0, nullptr) }; // 3161995902
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_NewSubtype_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_NewSubtype,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_OldSubtype_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::NewProp_OldSubtype,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnSigilSubtypeChanged", Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::SigilInventoryWidget_eventOnSigilSubtypeChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::SigilInventoryWidget_eventOnSigilSubtypeChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnSigilSubtypeChanged)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_ENUM(ESigilSubType,Z_Param_NewSubtype);
	P_GET_ENUM(ESigilSubType,Z_Param_OldSubtype);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilSubtypeChanged(Z_Param_SlotIndex,ESigilSubType(Z_Param_NewSubtype),ESigilSubType(Z_Param_OldSubtype));
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnSigilSubtypeChanged ***********************

// ********** Begin Class USigilInventoryWidget Function OnSigilUnequipped *************************
struct Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics
{
	struct SigilInventoryWidget_eventOnSigilUnequipped_Parms
	{
		int32 SlotIndex;
		ASigilItem* Sigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilUnequipped_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::NewProp_Sigil = { "Sigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSigilUnequipped_Parms, Sigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::NewProp_SlotIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::NewProp_Sigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnSigilUnequipped", Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::SigilInventoryWidget_eventOnSigilUnequipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::SigilInventoryWidget_eventOnSigilUnequipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnSigilUnequipped)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_GET_OBJECT(ASigilItem,Z_Param_Sigil);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilUnequipped(Z_Param_SlotIndex,Z_Param_Sigil);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnSigilUnequipped ***************************

// ********** Begin Class USigilInventoryWidget Function OnSlotUnlocked ****************************
struct SigilInventoryWidget_eventOnSlotUnlocked_Parms
{
	int32 SlotIndex;
};
static FName NAME_USigilInventoryWidget_OnSlotUnlocked = FName(TEXT("OnSlotUnlocked"));
void USigilInventoryWidget::OnSlotUnlocked(int32 SlotIndex)
{
	SigilInventoryWidget_eventOnSlotUnlocked_Parms Parms;
	Parms.SlotIndex=SlotIndex;
	UFunction* Func = FindFunctionChecked(NAME_USigilInventoryWidget_OnSlotUnlocked);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSlotUnlocked_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnSlotUnlocked", Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::PropPointers), sizeof(SigilInventoryWidget_eventOnSlotUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilInventoryWidget_eventOnSlotUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilInventoryWidget Function OnSlotUnlocked ******************************

// ********** Begin Class USigilInventoryWidget Function OnSlotUnlockedCallback ********************
struct Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics
{
	struct SigilInventoryWidget_eventOnSlotUnlockedCallback_Parms
	{
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnSlotUnlockedCallback_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnSlotUnlockedCallback", Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::SigilInventoryWidget_eventOnSlotUnlockedCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::SigilInventoryWidget_eventOnSlotUnlockedCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnSlotUnlockedCallback)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSlotUnlockedCallback(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnSlotUnlockedCallback **********************

// ********** Begin Class USigilInventoryWidget Function OnStatsChanged ****************************
struct Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics
{
	struct SigilInventoryWidget_eventOnStatsChanged_Parms
	{
		FSigilSystemStats NewStats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewStats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::NewProp_NewStats = { "NewStats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnStatsChanged_Parms, NewStats), Z_Construct_UScriptStruct_FSigilSystemStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewStats_MetaData), NewProp_NewStats_MetaData) }; // 2041897055
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::NewProp_NewStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnStatsChanged", Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::SigilInventoryWidget_eventOnStatsChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::SigilInventoryWidget_eventOnStatsChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execOnStatsChanged)
{
	P_GET_STRUCT_REF(FSigilSystemStats,Z_Param_Out_NewStats);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnStatsChanged(Z_Param_Out_NewStats);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function OnStatsChanged ******************************

// ********** Begin Class USigilInventoryWidget Function OnStatsUpdated ****************************
struct SigilInventoryWidget_eventOnStatsUpdated_Parms
{
	FSigilSystemStats NewStats;
};
static FName NAME_USigilInventoryWidget_OnStatsUpdated = FName(TEXT("OnStatsUpdated"));
void USigilInventoryWidget::OnStatsUpdated(FSigilSystemStats const& NewStats)
{
	SigilInventoryWidget_eventOnStatsUpdated_Parms Parms;
	Parms.NewStats=NewStats;
	UFunction* Func = FindFunctionChecked(NAME_USigilInventoryWidget_OnStatsUpdated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewStats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::NewProp_NewStats = { "NewStats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventOnStatsUpdated_Parms, NewStats), Z_Construct_UScriptStruct_FSigilSystemStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewStats_MetaData), NewProp_NewStats_MetaData) }; // 2041897055
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::NewProp_NewStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "OnStatsUpdated", Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::PropPointers), sizeof(SigilInventoryWidget_eventOnStatsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilInventoryWidget_eventOnStatsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilInventoryWidget Function OnStatsUpdated ******************************

// ********** Begin Class USigilInventoryWidget Function UnlockSlot ********************************
struct Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics
{
	struct SigilInventoryWidget_eventUnlockSlot_Parms
	{
		int32 SlotIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SlotIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::NewProp_SlotIndex = { "SlotIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilInventoryWidget_eventUnlockSlot_Parms, SlotIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::NewProp_SlotIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "UnlockSlot", Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::SigilInventoryWidget_eventUnlockSlot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::SigilInventoryWidget_eventUnlockSlot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execUnlockSlot)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SlotIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockSlot(Z_Param_SlotIndex);
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function UnlockSlot **********************************

// ********** Begin Class USigilInventoryWidget Function UpdateAllSlots ****************************
struct Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "UpdateAllSlots", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execUpdateAllSlots)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAllSlots();
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function UpdateAllSlots ******************************

// ********** Begin Class USigilInventoryWidget Function UpdateReforgeButton ***********************
struct Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "UpdateReforgeButton", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execUpdateReforgeButton)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateReforgeButton();
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function UpdateReforgeButton *************************

// ********** Begin Class USigilInventoryWidget Function UpdateStats *******************************
struct Z_Construct_UFunction_USigilInventoryWidget_UpdateStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualiza\xc3\xa7\xc3\xa3o de dados\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza\xc3\xa7\xc3\xa3o de dados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilInventoryWidget_UpdateStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilInventoryWidget, nullptr, "UpdateStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilInventoryWidget_UpdateStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilInventoryWidget_UpdateStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilInventoryWidget_UpdateStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilInventoryWidget_UpdateStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilInventoryWidget::execUpdateStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStats();
	P_NATIVE_END;
}
// ********** End Class USigilInventoryWidget Function UpdateStats *********************************

// ********** Begin Class USigilInventoryWidget ****************************************************
void USigilInventoryWidget::StaticRegisterNativesUSigilInventoryWidget()
{
	UClass* Class = USigilInventoryWidget::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateSlots", &USigilInventoryWidget::execCreateSlots },
		{ "GetAllSlotWidgets", &USigilInventoryWidget::execGetAllSlotWidgets },
		{ "GetSlotWidget", &USigilInventoryWidget::execGetSlotWidget },
		{ "InitializeInventory", &USigilInventoryWidget::execInitializeInventory },
		{ "OnFusionCompleted", &USigilInventoryWidget::execOnFusionCompleted },
		{ "OnPlayerLevelChanged", &USigilInventoryWidget::execOnPlayerLevelChanged },
		{ "OnReforgeButtonClicked", &USigilInventoryWidget::execOnReforgeButtonClicked },
		{ "OnSigilEquipped", &USigilInventoryWidget::execOnSigilEquipped },
		{ "OnSigilExperienceGained", &USigilInventoryWidget::execOnSigilExperienceGained },
		{ "OnSigilSubtypeChanged", &USigilInventoryWidget::execOnSigilSubtypeChanged },
		{ "OnSigilUnequipped", &USigilInventoryWidget::execOnSigilUnequipped },
		{ "OnSlotUnlockedCallback", &USigilInventoryWidget::execOnSlotUnlockedCallback },
		{ "OnStatsChanged", &USigilInventoryWidget::execOnStatsChanged },
		{ "UnlockSlot", &USigilInventoryWidget::execUnlockSlot },
		{ "UpdateAllSlots", &USigilInventoryWidget::execUpdateAllSlots },
		{ "UpdateReforgeButton", &USigilInventoryWidget::execUpdateReforgeButton },
		{ "UpdateStats", &USigilInventoryWidget::execUpdateStats },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilInventoryWidget;
UClass* USigilInventoryWidget::GetPrivateStaticClass()
{
	using TClass = USigilInventoryWidget;
	if (!Z_Registration_Info_UClass_USigilInventoryWidget.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilInventoryWidget"),
			Z_Registration_Info_UClass_USigilInventoryWidget.InnerSingleton,
			StaticRegisterNativesUSigilInventoryWidget,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilInventoryWidget.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilInventoryWidget_NoRegister()
{
	return USigilInventoryWidget::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilInventoryWidget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Widget principal do invent\xc3\xa1rio de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "UI/SigilWidgets.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget principal do invent\xc3\xa1rio de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainCanvas_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Container principal\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Container principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotsContainer_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Container dos slots\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Container dos slots" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatsPanel_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Painel de estat\xc3\xadsticas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Painel de estat\xc3\xadsticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPowerText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texto de poder total\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texto de poder total" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedSigilsText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texto de s\xc3\xadgilos equipados\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texto de s\xc3\xadgilos equipados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bot\xc3\xa3o de reforge\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bot\xc3\xa3o de reforge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeCooldownText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texto de cooldown de reforge\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texto de cooldown de reforge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilSlots_MetaData[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Slots de s\xc3\xadgilos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slots de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlots_MetaData[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// N\xc3\xbamero m\xc3\xa1ximo de slots\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de slots" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilManager_MetaData[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Refer\xc3\xaancia ao manager\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao manager" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotWidgetClass_MetaData[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Classe do slot widget\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe do slot widget" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MainCanvas;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SlotsContainer;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StatsPanel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TotalPowerText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EquippedSigilsText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReforgeButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReforgeCooldownText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilSlots_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SigilSlots;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSlots;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilManager;
	static const UECodeGen_Private::FClassPropertyParams NewProp_SlotWidgetClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilInventoryWidget_CreateSlots, "CreateSlots" }, // 2664359010
		{ &Z_Construct_UFunction_USigilInventoryWidget_GetAllSlotWidgets, "GetAllSlotWidgets" }, // 1160725826
		{ &Z_Construct_UFunction_USigilInventoryWidget_GetSlotWidget, "GetSlotWidget" }, // 213722339
		{ &Z_Construct_UFunction_USigilInventoryWidget_InitializeInventory, "InitializeInventory" }, // 218329383
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnFusionCompleted, "OnFusionCompleted" }, // 2626775786
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnInventoryInitialized, "OnInventoryInitialized" }, // 675670083
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnPlayerLevelChanged, "OnPlayerLevelChanged" }, // 1712357222
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnReforgeAvailable, "OnReforgeAvailable" }, // 1661071703
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnReforgeButtonClicked, "OnReforgeButtonClicked" }, // 1545038276
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnSigilEquipped, "OnSigilEquipped" }, // 1763944341
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnSigilExperienceGained, "OnSigilExperienceGained" }, // 406329953
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnSigilSubtypeChanged, "OnSigilSubtypeChanged" }, // 4176295562
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnSigilUnequipped, "OnSigilUnequipped" }, // 2211448364
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlocked, "OnSlotUnlocked" }, // 1094351848
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnSlotUnlockedCallback, "OnSlotUnlockedCallback" }, // 1522290645
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnStatsChanged, "OnStatsChanged" }, // 3404146487
		{ &Z_Construct_UFunction_USigilInventoryWidget_OnStatsUpdated, "OnStatsUpdated" }, // 1664027695
		{ &Z_Construct_UFunction_USigilInventoryWidget_UnlockSlot, "UnlockSlot" }, // 4235965328
		{ &Z_Construct_UFunction_USigilInventoryWidget_UpdateAllSlots, "UpdateAllSlots" }, // 2274375493
		{ &Z_Construct_UFunction_USigilInventoryWidget_UpdateReforgeButton, "UpdateReforgeButton" }, // 1690714081
		{ &Z_Construct_UFunction_USigilInventoryWidget_UpdateStats, "UpdateStats" }, // 3482899656
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilInventoryWidget>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_MainCanvas = { "MainCanvas", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, MainCanvas), Z_Construct_UClass_UCanvasPanel_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainCanvas_MetaData), NewProp_MainCanvas_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SlotsContainer = { "SlotsContainer", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, SlotsContainer), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotsContainer_MetaData), NewProp_SlotsContainer_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_StatsPanel = { "StatsPanel", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, StatsPanel), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatsPanel_MetaData), NewProp_StatsPanel_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_TotalPowerText = { "TotalPowerText", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, TotalPowerText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPowerText_MetaData), NewProp_TotalPowerText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_EquippedSigilsText = { "EquippedSigilsText", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, EquippedSigilsText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedSigilsText_MetaData), NewProp_EquippedSigilsText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_ReforgeButton = { "ReforgeButton", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, ReforgeButton), Z_Construct_UClass_UButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeButton_MetaData), NewProp_ReforgeButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_ReforgeCooldownText = { "ReforgeCooldownText", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, ReforgeCooldownText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeCooldownText_MetaData), NewProp_ReforgeCooldownText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SigilSlots_Inner = { "SigilSlots", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USigilSlotWidget_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SigilSlots = { "SigilSlots", nullptr, (EPropertyFlags)0x011400800000000c, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, SigilSlots), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilSlots_MetaData), NewProp_SigilSlots_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_MaxSlots = { "MaxSlots", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, MaxSlots), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlots_MetaData), NewProp_MaxSlots_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SigilManager = { "SigilManager", nullptr, (EPropertyFlags)0x011400000008000c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, SigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilManager_MetaData), NewProp_SigilManager_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SlotWidgetClass = { "SlotWidgetClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilInventoryWidget, SlotWidgetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_USigilSlotWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotWidgetClass_MetaData), NewProp_SlotWidgetClass_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilInventoryWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_MainCanvas,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SlotsContainer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_StatsPanel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_TotalPowerText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_EquippedSigilsText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_ReforgeButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_ReforgeCooldownText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SigilSlots_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SigilSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_MaxSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SigilManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilInventoryWidget_Statics::NewProp_SlotWidgetClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilInventoryWidget_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilInventoryWidget_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilInventoryWidget_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilInventoryWidget_Statics::ClassParams = {
	&USigilInventoryWidget::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilInventoryWidget_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilInventoryWidget_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilInventoryWidget_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilInventoryWidget_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilInventoryWidget()
{
	if (!Z_Registration_Info_UClass_USigilInventoryWidget.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilInventoryWidget.OuterSingleton, Z_Construct_UClass_USigilInventoryWidget_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilInventoryWidget.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilInventoryWidget);
USigilInventoryWidget::~USigilInventoryWidget() {}
// ********** End Class USigilInventoryWidget ******************************************************

// ********** Begin Class USigilNotificationWidget Function HideNotification ***********************
struct Z_Construct_UFunction_USigilNotificationWidget_HideNotification_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNotificationWidget_HideNotification_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNotificationWidget, nullptr, "HideNotification", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_HideNotification_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNotificationWidget_HideNotification_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilNotificationWidget_HideNotification()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNotificationWidget_HideNotification_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNotificationWidget::execHideNotification)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideNotification();
	P_NATIVE_END;
}
// ********** End Class USigilNotificationWidget Function HideNotification *************************

// ********** Begin Class USigilNotificationWidget Function OnNotificationHidden *******************
static FName NAME_USigilNotificationWidget_OnNotificationHidden = FName(TEXT("OnNotificationHidden"));
void USigilNotificationWidget::OnNotificationHidden()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilNotificationWidget_OnNotificationHidden);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Notification Events" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNotificationWidget, nullptr, "OnNotificationHidden", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilNotificationWidget Function OnNotificationHidden *********************

// ********** Begin Class USigilNotificationWidget Function OnNotificationShown ********************
struct SigilNotificationWidget_eventOnNotificationShown_Parms
{
	FSigilNotificationData Data;
};
static FName NAME_USigilNotificationWidget_OnNotificationShown = FName(TEXT("OnNotificationShown"));
void USigilNotificationWidget::OnNotificationShown(FSigilNotificationData const& Data)
{
	SigilNotificationWidget_eventOnNotificationShown_Parms Parms;
	Parms.Data=Data;
	UFunction* Func = FindFunctionChecked(NAME_USigilNotificationWidget_OnNotificationShown);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Notification Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// EVENTOS BLUEPRINT\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "EVENTOS BLUEPRINT" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Data;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNotificationWidget_eventOnNotificationShown_Parms, Data), Z_Construct_UScriptStruct_FSigilNotificationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) }; // 80670301
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::NewProp_Data,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNotificationWidget, nullptr, "OnNotificationShown", Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::PropPointers), sizeof(SigilNotificationWidget_eventOnNotificationShown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(SigilNotificationWidget_eventOnNotificationShown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilNotificationWidget Function OnNotificationShown **********************

// ********** Begin Class USigilNotificationWidget Function ShowNotification ***********************
struct Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics
{
	struct SigilNotificationWidget_eventShowNotification_Parms
	{
		FSigilNotificationData NotificationData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Notification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// FUN\xc3\x87\xc3\x95""ES PRINCIPAIS\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FUN\xc3\x87\xc3\x95""ES PRINCIPAIS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NotificationData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::NewProp_NotificationData = { "NotificationData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilNotificationWidget_eventShowNotification_Parms, NotificationData), Z_Construct_UScriptStruct_FSigilNotificationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationData_MetaData), NewProp_NotificationData_MetaData) }; // 80670301
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::NewProp_NotificationData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilNotificationWidget, nullptr, "ShowNotification", Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::SigilNotificationWidget_eventShowNotification_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::SigilNotificationWidget_eventShowNotification_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilNotificationWidget_ShowNotification()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilNotificationWidget_ShowNotification_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilNotificationWidget::execShowNotification)
{
	P_GET_STRUCT_REF(FSigilNotificationData,Z_Param_Out_NotificationData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowNotification(Z_Param_Out_NotificationData);
	P_NATIVE_END;
}
// ********** End Class USigilNotificationWidget Function ShowNotification *************************

// ********** Begin Class USigilNotificationWidget *************************************************
void USigilNotificationWidget::StaticRegisterNativesUSigilNotificationWidget()
{
	UClass* Class = USigilNotificationWidget::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "HideNotification", &USigilNotificationWidget::execHideNotification },
		{ "ShowNotification", &USigilNotificationWidget::execShowNotification },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilNotificationWidget;
UClass* USigilNotificationWidget::GetPrivateStaticClass()
{
	using TClass = USigilNotificationWidget;
	if (!Z_Registration_Info_UClass_USigilNotificationWidget.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilNotificationWidget"),
			Z_Registration_Info_UClass_USigilNotificationWidget.InnerSingleton,
			StaticRegisterNativesUSigilNotificationWidget,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilNotificationWidget.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilNotificationWidget_NoRegister()
{
	return USigilNotificationWidget::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilNotificationWidget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Widget para notifica\xc3\xa7\xc3\xb5""es do sistema de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "UI/SigilWidgets.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget para notifica\xc3\xa7\xc3\xb5""es do sistema de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// COMPONENTES UI\n// ========================================\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "COMPONENTES UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DescriptionText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DurationBar_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NotificationBorder;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NotificationIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DescriptionText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DurationBar;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilNotificationWidget_HideNotification, "HideNotification" }, // 2428776862
		{ &Z_Construct_UFunction_USigilNotificationWidget_OnNotificationHidden, "OnNotificationHidden" }, // 1858447801
		{ &Z_Construct_UFunction_USigilNotificationWidget_OnNotificationShown, "OnNotificationShown" }, // 2301465977
		{ &Z_Construct_UFunction_USigilNotificationWidget_ShowNotification, "ShowNotification" }, // 136192124
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilNotificationWidget>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_NotificationBorder = { "NotificationBorder", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNotificationWidget, NotificationBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationBorder_MetaData), NewProp_NotificationBorder_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_NotificationIcon = { "NotificationIcon", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNotificationWidget, NotificationIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationIcon_MetaData), NewProp_NotificationIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_TitleText = { "TitleText", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNotificationWidget, TitleText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleText_MetaData), NewProp_TitleText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_DescriptionText = { "DescriptionText", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNotificationWidget, DescriptionText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DescriptionText_MetaData), NewProp_DescriptionText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_DurationBar = { "DurationBar", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilNotificationWidget, DurationBar), Z_Construct_UClass_UProgressBar_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DurationBar_MetaData), NewProp_DurationBar_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilNotificationWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_NotificationBorder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_NotificationIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_TitleText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_DescriptionText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilNotificationWidget_Statics::NewProp_DurationBar,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilNotificationWidget_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilNotificationWidget_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilNotificationWidget_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilNotificationWidget_Statics::ClassParams = {
	&USigilNotificationWidget::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilNotificationWidget_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilNotificationWidget_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilNotificationWidget_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilNotificationWidget_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilNotificationWidget()
{
	if (!Z_Registration_Info_UClass_USigilNotificationWidget.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilNotificationWidget.OuterSingleton, Z_Construct_UClass_USigilNotificationWidget_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilNotificationWidget.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilNotificationWidget);
USigilNotificationWidget::~USigilNotificationWidget() {}
// ********** End Class USigilNotificationWidget ***************************************************

// ********** Begin Class USigilHUDWidget Function ClearAllNotifications ***************************
struct Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "HUD" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilHUDWidget, nullptr, "ClearAllNotifications", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilHUDWidget::execClearAllNotifications)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllNotifications();
	P_NATIVE_END;
}
// ********** End Class USigilHUDWidget Function ClearAllNotifications *****************************

// ********** Begin Class USigilHUDWidget Function InitializeHUD ***********************************
struct Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics
{
	struct SigilHUDWidget_eventInitializeHUD_Parms
	{
		USigilManagerComponent* SigilManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// FUN\xc3\x87\xc3\x95""ES PRINCIPAIS\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FUN\xc3\x87\xc3\x95""ES PRINCIPAIS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilManager_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SigilManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::NewProp_SigilManager = { "SigilManager", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilHUDWidget_eventInitializeHUD_Parms, SigilManager), Z_Construct_UClass_USigilManagerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilManager_MetaData), NewProp_SigilManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::NewProp_SigilManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilHUDWidget, nullptr, "InitializeHUD", Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::SigilHUDWidget_eventInitializeHUD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::SigilHUDWidget_eventInitializeHUD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilHUDWidget_InitializeHUD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilHUDWidget_InitializeHUD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilHUDWidget::execInitializeHUD)
{
	P_GET_OBJECT(USigilManagerComponent,Z_Param_SigilManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeHUD(Z_Param_SigilManager);
	P_NATIVE_END;
}
// ********** End Class USigilHUDWidget Function InitializeHUD *************************************

// ********** Begin Class USigilHUDWidget Function OnHUDInitialized ********************************
static FName NAME_USigilHUDWidget_OnHUDInitialized = FName(TEXT("OnHUDInitialized"));
void USigilHUDWidget::OnHUDInitialized()
{
	UFunction* Func = FindFunctionChecked(NAME_USigilHUDWidget_OnHUDInitialized);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "HUD Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// EVENTOS BLUEPRINT\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "EVENTOS BLUEPRINT" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilHUDWidget, nullptr, "OnHUDInitialized", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class USigilHUDWidget Function OnHUDInitialized **********************************

// ********** Begin Class USigilHUDWidget Function ShowNotification ********************************
struct Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics
{
	struct SigilHUDWidget_eventShowNotification_Parms
	{
		FSigilNotificationData NotificationData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "HUD" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NotificationData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::NewProp_NotificationData = { "NotificationData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilHUDWidget_eventShowNotification_Parms, NotificationData), Z_Construct_UScriptStruct_FSigilNotificationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationData_MetaData), NewProp_NotificationData_MetaData) }; // 80670301
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::NewProp_NotificationData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilHUDWidget, nullptr, "ShowNotification", Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::SigilHUDWidget_eventShowNotification_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::SigilHUDWidget_eventShowNotification_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilHUDWidget_ShowNotification()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilHUDWidget_ShowNotification_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilHUDWidget::execShowNotification)
{
	P_GET_STRUCT_REF(FSigilNotificationData,Z_Param_Out_NotificationData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowNotification(Z_Param_Out_NotificationData);
	P_NATIVE_END;
}
// ********** End Class USigilHUDWidget Function ShowNotification **********************************

// ********** Begin Class USigilHUDWidget **********************************************************
void USigilHUDWidget::StaticRegisterNativesUSigilHUDWidget()
{
	UClass* Class = USigilHUDWidget::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearAllNotifications", &USigilHUDWidget::execClearAllNotifications },
		{ "InitializeHUD", &USigilHUDWidget::execInitializeHUD },
		{ "ShowNotification", &USigilHUDWidget::execShowNotification },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilHUDWidget;
UClass* USigilHUDWidget::GetPrivateStaticClass()
{
	using TClass = USigilHUDWidget;
	if (!Z_Registration_Info_UClass_USigilHUDWidget.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilHUDWidget"),
			Z_Registration_Info_UClass_USigilHUDWidget.InnerSingleton,
			StaticRegisterNativesUSigilHUDWidget,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilHUDWidget.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilHUDWidget_NoRegister()
{
	return USigilHUDWidget::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilHUDWidget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * HUD principal do sistema de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "UI/SigilWidgets.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HUD principal do sistema de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// COMPONENTES UI\n// ========================================\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "COMPONENTES UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationsContainer_MetaData[] = {
		{ "BindWidget", "" },
		{ "Category", "UI Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationWidgetClass_MetaData[] = {
		{ "Category", "HUD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// PROPRIEDADES\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PROPRIEDADES" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveNotifications_MetaData[] = {
		{ "Category", "HUD" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxNotifications_MetaData[] = {
		{ "Category", "HUD" },
		{ "ModuleRelativePath", "Public/UI/SigilWidgets.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InventoryWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NotificationsContainer;
	static const UECodeGen_Private::FClassPropertyParams NewProp_NotificationWidgetClass;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveNotifications_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveNotifications;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxNotifications;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilHUDWidget_ClearAllNotifications, "ClearAllNotifications" }, // 865773619
		{ &Z_Construct_UFunction_USigilHUDWidget_InitializeHUD, "InitializeHUD" }, // 4163338784
		{ &Z_Construct_UFunction_USigilHUDWidget_OnHUDInitialized, "OnHUDInitialized" }, // 3622785605
		{ &Z_Construct_UFunction_USigilHUDWidget_ShowNotification, "ShowNotification" }, // 3470348487
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilHUDWidget>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_InventoryWidget = { "InventoryWidget", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilHUDWidget, InventoryWidget), Z_Construct_UClass_USigilInventoryWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryWidget_MetaData), NewProp_InventoryWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_NotificationsContainer = { "NotificationsContainer", nullptr, (EPropertyFlags)0x011400000008001c, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilHUDWidget, NotificationsContainer), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationsContainer_MetaData), NewProp_NotificationsContainer_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_NotificationWidgetClass = { "NotificationWidgetClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilHUDWidget, NotificationWidgetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_USigilNotificationWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationWidgetClass_MetaData), NewProp_NotificationWidgetClass_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_ActiveNotifications_Inner = { "ActiveNotifications", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USigilNotificationWidget_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_ActiveNotifications = { "ActiveNotifications", nullptr, (EPropertyFlags)0x011400800000000c, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilHUDWidget, ActiveNotifications), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveNotifications_MetaData), NewProp_ActiveNotifications_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_MaxNotifications = { "MaxNotifications", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilHUDWidget, MaxNotifications), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxNotifications_MetaData), NewProp_MaxNotifications_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilHUDWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_InventoryWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_NotificationsContainer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_NotificationWidgetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_ActiveNotifications_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_ActiveNotifications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilHUDWidget_Statics::NewProp_MaxNotifications,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilHUDWidget_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilHUDWidget_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilHUDWidget_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilHUDWidget_Statics::ClassParams = {
	&USigilHUDWidget::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilHUDWidget_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilHUDWidget_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilHUDWidget_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilHUDWidget_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilHUDWidget()
{
	if (!Z_Registration_Info_UClass_USigilHUDWidget.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilHUDWidget.OuterSingleton, Z_Construct_UClass_USigilHUDWidget_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilHUDWidget.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilHUDWidget);
USigilHUDWidget::~USigilHUDWidget() {}
// ********** End Class USigilHUDWidget ************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESigilSlotState_StaticEnum, TEXT("ESigilSlotState"), &Z_Registration_Info_UEnum_ESigilSlotState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1671570546U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilSlotVisualConfig::StaticStruct, Z_Construct_UScriptStruct_FSigilSlotVisualConfig_Statics::NewStructOps, TEXT("SigilSlotVisualConfig"), &Z_Registration_Info_UScriptStruct_FSigilSlotVisualConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilSlotVisualConfig), 3185679346U) },
		{ FSigilNotificationData::StaticStruct, Z_Construct_UScriptStruct_FSigilNotificationData_Statics::NewStructOps, TEXT("SigilNotificationData"), &Z_Registration_Info_UScriptStruct_FSigilNotificationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilNotificationData), 80670301U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilDragDropOperation, USigilDragDropOperation::StaticClass, TEXT("USigilDragDropOperation"), &Z_Registration_Info_UClass_USigilDragDropOperation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilDragDropOperation), 1027517792U) },
		{ Z_Construct_UClass_USigilSlotWidget, USigilSlotWidget::StaticClass, TEXT("USigilSlotWidget"), &Z_Registration_Info_UClass_USigilSlotWidget, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilSlotWidget), 2810852779U) },
		{ Z_Construct_UClass_USigilInventoryWidget, USigilInventoryWidget::StaticClass, TEXT("USigilInventoryWidget"), &Z_Registration_Info_UClass_USigilInventoryWidget, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilInventoryWidget), 2243881951U) },
		{ Z_Construct_UClass_USigilNotificationWidget, USigilNotificationWidget::StaticClass, TEXT("USigilNotificationWidget"), &Z_Registration_Info_UClass_USigilNotificationWidget, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilNotificationWidget), 1546431216U) },
		{ Z_Construct_UClass_USigilHUDWidget, USigilHUDWidget::StaticClass, TEXT("USigilHUDWidget"), &Z_Registration_Info_UClass_USigilHUDWidget, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilHUDWidget), 3084845756U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_4052351942(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_UI_SigilWidgets_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
