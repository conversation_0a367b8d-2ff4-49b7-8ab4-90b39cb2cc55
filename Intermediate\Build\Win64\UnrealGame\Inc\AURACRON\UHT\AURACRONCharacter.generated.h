// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Character/AURACRONCharacter.h"

#ifdef AURACRON_AURACRONCharacter_generated_h
#error "AURACRONCharacter.generated.h already included, missing '#pragma once' in AURACRONCharacter.h"
#endif
#define AURACRON_AURACRONCharacter_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AAURACRONCharacter;
enum class EAURACRONSigilType : uint8;
enum class EAURACRONTemporalEffectType : uint8;

// ********** Begin Class AAURACRONCharacter *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void SetTeamID_Implementation(int32 NewTeamID); \
	virtual void UnequipSigil_Implementation(EAURACRONSigilType SigilType); \
	virtual void EquipSigil_Implementation(EAURACRONSigilType SigilType); \
	DECLARE_FUNCTION(execOnRep_EquippedSigils); \
	DECLARE_FUNCTION(execOnRep_TeamID); \
	DECLARE_FUNCTION(execApplyTemporalEffect); \
	DECLARE_FUNCTION(execIsAlly); \
	DECLARE_FUNCTION(execGetTeamID); \
	DECLARE_FUNCTION(execSetTeamID); \
	DECLARE_FUNCTION(execGetAbilityPower); \
	DECLARE_FUNCTION(execGetAttackDamage); \
	DECLARE_FUNCTION(execGetMaxMana); \
	DECLARE_FUNCTION(execGetMana); \
	DECLARE_FUNCTION(execGetMaxHealth); \
	DECLARE_FUNCTION(execGetHealth); \
	DECLARE_FUNCTION(execGetEquippedSigils); \
	DECLARE_FUNCTION(execIsSigilEquipped); \
	DECLARE_FUNCTION(execUnequipSigil); \
	DECLARE_FUNCTION(execEquipSigil); \
	DECLARE_FUNCTION(execApplyDefaultEffects); \
	DECLARE_FUNCTION(execGiveDefaultAbilities); \
	DECLARE_FUNCTION(execInitializeAbilitySystem);


#define FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_AAURACRONCharacter_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONCharacter(); \
	friend struct Z_Construct_UClass_AAURACRONCharacter_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONCharacter_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONCharacter, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONCharacter_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONCharacter) \
	virtual UObject* _getUObject() const override { return const_cast<AAURACRONCharacter*>(this); } \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		TeamID=NETFIELD_REP_START, \
		EquippedSigils, \
		NETFIELD_REP_END=EquippedSigils	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONCharacter(AAURACRONCharacter&&) = delete; \
	AAURACRONCharacter(const AAURACRONCharacter&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONCharacter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONCharacter); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(AAURACRONCharacter) \
	NO_API virtual ~AAURACRONCharacter();


#define FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_35_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h_38_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONCharacter;

// ********** End Class AAURACRONCharacter *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Character_AURACRONCharacter_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
