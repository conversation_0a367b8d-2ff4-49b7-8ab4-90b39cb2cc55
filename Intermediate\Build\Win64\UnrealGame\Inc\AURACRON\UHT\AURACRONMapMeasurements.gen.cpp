// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONMapMeasurements.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONMapMeasurements() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UAURACRONMapMeasurements();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONMapMeasurements_NoRegister();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapDimensions();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UBlueprintFunctionLibrary();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONMapDimensions ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions;
class UScriptStruct* FAURACRONMapDimensions::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONMapDimensions, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONMapDimensions"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para definir as dimens\xc3\xb5""es principais do mapa AURACRON\n *\n * AN\xc3\x81LISE DETALHADA DOS CONCORRENTES:\n * - League of Legends (Summoner's Rift): 16,000 x 16,000 units = 160m x 160m\n * - Dota 2: ~11,000 x 11,000 units = 110m x 110m\n * - AURACRON: 15,000 x 15,000 cm = 150m x 150m (meio termo otimizado)\n *\n * UE 5.6: 1 unit = 1 centimeter (padr\xc3\xa3o oficial Unreal Engine)\n *\n * COORDENADAS DO MAPA:\n * - Centro: (0, 0, altura_ambiente)\n * - Limites: X[-7500, +7500], Y[-7500, +7500]\n * - Team 1 Base: (-6500, -6500, altura_ambiente)\n * - Team 2 Base: (+6500, +6500, altura_ambiente)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para definir as dimens\xc3\xb5""es principais do mapa AURACRON\n\nAN\xc3\x81LISE DETALHADA DOS CONCORRENTES:\n- League of Legends (Summoner's Rift): 16,000 x 16,000 units = 160m x 160m\n- Dota 2: ~11,000 x 11,000 units = 110m x 110m\n- AURACRON: 15,000 x 15,000 cm = 150m x 150m (meio termo otimizado)\n\nUE 5.6: 1 unit = 1 centimeter (padr\xc3\xa3o oficial Unreal Engine)\n\nCOORDENADAS DO MAPA:\n- Centro: (0, 0, altura_ambiente)\n- Limites: X[-7500, +7500], Y[-7500, +7500]\n- Team 1 Base: (-6500, -6500, altura_ambiente)\n- Team 2 Base: (+6500, +6500, altura_ambiente)" },
#endif
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONMapDimensions>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONMapDimensions",
	nullptr,
	0,
	sizeof(FAURACRONMapDimensions),
	alignof(FAURACRONMapDimensions),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapDimensions()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONMapDimensions **********************************************

// ********** Begin Class UAURACRONMapMeasurements Function GetBasePositions ***********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics
{
	struct AURACRONMapMeasurements_eventGetBasePositions_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xb5""es das bases */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xb5""es das bases" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetBasePositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetBasePositions", Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::AURACRONMapMeasurements_eventGetBasePositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::AURACRONMapMeasurements_eventGetBasePositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetBasePositions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetBasePositions();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetBasePositions *************************

// ********** Begin Class UAURACRONMapMeasurements Function GetBotLanePoints ***********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics
{
	struct AURACRONMapMeasurements_eventGetBotLanePoints_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pontos da lane inferior (Bot Lane) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pontos da lane inferior (Bot Lane)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetBotLanePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetBotLanePoints", Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::AURACRONMapMeasurements_eventGetBotLanePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::AURACRONMapMeasurements_eventGetBotLanePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetBotLanePoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetBotLanePoints();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetBotLanePoints *************************

// ********** Begin Class UAURACRONMapMeasurements Function GetClosestLane *************************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics
{
	struct AURACRONMapMeasurements_eventGetClosestLane_Parms
	{
		FVector Position;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter lane mais pr\xc3\xb3xima de uma posi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter lane mais pr\xc3\xb3xima de uma posi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetClosestLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetClosestLane_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetClosestLane", Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::AURACRONMapMeasurements_eventGetClosestLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::AURACRONMapMeasurements_eventGetClosestLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetClosestLane)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAURACRONMapMeasurements::GetClosestLane(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetClosestLane ***************************

// ********** Begin Class UAURACRONMapMeasurements Function GetDistanceInMeters ********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics
{
	struct AURACRONMapMeasurements_eventGetDistanceInMeters_Parms
	{
		FVector PointA;
		FVector PointB;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular dist\xc3\xa2ncia entre dois pontos em metros */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular dist\xc3\xa2ncia entre dois pontos em metros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::NewProp_PointA = { "PointA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetDistanceInMeters_Parms, PointA), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointA_MetaData), NewProp_PointA_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::NewProp_PointB = { "PointB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetDistanceInMeters_Parms, PointB), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointB_MetaData), NewProp_PointB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetDistanceInMeters_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::NewProp_PointA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::NewProp_PointB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetDistanceInMeters", Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::AURACRONMapMeasurements_eventGetDistanceInMeters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::AURACRONMapMeasurements_eventGetDistanceInMeters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetDistanceInMeters)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PointA);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PointB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONMapMeasurements::GetDistanceInMeters(Z_Param_Out_PointA,Z_Param_Out_PointB);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetDistanceInMeters **********************

// ********** Begin Class UAURACRONMapMeasurements Function GetEnvironmentCenter *******************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics
{
	struct AURACRONMapMeasurements_eventGetEnvironmentCenter_Parms
	{
		int32 EnvironmentType;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter centro do ambiente baseado no tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter centro do ambiente baseado no tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetEnvironmentCenter_Parms, EnvironmentType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetEnvironmentCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetEnvironmentCenter", Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::AURACRONMapMeasurements_eventGetEnvironmentCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::AURACRONMapMeasurements_eventGetEnvironmentCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetEnvironmentCenter)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_EnvironmentType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONMapMeasurements::GetEnvironmentCenter(Z_Param_EnvironmentType);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetEnvironmentCenter *********************

// ********** Begin Class UAURACRONMapMeasurements Function GetEnvironmentRadius *******************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics
{
	struct AURACRONMapMeasurements_eventGetEnvironmentRadius_Parms
	{
		int32 EnvironmentType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter raio do ambiente baseado no tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter raio do ambiente baseado no tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetEnvironmentRadius_Parms, EnvironmentType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetEnvironmentRadius_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetEnvironmentRadius", Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::AURACRONMapMeasurements_eventGetEnvironmentRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::AURACRONMapMeasurements_eventGetEnvironmentRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetEnvironmentRadius)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_EnvironmentType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONMapMeasurements::GetEnvironmentRadius(Z_Param_EnvironmentType);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetEnvironmentRadius *********************

// ********** Begin Class UAURACRONMapMeasurements Function GetInstance ****************************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics
{
	struct AURACRONMapMeasurements_eventGetInstance_Parms
	{
		const UWorld* World;
		UAURACRONMapMeasurements* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter inst\xc3\xa2ncia singleton para o mundo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter inst\xc3\xa2ncia singleton para o mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_World_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetInstance_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_World_MetaData), NewProp_World_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAURACRONMapMeasurements_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetInstance", Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::AURACRONMapMeasurements_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::AURACRONMapMeasurements_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetInstance)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAURACRONMapMeasurements**)Z_Param__Result=UAURACRONMapMeasurements::GetInstance(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetInstance ******************************

// ********** Begin Class UAURACRONMapMeasurements Function GetIslandPositions *********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics
{
	struct AURACRONMapMeasurements_eventGetIslandPositions_Parms
	{
		int32 IslandType;
		TArray<FVector> FlowPoints;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular posi\xc3\xa7\xc3\xb5""es das ilhas estrat\xc3\xa9gicas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular posi\xc3\xa7\xc3\xb5""es das ilhas estrat\xc3\xa9gicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetIslandPositions_Parms, IslandType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_FlowPoints_Inner = { "FlowPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_FlowPoints = { "FlowPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetIslandPositions_Parms, FlowPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowPoints_MetaData), NewProp_FlowPoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetIslandPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_FlowPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_FlowPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetIslandPositions", Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::AURACRONMapMeasurements_eventGetIslandPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::AURACRONMapMeasurements_eventGetIslandPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetIslandPositions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_IslandType);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_FlowPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetIslandPositions(Z_Param_IslandType,Z_Param_Out_FlowPoints);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetIslandPositions ***********************

// ********** Begin Class UAURACRONMapMeasurements Function GetJungleCampPositions *****************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics
{
	struct AURACRONMapMeasurements_eventGetJungleCampPositions_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xb5""es dos camps da jungle */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xb5""es dos camps da jungle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetJungleCampPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetJungleCampPositions", Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::AURACRONMapMeasurements_eventGetJungleCampPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::AURACRONMapMeasurements_eventGetJungleCampPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetJungleCampPositions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetJungleCampPositions();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetJungleCampPositions *******************

// ********** Begin Class UAURACRONMapMeasurements Function GetLanePosition ************************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics
{
	struct AURACRONMapMeasurements_eventGetLanePosition_Parms
	{
		int32 LaneIndex;
		float LaneProgress;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular posi\xc3\xa7\xc3\xa3o na lane baseada em porcentagem (0.0 = in\xc3\xad""cio, 1.0 = fim) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular posi\xc3\xa7\xc3\xa3o na lane baseada em porcentagem (0.0 = in\xc3\xad""cio, 1.0 = fim)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LaneIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::NewProp_LaneIndex = { "LaneIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetLanePosition_Parms, LaneIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::NewProp_LaneProgress = { "LaneProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetLanePosition_Parms, LaneProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetLanePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::NewProp_LaneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::NewProp_LaneProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetLanePosition", Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::AURACRONMapMeasurements_eventGetLanePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::AURACRONMapMeasurements_eventGetLanePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetLanePosition)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LaneIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LaneProgress);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONMapMeasurements::GetLanePosition(Z_Param_LaneIndex,Z_Param_LaneProgress);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetLanePosition **************************

// ********** Begin Class UAURACRONMapMeasurements Function GetMapPhaseFromTime ********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics
{
	struct AURACRONMapMeasurements_eventGetMapPhaseFromTime_Parms
	{
		float ElapsedTimeSeconds;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter fase do mapa baseada no tempo decorrido */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter fase do mapa baseada no tempo decorrido" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElapsedTimeSeconds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::NewProp_ElapsedTimeSeconds = { "ElapsedTimeSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetMapPhaseFromTime_Parms, ElapsedTimeSeconds), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetMapPhaseFromTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::NewProp_ElapsedTimeSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetMapPhaseFromTime", Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::AURACRONMapMeasurements_eventGetMapPhaseFromTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::AURACRONMapMeasurements_eventGetMapPhaseFromTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetMapPhaseFromTime)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ElapsedTimeSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAURACRONMapMeasurements::GetMapPhaseFromTime(Z_Param_ElapsedTimeSeconds);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetMapPhaseFromTime **********************

// ********** Begin Class UAURACRONMapMeasurements Function GetMapScaleFactorForPhase **************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics
{
	struct AURACRONMapMeasurements_eventGetMapScaleFactorForPhase_Parms
	{
		int32 MapPhase;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular fator de escala baseado na fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular fator de escala baseado na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetMapScaleFactorForPhase_Parms, MapPhase), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetMapScaleFactorForPhase_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::NewProp_MapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetMapScaleFactorForPhase", Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::AURACRONMapMeasurements_eventGetMapScaleFactorForPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::AURACRONMapMeasurements_eventGetMapScaleFactorForPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetMapScaleFactorForPhase)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONMapMeasurements::GetMapScaleFactorForPhase(Z_Param_MapPhase);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetMapScaleFactorForPhase ****************

// ********** Begin Class UAURACRONMapMeasurements Function GetMidLanePoints ***********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics
{
	struct AURACRONMapMeasurements_eventGetMidLanePoints_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pontos da lane do meio (Mid Lane) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pontos da lane do meio (Mid Lane)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetMidLanePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetMidLanePoints", Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::AURACRONMapMeasurements_eventGetMidLanePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::AURACRONMapMeasurements_eventGetMidLanePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetMidLanePoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetMidLanePoints();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetMidLanePoints *************************

// ********** Begin Class UAURACRONMapMeasurements Function GetPrismalFlowPosition *****************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics
{
	struct AURACRONMapMeasurements_eventGetPrismalFlowPosition_Parms
	{
		float T;
		FVector MapCenter;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular posi\xc3\xa7\xc3\xa3o na curva do Prismal Flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular posi\xc3\xa7\xc3\xa3o na curva do Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetPrismalFlowPosition_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::NewProp_MapCenter = { "MapCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetPrismalFlowPosition_Parms, MapCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapCenter_MetaData), NewProp_MapCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetPrismalFlowPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::NewProp_MapCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetPrismalFlowPosition", Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::AURACRONMapMeasurements_eventGetPrismalFlowPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::AURACRONMapMeasurements_eventGetPrismalFlowPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetPrismalFlowPosition)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_MapCenter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONMapMeasurements::GetPrismalFlowPosition(Z_Param_T,Z_Param_Out_MapCenter);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetPrismalFlowPosition *******************

// ********** Begin Class UAURACRONMapMeasurements Function GetPrismalFlowWidth ********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics
{
	struct AURACRONMapMeasurements_eventGetPrismalFlowWidth_Parms
	{
		float T;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular largura do Prismal Flow baseada na posi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular largura do Prismal Flow baseada na posi\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetPrismalFlowWidth_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetPrismalFlowWidth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetPrismalFlowWidth", Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::AURACRONMapMeasurements_eventGetPrismalFlowWidth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::AURACRONMapMeasurements_eventGetPrismalFlowWidth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetPrismalFlowWidth)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONMapMeasurements::GetPrismalFlowWidth(Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetPrismalFlowWidth **********************

// ********** Begin Class UAURACRONMapMeasurements Function GetPurgatoryAnchorPosition *************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics
{
	struct AURACRONMapMeasurements_eventGetPurgatoryAnchorPosition_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xa3o do Purgatory Anchor */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xa3o do Purgatory Anchor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetPurgatoryAnchorPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetPurgatoryAnchorPosition", Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::AURACRONMapMeasurements_eventGetPurgatoryAnchorPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::AURACRONMapMeasurements_eventGetPurgatoryAnchorPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetPurgatoryAnchorPosition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAURACRONMapMeasurements::GetPurgatoryAnchorPosition();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetPurgatoryAnchorPosition ***************

// ********** Begin Class UAURACRONMapMeasurements Function GetStrategicObjectivePositions *********
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics
{
	struct AURACRONMapMeasurements_eventGetStrategicObjectivePositions_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xb5""es dos objetivos estrat\xc3\xa9gicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xb5""es dos objetivos estrat\xc3\xa9gicos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetStrategicObjectivePositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetStrategicObjectivePositions", Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::AURACRONMapMeasurements_eventGetStrategicObjectivePositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::AURACRONMapMeasurements_eventGetStrategicObjectivePositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetStrategicObjectivePositions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetStrategicObjectivePositions();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetStrategicObjectivePositions ***********

// ********** Begin Class UAURACRONMapMeasurements Function GetTopLanePoints ***********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics
{
	struct AURACRONMapMeasurements_eventGetTopLanePoints_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pontos da lane superior (Top Lane) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pontos da lane superior (Top Lane)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTopLanePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetTopLanePoints", Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::AURACRONMapMeasurements_eventGetTopLanePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::AURACRONMapMeasurements_eventGetTopLanePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetTopLanePoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetTopLanePoints();
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetTopLanePoints *************************

// ********** Begin Class UAURACRONMapMeasurements Function GetTowerPositions **********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics
{
	struct AURACRONMapMeasurements_eventGetTowerPositions_Parms
	{
		int32 LaneIndex;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xb5""es das torres por lane */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xb5""es das torres por lane" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LaneIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::NewProp_LaneIndex = { "LaneIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTowerPositions_Parms, LaneIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTowerPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::NewProp_LaneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetTowerPositions", Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::AURACRONMapMeasurements_eventGetTowerPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::AURACRONMapMeasurements_eventGetTowerPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetTowerPositions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LaneIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetTowerPositions(Z_Param_LaneIndex);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetTowerPositions ************************

// ********** Begin Class UAURACRONMapMeasurements Function GetTrailPositions **********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics
{
	struct AURACRONMapMeasurements_eventGetTrailPositions_Parms
	{
		int32 TrailType;
		float TimeOfDay;
		FVector MapCenter;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xb5""es das trilhas baseadas no tipo e tempo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xb5""es das trilhas baseadas no tipo e tempo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TrailType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeOfDay;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_TrailType = { "TrailType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTrailPositions_Parms, TrailType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_TimeOfDay = { "TimeOfDay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTrailPositions_Parms, TimeOfDay), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_MapCenter = { "MapCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTrailPositions_Parms, MapCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapCenter_MetaData), NewProp_MapCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventGetTrailPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_TrailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_TimeOfDay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_MapCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "GetTrailPositions", Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::AURACRONMapMeasurements_eventGetTrailPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::AURACRONMapMeasurements_eventGetTrailPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execGetTrailPositions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TrailType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeOfDay);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_MapCenter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=UAURACRONMapMeasurements::GetTrailPositions(Z_Param_TrailType,Z_Param_TimeOfDay,Z_Param_Out_MapCenter);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function GetTrailPositions ************************

// ********** Begin Class UAURACRONMapMeasurements Function IsPositionInLane ***********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics
{
	struct AURACRONMapMeasurements_eventIsPositionInLane_Parms
	{
		FVector Position;
		int32 LaneIndex;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|MapMeasurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro de uma lane */" },
#endif
		{ "CPP_Default_Tolerance", "400.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro de uma lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LaneIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventIsPositionInLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_LaneIndex = { "LaneIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventIsPositionInLane_Parms, LaneIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventIsPositionInLane_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONMapMeasurements_eventIsPositionInLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONMapMeasurements_eventIsPositionInLane_Parms), &Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_LaneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "IsPositionInLane", Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::AURACRONMapMeasurements_eventIsPositionInLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::AURACRONMapMeasurements_eventIsPositionInLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execIsPositionInLane)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FIntProperty,Z_Param_LaneIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAURACRONMapMeasurements::IsPositionInLane(Z_Param_Out_Position,Z_Param_LaneIndex,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function IsPositionInLane *************************

// ********** Begin Class UAURACRONMapMeasurements Function IsPositionWithinMapBounds **************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics
{
	struct AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms
	{
		FVector Position;
		FVector MapCenter;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro dos limites do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro dos limites do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapCenter;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_MapCenter = { "MapCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms, MapCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapCenter_MetaData), NewProp_MapCenter_MetaData) };
void Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms), &Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_MapCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "IsPositionWithinMapBounds", Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::AURACRONMapMeasurements_eventIsPositionWithinMapBounds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execIsPositionWithinMapBounds)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_MapCenter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAURACRONMapMeasurements::IsPositionWithinMapBounds(Z_Param_Out_Position,Z_Param_Out_MapCenter);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function IsPositionWithinMapBounds ****************

// ********** Begin Class UAURACRONMapMeasurements Function MetersToUnrealUnits ********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics
{
	struct AURACRONMapMeasurements_eventMetersToUnrealUnits_Parms
	{
		float Meters;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Converter metros para unidades Unreal (cent\xc3\xadmetros) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Converter metros para unidades Unreal (cent\xc3\xadmetros)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Meters;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::NewProp_Meters = { "Meters", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventMetersToUnrealUnits_Parms, Meters), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventMetersToUnrealUnits_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::NewProp_Meters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "MetersToUnrealUnits", Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::AURACRONMapMeasurements_eventMetersToUnrealUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::AURACRONMapMeasurements_eventMetersToUnrealUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execMetersToUnrealUnits)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Meters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONMapMeasurements::MetersToUnrealUnits(Z_Param_Meters);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function MetersToUnrealUnits **********************

// ********** Begin Class UAURACRONMapMeasurements Function UnrealUnitsToMeters ********************
struct Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics
{
	struct AURACRONMapMeasurements_eventUnrealUnitsToMeters_Parms
	{
		float UnrealUnits;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Measurements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Converter unidades Unreal (cent\xc3\xadmetros) para metros */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Converter unidades Unreal (cent\xc3\xadmetros) para metros" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnrealUnits;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::NewProp_UnrealUnits = { "UnrealUnits", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventUnrealUnitsToMeters_Parms, UnrealUnits), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONMapMeasurements_eventUnrealUnitsToMeters_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::NewProp_UnrealUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONMapMeasurements, nullptr, "UnrealUnitsToMeters", Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::AURACRONMapMeasurements_eventUnrealUnitsToMeters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::AURACRONMapMeasurements_eventUnrealUnitsToMeters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONMapMeasurements::execUnrealUnitsToMeters)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_UnrealUnits);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAURACRONMapMeasurements::UnrealUnitsToMeters(Z_Param_UnrealUnits);
	P_NATIVE_END;
}
// ********** End Class UAURACRONMapMeasurements Function UnrealUnitsToMeters **********************

// ********** Begin Class UAURACRONMapMeasurements *************************************************
void UAURACRONMapMeasurements::StaticRegisterNativesUAURACRONMapMeasurements()
{
	UClass* Class = UAURACRONMapMeasurements::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetBasePositions", &UAURACRONMapMeasurements::execGetBasePositions },
		{ "GetBotLanePoints", &UAURACRONMapMeasurements::execGetBotLanePoints },
		{ "GetClosestLane", &UAURACRONMapMeasurements::execGetClosestLane },
		{ "GetDistanceInMeters", &UAURACRONMapMeasurements::execGetDistanceInMeters },
		{ "GetEnvironmentCenter", &UAURACRONMapMeasurements::execGetEnvironmentCenter },
		{ "GetEnvironmentRadius", &UAURACRONMapMeasurements::execGetEnvironmentRadius },
		{ "GetInstance", &UAURACRONMapMeasurements::execGetInstance },
		{ "GetIslandPositions", &UAURACRONMapMeasurements::execGetIslandPositions },
		{ "GetJungleCampPositions", &UAURACRONMapMeasurements::execGetJungleCampPositions },
		{ "GetLanePosition", &UAURACRONMapMeasurements::execGetLanePosition },
		{ "GetMapPhaseFromTime", &UAURACRONMapMeasurements::execGetMapPhaseFromTime },
		{ "GetMapScaleFactorForPhase", &UAURACRONMapMeasurements::execGetMapScaleFactorForPhase },
		{ "GetMidLanePoints", &UAURACRONMapMeasurements::execGetMidLanePoints },
		{ "GetPrismalFlowPosition", &UAURACRONMapMeasurements::execGetPrismalFlowPosition },
		{ "GetPrismalFlowWidth", &UAURACRONMapMeasurements::execGetPrismalFlowWidth },
		{ "GetPurgatoryAnchorPosition", &UAURACRONMapMeasurements::execGetPurgatoryAnchorPosition },
		{ "GetStrategicObjectivePositions", &UAURACRONMapMeasurements::execGetStrategicObjectivePositions },
		{ "GetTopLanePoints", &UAURACRONMapMeasurements::execGetTopLanePoints },
		{ "GetTowerPositions", &UAURACRONMapMeasurements::execGetTowerPositions },
		{ "GetTrailPositions", &UAURACRONMapMeasurements::execGetTrailPositions },
		{ "IsPositionInLane", &UAURACRONMapMeasurements::execIsPositionInLane },
		{ "IsPositionWithinMapBounds", &UAURACRONMapMeasurements::execIsPositionWithinMapBounds },
		{ "MetersToUnrealUnits", &UAURACRONMapMeasurements::execMetersToUnrealUnits },
		{ "UnrealUnitsToMeters", &UAURACRONMapMeasurements::execUnrealUnitsToMeters },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONMapMeasurements;
UClass* UAURACRONMapMeasurements::GetPrivateStaticClass()
{
	using TClass = UAURACRONMapMeasurements;
	if (!Z_Registration_Info_UClass_UAURACRONMapMeasurements.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONMapMeasurements"),
			Z_Registration_Info_UClass_UAURACRONMapMeasurements.InnerSingleton,
			StaticRegisterNativesUAURACRONMapMeasurements,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONMapMeasurements.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONMapMeasurements_NoRegister()
{
	return UAURACRONMapMeasurements::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONMapMeasurements_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Biblioteca de fun\xc3\xa7\xc3\xb5""es utilit\xc3\xa1rias para medidas e convers\xc3\xb5""es do mapa AURACRON\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONMapMeasurements.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONMapMeasurements.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biblioteca de fun\xc3\xa7\xc3\xb5""es utilit\xc3\xa1rias para medidas e convers\xc3\xb5""es do mapa AURACRON" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetBasePositions, "GetBasePositions" }, // 1735724869
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetBotLanePoints, "GetBotLanePoints" }, // 2260427392
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetClosestLane, "GetClosestLane" }, // 458270605
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetDistanceInMeters, "GetDistanceInMeters" }, // 3807866265
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentCenter, "GetEnvironmentCenter" }, // 1078963056
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetEnvironmentRadius, "GetEnvironmentRadius" }, // 1817644640
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetInstance, "GetInstance" }, // 2811829585
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetIslandPositions, "GetIslandPositions" }, // 4168534740
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetJungleCampPositions, "GetJungleCampPositions" }, // 3044353589
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetLanePosition, "GetLanePosition" }, // 4264667386
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapPhaseFromTime, "GetMapPhaseFromTime" }, // 531580844
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetMapScaleFactorForPhase, "GetMapScaleFactorForPhase" }, // 3343821202
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetMidLanePoints, "GetMidLanePoints" }, // 780661268
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowPosition, "GetPrismalFlowPosition" }, // 2678908311
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetPrismalFlowWidth, "GetPrismalFlowWidth" }, // 3284057495
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetPurgatoryAnchorPosition, "GetPurgatoryAnchorPosition" }, // 280052071
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetStrategicObjectivePositions, "GetStrategicObjectivePositions" }, // 3384297496
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetTopLanePoints, "GetTopLanePoints" }, // 3004126800
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetTowerPositions, "GetTowerPositions" }, // 1183115660
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_GetTrailPositions, "GetTrailPositions" }, // 535078105
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionInLane, "IsPositionInLane" }, // 3411986547
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_IsPositionWithinMapBounds, "IsPositionWithinMapBounds" }, // 966679440
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_MetersToUnrealUnits, "MetersToUnrealUnits" }, // 264323292
		{ &Z_Construct_UFunction_UAURACRONMapMeasurements_UnrealUnitsToMeters, "UnrealUnitsToMeters" }, // 3624722556
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONMapMeasurements>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAURACRONMapMeasurements_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UBlueprintFunctionLibrary,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONMapMeasurements_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONMapMeasurements_Statics::ClassParams = {
	&UAURACRONMapMeasurements::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONMapMeasurements_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONMapMeasurements_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONMapMeasurements()
{
	if (!Z_Registration_Info_UClass_UAURACRONMapMeasurements.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONMapMeasurements.OuterSingleton, Z_Construct_UClass_UAURACRONMapMeasurements_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONMapMeasurements.OuterSingleton;
}
UAURACRONMapMeasurements::UAURACRONMapMeasurements(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONMapMeasurements);
UAURACRONMapMeasurements::~UAURACRONMapMeasurements() {}
// ********** End Class UAURACRONMapMeasurements ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONMapDimensions::StaticStruct, Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics::NewStructOps, TEXT("AURACRONMapDimensions"), &Z_Registration_Info_UScriptStruct_FAURACRONMapDimensions, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONMapDimensions), 1557901837U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONMapMeasurements, UAURACRONMapMeasurements::StaticClass, TEXT("UAURACRONMapMeasurements"), &Z_Registration_Info_UClass_UAURACRONMapMeasurements, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONMapMeasurements), 1216680579U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h__Script_AURACRON_907285106(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
