// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGChaosIslandManager.h"

#ifdef AURACRON_AURACRONPCGChaosIslandManager_generated_h
#error "AURACRONPCGChaosIslandManager.generated.h already included, missing '#pragma once' in AURACRONPCGChaosIslandManager.h"
#endif
#define AURACRON_AURACRONPCGChaosIslandManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AAURACRONPCGChaosIsland;
class AAURACRONPCGChaosPortal;
class AAURACRONPCGPrismalFlow;
enum class EAURACRONMapPhase : uint8;

// ********** Begin Delegate FOnChaosManagerFullyInitialized ***************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_22_DELEGATE \
AURACRON_API void FOnChaosManagerFullyInitialized_DelegateWrapper(const FMulticastScriptDelegate& OnChaosManagerFullyInitialized);


// ********** End Delegate FOnChaosManagerFullyInitialized *****************************************

// ********** Begin Delegate FOnChaosIslandsListChanged ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_23_DELEGATE \
AURACRON_API void FOnChaosIslandsListChanged_DelegateWrapper(const FMulticastScriptDelegate& OnChaosIslandsListChanged, TArray<AAURACRONPCGChaosIsland*> const& Islands);


// ********** End Delegate FOnChaosIslandsListChanged **********************************************

// ********** Begin Delegate FOnMapPhaseUpdated ****************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_24_DELEGATE \
AURACRON_API void FOnMapPhaseUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnMapPhaseUpdated, EAURACRONMapPhase NewPhase);


// ********** End Delegate FOnMapPhaseUpdated ******************************************************

// ********** Begin Class AAURACRONPCGChaosIslandManager *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void ClientNotifyAllIslandsDestroyed_Implementation(); \
	virtual void ClientNotifyIslandSpawned_Implementation(AAURACRONPCGChaosIsland* Island, FVector const& Location); \
	virtual bool ServerDestroyAllChaosIslands_Validate(); \
	virtual void ServerDestroyAllChaosIslands_Implementation(); \
	virtual bool ServerSpawnChaosIsland_Validate(FVector const& ); \
	virtual void ServerSpawnChaosIsland_Implementation(FVector const& Location); \
	DECLARE_FUNCTION(execOnRep_MapPhaseChanged); \
	DECLARE_FUNCTION(execOnRep_ChaosIslandsUpdated); \
	DECLARE_FUNCTION(execCalculateLineIntersection); \
	DECLARE_FUNCTION(execUpdateEffectsIntensity); \
	DECLARE_FUNCTION(execIsPointTooCloseToExistingIslands); \
	DECLARE_FUNCTION(execSpawnChaosPortal); \
	DECLARE_FUNCTION(execSpawnChaosIsland); \
	DECLARE_FUNCTION(execClientNotifyAllIslandsDestroyed); \
	DECLARE_FUNCTION(execClientNotifyIslandSpawned); \
	DECLARE_FUNCTION(execServerDestroyAllChaosIslands); \
	DECLARE_FUNCTION(execServerSpawnChaosIsland); \
	DECLARE_FUNCTION(execSetAllChaosPortalsActive); \
	DECLARE_FUNCTION(execSetAllChaosIslandsActive); \
	DECLARE_FUNCTION(execFindAllFlowIntersections); \
	DECLARE_FUNCTION(execIsPointAtFlowIntersection); \
	DECLARE_FUNCTION(execGetAllChaosPortals); \
	DECLARE_FUNCTION(execGetAllChaosIslands); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateChaosIslands); \
	DECLARE_FUNCTION(execInitialize);


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGChaosIslandManager(); \
	friend struct Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGChaosIslandManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGChaosIslandManager) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		PrismalFlow=NETFIELD_REP_START, \
		ChaosIslands, \
		ChaosPortals, \
		MaxChaosIslands, \
		bAutoSpawnPortals, \
		CurrentMapPhase, \
		NETFIELD_REP_END=CurrentMapPhase	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGChaosIslandManager(AAURACRONPCGChaosIslandManager&&) = delete; \
	AAURACRONPCGChaosIslandManager(const AAURACRONPCGChaosIslandManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGChaosIslandManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGChaosIslandManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGChaosIslandManager) \
	NO_API virtual ~AAURACRONPCGChaosIslandManager();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_35_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h_38_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGChaosIslandManager;

// ********** End Class AAURACRONPCGChaosIslandManager *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
