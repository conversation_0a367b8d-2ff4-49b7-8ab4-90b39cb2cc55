// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGChaosPortal() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EChaosPortalType();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EChaosPortalType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EChaosPortalType;
static UEnum* EChaosPortalType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EChaosPortalType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EChaosPortalType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EChaosPortalType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EChaosPortalType"));
	}
	return Z_Registration_Info_UEnum_EChaosPortalType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EChaosPortalType>()
{
	return EChaosPortalType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de portais de caos\n */" },
#endif
		{ "Elite.DisplayName", "Elite" },
		{ "Elite.Name", "EChaosPortalType::Elite" },
		{ "Legendary.DisplayName", "Lend\xc3\xa1rio" },
		{ "Legendary.Name", "EChaosPortalType::Legendary" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
		{ "Standard.DisplayName", "Padr\xc3\xa3o" },
		{ "Standard.Name", "EChaosPortalType::Standard" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de portais de caos" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EChaosPortalType::Standard", (int64)EChaosPortalType::Standard },
		{ "EChaosPortalType::Elite", (int64)EChaosPortalType::Elite },
		{ "EChaosPortalType::Legendary", (int64)EChaosPortalType::Legendary },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EChaosPortalType",
	"EChaosPortalType",
	Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EChaosPortalType()
{
	if (!Z_Registration_Info_UEnum_EChaosPortalType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EChaosPortalType.InnerSingleton, Z_Construct_UEnum_AURACRON_EChaosPortalType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EChaosPortalType.InnerSingleton;
}
// ********** End Enum EChaosPortalType ************************************************************

// ********** Begin Class AAURACRONPCGChaosPortal Function ActivateEnvironmentalHazard *************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics
{
	struct AURACRONPCGChaosPortal_eventActivateEnvironmentalHazard_Parms
	{
		float HazardIntensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar perigo ambiental */" },
#endif
		{ "CPP_Default_HazardIntensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar perigo ambiental" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HazardIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::NewProp_HazardIntensity = { "HazardIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventActivateEnvironmentalHazard_Parms, HazardIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::NewProp_HazardIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "ActivateEnvironmentalHazard", Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::AURACRONPCGChaosPortal_eventActivateEnvironmentalHazard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::AURACRONPCGChaosPortal_eventActivateEnvironmentalHazard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execActivateEnvironmentalHazard)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_HazardIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateEnvironmentalHazard(Z_Param_HazardIntensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function ActivateEnvironmentalHazard ***************

// ********** Begin Class AAURACRONPCGChaosPortal Function ActivatePortal **************************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics
{
	struct AURACRONPCGChaosPortal_eventActivatePortal_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar portal com dura\xc3\xa7\xc3\xa3o e intensidade espec\xc3\xad""ficas */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar portal com dura\xc3\xa7\xc3\xa3o e intensidade espec\xc3\xad""ficas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventActivatePortal_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventActivatePortal_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "ActivatePortal", Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::AURACRONPCGChaosPortal_eventActivatePortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::AURACRONPCGChaosPortal_eventActivatePortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execActivatePortal)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivatePortal(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function ActivatePortal ****************************

// ********** Begin Class AAURACRONPCGChaosPortal Function ActivateTerrainInstability **************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics
{
	struct AURACRONPCGChaosPortal_eventActivateTerrainInstability_Parms
	{
		float InstabilityIntensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar instabilidade de terreno */" },
#endif
		{ "CPP_Default_InstabilityIntensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar instabilidade de terreno" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InstabilityIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::NewProp_InstabilityIntensity = { "InstabilityIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventActivateTerrainInstability_Parms, InstabilityIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::NewProp_InstabilityIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "ActivateTerrainInstability", Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::AURACRONPCGChaosPortal_eventActivateTerrainInstability_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::AURACRONPCGChaosPortal_eventActivateTerrainInstability_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execActivateTerrainInstability)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_InstabilityIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateTerrainInstability(Z_Param_InstabilityIntensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function ActivateTerrainInstability ****************

// ********** Begin Class AAURACRONPCGChaosPortal Function DeactivatePortal ************************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics
{
	struct AURACRONPCGChaosPortal_eventDeactivatePortal_Parms
	{
		float FadeOutTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desativar portal com fade out */" },
#endif
		{ "CPP_Default_FadeOutTime", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar portal com fade out" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeOutTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::NewProp_FadeOutTime = { "FadeOutTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventDeactivatePortal_Parms, FadeOutTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::NewProp_FadeOutTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "DeactivatePortal", Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::AURACRONPCGChaosPortal_eventDeactivatePortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::AURACRONPCGChaosPortal_eventDeactivatePortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execDeactivatePortal)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_FadeOutTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivatePortal(Z_Param_FadeOutTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function DeactivatePortal **************************

// ********** Begin Class AAURACRONPCGChaosPortal Function IsAtFlowIntersection ********************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics
{
	struct AURACRONPCGChaosPortal_eventIsAtFlowIntersection_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se o portal est\xc3\xa1 em um ponto de interse\xc3\xa7\xc3\xa3o do Fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se o portal est\xc3\xa1 em um ponto de interse\xc3\xa7\xc3\xa3o do Fluxo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGChaosPortal_eventIsAtFlowIntersection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosPortal_eventIsAtFlowIntersection_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "IsAtFlowIntersection", Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::AURACRONPCGChaosPortal_eventIsAtFlowIntersection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x40040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::AURACRONPCGChaosPortal_eventIsAtFlowIntersection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execIsAtFlowIntersection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAtFlowIntersection();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function IsAtFlowIntersection **********************

// ********** Begin Class AAURACRONPCGChaosPortal Function OnPlayerEnterPortalRadius ***************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics
{
	struct AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando jogador entra no raio do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando jogador entra no raio do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "OnPlayerEnterPortalRadius", Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::AURACRONPCGChaosPortal_eventOnPlayerEnterPortalRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execOnPlayerEnterPortalRadius)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerEnterPortalRadius(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function OnPlayerEnterPortalRadius *****************

// ********** Begin Class AAURACRONPCGChaosPortal Function OnPlayerExitPortalRadius ****************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics
{
	struct AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando jogador sai do raio do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando jogador sai do raio do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "OnPlayerExitPortalRadius", Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::AURACRONPCGChaosPortal_eventOnPlayerExitPortalRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execOnPlayerExitPortalRadius)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerExitPortalRadius(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function OnPlayerExitPortalRadius ******************

// ********** Begin Class AAURACRONPCGChaosPortal Function SetPortalIntensity **********************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics
{
	struct AURACRONPCGChaosPortal_eventSetPortalIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar intensidade do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar intensidade do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSetPortalIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SetPortalIntensity", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::AURACRONPCGChaosPortal_eventSetPortalIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::AURACRONPCGChaosPortal_eventSetPortalIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSetPortalIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPortalIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SetPortalIntensity ************************

// ********** Begin Class AAURACRONPCGChaosPortal Function SetPortalLifetime ***********************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics
{
	struct AURACRONPCGChaosPortal_eventSetPortalLifetime_Parms
	{
		float Lifetime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar tempo de vida do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar tempo de vida do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Lifetime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::NewProp_Lifetime = { "Lifetime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSetPortalLifetime_Parms, Lifetime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::NewProp_Lifetime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SetPortalLifetime", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::AURACRONPCGChaosPortal_eventSetPortalLifetime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::AURACRONPCGChaosPortal_eventSetPortalLifetime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSetPortalLifetime)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Lifetime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPortalLifetime(Z_Param_Lifetime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SetPortalLifetime *************************

// ********** Begin Class AAURACRONPCGChaosPortal Function SetPortalRadius *************************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics
{
	struct AURACRONPCGChaosPortal_eventSetPortalRadius_Parms
	{
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar raio do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar raio do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSetPortalRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SetPortalRadius", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::AURACRONPCGChaosPortal_eventSetPortalRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::AURACRONPCGChaosPortal_eventSetPortalRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSetPortalRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPortalRadius(Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SetPortalRadius ***************************

// ********** Begin Class AAURACRONPCGChaosPortal Function SetPortalType ***************************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics
{
	struct AURACRONPCGChaosPortal_eventSetPortalType_Parms
	{
		EChaosPortalType Type;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar tipo do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar tipo do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSetPortalType_Parms, Type), Z_Construct_UEnum_AURACRON_EChaosPortalType, METADATA_PARAMS(0, nullptr) }; // 4235740673
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::NewProp_Type,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SetPortalType", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::AURACRONPCGChaosPortal_eventSetPortalType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::AURACRONPCGChaosPortal_eventSetPortalType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSetPortalType)
{
	P_GET_ENUM(EChaosPortalType,Z_Param_Type);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPortalType(EChaosPortalType(Z_Param_Type));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SetPortalType *****************************

// ********** Begin Class AAURACRONPCGChaosPortal Function SetQualityScale *************************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics
{
	struct AURACRONPCGChaosPortal_eventSetQualityScale_Parms
	{
		float NewQualityScale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar escala de qualidade (para ajuste de performance) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar escala de qualidade (para ajuste de performance)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewQualityScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::NewProp_NewQualityScale = { "NewQualityScale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSetQualityScale_Parms, NewQualityScale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::NewProp_NewQualityScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SetQualityScale", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::AURACRONPCGChaosPortal_eventSetQualityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::AURACRONPCGChaosPortal_eventSetQualityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSetQualityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewQualityScale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetQualityScale(Z_Param_NewQualityScale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SetQualityScale ***************************

// ********** Begin Class AAURACRONPCGChaosPortal Function SpawnEnvironmentalHazardVisual **********
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics
{
	struct AURACRONPCGChaosPortal_eventSpawnEnvironmentalHazardVisual_Parms
	{
		FVector Location;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar efeito visual de perigo ambiental */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar efeito visual de perigo ambiental" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnEnvironmentalHazardVisual_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnEnvironmentalHazardVisual_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SpawnEnvironmentalHazardVisual", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::AURACRONPCGChaosPortal_eventSpawnEnvironmentalHazardVisual_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::AURACRONPCGChaosPortal_eventSpawnEnvironmentalHazardVisual_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSpawnEnvironmentalHazardVisual)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnEnvironmentalHazardVisual(Z_Param_Location,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SpawnEnvironmentalHazardVisual ************

// ********** Begin Class AAURACRONPCGChaosPortal Function SpawnHighRiskReward *********************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics
{
	struct AURACRONPCGChaosPortal_eventSpawnHighRiskReward_Parms
	{
		float RewardTier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar recompensa de alto risco */" },
#endif
		{ "CPP_Default_RewardTier", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar recompensa de alto risco" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardTier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::NewProp_RewardTier = { "RewardTier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnHighRiskReward_Parms, RewardTier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::NewProp_RewardTier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SpawnHighRiskReward", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::AURACRONPCGChaosPortal_eventSpawnHighRiskReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::AURACRONPCGChaosPortal_eventSpawnHighRiskReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSpawnHighRiskReward)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_RewardTier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnHighRiskReward(Z_Param_RewardTier);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SpawnHighRiskReward ***********************

// ********** Begin Class AAURACRONPCGChaosPortal Function SpawnHighRiskRewardVisual ***************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics
{
	struct AURACRONPCGChaosPortal_eventSpawnHighRiskRewardVisual_Parms
	{
		FVector Location;
		float RewardTier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar efeito visual de recompensa de alto risco */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar efeito visual de recompensa de alto risco" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardTier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnHighRiskRewardVisual_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::NewProp_RewardTier = { "RewardTier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnHighRiskRewardVisual_Parms, RewardTier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::NewProp_RewardTier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SpawnHighRiskRewardVisual", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::AURACRONPCGChaosPortal_eventSpawnHighRiskRewardVisual_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::AURACRONPCGChaosPortal_eventSpawnHighRiskRewardVisual_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSpawnHighRiskRewardVisual)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_RewardTier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnHighRiskRewardVisual(Z_Param_Location,Z_Param_RewardTier);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SpawnHighRiskRewardVisual *****************

// ********** Begin Class AAURACRONPCGChaosPortal Function SpawnTerrainInstabilityVisual ***********
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics
{
	struct AURACRONPCGChaosPortal_eventSpawnTerrainInstabilityVisual_Parms
	{
		FVector Location;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar efeito visual de instabilidade de terreno */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar efeito visual de instabilidade de terreno" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnTerrainInstabilityVisual_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventSpawnTerrainInstabilityVisual_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "SpawnTerrainInstabilityVisual", Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::AURACRONPCGChaosPortal_eventSpawnTerrainInstabilityVisual_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::AURACRONPCGChaosPortal_eventSpawnTerrainInstabilityVisual_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execSpawnTerrainInstabilityVisual)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnTerrainInstabilityVisual(Z_Param_Location,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function SpawnTerrainInstabilityVisual *************

// ********** Begin Class AAURACRONPCGChaosPortal Function TriggerPortalEffect *********************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics
{
	struct AURACRONPCGChaosPortal_eventTriggerPortalEffect_Parms
	{
		float SpawnProbability;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Disparar efeito do portal com probabilidade */" },
#endif
		{ "CPP_Default_SpawnProbability", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Disparar efeito do portal com probabilidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnProbability;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::NewProp_SpawnProbability = { "SpawnProbability", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventTriggerPortalEffect_Parms, SpawnProbability), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::NewProp_SpawnProbability,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "TriggerPortalEffect", Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::AURACRONPCGChaosPortal_eventTriggerPortalEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::AURACRONPCGChaosPortal_eventTriggerPortalEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execTriggerPortalEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SpawnProbability);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerPortalEffect(Z_Param_SpawnProbability);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function TriggerPortalEffect ***********************

// ********** Begin Class AAURACRONPCGChaosPortal Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics
{
	struct AURACRONPCGChaosPortal_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar portal para fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar portal para fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosPortal_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosPortal, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::AURACRONPCGChaosPortal_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::AURACRONPCGChaosPortal_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosPortal::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosPortal Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGChaosPortal **************************************************
void AAURACRONPCGChaosPortal::StaticRegisterNativesAAURACRONPCGChaosPortal()
{
	UClass* Class = AAURACRONPCGChaosPortal::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateEnvironmentalHazard", &AAURACRONPCGChaosPortal::execActivateEnvironmentalHazard },
		{ "ActivatePortal", &AAURACRONPCGChaosPortal::execActivatePortal },
		{ "ActivateTerrainInstability", &AAURACRONPCGChaosPortal::execActivateTerrainInstability },
		{ "DeactivatePortal", &AAURACRONPCGChaosPortal::execDeactivatePortal },
		{ "IsAtFlowIntersection", &AAURACRONPCGChaosPortal::execIsAtFlowIntersection },
		{ "OnPlayerEnterPortalRadius", &AAURACRONPCGChaosPortal::execOnPlayerEnterPortalRadius },
		{ "OnPlayerExitPortalRadius", &AAURACRONPCGChaosPortal::execOnPlayerExitPortalRadius },
		{ "SetPortalIntensity", &AAURACRONPCGChaosPortal::execSetPortalIntensity },
		{ "SetPortalLifetime", &AAURACRONPCGChaosPortal::execSetPortalLifetime },
		{ "SetPortalRadius", &AAURACRONPCGChaosPortal::execSetPortalRadius },
		{ "SetPortalType", &AAURACRONPCGChaosPortal::execSetPortalType },
		{ "SetQualityScale", &AAURACRONPCGChaosPortal::execSetQualityScale },
		{ "SpawnEnvironmentalHazardVisual", &AAURACRONPCGChaosPortal::execSpawnEnvironmentalHazardVisual },
		{ "SpawnHighRiskReward", &AAURACRONPCGChaosPortal::execSpawnHighRiskReward },
		{ "SpawnHighRiskRewardVisual", &AAURACRONPCGChaosPortal::execSpawnHighRiskRewardVisual },
		{ "SpawnTerrainInstabilityVisual", &AAURACRONPCGChaosPortal::execSpawnTerrainInstabilityVisual },
		{ "TriggerPortalEffect", &AAURACRONPCGChaosPortal::execTriggerPortalEffect },
		{ "UpdateForMapPhase", &AAURACRONPCGChaosPortal::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGChaosPortal;
UClass* AAURACRONPCGChaosPortal::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGChaosPortal;
	if (!Z_Registration_Info_UClass_AAURACRONPCGChaosPortal.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGChaosPortal"),
			Z_Registration_Info_UClass_AAURACRONPCGChaosPortal.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGChaosPortal,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGChaosPortal.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister()
{
	return AAURACRONPCGChaosPortal::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator que representa um portal de caos no mapa\n * Implementa o efeito especial ChaosPortals para a Fase 4 (Resolu\xc3\xa7\xc3\xa3o)\n * \n * Integra\xc3\xa7\xc3\xa3o com Ilhas Caos:\n * - Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n * - Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n * - Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGChaosPortal.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator que representa um portal de caos no mapa\nImplementa o efeito especial ChaosPortals para a Fase 4 (Resolu\xc3\xa7\xc3\xa3o)\n\nIntegra\xc3\xa7\xc3\xa3o com Ilhas Caos:\n- Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n- Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n- Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalMesh_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de malha est\xc3\xa1tica */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de malha est\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalEffect_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeito de part\xc3\xad""culas */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeito de part\xc3\xad""culas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalLight_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de luz */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de luz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalSound_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de \xc3\xa1udio */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de \xc3\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerSphere_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de colis\xc3\xa3o para trigger */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para trigger" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalDynamicMaterial_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material din\xc3\xa2mico do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material din\xc3\xa2mico do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectRadius_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de efeito do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de efeito do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalDuration_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do portal (0 = permanente) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do portal (0 = permanente)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalIntensity_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalColor_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationSpeed_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de rota\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de rota\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulsateFrequency_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia de pulsa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia de pulsa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulsateIntensity_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade de pulsa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade de pulsa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectInterval_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo entre efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo entre efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScale_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de qualidade (para ajuste de performance) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de qualidade (para ajuste de performance)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalType_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardsTable_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tabela de recompensas de alto risco */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tabela de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardProbability_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Probabilidade de gerar perigo ambiental */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Probabilidade de gerar perigo ambiental" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainInstabilityProbability_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Probabilidade de instabilidade de terreno */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Probabilidade de instabilidade de terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardProbability_MetaData[] = {
		{ "Category", "AURACRON|ChaosPortal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Probabilidade de recompensa de alto risco */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Probabilidade de recompensa de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TriggerSphere;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalDynamicMaterial;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PortalDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PortalIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulsateFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulsateIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScale;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PortalType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PortalType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HighRiskRewardsTable;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalHazardProbability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainInstabilityProbability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighRiskRewardProbability;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateEnvironmentalHazard, "ActivateEnvironmentalHazard" }, // 827081512
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivatePortal, "ActivatePortal" }, // 4063424152
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_ActivateTerrainInstability, "ActivateTerrainInstability" }, // 245122606
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_DeactivatePortal, "DeactivatePortal" }, // 598313518
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_IsAtFlowIntersection, "IsAtFlowIntersection" }, // 2707132760
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerEnterPortalRadius, "OnPlayerEnterPortalRadius" }, // 3203363848
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_OnPlayerExitPortalRadius, "OnPlayerExitPortalRadius" }, // 34280459
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalIntensity, "SetPortalIntensity" }, // 3686116922
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalLifetime, "SetPortalLifetime" }, // 3603394657
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalRadius, "SetPortalRadius" }, // 3244084395
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetPortalType, "SetPortalType" }, // 2500164952
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SetQualityScale, "SetQualityScale" }, // 2608015410
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnEnvironmentalHazardVisual, "SpawnEnvironmentalHazardVisual" }, // 2788731780
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskReward, "SpawnHighRiskReward" }, // 1844299203
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnHighRiskRewardVisual, "SpawnHighRiskRewardVisual" }, // 3050282497
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_SpawnTerrainInstabilityVisual, "SpawnTerrainInstabilityVisual" }, // 116165276
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_TriggerPortalEffect, "TriggerPortalEffect" }, // 2499065832
		{ &Z_Construct_UFunction_AAURACRONPCGChaosPortal_UpdateForMapPhase, "UpdateForMapPhase" }, // 684355906
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGChaosPortal>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalMesh = { "PortalMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalMesh_MetaData), NewProp_PortalMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalEffect = { "PortalEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalEffect_MetaData), NewProp_PortalEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalLight = { "PortalLight", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalLight), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalLight_MetaData), NewProp_PortalLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalSound = { "PortalSound", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalSound), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalSound_MetaData), NewProp_PortalSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_TriggerSphere = { "TriggerSphere", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, TriggerSphere), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerSphere_MetaData), NewProp_TriggerSphere_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalDynamicMaterial = { "PortalDynamicMaterial", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalDynamicMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalDynamicMaterial_MetaData), NewProp_PortalDynamicMaterial_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_EffectRadius = { "EffectRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, EffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectRadius_MetaData), NewProp_EffectRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalDuration = { "PortalDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalDuration_MetaData), NewProp_PortalDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalIntensity = { "PortalIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalIntensity_MetaData), NewProp_PortalIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalColor = { "PortalColor", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalColor_MetaData), NewProp_PortalColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_RotationSpeed = { "RotationSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, RotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationSpeed_MetaData), NewProp_RotationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PulsateFrequency = { "PulsateFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PulsateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulsateFrequency_MetaData), NewProp_PulsateFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PulsateIntensity = { "PulsateIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PulsateIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulsateIntensity_MetaData), NewProp_PulsateIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_EffectInterval = { "EffectInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, EffectInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectInterval_MetaData), NewProp_EffectInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_QualityScale = { "QualityScale", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, QualityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScale_MetaData), NewProp_QualityScale_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalType = { "PortalType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, PortalType), Z_Construct_UEnum_AURACRON_EChaosPortalType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalType_MetaData), NewProp_PortalType_MetaData) }; // 4235740673
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_HighRiskRewardsTable = { "HighRiskRewardsTable", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, HighRiskRewardsTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardsTable_MetaData), NewProp_HighRiskRewardsTable_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_EnvironmentalHazardProbability = { "EnvironmentalHazardProbability", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, EnvironmentalHazardProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardProbability_MetaData), NewProp_EnvironmentalHazardProbability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_TerrainInstabilityProbability = { "TerrainInstabilityProbability", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, TerrainInstabilityProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainInstabilityProbability_MetaData), NewProp_TerrainInstabilityProbability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_HighRiskRewardProbability = { "HighRiskRewardProbability", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, HighRiskRewardProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardProbability_MetaData), NewProp_HighRiskRewardProbability_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosPortal, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_TriggerSphere,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalDynamicMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_EffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_RotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PulsateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PulsateIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_EffectInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_QualityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_PortalType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_HighRiskRewardsTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_EnvironmentalHazardProbability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_TerrainInstabilityProbability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_HighRiskRewardProbability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::NewProp_CurrentMapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::ClassParams = {
	&AAURACRONPCGChaosPortal::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGChaosPortal.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGChaosPortal.OuterSingleton, Z_Construct_UClass_AAURACRONPCGChaosPortal_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGChaosPortal.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGChaosPortal);
AAURACRONPCGChaosPortal::~AAURACRONPCGChaosPortal() {}
// ********** End Class AAURACRONPCGChaosPortal ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EChaosPortalType_StaticEnum, TEXT("EChaosPortalType"), &Z_Registration_Info_UEnum_EChaosPortalType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4235740673U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGChaosPortal, AAURACRONPCGChaosPortal::StaticClass, TEXT("AAURACRONPCGChaosPortal"), &Z_Registration_Info_UClass_AAURACRONPCGChaosPortal, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGChaosPortal), 3285225987U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h__Script_AURACRON_2957310861(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosPortal_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
