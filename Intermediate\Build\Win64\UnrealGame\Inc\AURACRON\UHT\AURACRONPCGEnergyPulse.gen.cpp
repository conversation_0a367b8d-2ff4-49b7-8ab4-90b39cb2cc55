// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGEnergyPulse.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGEnergyPulse() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnergyType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPortalType();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function ApplyPulseEffects ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeitos aos jogadores e ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos aos jogadores e ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "ApplyPulseEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execApplyPulseEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyPulseEffects();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function ApplyPulseEffects *************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateEnergyPulseForPortalType **********
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms
	{
		EAURACRONPortalType PortalType;
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia baseado no tipo de portal */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia baseado no tipo de portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PortalType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PortalType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType = { "PortalType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms, PortalType), Z_Construct_UEnum_AURACRON_EAURACRONPortalType, METADATA_PARAMS(0, nullptr) }; // 1562177233
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateEnergyPulseForPortalType", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateEnergyPulseForPortalType)
{
	P_GET_ENUM(EAURACRONPortalType,Z_Param_PortalType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateEnergyPulseForPortalType(EAURACRONPortalType(Z_Param_PortalType),Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateEnergyPulseForPortalType ************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateGoldenEnergyPulse *****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia dourada (Portal Radiante) */" },
#endif
		{ "CPP_Default_Duration", "3.000000" },
		{ "CPP_Default_Intensity", "1.200000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia dourada (Portal Radiante)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateGoldenEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateGoldenEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateGoldenEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateGoldenEnergyPulse *******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateSilverEnergyPulse *****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia prateada (Portal Zephyr) */" },
#endif
		{ "CPP_Default_Duration", "2.500000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia prateada (Portal Zephyr)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateSilverEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateSilverEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateSilverEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateSilverEnergyPulse *******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateVioletEnergyPulse *****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia violeta (Portal Umbral) */" },
#endif
		{ "CPP_Default_Duration", "3.500000" },
		{ "CPP_Default_Intensity", "1.500000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia violeta (Portal Umbral)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateVioletEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateVioletEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateVioletEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateVioletEnergyPulse *******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function OnPlayerEnterPulseRadius ****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics
{
	struct AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando jogador entra no raio do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando jogador entra no raio do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms), &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "OnPlayerEnterPulseRadius", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execOnPlayerEnterPulseRadius)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerEnterPulseRadius(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function OnPlayerEnterPulseRadius ******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetPulseDuration ************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms
	{
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar tempo de vida do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar tempo de vida do pulso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetPulseDuration", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetPulseDuration)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPulseDuration(Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetPulseDuration **************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetPulseIntensity ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar intensidade do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar intensidade do pulso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetPulseIntensity", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetPulseIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPulseIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetPulseIntensity *************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetPulseRadius **************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms
	{
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar raio do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar raio do pulso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetPulseRadius", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetPulseRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPulseRadius(Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetPulseRadius ****************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetQualityScale *************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetQualityScale_Parms
	{
		float NewQualityScale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar escala de qualidade (para ajuste de performance) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar escala de qualidade (para ajuste de performance)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewQualityScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::NewProp_NewQualityScale = { "NewQualityScale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetQualityScale_Parms, NewQualityScale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::NewProp_NewQualityScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetQualityScale", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::AURACRONPCGEnergyPulse_eventSetQualityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::AURACRONPCGEnergyPulse_eventSetQualityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetQualityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewQualityScale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetQualityScale(Z_Param_NewQualityScale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetQualityScale ***************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function TriggerPulse ****************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventTriggerPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Disparar pulso com dura\xc3\xa7\xc3\xa3o e intensidade espec\xc3\xad""ficas */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Disparar pulso com dura\xc3\xa7\xc3\xa3o e intensidade espec\xc3\xad""ficas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventTriggerPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventTriggerPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "TriggerPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::AURACRONPCGEnergyPulse_eventTriggerPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::AURACRONPCGEnergyPulse_eventTriggerPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execTriggerPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function TriggerPulse ******************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics
{
	struct AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar pulso para fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar pulso para fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGEnergyPulse **************************************************
void AAURACRONPCGEnergyPulse::StaticRegisterNativesAAURACRONPCGEnergyPulse()
{
	UClass* Class = AAURACRONPCGEnergyPulse::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyPulseEffects", &AAURACRONPCGEnergyPulse::execApplyPulseEffects },
		{ "CreateEnergyPulseForPortalType", &AAURACRONPCGEnergyPulse::execCreateEnergyPulseForPortalType },
		{ "CreateGoldenEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateGoldenEnergyPulse },
		{ "CreateSilverEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateSilverEnergyPulse },
		{ "CreateVioletEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateVioletEnergyPulse },
		{ "OnPlayerEnterPulseRadius", &AAURACRONPCGEnergyPulse::execOnPlayerEnterPulseRadius },
		{ "SetPulseDuration", &AAURACRONPCGEnergyPulse::execSetPulseDuration },
		{ "SetPulseIntensity", &AAURACRONPCGEnergyPulse::execSetPulseIntensity },
		{ "SetPulseRadius", &AAURACRONPCGEnergyPulse::execSetPulseRadius },
		{ "SetQualityScale", &AAURACRONPCGEnergyPulse::execSetQualityScale },
		{ "TriggerPulse", &AAURACRONPCGEnergyPulse::execTriggerPulse },
		{ "UpdateForMapPhase", &AAURACRONPCGEnergyPulse::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse;
UClass* AAURACRONPCGEnergyPulse::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGEnergyPulse;
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGEnergyPulse"),
			Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGEnergyPulse,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister()
{
	return AAURACRONPCGEnergyPulse::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator que representa um pulso de energia no mapa\n * Implementa o efeito especial EnergyPulses para a Fase 4 (Resolu\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGEnergyPulse.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator que representa um pulso de energia no mapa\nImplementa o efeito especial EnergyPulses para a Fase 4 (Resolu\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseEffect_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeito de part\xc3\xad""culas */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeito de part\xc3\xad""culas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseLight_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de luz */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de luz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseSound_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de \xc3\xa1udio */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de \xc3\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseSphere_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de colis\xc3\xa3o para detec\xc3\xa7\xc3\xa3o */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para detec\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseRadius_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseDuration_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do pulso (0 = \xc3\xbanico) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do pulso (0 = \xc3\xbanico)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseIntensity_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseColor_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyType_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de energia do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de energia do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpansionSpeed_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de expans\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de expans\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScale_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de qualidade (para ajuste de performance) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de qualidade (para ajuste de performance)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseSphere;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PulseColor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnergyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnergyType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpansionSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScale;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects, "ApplyPulseEffects" }, // 4148912496
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType, "CreateEnergyPulseForPortalType" }, // 3677538082
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse, "CreateGoldenEnergyPulse" }, // 1726974687
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse, "CreateSilverEnergyPulse" }, // 265098458
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse, "CreateVioletEnergyPulse" }, // 342310940
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius, "OnPlayerEnterPulseRadius" }, // 1204967059
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration, "SetPulseDuration" }, // 4070704072
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity, "SetPulseIntensity" }, // 1922932354
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius, "SetPulseRadius" }, // 3422307786
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale, "SetQualityScale" }, // 3912224943
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse, "TriggerPulse" }, // 155446430
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase, "UpdateForMapPhase" }, // 374909575
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGEnergyPulse>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseEffect = { "PulseEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseEffect_MetaData), NewProp_PulseEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseLight = { "PulseLight", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseLight), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseLight_MetaData), NewProp_PulseLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSound = { "PulseSound", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseSound), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseSound_MetaData), NewProp_PulseSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSphere = { "PulseSphere", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseSphere), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseSphere_MetaData), NewProp_PulseSphere_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseRadius = { "PulseRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseRadius_MetaData), NewProp_PulseRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseDuration = { "PulseDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseDuration_MetaData), NewProp_PulseDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseIntensity = { "PulseIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseIntensity_MetaData), NewProp_PulseIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseColor = { "PulseColor", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseColor_MetaData), NewProp_PulseColor_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType = { "EnergyType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, EnergyType), Z_Construct_UEnum_AURACRON_EAURACRONEnergyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyType_MetaData), NewProp_EnergyType_MetaData) }; // 81350420
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ExpansionSpeed = { "ExpansionSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, ExpansionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpansionSpeed_MetaData), NewProp_ExpansionSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_QualityScale = { "QualityScale", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, QualityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScale_MetaData), NewProp_QualityScale_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSphere,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ExpansionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_QualityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::ClassParams = {
	&AAURACRONPCGEnergyPulse::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.OuterSingleton, Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGEnergyPulse);
AAURACRONPCGEnergyPulse::~AAURACRONPCGEnergyPulse() {}
// ********** End Class AAURACRONPCGEnergyPulse ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGEnergyPulse, AAURACRONPCGEnergyPulse::StaticClass, TEXT("AAURACRONPCGEnergyPulse"), &Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGEnergyPulse), 739080840U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_1259596027(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
