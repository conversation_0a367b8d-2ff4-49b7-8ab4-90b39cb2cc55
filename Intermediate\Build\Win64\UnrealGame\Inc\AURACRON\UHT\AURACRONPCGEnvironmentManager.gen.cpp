// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "AURACRONStructs.h"
#include "Engine/TimerHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGEnvironmentManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPortal_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONTeleportDestinations();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDirectionalLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPostProcessComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkyLightComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONEnvironmentArray *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray;
class UScriptStruct* FAURACRONEnvironmentArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONEnvironmentArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONEnvironmentArray"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para array de ambientes (resolve problema UHT com TMap<Enum, TArray<Type>>)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para array de ambientes (resolve problema UHT com TMap<Enum, TArray<Type>>)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Environments_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Environments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Environments;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONEnvironmentArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::NewProp_Environments_Inner = { "Environments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::NewProp_Environments = { "Environments", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentArray, Environments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Environments_MetaData), NewProp_Environments_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::NewProp_Environments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::NewProp_Environments,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONEnvironmentArray",
	Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::PropPointers),
	sizeof(FAURACRONEnvironmentArray),
	alignof(FAURACRONEnvironmentArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentArray()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONEnvironmentArray *******************************************

// ********** Begin ScriptStruct FAURACRONTeleportDestinations *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations;
class UScriptStruct* FAURACRONTeleportDestinations::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONTeleportDestinations, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONTeleportDestinations"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar destinos de teletransporte para portais t\xc3\xa1ticos\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar destinos de teletransporte para portais t\xc3\xa1ticos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Locations_MetaData[] = {
		{ "Category", "AURACRONTeleportDestinations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\xa7\xc3\xb5""es de destino para teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\xa7\xc3\xb5""es de destino para teletransporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotations_MetaData[] = {
		{ "Category", "AURACRONTeleportDestinations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\xa7\xc3\xb5""es de destino para teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\xa7\xc3\xb5""es de destino para teletransporte" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Locations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Locations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Rotations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONTeleportDestinations>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Locations_Inner = { "Locations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Locations = { "Locations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTeleportDestinations, Locations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Locations_MetaData), NewProp_Locations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Rotations_Inner = { "Rotations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Rotations = { "Rotations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONTeleportDestinations, Rotations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotations_MetaData), NewProp_Rotations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Locations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Locations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Rotations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewProp_Rotations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONTeleportDestinations",
	Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::PropPointers),
	sizeof(FAURACRONTeleportDestinations),
	alignof(FAURACRONTeleportDestinations),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONTeleportDestinations()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONTeleportDestinations ***************************************

// ********** Begin ScriptStruct FAURACRONDeviceEnvironmentSettings ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings;
class UScriptStruct* FAURACRONDeviceEnvironmentSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONDeviceEnvironmentSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de ambiente para diferentes tipos de dispositivos\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de ambiente para diferentes tipos de dispositivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEntryDevice_MetaData[] = {
		{ "Category", "AURACRONDeviceEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se \xc3\xa9 um dispositivo de entrada (baixo desempenho) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se \xc3\xa9 um dispositivo de entrada (baixo desempenho)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllEnvironmentsAccessible_MetaData[] = {
		{ "Category", "AURACRONDeviceEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se todos os ambientes est\xc3\xa3o acess\xc3\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se todos os ambientes est\xc3\xa3o acess\xc3\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPreviewZones_MetaData[] = {
		{ "Category", "AURACRONDeviceEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se os ambientes n\xc3\xa3o ativos devem ser mostrados como \"preview zones\" */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se os ambientes n\xc3\xa3o ativos devem ser mostrados como \"preview zones\"" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static void NewProp_bAllEnvironmentsAccessible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllEnvironmentsAccessible;
	static void NewProp_bShowPreviewZones_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPreviewZones;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONDeviceEnvironmentSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((FAURACRONDeviceEnvironmentSettings*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONDeviceEnvironmentSettings), &Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEntryDevice_MetaData), NewProp_bIsEntryDevice_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bAllEnvironmentsAccessible_SetBit(void* Obj)
{
	((FAURACRONDeviceEnvironmentSettings*)Obj)->bAllEnvironmentsAccessible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bAllEnvironmentsAccessible = { "bAllEnvironmentsAccessible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONDeviceEnvironmentSettings), &Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bAllEnvironmentsAccessible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllEnvironmentsAccessible_MetaData), NewProp_bAllEnvironmentsAccessible_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bShowPreviewZones_SetBit(void* Obj)
{
	((FAURACRONDeviceEnvironmentSettings*)Obj)->bShowPreviewZones = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bShowPreviewZones = { "bShowPreviewZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONDeviceEnvironmentSettings), &Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bShowPreviewZones_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPreviewZones_MetaData), NewProp_bShowPreviewZones_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bAllEnvironmentsAccessible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewProp_bShowPreviewZones,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONDeviceEnvironmentSettings",
	Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::PropPointers),
	sizeof(FAURACRONDeviceEnvironmentSettings),
	alignof(FAURACRONDeviceEnvironmentSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONDeviceEnvironmentSettings **********************************

// ********** Begin ScriptStruct FAURACRONEnvironmentSettings **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings;
class UScriptStruct* FAURACRONEnvironmentSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONEnvironmentSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas de um ambiente\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas de um ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentName_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\xa7\xc3\xa3o das caracter\xc3\xadsticas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\xa7\xc3\xa3o das caracter\xc3\xadsticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseHeight_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura base do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura base do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightColor_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da ilumina\xc3\xa7\xc3\xa3o ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da ilumina\xc3\xa7\xc3\xa3o ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightIntensity_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da ilumina\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da ilumina\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogColor_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do fog/neblina */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do fog/neblina" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogDensity_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade do fog */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade do fog" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessSettings_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de post-processing */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de post-processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleEffects_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos de part\xc3\xad""culas espec\xc3\xad""ficos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos de part\xc3\xad""culas espec\xc3\xad""ficos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientSounds_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons ambiente espec\xc3\xad""ficos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons ambiente espec\xc3\xad""ficos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UniqueMechanics_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mec\xc3\xa2nicas \xc3\xbanicas do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mec\xc3\xa2nicas \xc3\xbanicas do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentDuration_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do ambiente em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do ambiente em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONEnvironmentSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o ambiente est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o ambiente est\xc3\xa1 ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnvironmentName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FogColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FogDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PostProcessSettings_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PostProcessSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PostProcessSettings;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParticleEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ParticleEffects;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AmbientSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AmbientSounds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UniqueMechanics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UniqueMechanics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_UniqueMechanics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentDuration;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONEnvironmentSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2509470107
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentName = { "EnvironmentName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, EnvironmentName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentName_MetaData), NewProp_EnvironmentName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_BaseHeight = { "BaseHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, BaseHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseHeight_MetaData), NewProp_BaseHeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_AmbientLightColor = { "AmbientLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, AmbientLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightColor_MetaData), NewProp_AmbientLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_LightIntensity = { "LightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, LightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightIntensity_MetaData), NewProp_LightIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_FogColor = { "FogColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, FogColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogColor_MetaData), NewProp_FogColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_FogDensity = { "FogDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, FogDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogDensity_MetaData), NewProp_FogDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_PostProcessSettings_ValueProp = { "PostProcessSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_PostProcessSettings_Key_KeyProp = { "PostProcessSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_PostProcessSettings = { "PostProcessSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, PostProcessSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessSettings_MetaData), NewProp_PostProcessSettings_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_ParticleEffects_Inner = { "ParticleEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_ParticleEffects = { "ParticleEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, ParticleEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleEffects_MetaData), NewProp_ParticleEffects_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_AmbientSounds_Inner = { "AmbientSounds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_AmbientSounds = { "AmbientSounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, AmbientSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientSounds_MetaData), NewProp_AmbientSounds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_UniqueMechanics_ValueProp = { "UniqueMechanics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_UniqueMechanics_Key_KeyProp = { "UniqueMechanics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_UniqueMechanics = { "UniqueMechanics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, UniqueMechanics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UniqueMechanics_MetaData), NewProp_UniqueMechanics_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentDuration = { "EnvironmentDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONEnvironmentSettings, EnvironmentDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentDuration_MetaData), NewProp_EnvironmentDuration_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONEnvironmentSettings*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONEnvironmentSettings), &Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_BaseHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_AmbientLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_LightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_FogColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_FogDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_PostProcessSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_PostProcessSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_PostProcessSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_ParticleEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_ParticleEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_AmbientSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_AmbientSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_UniqueMechanics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_UniqueMechanics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_UniqueMechanics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_EnvironmentDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONEnvironmentSettings",
	Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::PropPointers),
	sizeof(FAURACRONEnvironmentSettings),
	alignof(FAURACRONEnvironmentSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONEnvironmentSettings ****************************************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ActivateEnvironmentInstances *****
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics
{
	struct AURACRONPCGEnvironmentManager_eventActivateEnvironmentInstances_Parms
	{
		EAURACRONEnvironmentType EnvironmentType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar todas as inst\xc3\xa2ncias de um ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar todas as inst\xc3\xa2ncias de um ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventActivateEnvironmentInstances_Parms, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::NewProp_EnvironmentType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ActivateEnvironmentInstances", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::AURACRONPCGEnvironmentManager_eventActivateEnvironmentInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::AURACRONPCGEnvironmentManager_eventActivateEnvironmentInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execActivateEnvironmentInstances)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_EnvironmentType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateEnvironmentInstances(EAURACRONEnvironmentType(Z_Param_EnvironmentType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ActivateEnvironmentInstances *******

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ActivatePortalsForEnvironment ****
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics
{
	struct AURACRONPCGEnvironmentManager_eventActivatePortalsForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar portais para um ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar portais para um ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventActivatePortalsForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ActivatePortalsForEnvironment", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::AURACRONPCGEnvironmentManager_eventActivatePortalsForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::AURACRONPCGEnvironmentManager_eventActivatePortalsForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execActivatePortalsForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivatePortalsForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ActivatePortalsForEnvironment ******

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ApplyMapTacticalAdvantages *******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics
{
	struct AURACRONPCGEnvironmentManager_eventApplyMapTacticalAdvantages_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar vantagens t\xc3\xa1ticas do mapa atual aos jogadores */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar vantagens t\xc3\xa1ticas do mapa atual aos jogadores" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventApplyMapTacticalAdvantages_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ApplyMapTacticalAdvantages", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::AURACRONPCGEnvironmentManager_eventApplyMapTacticalAdvantages_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::AURACRONPCGEnvironmentManager_eventApplyMapTacticalAdvantages_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execApplyMapTacticalAdvantages)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyMapTacticalAdvantages(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ApplyMapTacticalAdvantages *********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ApplyTemporaryEnvironmentEffect **
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics
{
	struct AURACRONPCGEnvironmentManager_eventApplyTemporaryEnvironmentEffect_Parms
	{
		FString EffectName;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeitos especiais tempor\xc3\xa1rios */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos especiais tempor\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::NewProp_EffectName = { "EffectName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventApplyTemporaryEnvironmentEffect_Parms, EffectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectName_MetaData), NewProp_EffectName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventApplyTemporaryEnvironmentEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::NewProp_EffectName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ApplyTemporaryEnvironmentEffect", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::AURACRONPCGEnvironmentManager_eventApplyTemporaryEnvironmentEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::AURACRONPCGEnvironmentManager_eventApplyTemporaryEnvironmentEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execApplyTemporaryEnvironmentEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EffectName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTemporaryEnvironmentEffect(Z_Param_EffectName,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ApplyTemporaryEnvironmentEffect ****

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ApplyTransitionToInstances *******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics
{
	struct AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms
	{
		EAURACRONEnvironmentType EnvironmentType;
		float TransitionProgress;
		bool bFadingIn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar transi\xc3\xa7\xc3\xa3o a todas as inst\xc3\xa2ncias */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar transi\xc3\xa7\xc3\xa3o a todas as inst\xc3\xa2ncias" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static void NewProp_bFadingIn_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFadingIn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms, TransitionProgress), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_bFadingIn_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms*)Obj)->bFadingIn = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_bFadingIn = { "bFadingIn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_bFadingIn_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::NewProp_bFadingIn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ApplyTransitionToInstances", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::AURACRONPCGEnvironmentManager_eventApplyTransitionToInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execApplyTransitionToInstances)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_EnvironmentType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionProgress);
	P_GET_UBOOL(Z_Param_bFadingIn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTransitionToInstances(EAURACRONEnvironmentType(Z_Param_EnvironmentType),Z_Param_TransitionProgress,Z_Param_bFadingIn);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ApplyTransitionToInstances *********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function AreConvergenceSystemsReady *******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics
{
	struct AURACRONPCGEnvironmentManager_eventAreConvergenceSystemsReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseValidation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se os sistemas de converg\xc3\xaancia est\xc3\xa3o prontos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se os sistemas de converg\xc3\xaancia est\xc3\xa3o prontos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventAreConvergenceSystemsReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventAreConvergenceSystemsReady_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "AreConvergenceSystemsReady", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::AURACRONPCGEnvironmentManager_eventAreConvergenceSystemsReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::AURACRONPCGEnvironmentManager_eventAreConvergenceSystemsReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execAreConvergenceSystemsReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreConvergenceSystemsReady();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function AreConvergenceSystemsReady *********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function AreIntensificationSystemsReady ***
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics
{
	struct AURACRONPCGEnvironmentManager_eventAreIntensificationSystemsReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseValidation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se os sistemas de intensifica\xc3\xa7\xc3\xa3o est\xc3\xa3o prontos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se os sistemas de intensifica\xc3\xa7\xc3\xa3o est\xc3\xa3o prontos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventAreIntensificationSystemsReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventAreIntensificationSystemsReady_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "AreIntensificationSystemsReady", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::AURACRONPCGEnvironmentManager_eventAreIntensificationSystemsReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::AURACRONPCGEnvironmentManager_eventAreIntensificationSystemsReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execAreIntensificationSystemsReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreIntensificationSystemsReady();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function AreIntensificationSystemsReady *****

// ********** Begin Class AAURACRONPCGEnvironmentManager Function AreResolutionSystemsReady ********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics
{
	struct AURACRONPCGEnvironmentManager_eventAreResolutionSystemsReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseValidation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se os sistemas de resolu\xc3\xa7\xc3\xa3o est\xc3\xa3o prontos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se os sistemas de resolu\xc3\xa7\xc3\xa3o est\xc3\xa3o prontos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventAreResolutionSystemsReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventAreResolutionSystemsReady_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "AreResolutionSystemsReady", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::AURACRONPCGEnvironmentManager_eventAreResolutionSystemsReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::AURACRONPCGEnvironmentManager_eventAreResolutionSystemsReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execAreResolutionSystemsReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreResolutionSystemsReady();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function AreResolutionSystemsReady **********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureBlurredEnvironmentBoundaries 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics
{
	struct AURACRONPCGEnvironmentManager_eventConfigureBlurredEnvironmentBoundaries_Parms
	{
		float BlurIntensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar fronteiras confusas entre ambientes (dispositivos High) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar fronteiras confusas entre ambientes (dispositivos High)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlurIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::NewProp_BlurIntensity = { "BlurIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventConfigureBlurredEnvironmentBoundaries_Parms, BlurIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::NewProp_BlurIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureBlurredEnvironmentBoundaries", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::AURACRONPCGEnvironmentManager_eventConfigureBlurredEnvironmentBoundaries_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::AURACRONPCGEnvironmentManager_eventConfigureBlurredEnvironmentBoundaries_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureBlurredEnvironmentBoundaries)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_BlurIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureBlurredEnvironmentBoundaries(Z_Param_BlurIntensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureBlurredEnvironmentBoundaries 

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForAwakeningPhase *******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics
{
	struct AURACRONPCGEnvironmentManager_eventConfigureForAwakeningPhase_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar para Fase 1: Despertar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar para Fase 1: Despertar" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureForAwakeningPhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureForAwakeningPhase_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForAwakeningPhase", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::AURACRONPCGEnvironmentManager_eventConfigureForAwakeningPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::AURACRONPCGEnvironmentManager_eventConfigureForAwakeningPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForAwakeningPhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForAwakeningPhase(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForAwakeningPhase *********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForAwakeningPhaseModern *
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseConfiguration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar ambientes para fase de despertar (vers\xc3\xa3o moderna) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ambientes para fase de despertar (vers\xc3\xa3o moderna)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForAwakeningPhaseModern", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForAwakeningPhaseModern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForAwakeningPhaseModern();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForAwakeningPhaseModern ***

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForConvergencePhase *****
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics
{
	struct AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms
	{
		bool bIsEntryDevice;
		bool bIsMidDevice;
		bool bIsHighDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar para Fase 2: Converg\xc3\xaancia */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar para Fase 2: Converg\xc3\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static void NewProp_bIsMidDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMidDevice;
	static void NewProp_bIsHighDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHighDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms*)Obj)->bIsMidDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice = { "bIsMidDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms*)Obj)->bIsHighDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice = { "bIsHighDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForConvergencePhase", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::AURACRONPCGEnvironmentManager_eventConfigureForConvergencePhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForConvergencePhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_GET_UBOOL(Z_Param_bIsMidDevice);
	P_GET_UBOOL(Z_Param_bIsHighDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForConvergencePhase(Z_Param_bIsEntryDevice,Z_Param_bIsMidDevice,Z_Param_bIsHighDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForConvergencePhase *******

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForConvergencePhaseModern 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseConfiguration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar ambientes para fase de converg\xc3\xaancia (vers\xc3\xa3o moderna) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ambientes para fase de converg\xc3\xaancia (vers\xc3\xa3o moderna)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForConvergencePhaseModern", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForConvergencePhaseModern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForConvergencePhaseModern();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForConvergencePhaseModern *

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForDeviceType ***********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics
{
	struct AURACRONPCGEnvironmentManager_eventConfigureForDeviceType_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar ambientes para dispositivo espec\xc3\xad""fico (Entry, Mid, High) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ambientes para dispositivo espec\xc3\xad""fico (Entry, Mid, High)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureForDeviceType_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureForDeviceType_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForDeviceType", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::AURACRONPCGEnvironmentManager_eventConfigureForDeviceType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::AURACRONPCGEnvironmentManager_eventConfigureForDeviceType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForDeviceType)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForDeviceType(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForDeviceType *************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForIntensificationPhaseModern 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseConfiguration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar ambientes para fase de intensifica\xc3\xa7\xc3\xa3o (vers\xc3\xa3o moderna) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ambientes para fase de intensifica\xc3\xa7\xc3\xa3o (vers\xc3\xa3o moderna)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForIntensificationPhaseModern", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForIntensificationPhaseModern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForIntensificationPhaseModern();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForIntensificationPhaseModern 

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureForResolutionPhaseModern 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|PhaseConfiguration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar ambientes para fase de resolu\xc3\xa7\xc3\xa3o (vers\xc3\xa3o moderna) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ambientes para fase de resolu\xc3\xa7\xc3\xa3o (vers\xc3\xa3o moderna)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureForResolutionPhaseModern", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureForResolutionPhaseModern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForResolutionPhaseModern();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureForResolutionPhaseModern **

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureSimultaneousEnvironments 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics
{
	struct AURACRONPCGEnvironmentManager_eventConfigureSimultaneousEnvironments_Parms
	{
		bool bSimplifiedTransitions;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar 2 ambientes simult\xc3\xa2neos com transi\xc3\xa7\xc3\xb5""es simplificadas (dispositivos Mid) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar 2 ambientes simult\xc3\xa2neos com transi\xc3\xa7\xc3\xb5""es simplificadas (dispositivos Mid)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSimplifiedTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSimplifiedTransitions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::NewProp_bSimplifiedTransitions_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureSimultaneousEnvironments_Parms*)Obj)->bSimplifiedTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::NewProp_bSimplifiedTransitions = { "bSimplifiedTransitions", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureSimultaneousEnvironments_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::NewProp_bSimplifiedTransitions_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::NewProp_bSimplifiedTransitions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureSimultaneousEnvironments", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::AURACRONPCGEnvironmentManager_eventConfigureSimultaneousEnvironments_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::AURACRONPCGEnvironmentManager_eventConfigureSimultaneousEnvironments_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureSimultaneousEnvironments)
{
	P_GET_UBOOL(Z_Param_bSimplifiedTransitions);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureSimultaneousEnvironments(Z_Param_bSimplifiedTransitions);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureSimultaneousEnvironments **

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ConfigureSmoothTransitionToZephyr 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics
{
	struct AURACRONPCGEnvironmentManager_eventConfigureSmoothTransitionToZephyr_Parms
	{
		bool bShowAbyssPreview;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar transi\xc3\xa7\xc3\xa3o suave para Firmamento Zephyr (dispositivos Entry) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar transi\xc3\xa7\xc3\xa3o suave para Firmamento Zephyr (dispositivos Entry)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bShowAbyssPreview_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowAbyssPreview;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::NewProp_bShowAbyssPreview_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventConfigureSmoothTransitionToZephyr_Parms*)Obj)->bShowAbyssPreview = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::NewProp_bShowAbyssPreview = { "bShowAbyssPreview", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventConfigureSmoothTransitionToZephyr_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::NewProp_bShowAbyssPreview_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::NewProp_bShowAbyssPreview,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ConfigureSmoothTransitionToZephyr", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::AURACRONPCGEnvironmentManager_eventConfigureSmoothTransitionToZephyr_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::AURACRONPCGEnvironmentManager_eventConfigureSmoothTransitionToZephyr_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execConfigureSmoothTransitionToZephyr)
{
	P_GET_UBOOL(Z_Param_bShowAbyssPreview);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureSmoothTransitionToZephyr(Z_Param_bShowAbyssPreview);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ConfigureSmoothTransitionToZephyr **

// ********** Begin Class AAURACRONPCGEnvironmentManager Function CreateTacticalPortals ************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar portais de posicionamento t\xc3\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar portais de posicionamento t\xc3\xa1tico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "CreateTacticalPortals", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execCreateTacticalPortals)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateTacticalPortals();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function CreateTacticalPortals **************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function DeactivateAllPortals *************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desativar todos os portais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar todos os portais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "DeactivateAllPortals", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execDeactivateAllPortals)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateAllPortals();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function DeactivateAllPortals ***************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function DeactivateEnvironmentInstances ***
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics
{
	struct AURACRONPCGEnvironmentManager_eventDeactivateEnvironmentInstances_Parms
	{
		EAURACRONEnvironmentType EnvironmentType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desativar todas as inst\xc3\xa2ncias de um ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar todas as inst\xc3\xa2ncias de um ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventDeactivateEnvironmentInstances_Parms, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::NewProp_EnvironmentType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "DeactivateEnvironmentInstances", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::AURACRONPCGEnvironmentManager_eventDeactivateEnvironmentInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::AURACRONPCGEnvironmentManager_eventDeactivateEnvironmentInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execDeactivateEnvironmentInstances)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_EnvironmentType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateEnvironmentInstances(EAURACRONEnvironmentType(Z_Param_EnvironmentType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function DeactivateEnvironmentInstances *****

// ********** Begin Class AAURACRONPCGEnvironmentManager Function FixPhaseManagerIntegrationIssues *
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Corrigir problemas de integra\xc3\xa7\xc3\xa3o com o PCGPhaseManager */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Corrigir problemas de integra\xc3\xa7\xc3\xa3o com o PCGPhaseManager" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "FixPhaseManagerIntegrationIssues", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execFixPhaseManagerIntegrationIssues)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FixPhaseManagerIntegrationIssues();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function FixPhaseManagerIntegrationIssues ***

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ForceTransitionToEnvironment *****
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics
{
	struct AURACRONPCGEnvironmentManager_eventForceTransitionToEnvironment_Parms
	{
		EAURACRONEnvironmentType TargetEnvironment;
		float TransitionDuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar transi\xc3\xa7\xc3\xa3o para ambiente espec\xc3\xad""fico */" },
#endif
		{ "CPP_Default_TransitionDuration", "30.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar transi\xc3\xa7\xc3\xa3o para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetEnvironment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::NewProp_TargetEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::NewProp_TargetEnvironment = { "TargetEnvironment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventForceTransitionToEnvironment_Parms, TargetEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventForceTransitionToEnvironment_Parms, TransitionDuration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::NewProp_TargetEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::NewProp_TargetEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::NewProp_TransitionDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ForceTransitionToEnvironment", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::AURACRONPCGEnvironmentManager_eventForceTransitionToEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::AURACRONPCGEnvironmentManager_eventForceTransitionToEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execForceTransitionToEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_TargetEnvironment);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionDuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceTransitionToEnvironment(EAURACRONEnvironmentType(Z_Param_TargetEnvironment),Z_Param_TransitionDuration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ForceTransitionToEnvironment *******

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetCurrentEnvironment ************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetCurrentEnvironment_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ambiente atualmente ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetCurrentEnvironment_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetCurrentEnvironment", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetCurrentEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetCurrentEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetCurrentEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetCurrentEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetCurrentEnvironment **************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetCurrentMapTacticalAdvantages **
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetCurrentMapTacticalAdvantages_Parms
	{
		FAURACRONMapTacticalAdvantages ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter vantagens t\xc3\xa1ticas do mapa atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter vantagens t\xc3\xa1ticas do mapa atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetCurrentMapTacticalAdvantages_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages, METADATA_PARAMS(0, nullptr) }; // 1083727124
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetCurrentMapTacticalAdvantages", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::AURACRONPCGEnvironmentManager_eventGetCurrentMapTacticalAdvantages_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::AURACRONPCGEnvironmentManager_eventGetCurrentMapTacticalAdvantages_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetCurrentMapTacticalAdvantages)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONMapTacticalAdvantages*)Z_Param__Result=P_THIS->GetCurrentMapTacticalAdvantages();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetCurrentMapTacticalAdvantages ****

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetCurrentSanctuaryIslandCount ***
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetCurrentSanctuaryIslandCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Retorna o n\xc3\xbamero atual de Ilhas Santu\xc3\xa1rio no ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retorna o n\xc3\xbamero atual de Ilhas Santu\xc3\xa1rio no ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetCurrentSanctuaryIslandCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetCurrentSanctuaryIslandCount", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::AURACRONPCGEnvironmentManager_eventGetCurrentSanctuaryIslandCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::AURACRONPCGEnvironmentManager_eventGetCurrentSanctuaryIslandCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetCurrentSanctuaryIslandCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCurrentSanctuaryIslandCount();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetCurrentSanctuaryIslandCount *****

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetEnvironmentInstances **********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetEnvironmentInstances_Parms
	{
		EAURACRONEnvironmentType EnvironmentType;
		TArray<AAURACRONPCGEnvironment*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todas as inst\xc3\xa2ncias de um tipo de ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todas as inst\xc3\xa2ncias de um tipo de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetEnvironmentInstances_Parms, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetEnvironmentInstances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetEnvironmentInstances", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::AURACRONPCGEnvironmentManager_eventGetEnvironmentInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::AURACRONPCGEnvironmentManager_eventGetEnvironmentInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetEnvironmentInstances)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_EnvironmentType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAURACRONPCGEnvironment*>*)Z_Param__Result=P_THIS->GetEnvironmentInstances(EAURACRONEnvironmentType(Z_Param_EnvironmentType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetEnvironmentInstances ************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetEnvironmentSettings ***********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetEnvironmentSettings_Parms
	{
		EAURACRONEnvironmentType Environment;
		FAURACRONEnvironmentSettings ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter configura\xc3\xa7\xc3\xb5""es de um ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\xa7\xc3\xb5""es de um ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetEnvironmentSettings_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetEnvironmentSettings_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings, METADATA_PARAMS(0, nullptr) }; // 190442897
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::NewProp_Environment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetEnvironmentSettings", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::AURACRONPCGEnvironmentManager_eventGetEnvironmentSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::AURACRONPCGEnvironmentManager_eventGetEnvironmentSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetEnvironmentSettings)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONEnvironmentSettings*)Z_Param__Result=P_THIS->GetEnvironmentSettings(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetEnvironmentSettings *************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetNextEnvironment ***************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetNextEnvironment_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter pr\xc3\xb3ximo ambiente na rota\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pr\xc3\xb3ximo ambiente na rota\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetNextEnvironment_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetNextEnvironment", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetNextEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetNextEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetNextEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetNextEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetNextEnvironment *****************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetPortalsForEnvironment *********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetPortalsForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
		TArray<AAURACRONPCGPortal*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter portais para um ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter portais para um ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetPortalsForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGPortal_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetPortalsForEnvironment_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_Environment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetPortalsForEnvironment", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetPortalsForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetPortalsForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetPortalsForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAURACRONPCGPortal*>*)Z_Param__Result=P_THIS->GetPortalsForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetPortalsForEnvironment ***********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetTimeRemainingInCurrentEnvironment 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetTimeRemainingInCurrentEnvironment_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter tempo restante no ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tempo restante no ambiente atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetTimeRemainingInCurrentEnvironment_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetTimeRemainingInCurrentEnvironment", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetTimeRemainingInCurrentEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::AURACRONPCGEnvironmentManager_eventGetTimeRemainingInCurrentEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetTimeRemainingInCurrentEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeRemainingInCurrentEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetTimeRemainingInCurrentEnvironment 

// ********** Begin Class AAURACRONPCGEnvironmentManager Function GetTransitionProgress ************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics
{
	struct AURACRONPCGEnvironmentManager_eventGetTransitionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventGetTransitionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "GetTransitionProgress", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::AURACRONPCGEnvironmentManager_eventGetTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::AURACRONPCGEnvironmentManager_eventGetTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execGetTransitionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTransitionProgress();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function GetTransitionProgress **************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function InitializeEnvironmentSystem ******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar o sistema de ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar o sistema de ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "InitializeEnvironmentSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execInitializeEnvironmentSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeEnvironmentSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function InitializeEnvironmentSystem ********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function IsInTransition *******************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics
{
	struct AURACRONPCGEnvironmentManager_eventIsInTransition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventIsInTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventIsInTransition_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "IsInTransition", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::AURACRONPCGEnvironmentManager_eventIsInTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::AURACRONPCGEnvironmentManager_eventIsInTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execIsInTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInTransition();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function IsInTransition *********************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function OnMapContraction *****************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics
{
	struct AURACRONPCGEnvironmentManager_eventOnMapContraction_Parms
	{
		float ContractionFactor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar contra\xc3\xa7\xc3\xa3o do mapa a todos os ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar contra\xc3\xa7\xc3\xa3o do mapa a todos os ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ContractionFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::NewProp_ContractionFactor = { "ContractionFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventOnMapContraction_Parms, ContractionFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::NewProp_ContractionFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "OnMapContraction", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::AURACRONPCGEnvironmentManager_eventOnMapContraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::AURACRONPCGEnvironmentManager_eventOnMapContraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execOnMapContraction)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ContractionFactor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnMapContraction(Z_Param_ContractionFactor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function OnMapContraction *******************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function OnMapPhaseChanged ****************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics
{
	struct AURACRONPCGEnvironmentManager_eventOnMapPhaseChanged_Parms
	{
		EAURACRONMapPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para mudan\xc3\xa7""as de fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para mudan\xc3\xa7""as de fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventOnMapPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "OnMapPhaseChanged", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::AURACRONPCGEnvironmentManager_eventOnMapPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::AURACRONPCGEnvironmentManager_eventOnMapPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execOnMapPhaseChanged)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_NewPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnMapPhaseChanged(EAURACRONMapPhase(Z_Param_NewPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function OnMapPhaseChanged ******************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function RegisterEnvironmentInstance ******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics
{
	struct AURACRONPCGEnvironmentManager_eventRegisterEnvironmentInstance_Parms
	{
		AAURACRONPCGEnvironment* EnvironmentInstance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar uma inst\xc3\xa2ncia de ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar uma inst\xc3\xa2ncia de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentInstance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::NewProp_EnvironmentInstance = { "EnvironmentInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventRegisterEnvironmentInstance_Parms, EnvironmentInstance), Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::NewProp_EnvironmentInstance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "RegisterEnvironmentInstance", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::AURACRONPCGEnvironmentManager_eventRegisterEnvironmentInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::AURACRONPCGEnvironmentManager_eventRegisterEnvironmentInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execRegisterEnvironmentInstance)
{
	P_GET_OBJECT(AAURACRONPCGEnvironment,Z_Param_EnvironmentInstance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterEnvironmentInstance(Z_Param_EnvironmentInstance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function RegisterEnvironmentInstance ********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function RemoveMapTacticalAdvantages ******
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics
{
	struct AURACRONPCGEnvironmentManager_eventRemoveMapTacticalAdvantages_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remover vantagens t\xc3\xa1ticas do mapa anterior */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover vantagens t\xc3\xa1ticas do mapa anterior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventRemoveMapTacticalAdvantages_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "RemoveMapTacticalAdvantages", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::AURACRONPCGEnvironmentManager_eventRemoveMapTacticalAdvantages_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::AURACRONPCGEnvironmentManager_eventRemoveMapTacticalAdvantages_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execRemoveMapTacticalAdvantages)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveMapTacticalAdvantages(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function RemoveMapTacticalAdvantages ********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function SetTeleportDestinations **********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics
{
	struct AURACRONPCGEnvironmentManager_eventSetTeleportDestinations_Parms
	{
		EAURACRONEnvironmentType Environment;
		TArray<FVector> Locations;
		TArray<FRotator> Rotations;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir pontos de teletransporte para um ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir pontos de teletransporte para um ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Locations_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Locations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Locations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Rotations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventSetTeleportDestinations_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Locations_Inner = { "Locations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Locations = { "Locations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventSetTeleportDestinations_Parms, Locations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Locations_MetaData), NewProp_Locations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Rotations_Inner = { "Rotations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Rotations = { "Rotations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventSetTeleportDestinations_Parms, Rotations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotations_MetaData), NewProp_Rotations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Environment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Locations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Locations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Rotations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::NewProp_Rotations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "SetTeleportDestinations", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::AURACRONPCGEnvironmentManager_eventSetTeleportDestinations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::AURACRONPCGEnvironmentManager_eventSetTeleportDestinations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execSetTeleportDestinations)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Locations);
	P_GET_TARRAY_REF(FRotator,Z_Param_Out_Rotations);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTeleportDestinations(EAURACRONEnvironmentType(Z_Param_Environment),Z_Param_Out_Locations,Z_Param_Out_Rotations);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function SetTeleportDestinations ************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function StartEnvironmentRotation *********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica dos ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica dos ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "StartEnvironmentRotation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execStartEnvironmentRotation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartEnvironmentRotation();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function StartEnvironmentRotation ***********

// ********** Begin Class AAURACRONPCGEnvironmentManager Function StopEnvironmentRotation **********
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "StopEnvironmentRotation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execStopEnvironmentRotation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopEnvironmentRotation();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function StopEnvironmentRotation ************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function UnregisterEnvironmentInstance ****
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics
{
	struct AURACRONPCGEnvironmentManager_eventUnregisterEnvironmentInstance_Parms
	{
		AAURACRONPCGEnvironment* EnvironmentInstance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desregistrar uma inst\xc3\xa2ncia de ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desregistrar uma inst\xc3\xa2ncia de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentInstance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::NewProp_EnvironmentInstance = { "EnvironmentInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventUnregisterEnvironmentInstance_Parms, EnvironmentInstance), Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::NewProp_EnvironmentInstance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "UnregisterEnvironmentInstance", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::AURACRONPCGEnvironmentManager_eventUnregisterEnvironmentInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::AURACRONPCGEnvironmentManager_eventUnregisterEnvironmentInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execUnregisterEnvironmentInstance)
{
	P_GET_OBJECT(AAURACRONPCGEnvironment,Z_Param_EnvironmentInstance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterEnvironmentInstance(Z_Param_EnvironmentInstance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function UnregisterEnvironmentInstance ******

// ********** Begin Class AAURACRONPCGEnvironmentManager Function UpdateForMapPhase ****************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics
{
	struct AURACRONPCGEnvironmentManager_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para nova fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para nova fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironmentManager_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::AURACRONPCGEnvironmentManager_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::AURACRONPCGEnvironmentManager_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function UpdateForMapPhase ******************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function UpdateTacticalPortals ************
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar portais de posicionamento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar portais de posicionamento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "UpdateTacticalPortals", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execUpdateTacticalPortals)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTacticalPortals();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function UpdateTacticalPortals **************

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ValidatePhaseManagerIntegration **
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics
{
	struct AURACRONPCGEnvironmentManager_eventValidatePhaseManagerIntegration_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validar integra\xc3\xa7\xc3\xa3o com o PCGPhaseManager */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar integra\xc3\xa7\xc3\xa3o com o PCGPhaseManager" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGEnvironmentManager_eventValidatePhaseManagerIntegration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironmentManager_eventValidatePhaseManagerIntegration_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ValidatePhaseManagerIntegration", Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::AURACRONPCGEnvironmentManager_eventValidatePhaseManagerIntegration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::AURACRONPCGEnvironmentManager_eventValidatePhaseManagerIntegration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execValidatePhaseManagerIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePhaseManagerIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ValidatePhaseManagerIntegration ****

// ********** Begin Class AAURACRONPCGEnvironmentManager Function ValidateSanctuaryIslandDistribution 
struct Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verifica e garante que as Ilhas Santu\xc3\xa1rio sejam distribu\xc3\xad""das em se\xc3\xa7\xc3\xb5""es calmas do fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica e garante que as Ilhas Santu\xc3\xa1rio sejam distribu\xc3\xad""das em se\xc3\xa7\xc3\xb5""es calmas do fluxo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironmentManager, nullptr, "ValidateSanctuaryIslandDistribution", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironmentManager::execValidateSanctuaryIslandDistribution)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateSanctuaryIslandDistribution();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironmentManager Function ValidateSanctuaryIslandDistribution 

// ********** Begin Class AAURACRONPCGEnvironmentManager *******************************************
void AAURACRONPCGEnvironmentManager::StaticRegisterNativesAAURACRONPCGEnvironmentManager()
{
	UClass* Class = AAURACRONPCGEnvironmentManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateEnvironmentInstances", &AAURACRONPCGEnvironmentManager::execActivateEnvironmentInstances },
		{ "ActivatePortalsForEnvironment", &AAURACRONPCGEnvironmentManager::execActivatePortalsForEnvironment },
		{ "ApplyMapTacticalAdvantages", &AAURACRONPCGEnvironmentManager::execApplyMapTacticalAdvantages },
		{ "ApplyTemporaryEnvironmentEffect", &AAURACRONPCGEnvironmentManager::execApplyTemporaryEnvironmentEffect },
		{ "ApplyTransitionToInstances", &AAURACRONPCGEnvironmentManager::execApplyTransitionToInstances },
		{ "AreConvergenceSystemsReady", &AAURACRONPCGEnvironmentManager::execAreConvergenceSystemsReady },
		{ "AreIntensificationSystemsReady", &AAURACRONPCGEnvironmentManager::execAreIntensificationSystemsReady },
		{ "AreResolutionSystemsReady", &AAURACRONPCGEnvironmentManager::execAreResolutionSystemsReady },
		{ "ConfigureBlurredEnvironmentBoundaries", &AAURACRONPCGEnvironmentManager::execConfigureBlurredEnvironmentBoundaries },
		{ "ConfigureForAwakeningPhase", &AAURACRONPCGEnvironmentManager::execConfigureForAwakeningPhase },
		{ "ConfigureForAwakeningPhaseModern", &AAURACRONPCGEnvironmentManager::execConfigureForAwakeningPhaseModern },
		{ "ConfigureForConvergencePhase", &AAURACRONPCGEnvironmentManager::execConfigureForConvergencePhase },
		{ "ConfigureForConvergencePhaseModern", &AAURACRONPCGEnvironmentManager::execConfigureForConvergencePhaseModern },
		{ "ConfigureForDeviceType", &AAURACRONPCGEnvironmentManager::execConfigureForDeviceType },
		{ "ConfigureForIntensificationPhaseModern", &AAURACRONPCGEnvironmentManager::execConfigureForIntensificationPhaseModern },
		{ "ConfigureForResolutionPhaseModern", &AAURACRONPCGEnvironmentManager::execConfigureForResolutionPhaseModern },
		{ "ConfigureSimultaneousEnvironments", &AAURACRONPCGEnvironmentManager::execConfigureSimultaneousEnvironments },
		{ "ConfigureSmoothTransitionToZephyr", &AAURACRONPCGEnvironmentManager::execConfigureSmoothTransitionToZephyr },
		{ "CreateTacticalPortals", &AAURACRONPCGEnvironmentManager::execCreateTacticalPortals },
		{ "DeactivateAllPortals", &AAURACRONPCGEnvironmentManager::execDeactivateAllPortals },
		{ "DeactivateEnvironmentInstances", &AAURACRONPCGEnvironmentManager::execDeactivateEnvironmentInstances },
		{ "FixPhaseManagerIntegrationIssues", &AAURACRONPCGEnvironmentManager::execFixPhaseManagerIntegrationIssues },
		{ "ForceTransitionToEnvironment", &AAURACRONPCGEnvironmentManager::execForceTransitionToEnvironment },
		{ "GetCurrentEnvironment", &AAURACRONPCGEnvironmentManager::execGetCurrentEnvironment },
		{ "GetCurrentMapTacticalAdvantages", &AAURACRONPCGEnvironmentManager::execGetCurrentMapTacticalAdvantages },
		{ "GetCurrentSanctuaryIslandCount", &AAURACRONPCGEnvironmentManager::execGetCurrentSanctuaryIslandCount },
		{ "GetEnvironmentInstances", &AAURACRONPCGEnvironmentManager::execGetEnvironmentInstances },
		{ "GetEnvironmentSettings", &AAURACRONPCGEnvironmentManager::execGetEnvironmentSettings },
		{ "GetNextEnvironment", &AAURACRONPCGEnvironmentManager::execGetNextEnvironment },
		{ "GetPortalsForEnvironment", &AAURACRONPCGEnvironmentManager::execGetPortalsForEnvironment },
		{ "GetTimeRemainingInCurrentEnvironment", &AAURACRONPCGEnvironmentManager::execGetTimeRemainingInCurrentEnvironment },
		{ "GetTransitionProgress", &AAURACRONPCGEnvironmentManager::execGetTransitionProgress },
		{ "InitializeEnvironmentSystem", &AAURACRONPCGEnvironmentManager::execInitializeEnvironmentSystem },
		{ "IsInTransition", &AAURACRONPCGEnvironmentManager::execIsInTransition },
		{ "OnMapContraction", &AAURACRONPCGEnvironmentManager::execOnMapContraction },
		{ "OnMapPhaseChanged", &AAURACRONPCGEnvironmentManager::execOnMapPhaseChanged },
		{ "RegisterEnvironmentInstance", &AAURACRONPCGEnvironmentManager::execRegisterEnvironmentInstance },
		{ "RemoveMapTacticalAdvantages", &AAURACRONPCGEnvironmentManager::execRemoveMapTacticalAdvantages },
		{ "SetTeleportDestinations", &AAURACRONPCGEnvironmentManager::execSetTeleportDestinations },
		{ "StartEnvironmentRotation", &AAURACRONPCGEnvironmentManager::execStartEnvironmentRotation },
		{ "StopEnvironmentRotation", &AAURACRONPCGEnvironmentManager::execStopEnvironmentRotation },
		{ "UnregisterEnvironmentInstance", &AAURACRONPCGEnvironmentManager::execUnregisterEnvironmentInstance },
		{ "UpdateForMapPhase", &AAURACRONPCGEnvironmentManager::execUpdateForMapPhase },
		{ "UpdateTacticalPortals", &AAURACRONPCGEnvironmentManager::execUpdateTacticalPortals },
		{ "ValidatePhaseManagerIntegration", &AAURACRONPCGEnvironmentManager::execValidatePhaseManagerIntegration },
		{ "ValidateSanctuaryIslandDistribution", &AAURACRONPCGEnvironmentManager::execValidateSanctuaryIslandDistribution },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager;
UClass* AAURACRONPCGEnvironmentManager::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGEnvironmentManager;
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGEnvironmentManager"),
			Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGEnvironmentManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager_NoRegister()
{
	return AAURACRONPCGEnvironmentManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador global dos 3 ambientes din\xc3\xa2micos do AURACRON\n *\n * RESPONSABILIDADES CLARIFICADAS:\n * ================================\n *\n * ESTA CLASSE (AURACRONPCGEnvironmentManager) - GERENCIADOR GLOBAL:\n * - Orquestra a rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica entre os 3 ambientes principais\n * - Gerencia transi\xc3\xa7\xc3\xb5""es suaves e timing entre ambientes\n * - Coordena m\xc3\xbaltiplas inst\xc3\xa2ncias de AURACRONPCGEnvironment\n * - Aplica efeitos globais (ilumina\xc3\xa7\xc3\xa3o, p\xc3\xb3s-processamento, fog, skybox)\n * - Integra com outros sistemas (PhaseManager, ObjectiveSystem, LaneSystem)\n * - Controla configura\xc3\xa7\xc3\xb5""es de ambiente por fase do mapa\n * - Gerencia efeitos tempor\xc3\xa1rios globais\n * - Notifica todos os sistemas sobre mudan\xc3\xa7""as de ambiente\n * - Controla replica\xc3\xa7\xc3\xa3o de estado para multiplayer\n *\n * AURACRONPCGEnvironment - ATOR INDIVIDUAL:\n * - Representa uma inst\xc3\xa2ncia espec\xc3\xad""fica de ambiente local\n * - Executa gera\xc3\xa7\xc3\xa3o PCG de elementos espec\xc3\xad""ficos\n * - Responde a comandos do Manager\n * - Gerencia caracter\xc3\xadsticas locais do ambiente\n *\n * RELA\xc3\x87\xc3\x83O: Este Manager (1) -> Environment Instances (N)\n * O Manager \xc3\xa9 o \"maestro\", os Environments s\xc3\xa3o os \"m\xc3\xbasicos\".\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGEnvironmentManager.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador global dos 3 ambientes din\xc3\xa2micos do AURACRON\n\nRESPONSABILIDADES CLARIFICADAS:\n\n\nESTA CLASSE (AURACRONPCGEnvironmentManager) - GERENCIADOR GLOBAL:\n- Orquestra a rota\xc3\xa7\xc3\xa3o autom\xc3\xa1tica entre os 3 ambientes principais\n- Gerencia transi\xc3\xa7\xc3\xb5""es suaves e timing entre ambientes\n- Coordena m\xc3\xbaltiplas inst\xc3\xa2ncias de AURACRONPCGEnvironment\n- Aplica efeitos globais (ilumina\xc3\xa7\xc3\xa3o, p\xc3\xb3s-processamento, fog, skybox)\n- Integra com outros sistemas (PhaseManager, ObjectiveSystem, LaneSystem)\n- Controla configura\xc3\xa7\xc3\xb5""es de ambiente por fase do mapa\n- Gerencia efeitos tempor\xc3\xa1rios globais\n- Notifica todos os sistemas sobre mudan\xc3\xa7""as de ambiente\n- Controla replica\xc3\xa7\xc3\xa3o de estado para multiplayer\n\nAURACRONPCGEnvironment - ATOR INDIVIDUAL:\n- Representa uma inst\xc3\xa2ncia espec\xc3\xad""fica de ambiente local\n- Executa gera\xc3\xa7\xc3\xa3o PCG de elementos espec\xc3\xad""ficos\n- Responde a comandos do Manager\n- Gerencia caracter\xc3\xadsticas locais do ambiente\n\nRELA\xc3\x87\xc3\x83O: Este Manager (1) -> Environment Instances (N)\nO Manager \xc3\xa9 o \"maestro\", os Environments s\xc3\xa3o os \"m\xc3\xbasicos\"." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentSettings_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es dos 3 ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es dos 3 ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalSanctuaryIslands_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
		{ "ClampMax", "8" },
		{ "ClampMin", "8" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero total de Ilhas Santu\xc3\xa1rio a serem geradas (conforme documenta\xc3\xa7\xc3\xa3o: 8) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero total de Ilhas Santu\xc3\xa1rio a serem geradas (conforme documenta\xc3\xa7\xc3\xa3o: 8)" },
#endif
		{ "UIMax", "8" },
		{ "UIMin", "8" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapTacticalAdvantages_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de vantagens t\xc3\xa1ticas para cada tipo de mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de vantagens t\xc3\xa1ticas para cada tipo de mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorsWithTacticalAdvantages_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores afetados pelas vantagens t\xc3\xa1ticas do mapa atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores afetados pelas vantagens t\xc3\xa1ticas do mapa atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneSystem_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias aos sistemas integrados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias aos sistemas integrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JungleSystem_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveSystem_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DirectionalLight_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de ilumina\xc3\xa7\xc3\xa3o */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de ilumina\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyLight_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessComponent_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de post-processing */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de post-processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStartRotation_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve iniciar rota\xc3\xa7\xc3\xa3o automaticamente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve iniciar rota\xc3\xa7\xc3\xa3o automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRotationActive_MetaData[] = {
		{ "Category", "AURACRON|EnvironmentManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a rota\xc3\xa7\xc3\xa3o est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a rota\xc3\xa7\xc3\xa3o est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente de destino durante transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente de destino durante transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInTransition_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 em transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeRemainingInEnvironment_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo restante no ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo restante no ambiente atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionDuration_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da transi\xc3\xa7\xc3\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso da transi\xc3\xa7\xc3\xa3o atual (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentRotationTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para rota\xc3\xa7\xc3\xa3o de ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para rota\xc3\xa7\xc3\xa3o de ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para transi\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapContractionPercentage_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Percentual atual de contra\xc3\xa7\xc3\xa3o do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Percentual atual de contra\xc3\xa7\xc3\xa3o do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTemporaryEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos tempor\xc3\xa1rios ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos tempor\xc3\xa1rios ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredEnvironmentInstances_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inst\xc3\xa2ncias de ambiente registradas por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inst\xc3\xa2ncias de ambiente registradas por tipo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeleportDestinations_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Destinos de teletransporte por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destinos de teletransporte por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxEnvironmentInstances_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de inst\xc3\xa2ncias de ambiente ativas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de inst\xc3\xa2ncias de ambiente ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentUpdateFrequency_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia de atualiza\xc3\xa7\xc3\xa3o dos ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia de atualiza\xc3\xa7\xc3\xa3o dos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedLighting_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se ilumina\xc3\xa7\xc3\xa3o avan\xc3\xa7""ada est\xc3\xa1 habilitada */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se ilumina\xc3\xa7\xc3\xa3o avan\xc3\xa7""ada est\xc3\xa1 habilitada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVolumetricFog_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se fog volum\xc3\xa9trico est\xc3\xa1 habilitado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se fog volum\xc3\xa9trico est\xc3\xa1 habilitado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableComplexParticles_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se part\xc3\xad""culas complexas est\xc3\xa3o habilitadas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se part\xc3\xad""culas complexas est\xc3\xa3o habilitadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentTransitionSpeed_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de transi\xc3\xa7\xc3\xa3o entre ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de transi\xc3\xa7\xc3\xa3o entre ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentBlendRadius_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de blend entre ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de blend entre ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentBoundaryBlurStrength_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""a do blur das bordas dos ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a do blur das bordas dos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowSimultaneousEnvironments_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se ambientes simult\xc3\xa2neos est\xc3\xa3o permitidos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se ambientes simult\xc3\xa2neos est\xc3\xa3o permitidos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveEnvironments_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de ambientes ativos simultaneamente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de ambientes ativos simultaneamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadiantPlainsAdvantageEffect_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeitos de gameplay para vantagens t\xc3\xa1ticas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos de gameplay para vantagens t\xc3\xa1ticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZephyrFirmamentAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PurgatoryRealmAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AwakeningPhaseAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConvergencePhaseAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntensificationPhaseAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResolutionPhaseAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighGroundAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CoverAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlankingAdvantageEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironmentManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnvironmentSettings_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentSettings_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EnvironmentSettings;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalSanctuaryIslands;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapTacticalAdvantages_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapTacticalAdvantages_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapTacticalAdvantages_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MapTacticalAdvantages;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorsWithTacticalAdvantages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActorsWithTacticalAdvantages;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_JungleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DirectionalLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SkyLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PostProcessComponent;
	static void NewProp_bAutoStartRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStartRotation;
	static void NewProp_bRotationActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRotationActive;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetEnvironment;
	static void NewProp_bIsInTransition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInTransition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeRemainingInEnvironment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentTransitionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnvironmentRotationTimer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionTimer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentMapContractionPercentage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActiveTemporaryEffects_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveTemporaryEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveTemporaryEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredEnvironmentInstances_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RegisteredEnvironmentInstances_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RegisteredEnvironmentInstances_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredEnvironmentInstances;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TeleportDestinations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TeleportDestinations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TeleportDestinations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TeleportDestinations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxEnvironmentInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentUpdateFrequency;
	static void NewProp_bEnableAdvancedLighting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedLighting;
	static void NewProp_bEnableVolumetricFog_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVolumetricFog;
	static void NewProp_bEnableComplexParticles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableComplexParticles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentTransitionSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentBlendRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentBoundaryBlurStrength;
	static void NewProp_bAllowSimultaneousEnvironments_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowSimultaneousEnvironments;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveEnvironments;
	static const UECodeGen_Private::FClassPropertyParams NewProp_RadiantPlainsAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ZephyrFirmamentAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_PurgatoryRealmAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AwakeningPhaseAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ConvergencePhaseAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_IntensificationPhaseAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ResolutionPhaseAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_HighGroundAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_CoverAdvantageEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_FlankingAdvantageEffect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivateEnvironmentInstances, "ActivateEnvironmentInstances" }, // 1079474815
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ActivatePortalsForEnvironment, "ActivatePortalsForEnvironment" }, // 2034325076
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyMapTacticalAdvantages, "ApplyMapTacticalAdvantages" }, // 166901063
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTemporaryEnvironmentEffect, "ApplyTemporaryEnvironmentEffect" }, // 491851304
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ApplyTransitionToInstances, "ApplyTransitionToInstances" }, // 3112057934
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreConvergenceSystemsReady, "AreConvergenceSystemsReady" }, // 2736934507
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreIntensificationSystemsReady, "AreIntensificationSystemsReady" }, // 939068533
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_AreResolutionSystemsReady, "AreResolutionSystemsReady" }, // 2765846490
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureBlurredEnvironmentBoundaries, "ConfigureBlurredEnvironmentBoundaries" }, // 2755543878
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhase, "ConfigureForAwakeningPhase" }, // 4189354450
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForAwakeningPhaseModern, "ConfigureForAwakeningPhaseModern" }, // 3679659203
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhase, "ConfigureForConvergencePhase" }, // 415516835
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForConvergencePhaseModern, "ConfigureForConvergencePhaseModern" }, // 3410168125
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForDeviceType, "ConfigureForDeviceType" }, // 584590971
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForIntensificationPhaseModern, "ConfigureForIntensificationPhaseModern" }, // 3650048879
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureForResolutionPhaseModern, "ConfigureForResolutionPhaseModern" }, // 638963162
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSimultaneousEnvironments, "ConfigureSimultaneousEnvironments" }, // 1097827180
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ConfigureSmoothTransitionToZephyr, "ConfigureSmoothTransitionToZephyr" }, // 3336412650
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_CreateTacticalPortals, "CreateTacticalPortals" }, // 2438385102
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateAllPortals, "DeactivateAllPortals" }, // 520133960
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_DeactivateEnvironmentInstances, "DeactivateEnvironmentInstances" }, // 4045662106
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_FixPhaseManagerIntegrationIssues, "FixPhaseManagerIntegrationIssues" }, // 2030260810
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ForceTransitionToEnvironment, "ForceTransitionToEnvironment" }, // 2894242420
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentEnvironment, "GetCurrentEnvironment" }, // 2880068794
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentMapTacticalAdvantages, "GetCurrentMapTacticalAdvantages" }, // 374790070
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetCurrentSanctuaryIslandCount, "GetCurrentSanctuaryIslandCount" }, // 216764794
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentInstances, "GetEnvironmentInstances" }, // 3569665410
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetEnvironmentSettings, "GetEnvironmentSettings" }, // 1076521212
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetNextEnvironment, "GetNextEnvironment" }, // 2972504844
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetPortalsForEnvironment, "GetPortalsForEnvironment" }, // 1006447415
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTimeRemainingInCurrentEnvironment, "GetTimeRemainingInCurrentEnvironment" }, // 1721585935
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_GetTransitionProgress, "GetTransitionProgress" }, // 1748021391
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_InitializeEnvironmentSystem, "InitializeEnvironmentSystem" }, // 2594009324
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_IsInTransition, "IsInTransition" }, // 4004626194
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapContraction, "OnMapContraction" }, // 2364530337
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_OnMapPhaseChanged, "OnMapPhaseChanged" }, // 1560420485
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RegisterEnvironmentInstance, "RegisterEnvironmentInstance" }, // 510140579
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_RemoveMapTacticalAdvantages, "RemoveMapTacticalAdvantages" }, // 2464839922
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_SetTeleportDestinations, "SetTeleportDestinations" }, // 2392494797
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StartEnvironmentRotation, "StartEnvironmentRotation" }, // 1516828959
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_StopEnvironmentRotation, "StopEnvironmentRotation" }, // 3033499708
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UnregisterEnvironmentInstance, "UnregisterEnvironmentInstance" }, // 3429463870
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateForMapPhase, "UpdateForMapPhase" }, // 780055994
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_UpdateTacticalPortals, "UpdateTacticalPortals" }, // 4180874891
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidatePhaseManagerIntegration, "ValidatePhaseManagerIntegration" }, // 1828072012
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironmentManager_ValidateSanctuaryIslandDistribution, "ValidateSanctuaryIslandDistribution" }, // 3209833021
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGEnvironmentManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings_ValueProp = { "EnvironmentSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings, METADATA_PARAMS(0, nullptr) }; // 190442897
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings_Key_KeyProp = { "EnvironmentSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings = { "EnvironmentSettings", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, EnvironmentSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentSettings_MetaData), NewProp_EnvironmentSettings_MetaData) }; // 2509470107 190442897
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TotalSanctuaryIslands = { "TotalSanctuaryIslands", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, TotalSanctuaryIslands), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalSanctuaryIslands_MetaData), NewProp_TotalSanctuaryIslands_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages_ValueProp = { "MapTacticalAdvantages", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONMapTacticalAdvantages, METADATA_PARAMS(0, nullptr) }; // 1083727124
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages_Key_KeyProp = { "MapTacticalAdvantages_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages = { "MapTacticalAdvantages", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, MapTacticalAdvantages), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapTacticalAdvantages_MetaData), NewProp_MapTacticalAdvantages_MetaData) }; // 2509470107 1083727124
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActorsWithTacticalAdvantages_Inner = { "ActorsWithTacticalAdvantages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActorsWithTacticalAdvantages = { "ActorsWithTacticalAdvantages", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, ActorsWithTacticalAdvantages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorsWithTacticalAdvantages_MetaData), NewProp_ActorsWithTacticalAdvantages_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_LaneSystem = { "LaneSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, LaneSystem), Z_Construct_UClass_AAURACRONPCGLaneSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneSystem_MetaData), NewProp_LaneSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_JungleSystem = { "JungleSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, JungleSystem), Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JungleSystem_MetaData), NewProp_JungleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ObjectiveSystem = { "ObjectiveSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, ObjectiveSystem), Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveSystem_MetaData), NewProp_ObjectiveSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_DirectionalLight = { "DirectionalLight", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, DirectionalLight), Z_Construct_UClass_UDirectionalLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DirectionalLight_MetaData), NewProp_DirectionalLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_SkyLight = { "SkyLight", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, SkyLight), Z_Construct_UClass_USkyLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyLight_MetaData), NewProp_SkyLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_PostProcessComponent = { "PostProcessComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, PostProcessComponent), Z_Construct_UClass_UPostProcessComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessComponent_MetaData), NewProp_PostProcessComponent_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAutoStartRotation_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bAutoStartRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAutoStartRotation = { "bAutoStartRotation", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAutoStartRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStartRotation_MetaData), NewProp_bAutoStartRotation_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bRotationActive_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bRotationActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bRotationActive = { "bRotationActive", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bRotationActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRotationActive_MetaData), NewProp_bRotationActive_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2509470107
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TargetEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TargetEnvironment = { "TargetEnvironment", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, TargetEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetEnvironment_MetaData), NewProp_TargetEnvironment_MetaData) }; // 2509470107
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bIsInTransition_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bIsInTransition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bIsInTransition = { "bIsInTransition", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bIsInTransition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInTransition_MetaData), NewProp_bIsInTransition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TimeRemainingInEnvironment = { "TimeRemainingInEnvironment", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, TimeRemainingInEnvironment), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeRemainingInEnvironment_MetaData), NewProp_TimeRemainingInEnvironment_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentTransitionDuration = { "CurrentTransitionDuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, CurrentTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionDuration_MetaData), NewProp_CurrentTransitionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentRotationTimer = { "EnvironmentRotationTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, EnvironmentRotationTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentRotationTimer_MetaData), NewProp_EnvironmentRotationTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TransitionTimer = { "TransitionTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, TransitionTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTimer_MetaData), NewProp_TransitionTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentMapContractionPercentage = { "CurrentMapContractionPercentage", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, CurrentMapContractionPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapContractionPercentage_MetaData), NewProp_CurrentMapContractionPercentage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActiveTemporaryEffects_ValueProp = { "ActiveTemporaryEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActiveTemporaryEffects_Key_KeyProp = { "ActiveTemporaryEffects_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActiveTemporaryEffects = { "ActiveTemporaryEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, ActiveTemporaryEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTemporaryEffects_MetaData), NewProp_ActiveTemporaryEffects_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances_ValueProp = { "RegisteredEnvironmentInstances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONEnvironmentArray, METADATA_PARAMS(0, nullptr) }; // 1954740977
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances_Key_KeyProp = { "RegisteredEnvironmentInstances_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances = { "RegisteredEnvironmentInstances", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, RegisteredEnvironmentInstances), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredEnvironmentInstances_MetaData), NewProp_RegisteredEnvironmentInstances_MetaData) }; // 2509470107 1954740977
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations_ValueProp = { "TeleportDestinations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONTeleportDestinations, METADATA_PARAMS(0, nullptr) }; // 656416751
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations_Key_KeyProp = { "TeleportDestinations_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations = { "TeleportDestinations", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, TeleportDestinations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeleportDestinations_MetaData), NewProp_TeleportDestinations_MetaData) }; // 2509470107 656416751
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MaxEnvironmentInstances = { "MaxEnvironmentInstances", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, MaxEnvironmentInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxEnvironmentInstances_MetaData), NewProp_MaxEnvironmentInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentUpdateFrequency = { "EnvironmentUpdateFrequency", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, EnvironmentUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentUpdateFrequency_MetaData), NewProp_EnvironmentUpdateFrequency_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableAdvancedLighting_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bEnableAdvancedLighting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableAdvancedLighting = { "bEnableAdvancedLighting", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableAdvancedLighting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedLighting_MetaData), NewProp_bEnableAdvancedLighting_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableVolumetricFog_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bEnableVolumetricFog = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableVolumetricFog = { "bEnableVolumetricFog", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableVolumetricFog_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVolumetricFog_MetaData), NewProp_bEnableVolumetricFog_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableComplexParticles_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bEnableComplexParticles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableComplexParticles = { "bEnableComplexParticles", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableComplexParticles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableComplexParticles_MetaData), NewProp_bEnableComplexParticles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentTransitionSpeed = { "EnvironmentTransitionSpeed", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, EnvironmentTransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentTransitionSpeed_MetaData), NewProp_EnvironmentTransitionSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentBlendRadius = { "EnvironmentBlendRadius", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, EnvironmentBlendRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentBlendRadius_MetaData), NewProp_EnvironmentBlendRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentBoundaryBlurStrength = { "EnvironmentBoundaryBlurStrength", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, EnvironmentBoundaryBlurStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentBoundaryBlurStrength_MetaData), NewProp_EnvironmentBoundaryBlurStrength_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAllowSimultaneousEnvironments_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironmentManager*)Obj)->bAllowSimultaneousEnvironments = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAllowSimultaneousEnvironments = { "bAllowSimultaneousEnvironments", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironmentManager), &Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAllowSimultaneousEnvironments_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowSimultaneousEnvironments_MetaData), NewProp_bAllowSimultaneousEnvironments_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MaxActiveEnvironments = { "MaxActiveEnvironments", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, MaxActiveEnvironments), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveEnvironments_MetaData), NewProp_MaxActiveEnvironments_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RadiantPlainsAdvantageEffect = { "RadiantPlainsAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, RadiantPlainsAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadiantPlainsAdvantageEffect_MetaData), NewProp_RadiantPlainsAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ZephyrFirmamentAdvantageEffect = { "ZephyrFirmamentAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, ZephyrFirmamentAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZephyrFirmamentAdvantageEffect_MetaData), NewProp_ZephyrFirmamentAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_PurgatoryRealmAdvantageEffect = { "PurgatoryRealmAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, PurgatoryRealmAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PurgatoryRealmAdvantageEffect_MetaData), NewProp_PurgatoryRealmAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_AwakeningPhaseAdvantageEffect = { "AwakeningPhaseAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, AwakeningPhaseAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AwakeningPhaseAdvantageEffect_MetaData), NewProp_AwakeningPhaseAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ConvergencePhaseAdvantageEffect = { "ConvergencePhaseAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, ConvergencePhaseAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConvergencePhaseAdvantageEffect_MetaData), NewProp_ConvergencePhaseAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_IntensificationPhaseAdvantageEffect = { "IntensificationPhaseAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, IntensificationPhaseAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntensificationPhaseAdvantageEffect_MetaData), NewProp_IntensificationPhaseAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ResolutionPhaseAdvantageEffect = { "ResolutionPhaseAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, ResolutionPhaseAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResolutionPhaseAdvantageEffect_MetaData), NewProp_ResolutionPhaseAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_HighGroundAdvantageEffect = { "HighGroundAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, HighGroundAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighGroundAdvantageEffect_MetaData), NewProp_HighGroundAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CoverAdvantageEffect = { "CoverAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, CoverAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CoverAdvantageEffect_MetaData), NewProp_CoverAdvantageEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_FlankingAdvantageEffect = { "FlankingAdvantageEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironmentManager, FlankingAdvantageEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlankingAdvantageEffect_MetaData), NewProp_FlankingAdvantageEffect_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TotalSanctuaryIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MapTacticalAdvantages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActorsWithTacticalAdvantages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActorsWithTacticalAdvantages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_LaneSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_JungleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ObjectiveSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_DirectionalLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_SkyLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_PostProcessComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAutoStartRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bRotationActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TargetEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TargetEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bIsInTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TimeRemainingInEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentRotationTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TransitionTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CurrentMapContractionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActiveTemporaryEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActiveTemporaryEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ActiveTemporaryEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RegisteredEnvironmentInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_TeleportDestinations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MaxEnvironmentInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableAdvancedLighting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableVolumetricFog,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bEnableComplexParticles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentTransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentBlendRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_EnvironmentBoundaryBlurStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_bAllowSimultaneousEnvironments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_MaxActiveEnvironments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_RadiantPlainsAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ZephyrFirmamentAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_PurgatoryRealmAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_AwakeningPhaseAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ConvergencePhaseAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_IntensificationPhaseAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_ResolutionPhaseAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_HighGroundAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_CoverAdvantageEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::NewProp_FlankingAdvantageEffect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::ClassParams = {
	&AAURACRONPCGEnvironmentManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGEnvironmentManager()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager.OuterSingleton, Z_Construct_UClass_AAURACRONPCGEnvironmentManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGEnvironmentManager);
AAURACRONPCGEnvironmentManager::~AAURACRONPCGEnvironmentManager() {}
// ********** End Class AAURACRONPCGEnvironmentManager *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONEnvironmentArray::StaticStruct, Z_Construct_UScriptStruct_FAURACRONEnvironmentArray_Statics::NewStructOps, TEXT("AURACRONEnvironmentArray"), &Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONEnvironmentArray), 1954740977U) },
		{ FAURACRONTeleportDestinations::StaticStruct, Z_Construct_UScriptStruct_FAURACRONTeleportDestinations_Statics::NewStructOps, TEXT("AURACRONTeleportDestinations"), &Z_Registration_Info_UScriptStruct_FAURACRONTeleportDestinations, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONTeleportDestinations), 656416751U) },
		{ FAURACRONDeviceEnvironmentSettings::StaticStruct, Z_Construct_UScriptStruct_FAURACRONDeviceEnvironmentSettings_Statics::NewStructOps, TEXT("AURACRONDeviceEnvironmentSettings"), &Z_Registration_Info_UScriptStruct_FAURACRONDeviceEnvironmentSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONDeviceEnvironmentSettings), 2289926425U) },
		{ FAURACRONEnvironmentSettings::StaticStruct, Z_Construct_UScriptStruct_FAURACRONEnvironmentSettings_Statics::NewStructOps, TEXT("AURACRONEnvironmentSettings"), &Z_Registration_Info_UScriptStruct_FAURACRONEnvironmentSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONEnvironmentSettings), 190442897U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGEnvironmentManager, AAURACRONPCGEnvironmentManager::StaticClass, TEXT("AAURACRONPCGEnvironmentManager"), &Z_Registration_Info_UClass_AAURACRONPCGEnvironmentManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGEnvironmentManager), 759943333U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h__Script_AURACRON_2916853131(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironmentManager_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
