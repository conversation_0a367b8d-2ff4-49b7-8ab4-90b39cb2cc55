// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGMathLibrary.h"

#ifdef AURACRON_AURACRONPCGMathLibrary_generated_h
#error "AURACRONPCGMathLibrary.generated.h already included, missing '#pragma once' in AURACRONPCGMathLibrary.h"
#endif
#define AURACRON_AURACRONPCGMathLibrary_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UWorld;
struct FAURACRONNoisePattern;
struct FAURACRONSplineCurve;
struct FLinearColor;

// ********** Begin ScriptStruct FAURACRONSplineCurve **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_18_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONSplineCurve_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONSplineCurve;
// ********** End ScriptStruct FAURACRONSplineCurve ************************************************

// ********** Begin ScriptStruct FAURACRONNoisePattern *********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_44_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONNoisePattern_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONNoisePattern;
// ********** End ScriptStruct FAURACRONNoisePattern ***********************************************

// ********** Begin Class UAURACRONPCGMathLibrary **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetTerrainHeightAtLocation); \
	DECLARE_FUNCTION(execCalculateLunarPhaseIntensity); \
	DECLARE_FUNCTION(execShiftHue); \
	DECLARE_FUNCTION(execCalculateObjectDensity); \
	DECLARE_FUNCTION(execShouldRenderObject); \
	DECLARE_FUNCTION(execCalculateLODLevel); \
	DECLARE_FUNCTION(execGetSolarIntensity); \
	DECLARE_FUNCTION(execGetLunarPhaseIntensity); \
	DECLARE_FUNCTION(execGetTimeBasedPosition); \
	DECLARE_FUNCTION(execGenerateTectonicBridgePoints); \
	DECLARE_FUNCTION(execGenerateBreathingForestPositions); \
	DECLARE_FUNCTION(execGenerateLivingCanyonPath); \
	DECLARE_FUNCTION(execGenerateCrystallinePlateauVertices); \
	DECLARE_FUNCTION(execDistributePointsAlongCurve); \
	DECLARE_FUNCTION(execGenerateHexagonalGrid); \
	DECLARE_FUNCTION(execGeneratePoissonDiscSampling); \
	DECLARE_FUNCTION(execGenerateHeightMap); \
	DECLARE_FUNCTION(execGenerateFractalNoise); \
	DECLARE_FUNCTION(execGeneratePerlinNoise3D); \
	DECLARE_FUNCTION(execGeneratePerlinNoise2D); \
	DECLARE_FUNCTION(execCreateOrbitalPath); \
	DECLARE_FUNCTION(execGetSplineTangent); \
	DECLARE_FUNCTION(execEvaluateSplineCurve); \
	DECLARE_FUNCTION(execCreateSerpentineCurve);


AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGMathLibrary_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONPCGMathLibrary(); \
	friend struct Z_Construct_UClass_UAURACRONPCGMathLibrary_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGMathLibrary_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONPCGMathLibrary, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONPCGMathLibrary_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONPCGMathLibrary)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAURACRONPCGMathLibrary(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONPCGMathLibrary(UAURACRONPCGMathLibrary&&) = delete; \
	UAURACRONPCGMathLibrary(const UAURACRONPCGMathLibrary&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONPCGMathLibrary); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONPCGMathLibrary); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAURACRONPCGMathLibrary) \
	NO_API virtual ~UAURACRONPCGMathLibrary();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_74_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h_77_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONPCGMathLibrary;

// ********** End Class UAURACRONPCGMathLibrary ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGMathLibrary_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
