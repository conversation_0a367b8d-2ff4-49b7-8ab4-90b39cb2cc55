// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGPerformanceManager.h"

#ifdef AURACRON_AURACRONPCGPerformanceManager_generated_h
#error "AURACRONPCGPerformanceManager.generated.h already included, missing '#pragma once' in AURACRONPCGPerformanceManager.h"
#endif
#define AURACRON_AURACRONPCGPerformanceManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAURACRONDeviceType : uint8;
struct FAURACRONDevicePerformanceProfile;
struct FAURACRONPCGPerformanceConfig;
struct FAURACRONPCGPerformanceMetrics;

// ********** Begin ScriptStruct FAURACRONDevicePerformanceProfile *********************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_47_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONDevicePerformanceProfile;
// ********** End ScriptStruct FAURACRONDevicePerformanceProfile ***********************************

// ********** Begin ScriptStruct FAURACRONPCGPerformanceConfig *************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGPerformanceConfig;
// ********** End ScriptStruct FAURACRONPCGPerformanceConfig ***************************************

// ********** Begin ScriptStruct FAURACRONPCGElementPerformanceInfo ********************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_165_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGElementPerformanceInfo;
// ********** End ScriptStruct FAURACRONPCGElementPerformanceInfo **********************************

// ********** Begin ScriptStruct FAURACRONPCGPerformanceMetrics ************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_220_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONPCGPerformanceMetrics;
// ********** End ScriptStruct FAURACRONPCGPerformanceMetrics **************************************

// ********** Begin Class AAURACRONPCGPerformanceManager *******************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnMetricsTimer); \
	DECLARE_FUNCTION(execOnLODUpdateTimer); \
	DECLARE_FUNCTION(execGetDeviceBasedQualitySettings); \
	DECLARE_FUNCTION(execIsHighDevice); \
	DECLARE_FUNCTION(execIsMidDevice); \
	DECLARE_FUNCTION(execIsEntryDevice); \
	DECLARE_FUNCTION(execGetDeviceQualityMultiplier); \
	DECLARE_FUNCTION(execApplyDeviceSpecificSettings); \
	DECLARE_FUNCTION(execGetCurrentDeviceProfile); \
	DECLARE_FUNCTION(execDetectDeviceType); \
	DECLARE_FUNCTION(execSetAutoOptimizationEnabled); \
	DECLARE_FUNCTION(execSetPerformanceConfig); \
	DECLARE_FUNCTION(execGetTargetFPS); \
	DECLARE_FUNCTION(execGetPerformanceMetrics); \
	DECLARE_FUNCTION(execForceUpdateLOD); \
	DECLARE_FUNCTION(execUnregisterPCGElement); \
	DECLARE_FUNCTION(execRegisterPCGElement); \
	DECLARE_FUNCTION(execInitializePerformanceSystem);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPerformanceManager_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGPerformanceManager(); \
	friend struct Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPerformanceManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGPerformanceManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGPerformanceManager_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGPerformanceManager)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGPerformanceManager(AAURACRONPCGPerformanceManager&&) = delete; \
	AAURACRONPCGPerformanceManager(const AAURACRONPCGPerformanceManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGPerformanceManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGPerformanceManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGPerformanceManager) \
	NO_API virtual ~AAURACRONPCGPerformanceManager();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_336_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h_339_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGPerformanceManager;

// ********** End Class AAURACRONPCGPerformanceManager *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h

// ********** Begin Enum EAURACRONPCGLODLevel ******************************************************
#define FOREACH_ENUM_EAURACRONPCGLODLEVEL(op) \
	op(EAURACRONPCGLODLevel::LOD0_Highest) \
	op(EAURACRONPCGLODLevel::LOD1_High) \
	op(EAURACRONPCGLODLevel::LOD2_Medium) \
	op(EAURACRONPCGLODLevel::LOD3_Low) \
	op(EAURACRONPCGLODLevel::LOD4_Culled) 

enum class EAURACRONPCGLODLevel : uint8;
template<> struct TIsUEnumClass<EAURACRONPCGLODLevel> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONPCGLODLevel>();
// ********** End Enum EAURACRONPCGLODLevel ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
