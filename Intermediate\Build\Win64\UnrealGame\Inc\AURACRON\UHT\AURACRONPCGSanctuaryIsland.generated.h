// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGSanctuaryIsland.h"

#ifdef AURACRON_AURACRONPCGSanctuaryIsland_generated_h
#error "AURACRONPCGSanctuaryIsland.generated.h already included, missing '#pragma once' in AURACRONPCGSanctuaryIsland.h"
#endif
#define AURACRON_AURACRONPCGSanctuaryIsland_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;

// ********** Begin Class ASanctuaryIsland *********************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHealCharactersInRange); \
	DECLARE_FUNCTION(execGrantProtection); \
	DECLARE_FUNCTION(execApplyHealing); \
	DECLARE_FUNCTION(execUpdateBasedOnMapPhase); \
	DECLARE_FUNCTION(execApplyAllSanctuaryEffects); \
	DECLARE_FUNCTION(execIsActorInSecureZone); \
	DECLARE_FUNCTION(execSetSecureZoneActive); \
	DECLARE_FUNCTION(execRemoveIslandEffects); \
	DECLARE_FUNCTION(execGrantVisionAmplificationEffect); \
	DECLARE_FUNCTION(execGetStrategicValue); \
	DECLARE_FUNCTION(execDeactivateSecureZone); \
	DECLARE_FUNCTION(execActivateSecureZone); \
	DECLARE_FUNCTION(execInitializeSanctuaryIsland); \
	DECLARE_FUNCTION(execHasValidProtectionConfiguration); \
	DECLARE_FUNCTION(execHasValidHealingConfiguration); \
	DECLARE_FUNCTION(execSetEnvironmentType); \
	DECLARE_FUNCTION(execGetEnvironmentType); \
	DECLARE_FUNCTION(execGrantProtectionEffect); \
	DECLARE_FUNCTION(execGrantHealingEffect); \
	DECLARE_FUNCTION(execSetInCalmFlowSection); \
	DECLARE_FUNCTION(execIsInCalmFlowSection); \
	DECLARE_FUNCTION(execIsSecureZoneActive);


AURACRON_API UClass* Z_Construct_UClass_ASanctuaryIsland_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesASanctuaryIsland(); \
	friend struct Z_Construct_UClass_ASanctuaryIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_ASanctuaryIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(ASanctuaryIsland, APrismalFlowIsland, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_ASanctuaryIsland_NoRegister) \
	DECLARE_SERIALIZER(ASanctuaryIsland) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		HealingIntensity=NETFIELD_REP_START, \
		HealingDuration, \
		ProtectionIntensity, \
		ProtectionDuration, \
		HealingGameplayEffect, \
		ProtectionGameplayEffect, \
		VisionAmplificationIntensity, \
		VisionAmplificationDuration, \
		VisionAmplificationGameplayEffect, \
		bSecureZoneActive, \
		SecureZoneRadius, \
		NETFIELD_REP_END=SecureZoneRadius	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ASanctuaryIsland(ASanctuaryIsland&&) = delete; \
	ASanctuaryIsland(const ASanctuaryIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ASanctuaryIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ASanctuaryIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ASanctuaryIsland) \
	NO_API virtual ~ASanctuaryIsland();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_19_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h_22_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ASanctuaryIsland;

// ********** End Class ASanctuaryIsland ***********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSanctuaryIsland_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
