// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGTypes.h"

#ifdef AURACRON_AURACRONPCGTypes_generated_h
#error "AURACRONPCGTypes.generated.h already included, missing '#pragma once' in AURACRONPCGTypes.h"
#endif
#define AURACRON_AURACRONPCGTypes_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAURACRONMapTypeConfig ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_24_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONMapTypeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONMapTypeConfig;
// ********** End ScriptStruct FAURACRONMapTypeConfig **********************************************

// ********** Begin ScriptStruct FAURACRONVisualEffectSettings *************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_66_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONVisualEffectSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONVisualEffectSettings;
// ********** End ScriptStruct FAURACRONVisualEffectSettings ***************************************

// ********** Begin ScriptStruct FAURACRONEnvironmentConfig ****************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h_95_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONEnvironmentConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONEnvironmentConfig;
// ********** End ScriptStruct FAURACRONEnvironmentConfig ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTypes_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
