// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGUtility.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGUtility() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGUtility();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGUtility_NoRegister();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGActorReferences();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONPCGActorReferences ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences;
class UScriptStruct* FAURACRONPCGActorReferences::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGActorReferences"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar refer\xc3\xaancias de atores PCG encontrados\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar refer\xc3\xaancias de atores PCG encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentActors_MetaData[] = {
		{ "Category", "AURACRONPCGActorReferences" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores de ambiente encontrados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores de ambiente encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailActors_MetaData[] = {
		{ "Category", "AURACRONPCGActorReferences" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores de trilha encontrados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores de trilha encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandActors_MetaData[] = {
		{ "Category", "AURACRONPCGActorReferences" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores de ilha encontrados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores de ilha encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowActor_MetaData[] = {
		{ "Category", "AURACRONPCGActorReferences" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ator Prismal Flow (\xc3\xbanico) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator Prismal Flow (\xc3\xbanico)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnvironmentActors;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TrailActors;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_IslandActors;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrismalFlowActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGActorReferences>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_EnvironmentActors_Inner = { "EnvironmentActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_EnvironmentActors = { "EnvironmentActors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGActorReferences, EnvironmentActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentActors_MetaData), NewProp_EnvironmentActors_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_TrailActors_Inner = { "TrailActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGTrail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_TrailActors = { "TrailActors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGActorReferences, TrailActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailActors_MetaData), NewProp_TrailActors_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_IslandActors_Inner = { "IslandActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_IslandActors = { "IslandActors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGActorReferences, IslandActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandActors_MetaData), NewProp_IslandActors_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_PrismalFlowActor = { "PrismalFlowActor", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGActorReferences, PrismalFlowActor), Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowActor_MetaData), NewProp_PrismalFlowActor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_EnvironmentActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_EnvironmentActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_TrailActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_TrailActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_IslandActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_IslandActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewProp_PrismalFlowActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGActorReferences",
	Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::PropPointers),
	sizeof(FAURACRONPCGActorReferences),
	alignof(FAURACRONPCGActorReferences),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGActorReferences()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGActorReferences *****************************************

// ********** Begin ScriptStruct FAURACRONPCGSearchOptions *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions;
class UScriptStruct* FAURACRONPCGSearchOptions::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGSearchOptions"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Op\xc3\xa7\xc3\xb5""es para busca de atores PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Op\xc3\xa7\xc3\xb5""es para busca de atores PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSearchEnvironments_MetaData[] = {
		{ "Category", "AURACRONPCGSearchOptions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Buscar atores de ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buscar atores de ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSearchTrails_MetaData[] = {
		{ "Category", "AURACRONPCGSearchOptions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Buscar atores de trilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buscar atores de trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSearchIslands_MetaData[] = {
		{ "Category", "AURACRONPCGSearchOptions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Buscar atores de ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buscar atores de ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSearchPrismalFlow_MetaData[] = {
		{ "Category", "AURACRONPCGSearchOptions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Buscar ator Prismal Flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buscar ator Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeInactiveActors_MetaData[] = {
		{ "Category", "AURACRONPCGSearchOptions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Incluir atores inativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Incluir atores inativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bVerboseLogging_MetaData[] = {
		{ "Category", "AURACRONPCGSearchOptions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Log detalhado da busca */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Log detalhado da busca" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSearchEnvironments_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSearchEnvironments;
	static void NewProp_bSearchTrails_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSearchTrails;
	static void NewProp_bSearchIslands_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSearchIslands;
	static void NewProp_bSearchPrismalFlow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSearchPrismalFlow;
	static void NewProp_bIncludeInactiveActors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeInactiveActors;
	static void NewProp_bVerboseLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVerboseLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGSearchOptions>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchEnvironments_SetBit(void* Obj)
{
	((FAURACRONPCGSearchOptions*)Obj)->bSearchEnvironments = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchEnvironments = { "bSearchEnvironments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGSearchOptions), &Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchEnvironments_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSearchEnvironments_MetaData), NewProp_bSearchEnvironments_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchTrails_SetBit(void* Obj)
{
	((FAURACRONPCGSearchOptions*)Obj)->bSearchTrails = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchTrails = { "bSearchTrails", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGSearchOptions), &Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchTrails_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSearchTrails_MetaData), NewProp_bSearchTrails_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchIslands_SetBit(void* Obj)
{
	((FAURACRONPCGSearchOptions*)Obj)->bSearchIslands = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchIslands = { "bSearchIslands", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGSearchOptions), &Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchIslands_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSearchIslands_MetaData), NewProp_bSearchIslands_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchPrismalFlow_SetBit(void* Obj)
{
	((FAURACRONPCGSearchOptions*)Obj)->bSearchPrismalFlow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchPrismalFlow = { "bSearchPrismalFlow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGSearchOptions), &Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchPrismalFlow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSearchPrismalFlow_MetaData), NewProp_bSearchPrismalFlow_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bIncludeInactiveActors_SetBit(void* Obj)
{
	((FAURACRONPCGSearchOptions*)Obj)->bIncludeInactiveActors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bIncludeInactiveActors = { "bIncludeInactiveActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGSearchOptions), &Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bIncludeInactiveActors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeInactiveActors_MetaData), NewProp_bIncludeInactiveActors_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bVerboseLogging_SetBit(void* Obj)
{
	((FAURACRONPCGSearchOptions*)Obj)->bVerboseLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bVerboseLogging = { "bVerboseLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGSearchOptions), &Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bVerboseLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bVerboseLogging_MetaData), NewProp_bVerboseLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchEnvironments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchTrails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bSearchPrismalFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bIncludeInactiveActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewProp_bVerboseLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGSearchOptions",
	Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::PropPointers),
	sizeof(FAURACRONPCGSearchOptions),
	alignof(FAURACRONPCGSearchOptions),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGSearchOptions *******************************************

// ********** Begin Class UAURACRONPCGUtility Function ApplyConfigurationToAllActors ***************
struct Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics
{
	struct AURACRONPCGUtility_eventApplyConfigurationToAllActors_Parms
	{
		FAURACRONPCGActorReferences ActorReferences;
		FString ConfigurationName;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar configura\xc3\xa7\xc3\xa3o a todos os atores PCG de um tipo\n     * @param ActorReferences - Refer\xc3\xaancias dos atores\n     * @param ConfigurationName - Nome da configura\xc3\xa7\xc3\xa3o a aplicar\n     * @param Value - Valor da configura\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\xa7\xc3\xa3o a todos os atores PCG de um tipo\n@param ActorReferences - Refer\xc3\xaancias dos atores\n@param ConfigurationName - Nome da configura\xc3\xa7\xc3\xa3o a aplicar\n@param Value - Valor da configura\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorReferences_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConfigurationName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActorReferences;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConfigurationName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::NewProp_ActorReferences = { "ActorReferences", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventApplyConfigurationToAllActors_Parms, ActorReferences), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorReferences_MetaData), NewProp_ActorReferences_MetaData) }; // 2729947204
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::NewProp_ConfigurationName = { "ConfigurationName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventApplyConfigurationToAllActors_Parms, ConfigurationName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConfigurationName_MetaData), NewProp_ConfigurationName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventApplyConfigurationToAllActors_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::NewProp_ActorReferences,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::NewProp_ConfigurationName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "ApplyConfigurationToAllActors", Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::AURACRONPCGUtility_eventApplyConfigurationToAllActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::AURACRONPCGUtility_eventApplyConfigurationToAllActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execApplyConfigurationToAllActors)
{
	P_GET_STRUCT_REF(FAURACRONPCGActorReferences,Z_Param_Out_ActorReferences);
	P_GET_PROPERTY(FStrProperty,Z_Param_ConfigurationName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAURACRONPCGUtility::ApplyConfigurationToAllActors(Z_Param_Out_ActorReferences,Z_Param_ConfigurationName,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function ApplyConfigurationToAllActors *****************

// ********** Begin Class UAURACRONPCGUtility Function FindAllPCGActors ****************************
struct Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics
{
	struct AURACRONPCGUtility_eventFindAllPCGActors_Parms
	{
		UWorld* World;
		FAURACRONPCGActorReferences ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vers\xc3\xa3o simplificada para Blueprint sem par\xc3\xa2metros */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vers\xc3\xa3o simplificada para Blueprint sem par\xc3\xa2metros" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindAllPCGActors_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindAllPCGActors_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, METADATA_PARAMS(0, nullptr) }; // 2729947204
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "FindAllPCGActors", Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::AURACRONPCGUtility_eventFindAllPCGActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::AURACRONPCGUtility_eventFindAllPCGActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execFindAllPCGActors)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONPCGActorReferences*)Z_Param__Result=UAURACRONPCGUtility::FindAllPCGActors(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function FindAllPCGActors ******************************

// ********** Begin Class UAURACRONPCGUtility Function FindPCGActors *******************************
struct Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics
{
	struct AURACRONPCGUtility_eventFindPCGActors_Parms
	{
		UWorld* World;
		FAURACRONPCGSearchOptions SearchOptions;
		FAURACRONPCGActorReferences ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Encontrar todos os atores PCG na cena\n     * @param World - Mundo onde buscar os atores\n     * @param SearchOptions - Op\xc3\xa7\xc3\xb5""es de busca (quais tipos buscar)\n     * @return Estrutura com refer\xc3\xaancias aos atores encontrados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontrar todos os atores PCG na cena\n@param World - Mundo onde buscar os atores\n@param SearchOptions - Op\xc3\xa7\xc3\xb5""es de busca (quais tipos buscar)\n@return Estrutura com refer\xc3\xaancias aos atores encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchOptions_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SearchOptions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindPCGActors_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::NewProp_SearchOptions = { "SearchOptions", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindPCGActors_Parms, SearchOptions), Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchOptions_MetaData), NewProp_SearchOptions_MetaData) }; // 3145783369
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindPCGActors_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, METADATA_PARAMS(0, nullptr) }; // 2729947204
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::NewProp_SearchOptions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "FindPCGActors", Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::AURACRONPCGUtility_eventFindPCGActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::AURACRONPCGUtility_eventFindPCGActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execFindPCGActors)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FAURACRONPCGSearchOptions,Z_Param_Out_SearchOptions);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONPCGActorReferences*)Z_Param__Result=UAURACRONPCGUtility::FindPCGActors(Z_Param_World,Z_Param_Out_SearchOptions);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function FindPCGActors *********************************

// ********** Begin Class UAURACRONPCGUtility Function FindPCGActorsOfClass ************************
struct Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics
{
	struct AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms
	{
		UWorld* World;
		UClass* ActorClass;
		bool bIncludeInactive;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Encontrar atores PCG de um tipo espec\xc3\xad""fico\n     * @param World - Mundo onde buscar\n     * @param ActorClass - Classe do ator a buscar\n     * @param bIncludeInactive - Incluir atores inativos\n     * @return Array de atores encontrados\n     */" },
#endif
		{ "CPP_Default_bIncludeInactive", "false" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontrar atores PCG de um tipo espec\xc3\xad""fico\n@param World - Mundo onde buscar\n@param ActorClass - Classe do ator a buscar\n@param bIncludeInactive - Incluir atores inativos\n@return Array de atores encontrados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static void NewProp_bIncludeInactive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeInactive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_bIncludeInactive_SetBit(void* Obj)
{
	((AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms*)Obj)->bIncludeInactive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_bIncludeInactive = { "bIncludeInactive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms), &Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_bIncludeInactive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_bIncludeInactive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "FindPCGActorsOfClass", Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::AURACRONPCGUtility_eventFindPCGActorsOfClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execFindPCGActorsOfClass)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_GET_UBOOL(Z_Param_bIncludeInactive);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=UAURACRONPCGUtility::FindPCGActorsOfClass(Z_Param_World,Z_Param_ActorClass,Z_Param_bIncludeInactive);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function FindPCGActorsOfClass **************************

// ********** Begin Class UAURACRONPCGUtility Function GetPCGActorStatistics ***********************
struct Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics
{
	struct AURACRONPCGUtility_eventGetPCGActorStatistics_Parms
	{
		UWorld* World;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\xadsticas dos atores PCG na cena\n     * @param World - Mundo a analisar\n     * @return String com estat\xc3\xadsticas formatadas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\xadsticas dos atores PCG na cena\n@param World - Mundo a analisar\n@return String com estat\xc3\xadsticas formatadas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventGetPCGActorStatistics_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventGetPCGActorStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "GetPCGActorStatistics", Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::AURACRONPCGUtility_eventGetPCGActorStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::AURACRONPCGUtility_eventGetPCGActorStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execGetPCGActorStatistics)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAURACRONPCGUtility::GetPCGActorStatistics(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function GetPCGActorStatistics *************************

// ********** Begin Class UAURACRONPCGUtility Function IsValidPCGActor *****************************
struct Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics
{
	struct AURACRONPCGUtility_eventIsValidPCGActor_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar se um ator PCG est\xc3\xa1 ativo e funcional\n     * @param Actor - Ator a validar\n     * @return True se o ator \xc3\xa9 v\xc3\xa1lido e ativo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar se um ator PCG est\xc3\xa1 ativo e funcional\n@param Actor - Ator a validar\n@return True se o ator \xc3\xa9 v\xc3\xa1lido e ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventIsValidPCGActor_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGUtility_eventIsValidPCGActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGUtility_eventIsValidPCGActor_Parms), &Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "IsValidPCGActor", Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::AURACRONPCGUtility_eventIsValidPCGActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::AURACRONPCGUtility_eventIsValidPCGActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execIsValidPCGActor)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAURACRONPCGUtility::IsValidPCGActor(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function IsValidPCGActor *******************************

// ********** Begin Class UAURACRONPCGUtility Function RegisterPCGActorChangeCallback **************
struct Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics
{
	struct AURACRONPCGUtility_eventRegisterPCGActorChangeCallback_Parms
	{
		UWorld* World;
		FString CallbackName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar callback para mudan\xc3\xa7""as em atores PCG\n     * @param World - Mundo a monitorar\n     * @param Callback - Fun\xc3\xa7\xc3\xa3o a chamar quando houver mudan\xc3\xa7""as\n     */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar callback para mudan\xc3\xa7""as em atores PCG\n@param World - Mundo a monitorar\n@param Callback - Fun\xc3\xa7\xc3\xa3o a chamar quando houver mudan\xc3\xa7""as" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventRegisterPCGActorChangeCallback_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::NewProp_CallbackName = { "CallbackName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGUtility_eventRegisterPCGActorChangeCallback_Parms, CallbackName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackName_MetaData), NewProp_CallbackName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::NewProp_CallbackName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGUtility, nullptr, "RegisterPCGActorChangeCallback", Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::AURACRONPCGUtility_eventRegisterPCGActorChangeCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::AURACRONPCGUtility_eventRegisterPCGActorChangeCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGUtility::execRegisterPCGActorChangeCallback)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_PROPERTY(FStrProperty,Z_Param_CallbackName);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAURACRONPCGUtility::RegisterPCGActorChangeCallback(Z_Param_World,Z_Param_CallbackName);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGUtility Function RegisterPCGActorChangeCallback ****************

// ********** Begin Class UAURACRONPCGUtility ******************************************************
void UAURACRONPCGUtility::StaticRegisterNativesUAURACRONPCGUtility()
{
	UClass* Class = UAURACRONPCGUtility::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyConfigurationToAllActors", &UAURACRONPCGUtility::execApplyConfigurationToAllActors },
		{ "FindAllPCGActors", &UAURACRONPCGUtility::execFindAllPCGActors },
		{ "FindPCGActors", &UAURACRONPCGUtility::execFindPCGActors },
		{ "FindPCGActorsOfClass", &UAURACRONPCGUtility::execFindPCGActorsOfClass },
		{ "GetPCGActorStatistics", &UAURACRONPCGUtility::execGetPCGActorStatistics },
		{ "IsValidPCGActor", &UAURACRONPCGUtility::execIsValidPCGActor },
		{ "RegisterPCGActorChangeCallback", &UAURACRONPCGUtility::execRegisterPCGActorChangeCallback },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONPCGUtility;
UClass* UAURACRONPCGUtility::GetPrivateStaticClass()
{
	using TClass = UAURACRONPCGUtility;
	if (!Z_Registration_Info_UClass_UAURACRONPCGUtility.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGUtility"),
			Z_Registration_Info_UClass_UAURACRONPCGUtility.InnerSingleton,
			StaticRegisterNativesUAURACRONPCGUtility,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONPCGUtility.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONPCGUtility_NoRegister()
{
	return UAURACRONPCGUtility::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONPCGUtility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe utilit\xc3\xa1ria est\xc3\xa1tica para fun\xc3\xa7\xc3\xb5""es comuns PCG\n * Centraliza funcionalidades compartilhadas para evitar duplica\xc3\xa7\xc3\xa3o de c\xc3\xb3""digo\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGUtility.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGUtility.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe utilit\xc3\xa1ria est\xc3\xa1tica para fun\xc3\xa7\xc3\xb5""es comuns PCG\nCentraliza funcionalidades compartilhadas para evitar duplica\xc3\xa7\xc3\xa3o de c\xc3\xb3""digo" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_ApplyConfigurationToAllActors, "ApplyConfigurationToAllActors" }, // 528361048
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_FindAllPCGActors, "FindAllPCGActors" }, // 2602159106
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActors, "FindPCGActors" }, // 433182702
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_FindPCGActorsOfClass, "FindPCGActorsOfClass" }, // 2998891969
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_GetPCGActorStatistics, "GetPCGActorStatistics" }, // 1686076776
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_IsValidPCGActor, "IsValidPCGActor" }, // 901554234
		{ &Z_Construct_UFunction_UAURACRONPCGUtility_RegisterPCGActorChangeCallback, "RegisterPCGActorChangeCallback" }, // 358478688
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONPCGUtility>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAURACRONPCGUtility_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGUtility_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONPCGUtility_Statics::ClassParams = {
	&UAURACRONPCGUtility::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGUtility_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONPCGUtility_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONPCGUtility()
{
	if (!Z_Registration_Info_UClass_UAURACRONPCGUtility.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONPCGUtility.OuterSingleton, Z_Construct_UClass_UAURACRONPCGUtility_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONPCGUtility.OuterSingleton;
}
UAURACRONPCGUtility::UAURACRONPCGUtility(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONPCGUtility);
UAURACRONPCGUtility::~UAURACRONPCGUtility() {}
// ********** End Class UAURACRONPCGUtility ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONPCGActorReferences::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGActorReferences_Statics::NewStructOps, TEXT("AURACRONPCGActorReferences"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGActorReferences, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGActorReferences), 2729947204U) },
		{ FAURACRONPCGSearchOptions::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGSearchOptions_Statics::NewStructOps, TEXT("AURACRONPCGSearchOptions"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGSearchOptions, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGSearchOptions), 3145783369U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONPCGUtility, UAURACRONPCGUtility::StaticClass, TEXT("UAURACRONPCGUtility"), &Z_Registration_Info_UClass_UAURACRONPCGUtility, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONPCGUtility), 3651301725U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h__Script_AURACRON_3312304923(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGUtility_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
