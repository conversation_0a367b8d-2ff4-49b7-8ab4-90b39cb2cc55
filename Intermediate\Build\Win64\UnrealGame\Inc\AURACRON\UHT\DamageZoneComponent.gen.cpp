// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Components/DamageZoneComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeDamageZoneComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UDamageZoneComponent();
AURACRON_API UClass* Z_Construct_UClass_UDamageZoneComponent_NoRegister();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnPlayerDamagedByZone ************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnPlayerDamagedByZone_Parms
	{
		AActor* Player;
		float DamageAmount;
		float DamageMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========================================\n// DELEGATES PARA EVENTOS DE DANO - UE 5.6 MODERN APIS\n// ========================================\n" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DELEGATES PARA EVENTOS DE DANO - UE 5.6 MODERN APIS" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPlayerDamagedByZone_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPlayerDamagedByZone_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPlayerDamagedByZone_Parms, DamageMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::NewProp_DamageMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnPlayerDamagedByZone__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::_Script_AURACRON_eventOnPlayerDamagedByZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::_Script_AURACRON_eventOnPlayerDamagedByZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlayerDamagedByZone_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDamagedByZone, AActor* Player, float DamageAmount, float DamageMultiplier)
{
	struct _Script_AURACRON_eventOnPlayerDamagedByZone_Parms
	{
		AActor* Player;
		float DamageAmount;
		float DamageMultiplier;
	};
	_Script_AURACRON_eventOnPlayerDamagedByZone_Parms Parms;
	Parms.Player=Player;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageMultiplier=DamageMultiplier;
	OnPlayerDamagedByZone.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerDamagedByZone **************************************************

// ********** Begin Delegate FOnPlayerInWarningZone ************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnPlayerInWarningZone_Parms
	{
		AActor* Player;
		float Distance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPlayerInWarningZone_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnPlayerInWarningZone_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::NewProp_Distance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnPlayerInWarningZone__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::_Script_AURACRON_eventOnPlayerInWarningZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::_Script_AURACRON_eventOnPlayerInWarningZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlayerInWarningZone_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerInWarningZone, AActor* Player, float Distance)
{
	struct _Script_AURACRON_eventOnPlayerInWarningZone_Parms
	{
		AActor* Player;
		float Distance;
	};
	_Script_AURACRON_eventOnPlayerInWarningZone_Parms Parms;
	Parms.Player=Player;
	Parms.Distance=Distance;
	OnPlayerInWarningZone.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerInWarningZone **************************************************

// ********** Begin Delegate FOnDamageZoneActivated ************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnDamageZoneActivated__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnDamageZoneActivated_DelegateWrapper(const FMulticastScriptDelegate& OnDamageZoneActivated)
{
	OnDamageZoneActivated.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnDamageZoneActivated **************************************************

// ********** Begin Delegate FOnDamageZoneDeactivated **********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnDamageZoneDeactivated__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnDamageZoneDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnDamageZoneDeactivated)
{
	OnDamageZoneDeactivated.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnDamageZoneDeactivated ************************************************

// ********** Begin Class UDamageZoneComponent Function GetCurrentSafeRadius ***********************
struct Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics
{
	struct DamageZoneComponent_eventGetCurrentSafeRadius_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m o raio seguro atual\n     * @return Raio seguro atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o raio seguro atual\n@return Raio seguro atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventGetCurrentSafeRadius_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "GetCurrentSafeRadius", Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::DamageZoneComponent_eventGetCurrentSafeRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::DamageZoneComponent_eventGetCurrentSafeRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execGetCurrentSafeRadius)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentSafeRadius();
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function GetCurrentSafeRadius *************************

// ********** Begin Class UDamageZoneComponent Function GetCurrentWarningRadius ********************
struct Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics
{
	struct DamageZoneComponent_eventGetCurrentWarningRadius_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m o raio de aviso atual\n     * @return Raio de aviso atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o raio de aviso atual\n@return Raio de aviso atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventGetCurrentWarningRadius_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "GetCurrentWarningRadius", Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::DamageZoneComponent_eventGetCurrentWarningRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::DamageZoneComponent_eventGetCurrentWarningRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execGetCurrentWarningRadius)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentWarningRadius();
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function GetCurrentWarningRadius **********************

// ********** Begin Class UDamageZoneComponent Function GetZoneCenter ******************************
struct Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics
{
	struct DamageZoneComponent_eventGetZoneCenter_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m o centro atual da zona de dano\n     * @return Centro da zona de dano\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o centro atual da zona de dano\n@return Centro da zona de dano" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventGetZoneCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "GetZoneCenter", Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::DamageZoneComponent_eventGetZoneCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::DamageZoneComponent_eventGetZoneCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execGetZoneCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetZoneCenter();
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function GetZoneCenter ********************************

// ********** Begin Class UDamageZoneComponent Function IsDamageZoneActive *************************
struct Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics
{
	struct DamageZoneComponent_eventIsDamageZoneActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se a zona de dano est\xc3\xa1 ativa\n     * @return True se a zona de dano estiver ativa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se a zona de dano est\xc3\xa1 ativa\n@return True se a zona de dano estiver ativa" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DamageZoneComponent_eventIsDamageZoneActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DamageZoneComponent_eventIsDamageZoneActive_Parms), &Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "IsDamageZoneActive", Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::DamageZoneComponent_eventIsDamageZoneActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::DamageZoneComponent_eventIsDamageZoneActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execIsDamageZoneActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDamageZoneActive();
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function IsDamageZoneActive ***************************

// ********** Begin Class UDamageZoneComponent Function SetDamagePerSecond *************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics
{
	struct DamageZoneComponent_eventSetDamagePerSecond_Parms
	{
		float NewDamagePerSecond;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define a quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura\n     * @param NewDamagePerSecond - Novo valor de dano por segundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define a quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura\n@param NewDamagePerSecond - Novo valor de dano por segundo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewDamagePerSecond;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::NewProp_NewDamagePerSecond = { "NewDamagePerSecond", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetDamagePerSecond_Parms, NewDamagePerSecond), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::NewProp_NewDamagePerSecond,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetDamagePerSecond", Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::DamageZoneComponent_eventSetDamagePerSecond_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::DamageZoneComponent_eventSetDamagePerSecond_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetDamagePerSecond)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewDamagePerSecond);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDamagePerSecond(Z_Param_NewDamagePerSecond);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetDamagePerSecond ***************************

// ********** Begin Class UDamageZoneComponent Function SetDamageScalingFactor *********************
struct Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics
{
	struct DamageZoneComponent_eventSetDamageScalingFactor_Parms
	{
		float NewScalingFactor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define o fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura\n     * @param NewScalingFactor - Novo fator de escala (1.0 = sem escala)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura\n@param NewScalingFactor - Novo fator de escala (1.0 = sem escala)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewScalingFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::NewProp_NewScalingFactor = { "NewScalingFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetDamageScalingFactor_Parms, NewScalingFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::NewProp_NewScalingFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetDamageScalingFactor", Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::DamageZoneComponent_eventSetDamageScalingFactor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::DamageZoneComponent_eventSetDamageScalingFactor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetDamageScalingFactor)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewScalingFactor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDamageScalingFactor(Z_Param_NewScalingFactor);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetDamageScalingFactor ***********************

// ********** Begin Class UDamageZoneComponent Function SetDamageZoneActive ************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics
{
	struct DamageZoneComponent_eventSetDamageZoneActive_Parms
	{
		bool bNewActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativa ou desativa o componente de zona de dano\n     * @param bNewActive - Novo estado de ativa\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa ou desativa o componente de zona de dano\n@param bNewActive - Novo estado de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bNewActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNewActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive_SetBit(void* Obj)
{
	((DamageZoneComponent_eventSetDamageZoneActive_Parms*)Obj)->bNewActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive = { "bNewActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DamageZoneComponent_eventSetDamageZoneActive_Parms), &Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::NewProp_bNewActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetDamageZoneActive", Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::DamageZoneComponent_eventSetDamageZoneActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::DamageZoneComponent_eventSetDamageZoneActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetDamageZoneActive)
{
	P_GET_UBOOL(Z_Param_bNewActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDamageZoneActive(Z_Param_bNewActive);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetDamageZoneActive **************************

// ********** Begin Class UDamageZoneComponent Function SetSafeRadius ******************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics
{
	struct DamageZoneComponent_eventSetSafeRadius_Parms
	{
		float NewRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define o raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano\n     * @param NewRadius - Novo raio da \xc3\xa1rea segura em unidades do mundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano\n@param NewRadius - Novo raio da \xc3\xa1rea segura em unidades do mundo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::NewProp_NewRadius = { "NewRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetSafeRadius_Parms, NewRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::NewProp_NewRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetSafeRadius", Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::DamageZoneComponent_eventSetSafeRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::DamageZoneComponent_eventSetSafeRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetSafeRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSafeRadius(Z_Param_NewRadius);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetSafeRadius ********************************

// ********** Begin Class UDamageZoneComponent Function SetVisualEffectsEnabled ********************
struct Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics
{
	struct DamageZoneComponent_eventSetVisualEffectsEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone|Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Habilita ou desabilita efeitos visuais da zona de dano\n     * @param bEnabled - Se os efeitos visuais devem estar habilitados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilita ou desabilita efeitos visuais da zona de dano\n@param bEnabled - Se os efeitos visuais devem estar habilitados" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((DamageZoneComponent_eventSetVisualEffectsEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DamageZoneComponent_eventSetVisualEffectsEnabled_Parms), &Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetVisualEffectsEnabled", Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::DamageZoneComponent_eventSetVisualEffectsEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::DamageZoneComponent_eventSetVisualEffectsEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetVisualEffectsEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualEffectsEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetVisualEffectsEnabled **********************

// ********** Begin Class UDamageZoneComponent Function SetVisualEffectsIntensity ******************
struct Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics
{
	struct DamageZoneComponent_eventSetVisualEffectsIntensity_Parms
	{
		float NewIntensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone|Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define a intensidade dos efeitos visuais\n     * @param NewIntensity - Nova intensidade (0.0 a 2.0)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define a intensidade dos efeitos visuais\n@param NewIntensity - Nova intensidade (0.0 a 2.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::NewProp_NewIntensity = { "NewIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetVisualEffectsIntensity_Parms, NewIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::NewProp_NewIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetVisualEffectsIntensity", Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::DamageZoneComponent_eventSetVisualEffectsIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::DamageZoneComponent_eventSetVisualEffectsIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetVisualEffectsIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualEffectsIntensity(Z_Param_NewIntensity);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetVisualEffectsIntensity ********************

// ********** Begin Class UDamageZoneComponent Function SetWarningRadius ***************************
struct Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics
{
	struct DamageZoneComponent_eventSetWarningRadius_Parms
	{
		float NewWarningRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define o raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais\n     * @param NewWarningRadius - Novo raio da \xc3\xa1rea de aviso em unidades do mundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define o raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais\n@param NewWarningRadius - Novo raio da \xc3\xa1rea de aviso em unidades do mundo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewWarningRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::NewProp_NewWarningRadius = { "NewWarningRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DamageZoneComponent_eventSetWarningRadius_Parms, NewWarningRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::NewProp_NewWarningRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UDamageZoneComponent, nullptr, "SetWarningRadius", Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::DamageZoneComponent_eventSetWarningRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::DamageZoneComponent_eventSetWarningRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDamageZoneComponent::execSetWarningRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewWarningRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWarningRadius(Z_Param_NewWarningRadius);
	P_NATIVE_END;
}
// ********** End Class UDamageZoneComponent Function SetWarningRadius *****************************

// ********** Begin Class UDamageZoneComponent *****************************************************
void UDamageZoneComponent::StaticRegisterNativesUDamageZoneComponent()
{
	UClass* Class = UDamageZoneComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetCurrentSafeRadius", &UDamageZoneComponent::execGetCurrentSafeRadius },
		{ "GetCurrentWarningRadius", &UDamageZoneComponent::execGetCurrentWarningRadius },
		{ "GetZoneCenter", &UDamageZoneComponent::execGetZoneCenter },
		{ "IsDamageZoneActive", &UDamageZoneComponent::execIsDamageZoneActive },
		{ "SetDamagePerSecond", &UDamageZoneComponent::execSetDamagePerSecond },
		{ "SetDamageScalingFactor", &UDamageZoneComponent::execSetDamageScalingFactor },
		{ "SetDamageZoneActive", &UDamageZoneComponent::execSetDamageZoneActive },
		{ "SetSafeRadius", &UDamageZoneComponent::execSetSafeRadius },
		{ "SetVisualEffectsEnabled", &UDamageZoneComponent::execSetVisualEffectsEnabled },
		{ "SetVisualEffectsIntensity", &UDamageZoneComponent::execSetVisualEffectsIntensity },
		{ "SetWarningRadius", &UDamageZoneComponent::execSetWarningRadius },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UDamageZoneComponent;
UClass* UDamageZoneComponent::GetPrivateStaticClass()
{
	using TClass = UDamageZoneComponent;
	if (!Z_Registration_Info_UClass_UDamageZoneComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("DamageZoneComponent"),
			Z_Registration_Info_UClass_UDamageZoneComponent.InnerSingleton,
			StaticRegisterNativesUDamageZoneComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UDamageZoneComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UDamageZoneComponent_NoRegister()
{
	return UDamageZoneComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UDamageZoneComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente respons\xc3\xa1vel por aplicar dano a jogadores que saem da \xc3\xa1rea segura do mapa\n * durante a fase de Resolu\xc3\xa7\xc3\xa3o quando ocorre a contra\xc3\xa7\xc3\xa3o do mapa.\n */" },
#endif
		{ "IncludePath", "Components/DamageZoneComponent.h" },
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente respons\xc3\xa1vel por aplicar dano a jogadores que saem da \xc3\xa1rea segura do mapa\ndurante a fase de Resolu\xc3\xa7\xc3\xa3o quando ocorre a contra\xc3\xa7\xc3\xa3o do mapa." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerDamagedByZone_MetaData[] = {
		{ "Category", "Damage Zone|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando um jogador recebe dano da zona */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando um jogador recebe dano da zona" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerInWarningZone_MetaData[] = {
		{ "Category", "Damage Zone|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando um jogador entra na zona de aviso */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando um jogador entra na zona de aviso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDamageZoneActivated_MetaData[] = {
		{ "Category", "Damage Zone|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando a zona de dano \xc3\xa9 ativada */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando a zona de dano \xc3\xa9 ativada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDamageZoneDeactivated_MetaData[] = {
		{ "Category", "Damage Zone|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando a zona de dano \xc3\xa9 desativada */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando a zona de dano \xc3\xa9 desativada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SafeRadius_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da \xc3\xa1rea segura onde jogadores n\xc3\xa3o recebem dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningRadius_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da \xc3\xa1rea de aviso, onde jogadores recebem alertas visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamagePerSecond_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quantidade de dano aplicado por segundo aos jogadores fora da \xc3\xa1rea segura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageScalingFactor_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fator de escala do dano conforme o jogador se afasta da \xc3\xa1rea segura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDamageZoneActive_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Determina se a zona de dano est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Determina se a zona de dano est\xc3\xa1 ativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo acumulado desde o \xc3\xbaltimo tick de dano */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado desde o \xc3\xbaltimo tick de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageInterval_MetaData[] = {
		{ "Category", "Damage Zone" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo entre aplica\xc3\xa7\xc3\xb5""es de dano em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo entre aplica\xc3\xa7\xc3\xb5""es de dano em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneCenter_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Centro da zona de dano, normalmente o centro do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Centro da zona de dano, normalmente o centro do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageZoneVFXComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente Niagara para efeitos visuais da zona de dano */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente Niagara para efeitos visuais da zona de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningZoneVFXComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente Niagara para efeitos visuais da zona de aviso */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente Niagara para efeitos visuais da zona de aviso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneMaterialParameterCollection_MetaData[] = {
		{ "Category", "Damage Zone|Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material Parameter Collection para controle visual */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Parameter Collection para controle visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowVisualEffects_MetaData[] = {
		{ "Category", "Damage Zone|Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se os efeitos visuais devem ser mostrados */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se os efeitos visuais devem ser mostrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualEffectsIntensity_MetaData[] = {
		{ "Category", "Damage Zone|Visual Effects" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade dos efeitos visuais */" },
#endif
		{ "ModuleRelativePath", "Public/Components/DamageZoneComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade dos efeitos visuais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerDamagedByZone;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerInWarningZone;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDamageZoneActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDamageZoneDeactivated;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SafeRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WarningRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamagePerSecond;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageScalingFactor;
	static void NewProp_bIsDamageZoneActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDamageZoneActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageInterval;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ZoneCenter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageZoneVFXComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WarningZoneVFXComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ZoneMaterialParameterCollection;
	static void NewProp_bShowVisualEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowVisualEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisualEffectsIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UDamageZoneComponent_GetCurrentSafeRadius, "GetCurrentSafeRadius" }, // 1898393907
		{ &Z_Construct_UFunction_UDamageZoneComponent_GetCurrentWarningRadius, "GetCurrentWarningRadius" }, // 2558995493
		{ &Z_Construct_UFunction_UDamageZoneComponent_GetZoneCenter, "GetZoneCenter" }, // 621024569
		{ &Z_Construct_UFunction_UDamageZoneComponent_IsDamageZoneActive, "IsDamageZoneActive" }, // 1311578890
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetDamagePerSecond, "SetDamagePerSecond" }, // 617265192
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetDamageScalingFactor, "SetDamageScalingFactor" }, // 4054332560
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetDamageZoneActive, "SetDamageZoneActive" }, // 2604692451
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetSafeRadius, "SetSafeRadius" }, // 1185038686
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsEnabled, "SetVisualEffectsEnabled" }, // 627504520
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetVisualEffectsIntensity, "SetVisualEffectsIntensity" }, // 2702638822
		{ &Z_Construct_UFunction_UDamageZoneComponent_SetWarningRadius, "SetWarningRadius" }, // 4209193659
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UDamageZoneComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnPlayerDamagedByZone = { "OnPlayerDamagedByZone", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, OnPlayerDamagedByZone), Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerDamagedByZone_MetaData), NewProp_OnPlayerDamagedByZone_MetaData) }; // 1943053785
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnPlayerInWarningZone = { "OnPlayerInWarningZone", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, OnPlayerInWarningZone), Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerInWarningZone_MetaData), NewProp_OnPlayerInWarningZone_MetaData) }; // 1540639787
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnDamageZoneActivated = { "OnDamageZoneActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, OnDamageZoneActivated), Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDamageZoneActivated_MetaData), NewProp_OnDamageZoneActivated_MetaData) }; // 2907772696
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnDamageZoneDeactivated = { "OnDamageZoneDeactivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, OnDamageZoneDeactivated), Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDamageZoneDeactivated_MetaData), NewProp_OnDamageZoneDeactivated_MetaData) }; // 1165752552
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_SafeRadius = { "SafeRadius", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, SafeRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SafeRadius_MetaData), NewProp_SafeRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_WarningRadius = { "WarningRadius", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, WarningRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningRadius_MetaData), NewProp_WarningRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamagePerSecond = { "DamagePerSecond", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamagePerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamagePerSecond_MetaData), NewProp_DamagePerSecond_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageScalingFactor = { "DamageScalingFactor", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamageScalingFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageScalingFactor_MetaData), NewProp_DamageScalingFactor_MetaData) };
void Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive_SetBit(void* Obj)
{
	((UDamageZoneComponent*)Obj)->bIsDamageZoneActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive = { "bIsDamageZoneActive", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDamageZoneComponent), &Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDamageZoneActive_MetaData), NewProp_bIsDamageZoneActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageInterval = { "DamageInterval", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamageInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageInterval_MetaData), NewProp_DamageInterval_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_ZoneCenter = { "ZoneCenter", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, ZoneCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneCenter_MetaData), NewProp_ZoneCenter_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageZoneVFXComponent = { "DamageZoneVFXComponent", nullptr, (EPropertyFlags)0x0124080000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, DamageZoneVFXComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageZoneVFXComponent_MetaData), NewProp_DamageZoneVFXComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_WarningZoneVFXComponent = { "WarningZoneVFXComponent", nullptr, (EPropertyFlags)0x0124080000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, WarningZoneVFXComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningZoneVFXComponent_MetaData), NewProp_WarningZoneVFXComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_ZoneMaterialParameterCollection = { "ZoneMaterialParameterCollection", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, ZoneMaterialParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneMaterialParameterCollection_MetaData), NewProp_ZoneMaterialParameterCollection_MetaData) };
void Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bShowVisualEffects_SetBit(void* Obj)
{
	((UDamageZoneComponent*)Obj)->bShowVisualEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bShowVisualEffects = { "bShowVisualEffects", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDamageZoneComponent), &Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bShowVisualEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowVisualEffects_MetaData), NewProp_bShowVisualEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_VisualEffectsIntensity = { "VisualEffectsIntensity", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDamageZoneComponent, VisualEffectsIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualEffectsIntensity_MetaData), NewProp_VisualEffectsIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnPlayerDamagedByZone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnPlayerInWarningZone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnDamageZoneActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_OnDamageZoneDeactivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_SafeRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_WarningRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamagePerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageScalingFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bIsDamageZoneActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_ZoneCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_DamageZoneVFXComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_WarningZoneVFXComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_ZoneMaterialParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_bShowVisualEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDamageZoneComponent_Statics::NewProp_VisualEffectsIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UDamageZoneComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UDamageZoneComponent_Statics::ClassParams = {
	&UDamageZoneComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UDamageZoneComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UDamageZoneComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UDamageZoneComponent()
{
	if (!Z_Registration_Info_UClass_UDamageZoneComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UDamageZoneComponent.OuterSingleton, Z_Construct_UClass_UDamageZoneComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UDamageZoneComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UDamageZoneComponent);
UDamageZoneComponent::~UDamageZoneComponent() {}
// ********** End Class UDamageZoneComponent *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UDamageZoneComponent, UDamageZoneComponent::StaticClass, TEXT("UDamageZoneComponent"), &Z_Registration_Info_UClass_UDamageZoneComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UDamageZoneComponent), 838488369U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_819520366(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Components_DamageZoneComponent_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
