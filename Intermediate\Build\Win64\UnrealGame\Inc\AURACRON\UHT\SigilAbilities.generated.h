// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Sigils/SigilAbilities.h"

#ifdef AURACRON_SigilAbilities_generated_h
#error "SigilAbilities.generated.h already included, missing '#pragma once' in SigilAbilities.h"
#endif
#define AURACRON_SigilAbilities_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ASigilItem;
class UPrimitiveComponent;
struct FActiveGameplayEffectHandle;
struct FHitResult;

// ********** Begin Class USigilAbilityBase ********************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execApplyAbilityEffect); \
	DECLARE_FUNCTION(execSpawnAbilityVFX); \
	DECLARE_FUNCTION(execCalculateEffectiveCooldown); \
	DECLARE_FUNCTION(execCalculateEffectiveDuration); \
	DECLARE_FUNCTION(execValidateSigilRequirements); \
	DECLARE_FUNCTION(execInitializeWithSigil);


AURACRON_API UClass* Z_Construct_UClass_USigilAbilityBase_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbilityBase(); \
	friend struct Z_Construct_UClass_USigilAbilityBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbilityBase_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbilityBase, UGameplayAbility, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbilityBase_NoRegister) \
	DECLARE_SERIALIZER(USigilAbilityBase)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbilityBase(USigilAbilityBase&&) = delete; \
	USigilAbilityBase(const USigilAbilityBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbilityBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbilityBase); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(USigilAbilityBase) \
	NO_API virtual ~USigilAbilityBase();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_53_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_56_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbilityBase;

// ********** End Class USigilAbilityBase **********************************************************

// ********** Begin Class USigilAbility_Aegis_Murallion ********************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsAlly); \
	DECLARE_FUNCTION(execRemoveProtectionFromAlly); \
	DECLARE_FUNCTION(execApplyProtectionToAlly); \
	DECLARE_FUNCTION(execOnActorExitBarrier); \
	DECLARE_FUNCTION(execOnActorEnterBarrier); \
	DECLARE_FUNCTION(execRemoveBarrier); \
	DECLARE_FUNCTION(execCreateBarrier);


AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Aegis_Murallion_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbility_Aegis_Murallion(); \
	friend struct Z_Construct_UClass_USigilAbility_Aegis_Murallion_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Aegis_Murallion_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbility_Aegis_Murallion, USigilAbilityBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbility_Aegis_Murallion_NoRegister) \
	DECLARE_SERIALIZER(USigilAbility_Aegis_Murallion)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbility_Aegis_Murallion(USigilAbility_Aegis_Murallion&&) = delete; \
	USigilAbility_Aegis_Murallion(const USigilAbility_Aegis_Murallion&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbility_Aegis_Murallion); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbility_Aegis_Murallion); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAbility_Aegis_Murallion) \
	NO_API virtual ~USigilAbility_Aegis_Murallion();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_136_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_139_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbility_Aegis_Murallion;

// ********** End Class USigilAbility_Aegis_Murallion **********************************************

// ********** Begin Class USigilAbility_Ruin_FracassoPrismal ***************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateEffectiveCooldownReduction); \
	DECLARE_FUNCTION(execRemoveDamageBuff); \
	DECLARE_FUNCTION(execApplyDamageBuff); \
	DECLARE_FUNCTION(execResetAbilityCooldowns);


AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbility_Ruin_FracassoPrismal(); \
	friend struct Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbility_Ruin_FracassoPrismal, USigilAbilityBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbility_Ruin_FracassoPrismal_NoRegister) \
	DECLARE_SERIALIZER(USigilAbility_Ruin_FracassoPrismal)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbility_Ruin_FracassoPrismal(USigilAbility_Ruin_FracassoPrismal&&) = delete; \
	USigilAbility_Ruin_FracassoPrismal(const USigilAbility_Ruin_FracassoPrismal&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbility_Ruin_FracassoPrismal); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbility_Ruin_FracassoPrismal); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAbility_Ruin_FracassoPrismal) \
	NO_API virtual ~USigilAbility_Ruin_FracassoPrismal();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_226_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_229_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbility_Ruin_FracassoPrismal;

// ********** End Class USigilAbility_Ruin_FracassoPrismal *****************************************

// ********** Begin Class USigilAbility_Vesper_SoproDeFluxo ****************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateDashEndLocation); \
	DECLARE_FUNCTION(execIsValidAllyTarget); \
	DECLARE_FUNCTION(execCalculateEffectiveShieldAmount); \
	DECLARE_FUNCTION(execApplyShieldToAlly); \
	DECLARE_FUNCTION(execOnDashCompleted); \
	DECLARE_FUNCTION(execCompleteDash); \
	DECLARE_FUNCTION(execUpdateDashMovement); \
	DECLARE_FUNCTION(execStartDashToAlly); \
	DECLARE_FUNCTION(execFindNearestAlly);


AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAbility_Vesper_SoproDeFluxo(); \
	friend struct Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAbility_Vesper_SoproDeFluxo, USigilAbilityBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAbility_Vesper_SoproDeFluxo_NoRegister) \
	DECLARE_SERIALIZER(USigilAbility_Vesper_SoproDeFluxo)


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAbility_Vesper_SoproDeFluxo(USigilAbility_Vesper_SoproDeFluxo&&) = delete; \
	USigilAbility_Vesper_SoproDeFluxo(const USigilAbility_Vesper_SoproDeFluxo&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAbility_Vesper_SoproDeFluxo); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAbility_Vesper_SoproDeFluxo); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAbility_Vesper_SoproDeFluxo) \
	NO_API virtual ~USigilAbility_Vesper_SoproDeFluxo();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_299_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h_302_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAbility_Vesper_SoproDeFluxo;

// ********** End Class USigilAbility_Vesper_SoproDeFluxo ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAbilities_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
