// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilAttributeSet.h"
#include "AttributeSet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilAttributeSet() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilAttributeSet();
AURACRON_API UClass* Z_Construct_UClass_USigilAttributeSet_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAttributeSet();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAttribute();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAttributeData();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class USigilAttributeSet Function ApplyFusionMultiplier ************************
struct Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics
{
	struct SigilAttributeSet_eventApplyFusionMultiplier_Parms
	{
		float Multiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplica multiplicador de fus\xc3\xa3o a todos os atributos\n     * Chamado quando fus\xc3\xa3o \xc3\xa9 ativada aos 6 minutos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica multiplicador de fus\xc3\xa3o a todos os atributos\nChamado quando fus\xc3\xa3o \xc3\xa9 ativada aos 6 minutos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Multiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::NewProp_Multiplier = { "Multiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventApplyFusionMultiplier_Parms, Multiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::NewProp_Multiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "ApplyFusionMultiplier", Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::SigilAttributeSet_eventApplyFusionMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::SigilAttributeSet_eventApplyFusionMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execApplyFusionMultiplier)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Multiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyFusionMultiplier(Z_Param_Multiplier);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function ApplyFusionMultiplier **************************

// ********** Begin Class USigilAttributeSet Function CalculateTotalSigilPower *********************
struct Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics
{
	struct SigilAttributeSet_eventCalculateTotalSigilPower_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Calcula poder total do sigilo\n     * Soma ponderada de todos os atributos espectrais\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcula poder total do sigilo\nSoma ponderada de todos os atributos espectrais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventCalculateTotalSigilPower_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "CalculateTotalSigilPower", Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::SigilAttributeSet_eventCalculateTotalSigilPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::SigilAttributeSet_eventCalculateTotalSigilPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execCalculateTotalSigilPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTotalSigilPower();
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function CalculateTotalSigilPower ***********************

// ********** Begin Class USigilAttributeSet Function GetEffectiveAttributeValue *******************
struct Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics
{
	struct SigilAttributeSet_eventGetEffectiveAttributeValue_Parms
	{
		FGameplayAttribute Attribute;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m valor efetivo de atributo (com multiplicadores)\n     * Inclui fus\xc3\xa3o e outros modificadores tempor\xc3\xa1rios\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m valor efetivo de atributo (com multiplicadores)\nInclui fus\xc3\xa3o e outros modificadores tempor\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attribute_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Attribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::NewProp_Attribute = { "Attribute", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventGetEffectiveAttributeValue_Parms, Attribute), Z_Construct_UScriptStruct_FGameplayAttribute, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attribute_MetaData), NewProp_Attribute_MetaData) }; // 1212282043
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventGetEffectiveAttributeValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::NewProp_Attribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "GetEffectiveAttributeValue", Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::SigilAttributeSet_eventGetEffectiveAttributeValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::SigilAttributeSet_eventGetEffectiveAttributeValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execGetEffectiveAttributeValue)
{
	P_GET_STRUCT_REF(FGameplayAttribute,Z_Param_Out_Attribute);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEffectiveAttributeValue(Z_Param_Out_Attribute);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function GetEffectiveAttributeValue *********************

// ********** Begin Class USigilAttributeSet Function IsAttributeAtMaximum *************************
struct Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics
{
	struct SigilAttributeSet_eventIsAttributeAtMaximum_Parms
	{
		FGameplayAttribute Attribute;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se atributo est\xc3\xa1 no m\xc3\xa1ximo\n     * Usado para valida\xc3\xa7\xc3\xa3o de upgrades\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se atributo est\xc3\xa1 no m\xc3\xa1ximo\nUsado para valida\xc3\xa7\xc3\xa3o de upgrades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attribute_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Attribute;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::NewProp_Attribute = { "Attribute", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventIsAttributeAtMaximum_Parms, Attribute), Z_Construct_UScriptStruct_FGameplayAttribute, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attribute_MetaData), NewProp_Attribute_MetaData) }; // 1212282043
void Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilAttributeSet_eventIsAttributeAtMaximum_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilAttributeSet_eventIsAttributeAtMaximum_Parms), &Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::NewProp_Attribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "IsAttributeAtMaximum", Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::SigilAttributeSet_eventIsAttributeAtMaximum_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::SigilAttributeSet_eventIsAttributeAtMaximum_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execIsAttributeAtMaximum)
{
	P_GET_STRUCT_REF(FGameplayAttribute,Z_Param_Out_Attribute);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAttributeAtMaximum(Z_Param_Out_Attribute);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function IsAttributeAtMaximum ***************************

// ********** Begin Class USigilAttributeSet Function OnRep_AttackPower ****************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics
{
	struct SigilAttributeSet_eventOnRep_AttackPower_Parms
	{
		FGameplayAttributeData OldAttackPower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos Derivados de Combate\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos Derivados de Combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldAttackPower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldAttackPower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::NewProp_OldAttackPower = { "OldAttackPower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_AttackPower_Parms, OldAttackPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldAttackPower_MetaData), NewProp_OldAttackPower_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::NewProp_OldAttackPower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_AttackPower", Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::SigilAttributeSet_eventOnRep_AttackPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::SigilAttributeSet_eventOnRep_AttackPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_AttackPower)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldAttackPower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_AttackPower(Z_Param_Out_OldAttackPower);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_AttackPower ******************************

// ********** Begin Class USigilAttributeSet Function OnRep_AttackSpeed ****************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics
{
	struct SigilAttributeSet_eventOnRep_AttackSpeed_Parms
	{
		FGameplayAttributeData OldAttackSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldAttackSpeed_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldAttackSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::NewProp_OldAttackSpeed = { "OldAttackSpeed", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_AttackSpeed_Parms, OldAttackSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldAttackSpeed_MetaData), NewProp_OldAttackSpeed_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::NewProp_OldAttackSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_AttackSpeed", Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::SigilAttributeSet_eventOnRep_AttackSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::SigilAttributeSet_eventOnRep_AttackSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_AttackSpeed)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldAttackSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_AttackSpeed(Z_Param_Out_OldAttackSpeed);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_AttackSpeed ******************************

// ********** Begin Class USigilAttributeSet Function OnRep_CCResistance ***************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics
{
	struct SigilAttributeSet_eventOnRep_CCResistance_Parms
	{
		FGameplayAttributeData OldCCResistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCCResistance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCCResistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::NewProp_OldCCResistance = { "OldCCResistance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_CCResistance_Parms, OldCCResistance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCCResistance_MetaData), NewProp_OldCCResistance_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::NewProp_OldCCResistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_CCResistance", Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::SigilAttributeSet_eventOnRep_CCResistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::SigilAttributeSet_eventOnRep_CCResistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_CCResistance)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCCResistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CCResistance(Z_Param_Out_OldCCResistance);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_CCResistance *****************************

// ********** Begin Class USigilAttributeSet Function OnRep_CooldownReduction **********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics
{
	struct SigilAttributeSet_eventOnRep_CooldownReduction_Parms
	{
		FGameplayAttributeData OldCooldownReduction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCooldownReduction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCooldownReduction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::NewProp_OldCooldownReduction = { "OldCooldownReduction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_CooldownReduction_Parms, OldCooldownReduction), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCooldownReduction_MetaData), NewProp_OldCooldownReduction_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::NewProp_OldCooldownReduction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_CooldownReduction", Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::SigilAttributeSet_eventOnRep_CooldownReduction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::SigilAttributeSet_eventOnRep_CooldownReduction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_CooldownReduction)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCooldownReduction);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CooldownReduction(Z_Param_Out_OldCooldownReduction);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_CooldownReduction ************************

// ********** Begin Class USigilAttributeSet Function OnRep_CriticalChance *************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics
{
	struct SigilAttributeSet_eventOnRep_CriticalChance_Parms
	{
		FGameplayAttributeData OldCriticalChance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCriticalChance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCriticalChance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::NewProp_OldCriticalChance = { "OldCriticalChance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_CriticalChance_Parms, OldCriticalChance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCriticalChance_MetaData), NewProp_OldCriticalChance_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::NewProp_OldCriticalChance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_CriticalChance", Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::SigilAttributeSet_eventOnRep_CriticalChance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::SigilAttributeSet_eventOnRep_CriticalChance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_CriticalChance)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCriticalChance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CriticalChance(Z_Param_Out_OldCriticalChance);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_CriticalChance ***************************

// ********** Begin Class USigilAttributeSet Function OnRep_CriticalMultiplier *********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics
{
	struct SigilAttributeSet_eventOnRep_CriticalMultiplier_Parms
	{
		FGameplayAttributeData OldCriticalMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCriticalMultiplier_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCriticalMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::NewProp_OldCriticalMultiplier = { "OldCriticalMultiplier", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_CriticalMultiplier_Parms, OldCriticalMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCriticalMultiplier_MetaData), NewProp_OldCriticalMultiplier_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::NewProp_OldCriticalMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_CriticalMultiplier", Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::SigilAttributeSet_eventOnRep_CriticalMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::SigilAttributeSet_eventOnRep_CriticalMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_CriticalMultiplier)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCriticalMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CriticalMultiplier(Z_Param_Out_OldCriticalMultiplier);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_CriticalMultiplier ***********************

// ********** Begin Class USigilAttributeSet Function OnRep_DefensePower ***************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics
{
	struct SigilAttributeSet_eventOnRep_DefensePower_Parms
	{
		FGameplayAttributeData OldDefensePower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldDefensePower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldDefensePower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::NewProp_OldDefensePower = { "OldDefensePower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_DefensePower_Parms, OldDefensePower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldDefensePower_MetaData), NewProp_OldDefensePower_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::NewProp_OldDefensePower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_DefensePower", Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::SigilAttributeSet_eventOnRep_DefensePower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::SigilAttributeSet_eventOnRep_DefensePower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_DefensePower)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldDefensePower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_DefensePower(Z_Param_Out_OldDefensePower);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_DefensePower *****************************

// ********** Begin Class USigilAttributeSet Function OnRep_FusionMultiplier ***********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics
{
	struct SigilAttributeSet_eventOnRep_FusionMultiplier_Parms
	{
		FGameplayAttributeData OldFusionMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de Estado\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de Estado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldFusionMultiplier_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldFusionMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::NewProp_OldFusionMultiplier = { "OldFusionMultiplier", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_FusionMultiplier_Parms, OldFusionMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldFusionMultiplier_MetaData), NewProp_OldFusionMultiplier_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::NewProp_OldFusionMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_FusionMultiplier", Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::SigilAttributeSet_eventOnRep_FusionMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::SigilAttributeSet_eventOnRep_FusionMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_FusionMultiplier)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldFusionMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_FusionMultiplier(Z_Param_Out_OldFusionMultiplier);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_FusionMultiplier *************************

// ********** Begin Class USigilAttributeSet Function OnRep_HealthRegeneration *********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics
{
	struct SigilAttributeSet_eventOnRep_HealthRegeneration_Parms
	{
		FGameplayAttributeData OldHealthRegeneration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldHealthRegeneration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldHealthRegeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::NewProp_OldHealthRegeneration = { "OldHealthRegeneration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_HealthRegeneration_Parms, OldHealthRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldHealthRegeneration_MetaData), NewProp_OldHealthRegeneration_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::NewProp_OldHealthRegeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_HealthRegeneration", Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::SigilAttributeSet_eventOnRep_HealthRegeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::SigilAttributeSet_eventOnRep_HealthRegeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_HealthRegeneration)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldHealthRegeneration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_HealthRegeneration(Z_Param_Out_OldHealthRegeneration);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_HealthRegeneration ***********************

// ********** Begin Class USigilAttributeSet Function OnRep_ManaRegeneration ***********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics
{
	struct SigilAttributeSet_eventOnRep_ManaRegeneration_Parms
	{
		FGameplayAttributeData OldManaRegeneration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de Recursos\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de Recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldManaRegeneration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldManaRegeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::NewProp_OldManaRegeneration = { "OldManaRegeneration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_ManaRegeneration_Parms, OldManaRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldManaRegeneration_MetaData), NewProp_OldManaRegeneration_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::NewProp_OldManaRegeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_ManaRegeneration", Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::SigilAttributeSet_eventOnRep_ManaRegeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::SigilAttributeSet_eventOnRep_ManaRegeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_ManaRegeneration)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldManaRegeneration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ManaRegeneration(Z_Param_Out_OldManaRegeneration);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_ManaRegeneration *************************

// ********** Begin Class USigilAttributeSet Function OnRep_MovementSpeed **************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics
{
	struct SigilAttributeSet_eventOnRep_MovementSpeed_Parms
	{
		FGameplayAttributeData OldMovementSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos de Mobilidade\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos de Mobilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMovementSpeed_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMovementSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::NewProp_OldMovementSpeed = { "OldMovementSpeed", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_MovementSpeed_Parms, OldMovementSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMovementSpeed_MetaData), NewProp_OldMovementSpeed_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::NewProp_OldMovementSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_MovementSpeed", Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::SigilAttributeSet_eventOnRep_MovementSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::SigilAttributeSet_eventOnRep_MovementSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_MovementSpeed)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMovementSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MovementSpeed(Z_Param_Out_OldMovementSpeed);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_MovementSpeed ****************************

// ********** Begin Class USigilAttributeSet Function OnRep_ObjectiveBonus *************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics
{
	struct SigilAttributeSet_eventOnRep_ObjectiveBonus_Parms
	{
		FGameplayAttributeData OldObjectiveBonus;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldObjectiveBonus_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldObjectiveBonus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::NewProp_OldObjectiveBonus = { "OldObjectiveBonus", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_ObjectiveBonus_Parms, OldObjectiveBonus), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldObjectiveBonus_MetaData), NewProp_OldObjectiveBonus_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::NewProp_OldObjectiveBonus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_ObjectiveBonus", Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::SigilAttributeSet_eventOnRep_ObjectiveBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::SigilAttributeSet_eventOnRep_ObjectiveBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_ObjectiveBonus)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldObjectiveBonus);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ObjectiveBonus(Z_Param_Out_OldObjectiveBonus);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_ObjectiveBonus ***************************

// ********** Begin Class USigilAttributeSet Function OnRep_SigilEfficiency ************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics
{
	struct SigilAttributeSet_eventOnRep_SigilEfficiency_Parms
	{
		FGameplayAttributeData OldSigilEfficiency;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilEfficiency_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilEfficiency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::NewProp_OldSigilEfficiency = { "OldSigilEfficiency", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SigilEfficiency_Parms, OldSigilEfficiency), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilEfficiency_MetaData), NewProp_OldSigilEfficiency_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::NewProp_OldSigilEfficiency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SigilEfficiency", Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::SigilAttributeSet_eventOnRep_SigilEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::SigilAttributeSet_eventOnRep_SigilEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SigilEfficiency)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilEfficiency);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilEfficiency(Z_Param_Out_OldSigilEfficiency);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SigilEfficiency **************************

// ********** Begin Class USigilAttributeSet Function OnRep_SigilExperience ************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics
{
	struct SigilAttributeSet_eventOnRep_SigilExperience_Parms
	{
		FGameplayAttributeData OldSigilExperience;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilExperience_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilExperience;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::NewProp_OldSigilExperience = { "OldSigilExperience", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SigilExperience_Parms, OldSigilExperience), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilExperience_MetaData), NewProp_OldSigilExperience_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::NewProp_OldSigilExperience,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SigilExperience", Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::SigilAttributeSet_eventOnRep_SigilExperience_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::SigilAttributeSet_eventOnRep_SigilExperience_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SigilExperience)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilExperience);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilExperience(Z_Param_Out_OldSigilExperience);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SigilExperience **************************

// ********** Begin Class USigilAttributeSet Function OnRep_SigilSlots *****************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics
{
	struct SigilAttributeSet_eventOnRep_SigilSlots_Parms
	{
		FGameplayAttributeData OldSigilSlots;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilSlots_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilSlots;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::NewProp_OldSigilSlots = { "OldSigilSlots", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SigilSlots_Parms, OldSigilSlots), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilSlots_MetaData), NewProp_OldSigilSlots_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::NewProp_OldSigilSlots,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SigilSlots", Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::SigilAttributeSet_eventOnRep_SigilSlots_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::SigilAttributeSet_eventOnRep_SigilSlots_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SigilSlots)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilSlots);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilSlots(Z_Param_Out_OldSigilSlots);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SigilSlots *******************************

// ********** Begin Class USigilAttributeSet Function OnRep_SpectralFocus **************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics
{
	struct SigilAttributeSet_eventOnRep_SpectralFocus_Parms
	{
		FGameplayAttributeData OldSpectralFocus;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSpectralFocus_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSpectralFocus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::NewProp_OldSpectralFocus = { "OldSpectralFocus", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SpectralFocus_Parms, OldSpectralFocus), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSpectralFocus_MetaData), NewProp_OldSpectralFocus_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::NewProp_OldSpectralFocus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SpectralFocus", Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::SigilAttributeSet_eventOnRep_SpectralFocus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::SigilAttributeSet_eventOnRep_SpectralFocus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SpectralFocus)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSpectralFocus);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SpectralFocus(Z_Param_Out_OldSpectralFocus);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SpectralFocus ****************************

// ********** Begin Class USigilAttributeSet Function OnRep_SpectralPower **************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics
{
	struct SigilAttributeSet_eventOnRep_SpectralPower_Parms
	{
		FGameplayAttributeData OldSpectralPower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos Prim\xc3\xa1rios\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos Prim\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSpectralPower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSpectralPower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::NewProp_OldSpectralPower = { "OldSpectralPower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SpectralPower_Parms, OldSpectralPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSpectralPower_MetaData), NewProp_OldSpectralPower_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::NewProp_OldSpectralPower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SpectralPower", Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::SigilAttributeSet_eventOnRep_SpectralPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::SigilAttributeSet_eventOnRep_SpectralPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SpectralPower)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSpectralPower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SpectralPower(Z_Param_Out_OldSpectralPower);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SpectralPower ****************************

// ********** Begin Class USigilAttributeSet Function OnRep_SpectralResilience *********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics
{
	struct SigilAttributeSet_eventOnRep_SpectralResilience_Parms
	{
		FGameplayAttributeData OldSpectralResilience;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSpectralResilience_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSpectralResilience;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::NewProp_OldSpectralResilience = { "OldSpectralResilience", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SpectralResilience_Parms, OldSpectralResilience), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSpectralResilience_MetaData), NewProp_OldSpectralResilience_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::NewProp_OldSpectralResilience,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SpectralResilience", Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::SigilAttributeSet_eventOnRep_SpectralResilience_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::SigilAttributeSet_eventOnRep_SpectralResilience_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SpectralResilience)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSpectralResilience);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SpectralResilience(Z_Param_Out_OldSpectralResilience);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SpectralResilience ***********************

// ********** Begin Class USigilAttributeSet Function OnRep_SpectralVelocity ***********************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics
{
	struct SigilAttributeSet_eventOnRep_SpectralVelocity_Parms
	{
		FGameplayAttributeData OldSpectralVelocity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSpectralVelocity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSpectralVelocity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::NewProp_OldSpectralVelocity = { "OldSpectralVelocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_SpectralVelocity_Parms, OldSpectralVelocity), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSpectralVelocity_MetaData), NewProp_OldSpectralVelocity_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::NewProp_OldSpectralVelocity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_SpectralVelocity", Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::SigilAttributeSet_eventOnRep_SpectralVelocity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::SigilAttributeSet_eventOnRep_SpectralVelocity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_SpectralVelocity)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSpectralVelocity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SpectralVelocity(Z_Param_Out_OldSpectralVelocity);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_SpectralVelocity *************************

// ********** Begin Class USigilAttributeSet Function OnRep_TeamFightBonus *************************
struct Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics
{
	struct SigilAttributeSet_eventOnRep_TeamFightBonus_Parms
	{
		FGameplayAttributeData OldTeamFightBonus;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atributos MOBA\n" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos MOBA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldTeamFightBonus_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldTeamFightBonus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::NewProp_OldTeamFightBonus = { "OldTeamFightBonus", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilAttributeSet_eventOnRep_TeamFightBonus_Parms, OldTeamFightBonus), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldTeamFightBonus_MetaData), NewProp_OldTeamFightBonus_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::NewProp_OldTeamFightBonus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "OnRep_TeamFightBonus", Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::SigilAttributeSet_eventOnRep_TeamFightBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::SigilAttributeSet_eventOnRep_TeamFightBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execOnRep_TeamFightBonus)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldTeamFightBonus);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_TeamFightBonus(Z_Param_Out_OldTeamFightBonus);
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function OnRep_TeamFightBonus ***************************

// ********** Begin Class USigilAttributeSet Function RecalculateDerivedAttributes *****************
struct Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Recalcula todos os atributos derivados\n     * Chamado quando atributos prim\xc3\xa1rios mudam\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recalcula todos os atributos derivados\nChamado quando atributos prim\xc3\xa1rios mudam" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "RecalculateDerivedAttributes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execRecalculateDerivedAttributes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecalculateDerivedAttributes();
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function RecalculateDerivedAttributes *******************

// ********** Begin Class USigilAttributeSet Function RemoveFusionMultiplier ***********************
struct Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove multiplicador de fus\xc3\xa3o\n     * Usado para reset ou debugging\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove multiplicador de fus\xc3\xa3o\nUsado para reset ou debugging" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilAttributeSet, nullptr, "RemoveFusionMultiplier", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilAttributeSet::execRemoveFusionMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveFusionMultiplier();
	P_NATIVE_END;
}
// ********** End Class USigilAttributeSet Function RemoveFusionMultiplier *************************

// ********** Begin Class USigilAttributeSet *******************************************************
void USigilAttributeSet::StaticRegisterNativesUSigilAttributeSet()
{
	UClass* Class = USigilAttributeSet::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyFusionMultiplier", &USigilAttributeSet::execApplyFusionMultiplier },
		{ "CalculateTotalSigilPower", &USigilAttributeSet::execCalculateTotalSigilPower },
		{ "GetEffectiveAttributeValue", &USigilAttributeSet::execGetEffectiveAttributeValue },
		{ "IsAttributeAtMaximum", &USigilAttributeSet::execIsAttributeAtMaximum },
		{ "OnRep_AttackPower", &USigilAttributeSet::execOnRep_AttackPower },
		{ "OnRep_AttackSpeed", &USigilAttributeSet::execOnRep_AttackSpeed },
		{ "OnRep_CCResistance", &USigilAttributeSet::execOnRep_CCResistance },
		{ "OnRep_CooldownReduction", &USigilAttributeSet::execOnRep_CooldownReduction },
		{ "OnRep_CriticalChance", &USigilAttributeSet::execOnRep_CriticalChance },
		{ "OnRep_CriticalMultiplier", &USigilAttributeSet::execOnRep_CriticalMultiplier },
		{ "OnRep_DefensePower", &USigilAttributeSet::execOnRep_DefensePower },
		{ "OnRep_FusionMultiplier", &USigilAttributeSet::execOnRep_FusionMultiplier },
		{ "OnRep_HealthRegeneration", &USigilAttributeSet::execOnRep_HealthRegeneration },
		{ "OnRep_ManaRegeneration", &USigilAttributeSet::execOnRep_ManaRegeneration },
		{ "OnRep_MovementSpeed", &USigilAttributeSet::execOnRep_MovementSpeed },
		{ "OnRep_ObjectiveBonus", &USigilAttributeSet::execOnRep_ObjectiveBonus },
		{ "OnRep_SigilEfficiency", &USigilAttributeSet::execOnRep_SigilEfficiency },
		{ "OnRep_SigilExperience", &USigilAttributeSet::execOnRep_SigilExperience },
		{ "OnRep_SigilSlots", &USigilAttributeSet::execOnRep_SigilSlots },
		{ "OnRep_SpectralFocus", &USigilAttributeSet::execOnRep_SpectralFocus },
		{ "OnRep_SpectralPower", &USigilAttributeSet::execOnRep_SpectralPower },
		{ "OnRep_SpectralResilience", &USigilAttributeSet::execOnRep_SpectralResilience },
		{ "OnRep_SpectralVelocity", &USigilAttributeSet::execOnRep_SpectralVelocity },
		{ "OnRep_TeamFightBonus", &USigilAttributeSet::execOnRep_TeamFightBonus },
		{ "RecalculateDerivedAttributes", &USigilAttributeSet::execRecalculateDerivedAttributes },
		{ "RemoveFusionMultiplier", &USigilAttributeSet::execRemoveFusionMultiplier },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAttributeSet;
UClass* USigilAttributeSet::GetPrivateStaticClass()
{
	using TClass = USigilAttributeSet;
	if (!Z_Registration_Info_UClass_USigilAttributeSet.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAttributeSet"),
			Z_Registration_Info_UClass_USigilAttributeSet.InnerSingleton,
			StaticRegisterNativesUSigilAttributeSet,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAttributeSet.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAttributeSet_NoRegister()
{
	return USigilAttributeSet::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAttributeSet_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Conjunto de atributos espectrais para s\xc3\xadgilos\n * Implementa sistema de atributos prim\xc3\xa1rios, derivados e de estado\n * Suporta replica\xc3\xa7\xc3\xa3o multiplayer para MOBA 5x5 (10 jogadores)\n */" },
#endif
		{ "IncludePath", "Sigils/SigilAttributeSet.h" },
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conjunto de atributos espectrais para s\xc3\xadgilos\nImplementa sistema de atributos prim\xc3\xa1rios, derivados e de estado\nSuporta replica\xc3\xa7\xc3\xa3o multiplayer para MOBA 5x5 (10 jogadores)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralPower_MetaData[] = {
		{ "Category", "Primary Spectral Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Poder Espectral - Aumenta dano de ataques e habilidades\n     * Base para c\xc3\xa1lculo de AttackPower\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder Espectral - Aumenta dano de ataques e habilidades\nBase para c\xc3\xa1lculo de AttackPower" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralResilience_MetaData[] = {
		{ "Category", "Primary Spectral Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Resist\xc3\xaancia Espectral - Aumenta defesa e resist\xc3\xaancia a dano\n     * Base para c\xc3\xa1lculo de DefensePower\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resist\xc3\xaancia Espectral - Aumenta defesa e resist\xc3\xaancia a dano\nBase para c\xc3\xa1lculo de DefensePower" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralVelocity_MetaData[] = {
		{ "Category", "Primary Spectral Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Velocidade Espectral - Aumenta velocidade de movimento e ataque\n     * Base para c\xc3\xa1lculo de MovementSpeed e AttackSpeed\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade Espectral - Aumenta velocidade de movimento e ataque\nBase para c\xc3\xa1lculo de MovementSpeed e AttackSpeed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralFocus_MetaData[] = {
		{ "Category", "Primary Spectral Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Foco Espectral - Reduz cooldowns e aumenta regenera\xc3\xa7\xc3\xa3o de mana\n     * Base para c\xc3\xa1lculo de CooldownReduction e ManaRegeneration\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foco Espectral - Reduz cooldowns e aumenta regenera\xc3\xa7\xc3\xa3o de mana\nBase para c\xc3\xa1lculo de CooldownReduction e ManaRegeneration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackPower_MetaData[] = {
		{ "Category", "Derived Combat Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Poder de Ataque - Derivado de SpectralPower\n     * F\xc3\xb3rmula: BaseDamage + (SpectralPower * 1.5)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder de Ataque - Derivado de SpectralPower\nF\xc3\xb3rmula: BaseDamage + (SpectralPower * 1.5)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefensePower_MetaData[] = {
		{ "Category", "Derived Combat Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Poder de Defesa - Derivado de SpectralResilience\n     * F\xc3\xb3rmula: BaseDefense + (SpectralResilience * 1.2)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder de Defesa - Derivado de SpectralResilience\nF\xc3\xb3rmula: BaseDefense + (SpectralResilience * 1.2)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSpeed_MetaData[] = {
		{ "Category", "Derived Combat Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Velocidade de Ataque - Derivado de SpectralVelocity\n     * F\xc3\xb3rmula: BaseAttackSpeed + (SpectralVelocity * 0.01)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de Ataque - Derivado de SpectralVelocity\nF\xc3\xb3rmula: BaseAttackSpeed + (SpectralVelocity * 0.01)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalChance_MetaData[] = {
		{ "Category", "Derived Combat Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Chance de Cr\xc3\xadtico - Baseado em combina\xc3\xa7\xc3\xa3o de atributos\n     * F\xc3\xb3rmula: BaseCritChance + ((SpectralPower + SpectralFocus) * 0.001)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chance de Cr\xc3\xadtico - Baseado em combina\xc3\xa7\xc3\xa3o de atributos\nF\xc3\xb3rmula: BaseCritChance + ((SpectralPower + SpectralFocus) * 0.001)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalMultiplier_MetaData[] = {
		{ "Category", "Derived Combat Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Multiplicador de Cr\xc3\xadtico - Baseado em SpectralPower\n     * F\xc3\xb3rmula: BaseCritMultiplier + (SpectralPower * 0.002)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de Cr\xc3\xadtico - Baseado em SpectralPower\nF\xc3\xb3rmula: BaseCritMultiplier + (SpectralPower * 0.002)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "Mobility Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Velocidade de Movimento - Derivado de SpectralVelocity\n     * F\xc3\xb3rmula: BaseMovementSpeed + (SpectralVelocity * 2.0)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de Movimento - Derivado de SpectralVelocity\nF\xc3\xb3rmula: BaseMovementSpeed + (SpectralVelocity * 2.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReduction_MetaData[] = {
		{ "Category", "Mobility Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Redu\xc3\xa7\xc3\xa3o de Cooldown - Derivado de SpectralFocus\n     * F\xc3\xb3rmula: BaseCDR + (SpectralFocus * 0.01) [Max 40%]\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\xa7\xc3\xa3o de Cooldown - Derivado de SpectralFocus\nF\xc3\xb3rmula: BaseCDR + (SpectralFocus * 0.01) [Max 40%]" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegeneration_MetaData[] = {
		{ "Category", "Resource Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Regenera\xc3\xa7\xc3\xa3o de Mana - Derivado de SpectralFocus\n     * F\xc3\xb3rmula: BaseManaRegen + (SpectralFocus * 0.5)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\xa7\xc3\xa3o de Mana - Derivado de SpectralFocus\nF\xc3\xb3rmula: BaseManaRegen + (SpectralFocus * 0.5)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegeneration_MetaData[] = {
		{ "Category", "Resource Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Regenera\xc3\xa7\xc3\xa3o de Vida - Derivado de SpectralResilience\n     * F\xc3\xb3rmula: BaseHealthRegen + (SpectralResilience * 0.3)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\xa7\xc3\xa3o de Vida - Derivado de SpectralResilience\nF\xc3\xb3rmula: BaseHealthRegen + (SpectralResilience * 0.3)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionMultiplier_MetaData[] = {
		{ "Category", "Sigil State Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Multiplicador de Fus\xc3\xa3o - Aplicado ap\xc3\xb3s 6 minutos\n     * Valor padr\xc3\xa3o: 1.0, Fus\xc3\xa3o: 1.5-2.0 baseado na raridade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de Fus\xc3\xa3o - Aplicado ap\xc3\xb3s 6 minutos\nValor padr\xc3\xa3o: 1.0, Fus\xc3\xa3o: 1.5-2.0 baseado na raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilSlots_MetaData[] = {
		{ "Category", "Sigil State Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Slots de Sigilo Dispon\xc3\xadveis - M\xc3\xa1ximo 6\n     * Aumenta com level do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slots de Sigilo Dispon\xc3\xadveis - M\xc3\xa1ximo 6\nAumenta com level do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilEfficiency_MetaData[] = {
		{ "Category", "Sigil State Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Efici\xc3\xaancia de Sigilo - Reduz penalidades por m\xc3\xbaltiplos s\xc3\xadgilos\n     * F\xc3\xb3rmula: BaseEfficiency + (SpectralFocus * 0.005)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efici\xc3\xaancia de Sigilo - Reduz penalidades por m\xc3\xbaltiplos s\xc3\xadgilos\nF\xc3\xb3rmula: BaseEfficiency + (SpectralFocus * 0.005)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilExperience_MetaData[] = {
		{ "Category", "Sigil State Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Experi\xc3\xaancia de Sigilo - Para progress\xc3\xa3o de s\xc3\xadgilos individuais\n     * Ganha experi\xc3\xaancia atrav\xc3\xa9s de combate e objetivos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\xaancia de Sigilo - Para progress\xc3\xa3o de s\xc3\xadgilos individuais\nGanha experi\xc3\xaancia atrav\xc3\xa9s de combate e objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamFightBonus_MetaData[] = {
		{ "Category", "MOBA Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * B\xc3\xb4nus de Team Fight - Aumenta com proximidade de aliados\n     * M\xc3\xa1ximo 25% com 4 aliados pr\xc3\xb3ximos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de Team Fight - Aumenta com proximidade de aliados\nM\xc3\xa1ximo 25% com 4 aliados pr\xc3\xb3ximos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveBonus_MetaData[] = {
		{ "Category", "MOBA Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * B\xc3\xb4nus de Objetivo - Aumenta dano contra estruturas e monstros \xc3\xa9picos\n     * Baseado em combina\xc3\xa7\xc3\xa3o de atributos espectrais\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de Objetivo - Aumenta dano contra estruturas e monstros \xc3\xa9picos\nBaseado em combina\xc3\xa7\xc3\xa3o de atributos espectrais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CCResistance_MetaData[] = {
		{ "Category", "MOBA Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Resist\xc3\xaancia a Crowd Control - Reduz dura\xc3\xa7\xc3\xa3o de CCs\n     * F\xc3\xb3rmula: BaseCCResistance + (SpectralResilience * 0.002)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilAttributeSet.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resist\xc3\xaancia a Crowd Control - Reduz dura\xc3\xa7\xc3\xa3o de CCs\nF\xc3\xb3rmula: BaseCCResistance + (SpectralResilience * 0.002)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralPower;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralResilience;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralVelocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralFocus;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttackPower;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefensePower;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttackSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CriticalChance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CriticalMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CooldownReduction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ManaRegeneration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealthRegeneration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilSlots;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilExperience;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TeamFightBonus;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveBonus;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CCResistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilAttributeSet_ApplyFusionMultiplier, "ApplyFusionMultiplier" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_CalculateTotalSigilPower, "CalculateTotalSigilPower" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_GetEffectiveAttributeValue, "GetEffectiveAttributeValue" }, // 2893733910
		{ &Z_Construct_UFunction_USigilAttributeSet_IsAttributeAtMaximum, "IsAttributeAtMaximum" }, // 264085780
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackPower, "OnRep_AttackPower" }, // 1457655963
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_AttackSpeed, "OnRep_AttackSpeed" }, // 1716560251
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_CCResistance, "OnRep_CCResistance" }, // 202045130
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_CooldownReduction, "OnRep_CooldownReduction" }, // 266452475
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalChance, "OnRep_CriticalChance" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_CriticalMultiplier, "OnRep_CriticalMultiplier" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_DefensePower, "OnRep_DefensePower" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_FusionMultiplier, "OnRep_FusionMultiplier" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_HealthRegeneration, "OnRep_HealthRegeneration" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_ManaRegeneration, "OnRep_ManaRegeneration" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_MovementSpeed, "OnRep_MovementSpeed" }, // 551033493
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_ObjectiveBonus, "OnRep_ObjectiveBonus" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilEfficiency, "OnRep_SigilEfficiency" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilExperience, "OnRep_SigilExperience" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SigilSlots, "OnRep_SigilSlots" }, // 514618466
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralFocus, "OnRep_SpectralFocus" }, // 224192612
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralPower, "OnRep_SpectralPower" }, // **********
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralResilience, "OnRep_SpectralResilience" }, // 97040400
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_SpectralVelocity, "OnRep_SpectralVelocity" }, // 3690437876
		{ &Z_Construct_UFunction_USigilAttributeSet_OnRep_TeamFightBonus, "OnRep_TeamFightBonus" }, // 496569162
		{ &Z_Construct_UFunction_USigilAttributeSet_RecalculateDerivedAttributes, "RecalculateDerivedAttributes" }, // 536187130
		{ &Z_Construct_UFunction_USigilAttributeSet_RemoveFusionMultiplier, "RemoveFusionMultiplier" }, // 2765006560
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAttributeSet>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralPower = { "SpectralPower", "OnRep_SpectralPower", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SpectralPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralPower_MetaData), NewProp_SpectralPower_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralResilience = { "SpectralResilience", "OnRep_SpectralResilience", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SpectralResilience), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralResilience_MetaData), NewProp_SpectralResilience_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralVelocity = { "SpectralVelocity", "OnRep_SpectralVelocity", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SpectralVelocity), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralVelocity_MetaData), NewProp_SpectralVelocity_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralFocus = { "SpectralFocus", "OnRep_SpectralFocus", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SpectralFocus), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralFocus_MetaData), NewProp_SpectralFocus_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_AttackPower = { "AttackPower", "OnRep_AttackPower", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, AttackPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackPower_MetaData), NewProp_AttackPower_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_DefensePower = { "DefensePower", "OnRep_DefensePower", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, DefensePower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefensePower_MetaData), NewProp_DefensePower_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_AttackSpeed = { "AttackSpeed", "OnRep_AttackSpeed", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, AttackSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSpeed_MetaData), NewProp_AttackSpeed_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CriticalChance = { "CriticalChance", "OnRep_CriticalChance", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, CriticalChance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalChance_MetaData), NewProp_CriticalChance_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CriticalMultiplier = { "CriticalMultiplier", "OnRep_CriticalMultiplier", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, CriticalMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalMultiplier_MetaData), NewProp_CriticalMultiplier_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_MovementSpeed = { "MovementSpeed", "OnRep_MovementSpeed", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, MovementSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CooldownReduction = { "CooldownReduction", "OnRep_CooldownReduction", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, CooldownReduction), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReduction_MetaData), NewProp_CooldownReduction_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_ManaRegeneration = { "ManaRegeneration", "OnRep_ManaRegeneration", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, ManaRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegeneration_MetaData), NewProp_ManaRegeneration_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_HealthRegeneration = { "HealthRegeneration", "OnRep_HealthRegeneration", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, HealthRegeneration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegeneration_MetaData), NewProp_HealthRegeneration_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_FusionMultiplier = { "FusionMultiplier", "OnRep_FusionMultiplier", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, FusionMultiplier), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionMultiplier_MetaData), NewProp_FusionMultiplier_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SigilSlots = { "SigilSlots", "OnRep_SigilSlots", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SigilSlots), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilSlots_MetaData), NewProp_SigilSlots_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SigilEfficiency = { "SigilEfficiency", "OnRep_SigilEfficiency", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SigilEfficiency), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilEfficiency_MetaData), NewProp_SigilEfficiency_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SigilExperience = { "SigilExperience", "OnRep_SigilExperience", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, SigilExperience), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilExperience_MetaData), NewProp_SigilExperience_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_TeamFightBonus = { "TeamFightBonus", "OnRep_TeamFightBonus", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, TeamFightBonus), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamFightBonus_MetaData), NewProp_TeamFightBonus_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_ObjectiveBonus = { "ObjectiveBonus", "OnRep_ObjectiveBonus", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, ObjectiveBonus), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveBonus_MetaData), NewProp_ObjectiveBonus_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CCResistance = { "CCResistance", "OnRep_CCResistance", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAttributeSet, CCResistance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CCResistance_MetaData), NewProp_CCResistance_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAttributeSet_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralResilience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SpectralFocus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_AttackPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_DefensePower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_AttackSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CriticalChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CriticalMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CooldownReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_ManaRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_HealthRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_FusionMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SigilSlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SigilEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_SigilExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_TeamFightBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_ObjectiveBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAttributeSet_Statics::NewProp_CCResistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAttributeSet_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAttributeSet_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAttributeSet,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAttributeSet_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAttributeSet_Statics::ClassParams = {
	&USigilAttributeSet::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilAttributeSet_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAttributeSet_Statics::PropPointers),
	0,
	0x003000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAttributeSet_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAttributeSet_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAttributeSet()
{
	if (!Z_Registration_Info_UClass_USigilAttributeSet.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAttributeSet.OuterSingleton, Z_Construct_UClass_USigilAttributeSet_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAttributeSet.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void USigilAttributeSet::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_SpectralPower(TEXT("SpectralPower"));
	static FName Name_SpectralResilience(TEXT("SpectralResilience"));
	static FName Name_SpectralVelocity(TEXT("SpectralVelocity"));
	static FName Name_SpectralFocus(TEXT("SpectralFocus"));
	static FName Name_AttackPower(TEXT("AttackPower"));
	static FName Name_DefensePower(TEXT("DefensePower"));
	static FName Name_AttackSpeed(TEXT("AttackSpeed"));
	static FName Name_CriticalChance(TEXT("CriticalChance"));
	static FName Name_CriticalMultiplier(TEXT("CriticalMultiplier"));
	static FName Name_MovementSpeed(TEXT("MovementSpeed"));
	static FName Name_CooldownReduction(TEXT("CooldownReduction"));
	static FName Name_ManaRegeneration(TEXT("ManaRegeneration"));
	static FName Name_HealthRegeneration(TEXT("HealthRegeneration"));
	static FName Name_FusionMultiplier(TEXT("FusionMultiplier"));
	static FName Name_SigilSlots(TEXT("SigilSlots"));
	static FName Name_SigilEfficiency(TEXT("SigilEfficiency"));
	static FName Name_SigilExperience(TEXT("SigilExperience"));
	static FName Name_TeamFightBonus(TEXT("TeamFightBonus"));
	static FName Name_ObjectiveBonus(TEXT("ObjectiveBonus"));
	static FName Name_CCResistance(TEXT("CCResistance"));
	const bool bIsValid = true
		&& Name_SpectralPower == ClassReps[(int32)ENetFields_Private::SpectralPower].Property->GetFName()
		&& Name_SpectralResilience == ClassReps[(int32)ENetFields_Private::SpectralResilience].Property->GetFName()
		&& Name_SpectralVelocity == ClassReps[(int32)ENetFields_Private::SpectralVelocity].Property->GetFName()
		&& Name_SpectralFocus == ClassReps[(int32)ENetFields_Private::SpectralFocus].Property->GetFName()
		&& Name_AttackPower == ClassReps[(int32)ENetFields_Private::AttackPower].Property->GetFName()
		&& Name_DefensePower == ClassReps[(int32)ENetFields_Private::DefensePower].Property->GetFName()
		&& Name_AttackSpeed == ClassReps[(int32)ENetFields_Private::AttackSpeed].Property->GetFName()
		&& Name_CriticalChance == ClassReps[(int32)ENetFields_Private::CriticalChance].Property->GetFName()
		&& Name_CriticalMultiplier == ClassReps[(int32)ENetFields_Private::CriticalMultiplier].Property->GetFName()
		&& Name_MovementSpeed == ClassReps[(int32)ENetFields_Private::MovementSpeed].Property->GetFName()
		&& Name_CooldownReduction == ClassReps[(int32)ENetFields_Private::CooldownReduction].Property->GetFName()
		&& Name_ManaRegeneration == ClassReps[(int32)ENetFields_Private::ManaRegeneration].Property->GetFName()
		&& Name_HealthRegeneration == ClassReps[(int32)ENetFields_Private::HealthRegeneration].Property->GetFName()
		&& Name_FusionMultiplier == ClassReps[(int32)ENetFields_Private::FusionMultiplier].Property->GetFName()
		&& Name_SigilSlots == ClassReps[(int32)ENetFields_Private::SigilSlots].Property->GetFName()
		&& Name_SigilEfficiency == ClassReps[(int32)ENetFields_Private::SigilEfficiency].Property->GetFName()
		&& Name_SigilExperience == ClassReps[(int32)ENetFields_Private::SigilExperience].Property->GetFName()
		&& Name_TeamFightBonus == ClassReps[(int32)ENetFields_Private::TeamFightBonus].Property->GetFName()
		&& Name_ObjectiveBonus == ClassReps[(int32)ENetFields_Private::ObjectiveBonus].Property->GetFName()
		&& Name_CCResistance == ClassReps[(int32)ENetFields_Private::CCResistance].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in USigilAttributeSet"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAttributeSet);
USigilAttributeSet::~USigilAttributeSet() {}
// ********** End Class USigilAttributeSet *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilAttributeSet, USigilAttributeSet::StaticClass, TEXT("USigilAttributeSet"), &Z_Registration_Info_UClass_USigilAttributeSet, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAttributeSet), 2206493563U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h__Script_AURACRON_4123162454(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
