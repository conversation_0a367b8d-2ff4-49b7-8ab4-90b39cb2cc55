// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Sigils/SigilAttributeSet.h"

#ifdef AURACRON_SigilAttributeSet_generated_h
#error "SigilAttributeSet.generated.h already included, missing '#pragma once' in SigilAttributeSet.h"
#endif
#define AURACRON_SigilAttributeSet_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"
#include "Net/Core/PushModel/PushModelMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FGameplayAttribute;
struct FGameplayAttributeData;

// ********** Begin Class USigilAttributeSet *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_CCResistance); \
	DECLARE_FUNCTION(execOnRep_ObjectiveBonus); \
	DECLARE_FUNCTION(execOnRep_TeamFightBonus); \
	DECLARE_FUNCTION(execOnRep_SigilExperience); \
	DECLARE_FUNCTION(execOnRep_SigilEfficiency); \
	DECLARE_FUNCTION(execOnRep_SigilSlots); \
	DECLARE_FUNCTION(execOnRep_FusionMultiplier); \
	DECLARE_FUNCTION(execOnRep_HealthRegeneration); \
	DECLARE_FUNCTION(execOnRep_ManaRegeneration); \
	DECLARE_FUNCTION(execOnRep_CooldownReduction); \
	DECLARE_FUNCTION(execOnRep_MovementSpeed); \
	DECLARE_FUNCTION(execOnRep_CriticalMultiplier); \
	DECLARE_FUNCTION(execOnRep_CriticalChance); \
	DECLARE_FUNCTION(execOnRep_AttackSpeed); \
	DECLARE_FUNCTION(execOnRep_DefensePower); \
	DECLARE_FUNCTION(execOnRep_AttackPower); \
	DECLARE_FUNCTION(execOnRep_SpectralFocus); \
	DECLARE_FUNCTION(execOnRep_SpectralVelocity); \
	DECLARE_FUNCTION(execOnRep_SpectralResilience); \
	DECLARE_FUNCTION(execOnRep_SpectralPower); \
	DECLARE_FUNCTION(execGetEffectiveAttributeValue); \
	DECLARE_FUNCTION(execIsAttributeAtMaximum); \
	DECLARE_FUNCTION(execCalculateTotalSigilPower); \
	DECLARE_FUNCTION(execRemoveFusionMultiplier); \
	DECLARE_FUNCTION(execApplyFusionMultiplier); \
	DECLARE_FUNCTION(execRecalculateDerivedAttributes);


AURACRON_API UClass* Z_Construct_UClass_USigilAttributeSet_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUSigilAttributeSet(); \
	friend struct Z_Construct_UClass_USigilAttributeSet_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_USigilAttributeSet_NoRegister(); \
public: \
	DECLARE_CLASS2(USigilAttributeSet, UAttributeSet, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_USigilAttributeSet_NoRegister) \
	DECLARE_SERIALIZER(USigilAttributeSet) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		SpectralPower=NETFIELD_REP_START, \
		SpectralResilience, \
		SpectralVelocity, \
		SpectralFocus, \
		AttackPower, \
		DefensePower, \
		AttackSpeed, \
		CriticalChance, \
		CriticalMultiplier, \
		MovementSpeed, \
		CooldownReduction, \
		ManaRegeneration, \
		HealthRegeneration, \
		FusionMultiplier, \
		SigilSlots, \
		SigilEfficiency, \
		SigilExperience, \
		TeamFightBonus, \
		ObjectiveBonus, \
		CCResistance, \
		NETFIELD_REP_END=CCResistance	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API) \
private: \
	REPLICATED_BASE_CLASS(USigilAttributeSet) \
public:


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	USigilAttributeSet(USigilAttributeSet&&) = delete; \
	USigilAttributeSet(const USigilAttributeSet&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, USigilAttributeSet); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(USigilAttributeSet); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(USigilAttributeSet) \
	NO_API virtual ~USigilAttributeSet();


#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_29_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h_32_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class USigilAttributeSet;

// ********** End Class USigilAttributeSet *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Sigils_SigilAttributeSet_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
