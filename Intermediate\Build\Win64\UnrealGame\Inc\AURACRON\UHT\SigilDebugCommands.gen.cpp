// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Debug/SigilDebugCommands.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilDebugCommands() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilDebugCommands();
AURACRON_API UClass* Z_Construct_UClass_USigilDebugCommands_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilDebugSettings();
AURACRON_API UClass* Z_Construct_UClass_USigilDebugSettings_NoRegister();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FMOBAScenarioConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilTestResult();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
DEVELOPERSETTINGS_API UClass* Z_Construct_UClass_UDeveloperSettings();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class USigilDebugSettings ******************************************************
void USigilDebugSettings::StaticRegisterNativesUSigilDebugSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilDebugSettings;
UClass* USigilDebugSettings::GetPrivateStaticClass()
{
	using TClass = USigilDebugSettings;
	if (!Z_Registration_Info_UClass_USigilDebugSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilDebugSettings"),
			Z_Registration_Info_UClass_USigilDebugSettings.InnerSingleton,
			StaticRegisterNativesUSigilDebugSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilDebugSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilDebugSettings_NoRegister()
{
	return USigilDebugSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilDebugSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o para o sistema de s\xc3\xadgilos\n */" },
#endif
		{ "DisplayName", "Sigil Debug Settings" },
		{ "IncludePath", "Debug/SigilDebugCommands.h" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o para o sistema de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugCommands_MetaData[] = {
		{ "Category", "Debug Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es gerais de debug\n" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es gerais de debug" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugInfo_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogDebugEvents_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugWidgets_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMOBACommands_MetaData[] = {
		{ "Category", "MOBA Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas para MOBA\n" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas para MOBA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTeamSize_MetaData[] = {
		{ "Category", "MOBA Debug" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultMatchDuration_MetaData[] = {
		{ "Category", "MOBA Debug" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultSigilTypes_MetaData[] = {
		{ "Category", "Sigil Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de spawn de s\xc3\xadgilos\n" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de spawn de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDebugSigils_MetaData[] = {
		{ "Category", "Sigil Debug" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilSpawnRadius_MetaData[] = {
		{ "Category", "Sigil Debug" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowVFXDebugInfo_MetaData[] = {
		{ "Category", "VFX Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de VFX debug\n" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de VFX debug" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXDebugDuration_MetaData[] = {
		{ "Category", "VFX Debug" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowNetworkDebugInfo_MetaData[] = {
		{ "Category", "Network Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de rede debug\n" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de rede debug" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkDebugUpdateRate_MetaData[] = {
		{ "Category", "Network Debug" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableDebugCommands_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugCommands;
	static void NewProp_bShowDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugInfo;
	static void NewProp_bLogDebugEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogDebugEvents;
	static void NewProp_bShowDebugWidgets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugWidgets;
	static void NewProp_bEnableMOBACommands_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMOBACommands;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DefaultTeamSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultMatchDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultSigilTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DefaultSigilTypes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxDebugSigils;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SigilSpawnRadius;
	static void NewProp_bShowVFXDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowVFXDebugInfo;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VFXDebugDuration;
	static void NewProp_bShowNetworkDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowNetworkDebugInfo;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkDebugUpdateRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilDebugSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableDebugCommands_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bEnableDebugCommands = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableDebugCommands = { "bEnableDebugCommands", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableDebugCommands_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugCommands_MetaData), NewProp_bEnableDebugCommands_MetaData) };
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugInfo_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bShowDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugInfo = { "bShowDebugInfo", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugInfo_MetaData), NewProp_bShowDebugInfo_MetaData) };
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bLogDebugEvents_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bLogDebugEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bLogDebugEvents = { "bLogDebugEvents", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bLogDebugEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogDebugEvents_MetaData), NewProp_bLogDebugEvents_MetaData) };
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugWidgets_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bShowDebugWidgets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugWidgets = { "bShowDebugWidgets", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugWidgets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugWidgets_MetaData), NewProp_bShowDebugWidgets_MetaData) };
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableMOBACommands_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bEnableMOBACommands = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableMOBACommands = { "bEnableMOBACommands", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableMOBACommands_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMOBACommands_MetaData), NewProp_bEnableMOBACommands_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultTeamSize = { "DefaultTeamSize", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, DefaultTeamSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTeamSize_MetaData), NewProp_DefaultTeamSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultMatchDuration = { "DefaultMatchDuration", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, DefaultMatchDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultMatchDuration_MetaData), NewProp_DefaultMatchDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultSigilTypes_Inner = { "DefaultSigilTypes", nullptr, (EPropertyFlags)0x0000000000004000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultSigilTypes = { "DefaultSigilTypes", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, DefaultSigilTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultSigilTypes_MetaData), NewProp_DefaultSigilTypes_MetaData) }; // 133831994
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_MaxDebugSigils = { "MaxDebugSigils", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, MaxDebugSigils), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDebugSigils_MetaData), NewProp_MaxDebugSigils_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_SigilSpawnRadius = { "SigilSpawnRadius", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, SigilSpawnRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilSpawnRadius_MetaData), NewProp_SigilSpawnRadius_MetaData) };
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowVFXDebugInfo_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bShowVFXDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowVFXDebugInfo = { "bShowVFXDebugInfo", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowVFXDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowVFXDebugInfo_MetaData), NewProp_bShowVFXDebugInfo_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_VFXDebugDuration = { "VFXDebugDuration", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, VFXDebugDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXDebugDuration_MetaData), NewProp_VFXDebugDuration_MetaData) };
void Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowNetworkDebugInfo_SetBit(void* Obj)
{
	((USigilDebugSettings*)Obj)->bShowNetworkDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowNetworkDebugInfo = { "bShowNetworkDebugInfo", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilDebugSettings), &Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowNetworkDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowNetworkDebugInfo_MetaData), NewProp_bShowNetworkDebugInfo_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_NetworkDebugUpdateRate = { "NetworkDebugUpdateRate", nullptr, (EPropertyFlags)0x0010000000004005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilDebugSettings, NetworkDebugUpdateRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkDebugUpdateRate_MetaData), NewProp_NetworkDebugUpdateRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilDebugSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableDebugCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bLogDebugEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowDebugWidgets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bEnableMOBACommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultTeamSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultMatchDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultSigilTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_DefaultSigilTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_MaxDebugSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_SigilSpawnRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowVFXDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_VFXDebugDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_bShowNetworkDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilDebugSettings_Statics::NewProp_NetworkDebugUpdateRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDebugSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilDebugSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDeveloperSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDebugSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilDebugSettings_Statics::ClassParams = {
	&USigilDebugSettings::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilDebugSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilDebugSettings_Statics::PropPointers),
	0,
	0x001000A6u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDebugSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilDebugSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilDebugSettings()
{
	if (!Z_Registration_Info_UClass_USigilDebugSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilDebugSettings.OuterSingleton, Z_Construct_UClass_USigilDebugSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilDebugSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilDebugSettings);
USigilDebugSettings::~USigilDebugSettings() {}
// ********** End Class USigilDebugSettings ********************************************************

// ********** Begin Class USigilDebugCommands ******************************************************
void USigilDebugCommands::StaticRegisterNativesUSigilDebugCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilDebugCommands;
UClass* USigilDebugCommands::GetPrivateStaticClass()
{
	using TClass = USigilDebugCommands;
	if (!Z_Registration_Info_UClass_USigilDebugCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilDebugCommands"),
			Z_Registration_Info_UClass_USigilDebugCommands.InnerSingleton,
			StaticRegisterNativesUSigilDebugCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilDebugCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilDebugCommands_NoRegister()
{
	return USigilDebugCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilDebugCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema de comandos de console para depura\xc3\xa7\xc3\xa3o do sistema de s\xc3\xadgilos\n * Otimizado para ambiente MOBA 5x5\n */" },
#endif
		{ "IncludePath", "Debug/SigilDebugCommands.h" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de comandos de console para depura\xc3\xa7\xc3\xa3o do sistema de s\xc3\xadgilos\nOtimizado para ambiente MOBA 5x5" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilDebugCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilDebugCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDebugCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilDebugCommands_Statics::ClassParams = {
	&USigilDebugCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilDebugCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilDebugCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilDebugCommands()
{
	if (!Z_Registration_Info_UClass_USigilDebugCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilDebugCommands.OuterSingleton, Z_Construct_UClass_USigilDebugCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilDebugCommands.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilDebugCommands);
USigilDebugCommands::~USigilDebugCommands() {}
// ********** End Class USigilDebugCommands ********************************************************

// ********** Begin ScriptStruct FSigilTestResult **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilTestResult;
class UScriptStruct* FSigilTestResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilTestResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilTestResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilTestResult, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilTestResult"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilTestResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilTestResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar informa\xc3\xa7\xc3\xb5""es de teste\n */" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar informa\xc3\xa7\xc3\xb5""es de teste" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestName_MetaData[] = {
		{ "Category", "SigilTestResult" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPassed_MetaData[] = {
		{ "Category", "SigilTestResult" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTime_MetaData[] = {
		{ "Category", "SigilTestResult" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "SigilTestResult" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "SigilTestResult" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static void NewProp_bPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPassed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilTestResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilTestResult, TestName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestName_MetaData), NewProp_TestName_MetaData) };
void Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_bPassed_SetBit(void* Obj)
{
	((FSigilTestResult*)Obj)->bPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_bPassed = { "bPassed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilTestResult), &Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_bPassed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPassed_MetaData), NewProp_bPassed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_ExecutionTime = { "ExecutionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilTestResult, ExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTime_MetaData), NewProp_ExecutionTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilTestResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_PerformanceMetrics_ValueProp = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_PerformanceMetrics_Key_KeyProp = { "PerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilTestResult, PerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilTestResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_bPassed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_ExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_PerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_PerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewProp_PerformanceMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilTestResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilTestResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilTestResult",
	Z_Construct_UScriptStruct_FSigilTestResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilTestResult_Statics::PropPointers),
	sizeof(FSigilTestResult),
	alignof(FSigilTestResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilTestResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilTestResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilTestResult()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilTestResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilTestResult.InnerSingleton, Z_Construct_UScriptStruct_FSigilTestResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilTestResult.InnerSingleton;
}
// ********** End ScriptStruct FSigilTestResult ****************************************************

// ********** Begin ScriptStruct FMOBAScenarioConfig ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig;
class UScriptStruct* FMOBAScenarioConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMOBAScenarioConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("MOBAScenarioConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xa3o de cen\xc3\xa1rios MOBA\n */" },
#endif
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de cen\xc3\xa1rios MOBA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamSize_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchDuration_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilsPerPlayer_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionFrequency_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVFX_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableReplication_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedSigilTypes_MetaData[] = {
		{ "Category", "MOBAScenarioConfig" },
		{ "ModuleRelativePath", "Public/Debug/SigilDebugCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MatchDuration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SigilsPerPlayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionFrequency;
	static void NewProp_bEnableVFX_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVFX;
	static void NewProp_bEnableReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableReplication;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AllowedSigilTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedSigilTypes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMOBAScenarioConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_TeamSize = { "TeamSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMOBAScenarioConfig, TeamSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamSize_MetaData), NewProp_TeamSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_MatchDuration = { "MatchDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMOBAScenarioConfig, MatchDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchDuration_MetaData), NewProp_MatchDuration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_SigilsPerPlayer = { "SigilsPerPlayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMOBAScenarioConfig, SigilsPerPlayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilsPerPlayer_MetaData), NewProp_SigilsPerPlayer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_FusionFrequency = { "FusionFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMOBAScenarioConfig, FusionFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionFrequency_MetaData), NewProp_FusionFrequency_MetaData) };
void Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableVFX_SetBit(void* Obj)
{
	((FMOBAScenarioConfig*)Obj)->bEnableVFX = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableVFX = { "bEnableVFX", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMOBAScenarioConfig), &Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableVFX_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVFX_MetaData), NewProp_bEnableVFX_MetaData) };
void Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableReplication_SetBit(void* Obj)
{
	((FMOBAScenarioConfig*)Obj)->bEnableReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableReplication = { "bEnableReplication", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMOBAScenarioConfig), &Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableReplication_MetaData), NewProp_bEnableReplication_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_AllowedSigilTypes_Inner = { "AllowedSigilTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_AllowedSigilTypes = { "AllowedSigilTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMOBAScenarioConfig, AllowedSigilTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedSigilTypes_MetaData), NewProp_AllowedSigilTypes_MetaData) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_TeamSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_MatchDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_SigilsPerPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_FusionFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_bEnableReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_AllowedSigilTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewProp_AllowedSigilTypes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"MOBAScenarioConfig",
	Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::PropPointers),
	sizeof(FMOBAScenarioConfig),
	alignof(FMOBAScenarioConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMOBAScenarioConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig.InnerSingleton, Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig.InnerSingleton;
}
// ********** End ScriptStruct FMOBAScenarioConfig *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilTestResult::StaticStruct, Z_Construct_UScriptStruct_FSigilTestResult_Statics::NewStructOps, TEXT("SigilTestResult"), &Z_Registration_Info_UScriptStruct_FSigilTestResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilTestResult), 788187109U) },
		{ FMOBAScenarioConfig::StaticStruct, Z_Construct_UScriptStruct_FMOBAScenarioConfig_Statics::NewStructOps, TEXT("MOBAScenarioConfig"), &Z_Registration_Info_UScriptStruct_FMOBAScenarioConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMOBAScenarioConfig), 902469406U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilDebugSettings, USigilDebugSettings::StaticClass, TEXT("USigilDebugSettings"), &Z_Registration_Info_UClass_USigilDebugSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilDebugSettings), 3397084240U) },
		{ Z_Construct_UClass_USigilDebugCommands, USigilDebugCommands::StaticClass, TEXT("USigilDebugCommands"), &Z_Registration_Info_UClass_USigilDebugCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilDebugCommands), 1651689172U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h__Script_AURACRON_1378124237(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Debug_SigilDebugCommands_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
