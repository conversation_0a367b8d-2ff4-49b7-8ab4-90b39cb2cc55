// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Fusion/SigilFusionSystem.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilFusionSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_ASigilItem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilFusionSystem();
AURACRON_API UClass* Z_Construct_UClass_USigilFusionSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilVFXManager_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilFusionResult();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilFusionState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilFusionType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionInstance();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionNotification();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionRecipe();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
UMG_API UClass* Z_Construct_UClass_UUserWidget_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESigilFusionState *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilFusionState;
static UEnum* ESigilFusionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilFusionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilFusionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilFusionState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilFusionState"));
	}
	return Z_Registration_Info_UEnum_ESigilFusionState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilFusionState>()
{
	return ESigilFusionState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "ESigilFusionState::Cancelled" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para o sistema de fus\xc3\xa3o\n" },
#endif
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "ESigilFusionState::Completed" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "ESigilFusionState::Failed" },
		{ "InProgress.DisplayName", "In Progress" },
		{ "InProgress.Name", "ESigilFusionState::InProgress" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESigilFusionState::None" },
		{ "Preparing.DisplayName", "Preparing" },
		{ "Preparing.Name", "ESigilFusionState::Preparing" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para o sistema de fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilFusionState::None", (int64)ESigilFusionState::None },
		{ "ESigilFusionState::Preparing", (int64)ESigilFusionState::Preparing },
		{ "ESigilFusionState::InProgress", (int64)ESigilFusionState::InProgress },
		{ "ESigilFusionState::Completed", (int64)ESigilFusionState::Completed },
		{ "ESigilFusionState::Failed", (int64)ESigilFusionState::Failed },
		{ "ESigilFusionState::Cancelled", (int64)ESigilFusionState::Cancelled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilFusionState",
	"ESigilFusionState",
	Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilFusionState()
{
	if (!Z_Registration_Info_UEnum_ESigilFusionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilFusionState.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilFusionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilFusionState.InnerSingleton;
}
// ********** End Enum ESigilFusionState ***********************************************************

// ********** Begin Enum ESigilFusionType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilFusionType;
static UEnum* ESigilFusionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilFusionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilFusionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilFusionType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilFusionType"));
	}
	return Z_Registration_Info_UEnum_ESigilFusionType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilFusionType>()
{
	return ESigilFusionType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced Fusion" },
		{ "Advanced.Name", "ESigilFusionType::Advanced" },
		{ "Basic.DisplayName", "Basic Fusion" },
		{ "Basic.Name", "ESigilFusionType::Basic" },
		{ "BlueprintType", "true" },
		{ "Legendary.DisplayName", "Legendary Fusion" },
		{ "Legendary.Name", "ESigilFusionType::Legendary" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
		{ "Spectral.DisplayName", "Spectral Fusion" },
		{ "Spectral.Name", "ESigilFusionType::Spectral" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilFusionType::Basic", (int64)ESigilFusionType::Basic },
		{ "ESigilFusionType::Advanced", (int64)ESigilFusionType::Advanced },
		{ "ESigilFusionType::Legendary", (int64)ESigilFusionType::Legendary },
		{ "ESigilFusionType::Spectral", (int64)ESigilFusionType::Spectral },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilFusionType",
	"ESigilFusionType",
	Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilFusionType()
{
	if (!Z_Registration_Info_UEnum_ESigilFusionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilFusionType.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilFusionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilFusionType.InnerSingleton;
}
// ********** End Enum ESigilFusionType ************************************************************

// ********** Begin Enum ESigilFusionResult ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilFusionResult;
static UEnum* ESigilFusionResult_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilFusionResult.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilFusionResult.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESigilFusionResult, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESigilFusionResult"));
	}
	return Z_Registration_Info_UEnum_ESigilFusionResult.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESigilFusionResult>()
{
	return ESigilFusionResult_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "ESigilFusionResult::Cancelled" },
		{ "CriticalSuccess.DisplayName", "Critical Success" },
		{ "CriticalSuccess.Name", "ESigilFusionResult::CriticalSuccess" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "ESigilFusionResult::Failed" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
		{ "Success.DisplayName", "Success" },
		{ "Success.Name", "ESigilFusionResult::Success" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilFusionResult::Success", (int64)ESigilFusionResult::Success },
		{ "ESigilFusionResult::Failed", (int64)ESigilFusionResult::Failed },
		{ "ESigilFusionResult::CriticalSuccess", (int64)ESigilFusionResult::CriticalSuccess },
		{ "ESigilFusionResult::Cancelled", (int64)ESigilFusionResult::Cancelled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESigilFusionResult",
	"ESigilFusionResult",
	Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESigilFusionResult()
{
	if (!Z_Registration_Info_UEnum_ESigilFusionResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilFusionResult.InnerSingleton, Z_Construct_UEnum_AURACRON_ESigilFusionResult_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilFusionResult.InnerSingleton;
}
// ********** End Enum ESigilFusionResult **********************************************************

// ********** Begin ScriptStruct FSigilFusionRecipe ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFusionRecipe;
class UScriptStruct* FSigilFusionRecipe::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionRecipe.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFusionRecipe.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFusionRecipe, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFusionRecipe"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionRecipe.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas para dados de fus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas para dados de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecipeID_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredSigilTypes_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResultSigilType_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionType_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessRate_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTime_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredLevel_MetaData[] = {
		{ "Category", "Fusion Recipe" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecipeID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredSigilTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredSigilTypes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResultSigilType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FusionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FusionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuccessRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFusionRecipe>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RecipeID = { "RecipeID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, RecipeID), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecipeID_MetaData), NewProp_RecipeID_MetaData) }; // 133831994
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RequiredSigilTypes_Inner = { "RequiredSigilTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RequiredSigilTypes = { "RequiredSigilTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, RequiredSigilTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredSigilTypes_MetaData), NewProp_RequiredSigilTypes_MetaData) }; // 133831994
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_ResultSigilType = { "ResultSigilType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, ResultSigilType), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResultSigilType_MetaData), NewProp_ResultSigilType_MetaData) }; // 133831994
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_FusionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_FusionType = { "FusionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, FusionType), Z_Construct_UEnum_AURACRON_ESigilFusionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionType_MetaData), NewProp_FusionType_MetaData) }; // 2685722532
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_SuccessRate = { "SuccessRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, SuccessRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessRate_MetaData), NewProp_SuccessRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_FusionTime = { "FusionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, FusionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTime_MetaData), NewProp_FusionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RequiredLevel = { "RequiredLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionRecipe, RequiredLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredLevel_MetaData), NewProp_RequiredLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RecipeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RequiredSigilTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RequiredSigilTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_ResultSigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_FusionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_FusionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_SuccessRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_FusionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewProp_RequiredLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFusionRecipe",
	Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::PropPointers),
	sizeof(FSigilFusionRecipe),
	alignof(FSigilFusionRecipe),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionRecipe()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionRecipe.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFusionRecipe.InnerSingleton, Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionRecipe.InnerSingleton;
}
// ********** End ScriptStruct FSigilFusionRecipe **************************************************

// ********** Begin ScriptStruct FSigilFusionInstance **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFusionInstance;
class UScriptStruct* FSigilFusionInstance::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionInstance.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFusionInstance.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFusionInstance, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFusionInstance"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionInstance.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFusionInstance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionID_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Recipe_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSigils_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RemainingTime_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerActor_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAutomatic_MetaData[] = {
		{ "Category", "Fusion Instance" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Recipe;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSigils;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RemainingTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerActor;
	static void NewProp_bIsAutomatic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAutomatic;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFusionInstance>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_FusionID = { "FusionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, FusionID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionID_MetaData), NewProp_FusionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_Recipe = { "Recipe", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, Recipe), Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Recipe_MetaData), NewProp_Recipe_MetaData) }; // 598289628
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_InputSigils_Inner = { "InputSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_InputSigils = { "InputSigils", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, InputSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSigils_MetaData), NewProp_InputSigils_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, CurrentState), Z_Construct_UEnum_AURACRON_ESigilFusionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1854197481
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, StartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_RemainingTime = { "RemainingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, RemainingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RemainingTime_MetaData), NewProp_RemainingTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_OwnerActor = { "OwnerActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionInstance, OwnerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerActor_MetaData), NewProp_OwnerActor_MetaData) };
void Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_bIsAutomatic_SetBit(void* Obj)
{
	((FSigilFusionInstance*)Obj)->bIsAutomatic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_bIsAutomatic = { "bIsAutomatic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSigilFusionInstance), &Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_bIsAutomatic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAutomatic_MetaData), NewProp_bIsAutomatic_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_FusionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_Recipe,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_InputSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_InputSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_RemainingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_OwnerActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewProp_bIsAutomatic,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFusionInstance",
	Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::PropPointers),
	sizeof(FSigilFusionInstance),
	alignof(FSigilFusionInstance),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionInstance()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionInstance.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFusionInstance.InnerSingleton, Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionInstance.InnerSingleton;
}
// ********** End ScriptStruct FSigilFusionInstance ************************************************

// ********** Begin ScriptStruct FSigilFusionNotification ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilFusionNotification;
class UScriptStruct* FSigilFusionNotification::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionNotification.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilFusionNotification.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilFusionNotification, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SigilFusionNotification"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionNotification.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilFusionNotification_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionState_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayDuration_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationColor_MetaData[] = {
		{ "Category", "Notification" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Title;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Message;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FusionState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FusionState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DisplayDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NotificationColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilFusionNotification>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionNotification, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionNotification, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_FusionState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_FusionState = { "FusionState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionNotification, FusionState), Z_Construct_UEnum_AURACRON_ESigilFusionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionState_MetaData), NewProp_FusionState_MetaData) }; // 1854197481
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_DisplayDuration = { "DisplayDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionNotification, DisplayDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayDuration_MetaData), NewProp_DisplayDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_NotificationColor = { "NotificationColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilFusionNotification, NotificationColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationColor_MetaData), NewProp_NotificationColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_FusionState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_FusionState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_DisplayDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewProp_NotificationColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SigilFusionNotification",
	Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::PropPointers),
	sizeof(FSigilFusionNotification),
	alignof(FSigilFusionNotification),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilFusionNotification()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilFusionNotification.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilFusionNotification.InnerSingleton, Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilFusionNotification.InnerSingleton;
}
// ********** End ScriptStruct FSigilFusionNotification ********************************************

// ********** Begin Delegate FOnSigilFusionStarted *************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilFusionStarted_Parms
	{
		FSigilFusionInstance FusionInstance;
		AActor* Owner;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegados para eventos de fus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegados para eventos de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionInstance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionInstance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::NewProp_FusionInstance = { "FusionInstance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionStarted_Parms, FusionInstance), Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionInstance_MetaData), NewProp_FusionInstance_MetaData) }; // 3952472978
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionStarted_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::NewProp_FusionInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::NewProp_Owner,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilFusionStarted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionStarted, FSigilFusionInstance const& FusionInstance, AActor* Owner)
{
	struct _Script_AURACRON_eventOnSigilFusionStarted_Parms
	{
		FSigilFusionInstance FusionInstance;
		AActor* Owner;
	};
	_Script_AURACRON_eventOnSigilFusionStarted_Parms Parms;
	Parms.FusionInstance=FusionInstance;
	Parms.Owner=Owner;
	OnSigilFusionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilFusionStarted ***************************************************

// ********** Begin Delegate FOnSigilFusionCompleted ***********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilFusionCompleted_Parms
	{
		FSigilFusionInstance FusionInstance;
		ESigilFusionResult Result;
		ASigilItem* ResultSigil;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionInstance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionInstance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Result_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Result;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResultSigil;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_FusionInstance = { "FusionInstance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionCompleted_Parms, FusionInstance), Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionInstance_MetaData), NewProp_FusionInstance_MetaData) }; // 3952472978
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_Result_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionCompleted_Parms, Result), Z_Construct_UEnum_AURACRON_ESigilFusionResult, METADATA_PARAMS(0, nullptr) }; // 2553120672
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_ResultSigil = { "ResultSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionCompleted_Parms, ResultSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_FusionInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_Result_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::NewProp_ResultSigil,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilFusionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionCompleted, FSigilFusionInstance const& FusionInstance, ESigilFusionResult Result, ASigilItem* ResultSigil)
{
	struct _Script_AURACRON_eventOnSigilFusionCompleted_Parms
	{
		FSigilFusionInstance FusionInstance;
		ESigilFusionResult Result;
		ASigilItem* ResultSigil;
	};
	_Script_AURACRON_eventOnSigilFusionCompleted_Parms Parms;
	Parms.FusionInstance=FusionInstance;
	Parms.Result=Result;
	Parms.ResultSigil=ResultSigil;
	OnSigilFusionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilFusionCompleted *************************************************

// ********** Begin Delegate FOnSigilFusionProgress ************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilFusionProgress_Parms
	{
		FSigilFusionInstance FusionInstance;
		float ProgressPercent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionInstance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionInstance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProgressPercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::NewProp_FusionInstance = { "FusionInstance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionProgress_Parms, FusionInstance), Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionInstance_MetaData), NewProp_FusionInstance_MetaData) }; // 3952472978
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::NewProp_ProgressPercent = { "ProgressPercent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionProgress_Parms, ProgressPercent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::NewProp_FusionInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::NewProp_ProgressPercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilFusionProgress__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilFusionProgress_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionProgress, FSigilFusionInstance const& FusionInstance, float ProgressPercent)
{
	struct _Script_AURACRON_eventOnSigilFusionProgress_Parms
	{
		FSigilFusionInstance FusionInstance;
		float ProgressPercent;
	};
	_Script_AURACRON_eventOnSigilFusionProgress_Parms Parms;
	Parms.FusionInstance=FusionInstance;
	Parms.ProgressPercent=ProgressPercent;
	OnSigilFusionProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilFusionProgress **************************************************

// ********** Begin Delegate FOnSigilFusionCancelled ***********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilFusionCancelled_Parms
	{
		FSigilFusionInstance FusionInstance;
		AActor* Owner;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionInstance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionInstance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::NewProp_FusionInstance = { "FusionInstance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionCancelled_Parms, FusionInstance), Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionInstance_MetaData), NewProp_FusionInstance_MetaData) }; // 3952472978
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionCancelled_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::NewProp_FusionInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::NewProp_Owner,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilFusionCancelled__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionCancelled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionCancelled_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilFusionCancelled_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionCancelled, FSigilFusionInstance const& FusionInstance, AActor* Owner)
{
	struct _Script_AURACRON_eventOnSigilFusionCancelled_Parms
	{
		FSigilFusionInstance FusionInstance;
		AActor* Owner;
	};
	_Script_AURACRON_eventOnSigilFusionCancelled_Parms Parms;
	Parms.FusionInstance=FusionInstance;
	Parms.Owner=Owner;
	OnSigilFusionCancelled.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilFusionCancelled *************************************************

// ********** Begin Delegate FOnSigilFusionNotification ********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilFusionNotification_Parms
	{
		FSigilFusionNotification Notification;
		AActor* Owner;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Notification_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Notification;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::NewProp_Notification = { "Notification", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionNotification_Parms, Notification), Z_Construct_UScriptStruct_FSigilFusionNotification, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Notification_MetaData), NewProp_Notification_MetaData) }; // 1294010451
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilFusionNotification_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::NewProp_Notification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::NewProp_Owner,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilFusionNotification__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionNotification_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilFusionNotification_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilFusionNotification_DelegateWrapper(const FMulticastScriptDelegate& OnSigilFusionNotification, FSigilFusionNotification const& Notification, AActor* Owner)
{
	struct _Script_AURACRON_eventOnSigilFusionNotification_Parms
	{
		FSigilFusionNotification Notification;
		AActor* Owner;
	};
	_Script_AURACRON_eventOnSigilFusionNotification_Parms Parms;
	Parms.Notification=Notification;
	Parms.Owner=Owner;
	OnSigilFusionNotification.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilFusionNotification **********************************************

// ********** Begin Delegate FOnSigilCreated *******************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilCreated_Parms
	{
		ASigilItem* CreatedSigil;
		AActor* Owner;
		FSigilFusionRecipe Recipe;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Recipe_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedSigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Recipe;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::NewProp_CreatedSigil = { "CreatedSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilCreated_Parms, CreatedSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilCreated_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::NewProp_Recipe = { "Recipe", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilCreated_Parms, Recipe), Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Recipe_MetaData), NewProp_Recipe_MetaData) }; // 598289628
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::NewProp_CreatedSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::NewProp_Recipe,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilCreated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilCreated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilCreated, ASigilItem* CreatedSigil, AActor* Owner, FSigilFusionRecipe const& Recipe)
{
	struct _Script_AURACRON_eventOnSigilCreated_Parms
	{
		ASigilItem* CreatedSigil;
		AActor* Owner;
		FSigilFusionRecipe Recipe;
	};
	_Script_AURACRON_eventOnSigilCreated_Parms Parms;
	Parms.CreatedSigil=CreatedSigil;
	Parms.Owner=Owner;
	Parms.Recipe=Recipe;
	OnSigilCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilCreated *********************************************************

// ********** Begin Delegate FOnSigilConsumed ******************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnSigilConsumed_Parms
	{
		ASigilItem* ConsumedSigil;
		AActor* Owner;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ConsumedSigil;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::NewProp_ConsumedSigil = { "ConsumedSigil", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilConsumed_Parms, ConsumedSigil), Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnSigilConsumed_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::NewProp_ConsumedSigil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::NewProp_Owner,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnSigilConsumed__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilConsumed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::_Script_AURACRON_eventOnSigilConsumed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSigilConsumed_DelegateWrapper(const FMulticastScriptDelegate& OnSigilConsumed, ASigilItem* ConsumedSigil, AActor* Owner)
{
	struct _Script_AURACRON_eventOnSigilConsumed_Parms
	{
		ASigilItem* ConsumedSigil;
		AActor* Owner;
	};
	_Script_AURACRON_eventOnSigilConsumed_Parms Parms;
	Parms.ConsumedSigil=ConsumedSigil;
	Parms.Owner=Owner;
	OnSigilConsumed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSigilConsumed ********************************************************

// ********** Begin Class USigilFusionSystem Function AddFusionRecipe ******************************
struct Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics
{
	struct SigilFusionSystem_eventAddFusionRecipe_Parms
	{
		FSigilFusionRecipe Recipe;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Recipes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de receitas\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de receitas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Recipe_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Recipe;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::NewProp_Recipe = { "Recipe", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventAddFusionRecipe_Parms, Recipe), Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Recipe_MetaData), NewProp_Recipe_MetaData) }; // 598289628
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::NewProp_Recipe,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "AddFusionRecipe", Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::SigilFusionSystem_eventAddFusionRecipe_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::SigilFusionSystem_eventAddFusionRecipe_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execAddFusionRecipe)
{
	P_GET_STRUCT_REF(FSigilFusionRecipe,Z_Param_Out_Recipe);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddFusionRecipe(Z_Param_Out_Recipe);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function AddFusionRecipe ********************************

// ********** Begin Class USigilFusionSystem Function CancelAllFusions *****************************
struct Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics
{
	struct SigilFusionSystem_eventCancelAllFusions_Parms
	{
		AActor* Owner;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "CPP_Default_Owner", "None" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventCancelAllFusions_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::NewProp_Owner,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "CancelAllFusions", Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::SigilFusionSystem_eventCancelAllFusions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::SigilFusionSystem_eventCancelAllFusions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execCancelAllFusions)
{
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelAllFusions(Z_Param_Owner);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function CancelAllFusions *******************************

// ********** Begin Class USigilFusionSystem Function CancelFusion *********************************
struct Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics
{
	struct SigilFusionSystem_eventCancelFusion_Parms
	{
		FGuid FusionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::NewProp_FusionID = { "FusionID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventCancelFusion_Parms, FusionID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionID_MetaData), NewProp_FusionID_MetaData) };
void Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilFusionSystem_eventCancelFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventCancelFusion_Parms), &Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::NewProp_FusionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "CancelFusion", Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::SigilFusionSystem_eventCancelFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::SigilFusionSystem_eventCancelFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_CancelFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_CancelFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execCancelFusion)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_FusionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelFusion(Z_Param_Out_FusionID);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function CancelFusion ***********************************

// ********** Begin Class USigilFusionSystem Function CanStartFusion *******************************
struct Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics
{
	struct SigilFusionSystem_eventCanStartFusion_Parms
	{
		TArray<ASigilItem*> InputSigils;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de consulta\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de consulta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSigils_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSigils;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_InputSigils_Inner = { "InputSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_InputSigils = { "InputSigils", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventCanStartFusion_Parms, InputSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSigils_MetaData), NewProp_InputSigils_MetaData) };
void Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilFusionSystem_eventCanStartFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventCanStartFusion_Parms), &Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_InputSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_InputSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "CanStartFusion", Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::SigilFusionSystem_eventCanStartFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::SigilFusionSystem_eventCanStartFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_CanStartFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_CanStartFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execCanStartFusion)
{
	P_GET_TARRAY_REF(ASigilItem*,Z_Param_Out_InputSigils);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanStartFusion(Z_Param_Out_InputSigils);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function CanStartFusion *********************************

// ********** Begin Class USigilFusionSystem Function DebugCancelAllFusions ************************
struct Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "DebugCancelAllFusions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execDebugCancelAllFusions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugCancelAllFusions();
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function DebugCancelAllFusions **************************

// ********** Begin Class USigilFusionSystem Function DebugPrintActiveFusions **********************
struct Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "DebugPrintActiveFusions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execDebugPrintActiveFusions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugPrintActiveFusions();
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function DebugPrintActiveFusions ************************

// ********** Begin Class USigilFusionSystem Function DebugStartTestFusion *************************
struct Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "DebugStartTestFusion", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execDebugStartTestFusion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugStartTestFusion();
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function DebugStartTestFusion ***************************

// ********** Begin Class USigilFusionSystem Function FindBestRecipe *******************************
struct Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics
{
	struct SigilFusionSystem_eventFindBestRecipe_Parms
	{
		TArray<ASigilItem*> InputSigils;
		FSigilFusionRecipe ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSigils_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSigils;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::NewProp_InputSigils_Inner = { "InputSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::NewProp_InputSigils = { "InputSigils", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventFindBestRecipe_Parms, InputSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSigils_MetaData), NewProp_InputSigils_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventFindBestRecipe_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(0, nullptr) }; // 598289628
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::NewProp_InputSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::NewProp_InputSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "FindBestRecipe", Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::SigilFusionSystem_eventFindBestRecipe_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::SigilFusionSystem_eventFindBestRecipe_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execFindBestRecipe)
{
	P_GET_TARRAY_REF(ASigilItem*,Z_Param_Out_InputSigils);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilFusionRecipe*)Z_Param__Result=P_THIS->FindBestRecipe(Z_Param_Out_InputSigils);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function FindBestRecipe *********************************

// ********** Begin Class USigilFusionSystem Function GetActiveFusionCount *************************
struct Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics
{
	struct SigilFusionSystem_eventGetActiveFusionCount_Parms
	{
		AActor* Owner;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "CPP_Default_Owner", "None" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetActiveFusionCount_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetActiveFusionCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "GetActiveFusionCount", Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::SigilFusionSystem_eventGetActiveFusionCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::SigilFusionSystem_eventGetActiveFusionCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execGetActiveFusionCount)
{
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveFusionCount(Z_Param_Owner);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function GetActiveFusionCount ***************************

// ********** Begin Class USigilFusionSystem Function GetActiveFusions *****************************
struct Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics
{
	struct SigilFusionSystem_eventGetActiveFusions_Parms
	{
		AActor* Owner;
		TArray<FSigilFusionInstance> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "CPP_Default_Owner", "None" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetActiveFusions_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(0, nullptr) }; // 3952472978
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetActiveFusions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3952472978
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "GetActiveFusions", Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::SigilFusionSystem_eventGetActiveFusions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::SigilFusionSystem_eventGetActiveFusions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execGetActiveFusions)
{
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilFusionInstance>*)Z_Param__Result=P_THIS->GetActiveFusions(Z_Param_Owner);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function GetActiveFusions *******************************

// ********** Begin Class USigilFusionSystem Function GetAvailableRecipes **************************
struct Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics
{
	struct SigilFusionSystem_eventGetAvailableRecipes_Parms
	{
		TArray<ASigilItem*> InputSigils;
		TArray<FSigilFusionRecipe> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Recipes" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSigils_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSigils;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_InputSigils_Inner = { "InputSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_InputSigils = { "InputSigils", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetAvailableRecipes_Parms, InputSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSigils_MetaData), NewProp_InputSigils_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(0, nullptr) }; // 598289628
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetAvailableRecipes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 598289628
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_InputSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_InputSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "GetAvailableRecipes", Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::SigilFusionSystem_eventGetAvailableRecipes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::SigilFusionSystem_eventGetAvailableRecipes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execGetAvailableRecipes)
{
	P_GET_TARRAY_REF(ASigilItem*,Z_Param_Out_InputSigils);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSigilFusionRecipe>*)Z_Param__Result=P_THIS->GetAvailableRecipes(Z_Param_Out_InputSigils);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function GetAvailableRecipes ****************************

// ********** Begin Class USigilFusionSystem Function GetFusionByID ********************************
struct Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics
{
	struct SigilFusionSystem_eventGetFusionByID_Parms
	{
		FGuid FusionID;
		FSigilFusionInstance ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::NewProp_FusionID = { "FusionID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetFusionByID_Parms, FusionID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionID_MetaData), NewProp_FusionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetFusionByID_Parms, ReturnValue), Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(0, nullptr) }; // 3952472978
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::NewProp_FusionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "GetFusionByID", Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::SigilFusionSystem_eventGetFusionByID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::SigilFusionSystem_eventGetFusionByID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_GetFusionByID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_GetFusionByID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execGetFusionByID)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_FusionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSigilFusionInstance*)Z_Param__Result=P_THIS->GetFusionByID(Z_Param_Out_FusionID);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function GetFusionByID **********************************

// ********** Begin Class USigilFusionSystem Function GetFusionProgress ****************************
struct Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics
{
	struct SigilFusionSystem_eventGetFusionProgress_Parms
	{
		FGuid FusionID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::NewProp_FusionID = { "FusionID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetFusionProgress_Parms, FusionID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionID_MetaData), NewProp_FusionID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventGetFusionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::NewProp_FusionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "GetFusionProgress", Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::SigilFusionSystem_eventGetFusionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::SigilFusionSystem_eventGetFusionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execGetFusionProgress)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_FusionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFusionProgress(Z_Param_Out_FusionID);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function GetFusionProgress ******************************

// ********** Begin Class USigilFusionSystem Function HideAllNotifications *************************
struct Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Notifications" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "HideAllNotifications", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execHideAllNotifications)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideAllNotifications();
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function HideAllNotifications ***************************

// ********** Begin Class USigilFusionSystem Function LoadDefaultRecipes ***************************
struct Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Recipes" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "LoadDefaultRecipes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execLoadDefaultRecipes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadDefaultRecipes();
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function LoadDefaultRecipes *****************************

// ********** Begin Class USigilFusionSystem Function RemoveFusionRecipe ***************************
struct Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics
{
	struct SigilFusionSystem_eventRemoveFusionRecipe_Parms
	{
		FGameplayTag RecipeID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Recipes" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecipeID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecipeID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::NewProp_RecipeID = { "RecipeID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventRemoveFusionRecipe_Parms, RecipeID), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecipeID_MetaData), NewProp_RecipeID_MetaData) }; // 133831994
void Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilFusionSystem_eventRemoveFusionRecipe_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventRemoveFusionRecipe_Parms), &Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::NewProp_RecipeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "RemoveFusionRecipe", Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::SigilFusionSystem_eventRemoveFusionRecipe_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::SigilFusionSystem_eventRemoveFusionRecipe_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execRemoveFusionRecipe)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_RecipeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveFusionRecipe(Z_Param_Out_RecipeID);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function RemoveFusionRecipe *****************************

// ********** Begin Class USigilFusionSystem Function SetAutomaticFusionEnabled ********************
struct Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics
{
	struct SigilFusionSystem_eventSetAutomaticFusionEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de configura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de configura\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((SigilFusionSystem_eventSetAutomaticFusionEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventSetAutomaticFusionEnabled_Parms), &Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "SetAutomaticFusionEnabled", Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::SigilFusionSystem_eventSetAutomaticFusionEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::SigilFusionSystem_eventSetAutomaticFusionEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execSetAutomaticFusionEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAutomaticFusionEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function SetAutomaticFusionEnabled **********************

// ********** Begin Class USigilFusionSystem Function SetDebugMode *********************************
struct Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics
{
	struct SigilFusionSystem_eventSetDebugMode_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((SigilFusionSystem_eventSetDebugMode_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventSetDebugMode_Parms), &Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "SetDebugMode", Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::SigilFusionSystem_eventSetDebugMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::SigilFusionSystem_eventSetDebugMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_SetDebugMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_SetDebugMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execSetDebugMode)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDebugMode(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function SetDebugMode ***********************************

// ********** Begin Class USigilFusionSystem Function SetDefaultFusionTime *************************
struct Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics
{
	struct SigilFusionSystem_eventSetDefaultFusionTime_Parms
	{
		float NewTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::NewProp_NewTime = { "NewTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventSetDefaultFusionTime_Parms, NewTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::NewProp_NewTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "SetDefaultFusionTime", Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::SigilFusionSystem_eventSetDefaultFusionTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::SigilFusionSystem_eventSetDefaultFusionTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execSetDefaultFusionTime)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDefaultFusionTime(Z_Param_NewTime);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function SetDefaultFusionTime ***************************

// ********** Begin Class USigilFusionSystem Function SetMaxSimultaneousFusions ********************
struct Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics
{
	struct SigilFusionSystem_eventSetMaxSimultaneousFusions_Parms
	{
		int32 NewMax;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewMax;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::NewProp_NewMax = { "NewMax", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventSetMaxSimultaneousFusions_Parms, NewMax), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::NewProp_NewMax,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "SetMaxSimultaneousFusions", Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::SigilFusionSystem_eventSetMaxSimultaneousFusions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::SigilFusionSystem_eventSetMaxSimultaneousFusions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execSetMaxSimultaneousFusions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewMax);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaxSimultaneousFusions(Z_Param_NewMax);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function SetMaxSimultaneousFusions **********************

// ********** Begin Class USigilFusionSystem Function ShowFusionNotification ***********************
struct Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics
{
	struct SigilFusionSystem_eventShowFusionNotification_Parms
	{
		FSigilFusionNotification Notification;
		AActor* Owner;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion Notifications" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de notifica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de notifica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Notification_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Notification;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::NewProp_Notification = { "Notification", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventShowFusionNotification_Parms, Notification), Z_Construct_UScriptStruct_FSigilFusionNotification, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Notification_MetaData), NewProp_Notification_MetaData) }; // 1294010451
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventShowFusionNotification_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::NewProp_Notification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::NewProp_Owner,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "ShowFusionNotification", Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::SigilFusionSystem_eventShowFusionNotification_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::SigilFusionSystem_eventShowFusionNotification_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execShowFusionNotification)
{
	P_GET_STRUCT_REF(FSigilFusionNotification,Z_Param_Out_Notification);
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowFusionNotification(Z_Param_Out_Notification,Z_Param_Owner);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function ShowFusionNotification *************************

// ********** Begin Class USigilFusionSystem Function StartAutomaticFusion *************************
struct Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics
{
	struct SigilFusionSystem_eventStartAutomaticFusion_Parms
	{
		TArray<ASigilItem*> InputSigils;
		AActor* Owner;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es principais de fus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es principais de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSigils_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSigils;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_InputSigils_Inner = { "InputSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_InputSigils = { "InputSigils", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventStartAutomaticFusion_Parms, InputSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSigils_MetaData), NewProp_InputSigils_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventStartAutomaticFusion_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilFusionSystem_eventStartAutomaticFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventStartAutomaticFusion_Parms), &Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_InputSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_InputSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "StartAutomaticFusion", Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::SigilFusionSystem_eventStartAutomaticFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::SigilFusionSystem_eventStartAutomaticFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execStartAutomaticFusion)
{
	P_GET_TARRAY_REF(ASigilItem*,Z_Param_Out_InputSigils);
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartAutomaticFusion(Z_Param_Out_InputSigils,Z_Param_Owner);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function StartAutomaticFusion ***************************

// ********** Begin Class USigilFusionSystem Function StartManualFusion ****************************
struct Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics
{
	struct SigilFusionSystem_eventStartManualFusion_Parms
	{
		FSigilFusionRecipe Recipe;
		TArray<ASigilItem*> InputSigils;
		AActor* Owner;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Fusion" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Recipe_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSigils_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Recipe;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSigils;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Owner;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_Recipe = { "Recipe", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventStartManualFusion_Parms, Recipe), Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Recipe_MetaData), NewProp_Recipe_MetaData) }; // 598289628
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_InputSigils_Inner = { "InputSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ASigilItem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_InputSigils = { "InputSigils", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventStartManualFusion_Parms, InputSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSigils_MetaData), NewProp_InputSigils_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_Owner = { "Owner", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilFusionSystem_eventStartManualFusion_Parms, Owner), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilFusionSystem_eventStartManualFusion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilFusionSystem_eventStartManualFusion_Parms), &Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_Recipe,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_InputSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_InputSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_Owner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilFusionSystem, nullptr, "StartManualFusion", Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::SigilFusionSystem_eventStartManualFusion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::SigilFusionSystem_eventStartManualFusion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilFusionSystem_StartManualFusion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilFusionSystem_StartManualFusion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilFusionSystem::execStartManualFusion)
{
	P_GET_STRUCT_REF(FSigilFusionRecipe,Z_Param_Out_Recipe);
	P_GET_TARRAY_REF(ASigilItem*,Z_Param_Out_InputSigils);
	P_GET_OBJECT(AActor,Z_Param_Owner);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartManualFusion(Z_Param_Out_Recipe,Z_Param_Out_InputSigils,Z_Param_Owner);
	P_NATIVE_END;
}
// ********** End Class USigilFusionSystem Function StartManualFusion ******************************

// ********** Begin Class USigilFusionSystem *******************************************************
void USigilFusionSystem::StaticRegisterNativesUSigilFusionSystem()
{
	UClass* Class = USigilFusionSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddFusionRecipe", &USigilFusionSystem::execAddFusionRecipe },
		{ "CancelAllFusions", &USigilFusionSystem::execCancelAllFusions },
		{ "CancelFusion", &USigilFusionSystem::execCancelFusion },
		{ "CanStartFusion", &USigilFusionSystem::execCanStartFusion },
		{ "DebugCancelAllFusions", &USigilFusionSystem::execDebugCancelAllFusions },
		{ "DebugPrintActiveFusions", &USigilFusionSystem::execDebugPrintActiveFusions },
		{ "DebugStartTestFusion", &USigilFusionSystem::execDebugStartTestFusion },
		{ "FindBestRecipe", &USigilFusionSystem::execFindBestRecipe },
		{ "GetActiveFusionCount", &USigilFusionSystem::execGetActiveFusionCount },
		{ "GetActiveFusions", &USigilFusionSystem::execGetActiveFusions },
		{ "GetAvailableRecipes", &USigilFusionSystem::execGetAvailableRecipes },
		{ "GetFusionByID", &USigilFusionSystem::execGetFusionByID },
		{ "GetFusionProgress", &USigilFusionSystem::execGetFusionProgress },
		{ "HideAllNotifications", &USigilFusionSystem::execHideAllNotifications },
		{ "LoadDefaultRecipes", &USigilFusionSystem::execLoadDefaultRecipes },
		{ "RemoveFusionRecipe", &USigilFusionSystem::execRemoveFusionRecipe },
		{ "SetAutomaticFusionEnabled", &USigilFusionSystem::execSetAutomaticFusionEnabled },
		{ "SetDebugMode", &USigilFusionSystem::execSetDebugMode },
		{ "SetDefaultFusionTime", &USigilFusionSystem::execSetDefaultFusionTime },
		{ "SetMaxSimultaneousFusions", &USigilFusionSystem::execSetMaxSimultaneousFusions },
		{ "ShowFusionNotification", &USigilFusionSystem::execShowFusionNotification },
		{ "StartAutomaticFusion", &USigilFusionSystem::execStartAutomaticFusion },
		{ "StartManualFusion", &USigilFusionSystem::execStartManualFusion },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilFusionSystem;
UClass* USigilFusionSystem::GetPrivateStaticClass()
{
	using TClass = USigilFusionSystem;
	if (!Z_Registration_Info_UClass_USigilFusionSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilFusionSystem"),
			Z_Registration_Info_UClass_USigilFusionSystem.InnerSingleton,
			StaticRegisterNativesUSigilFusionSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilFusionSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilFusionSystem_NoRegister()
{
	return USigilFusionSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilFusionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema de fus\xc3\xa3o autom\xc3\xa1tica de s\xc3\xadgilos para MOBA 5x5\n * Gerencia fus\xc3\xb5""es autom\xc3\xa1ticas com temporizador de 6 minutos, notifica\xc3\xa7\xc3\xb5""es e efeitos visuais\n */" },
#endif
		{ "IncludePath", "Fusion/SigilFusionSystem.h" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de fus\xc3\xa3o autom\xc3\xa1tica de s\xc3\xadgilos para MOBA 5x5\nGerencia fus\xc3\xb5""es autom\xc3\xa1ticas com temporizador de 6 minutos, notifica\xc3\xa7\xc3\xb5""es e efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticFusion_MetaData[] = {
		{ "Category", "Fusion Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es do sistema\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do sistema" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFusionTime_MetaData[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSimultaneousFusions_MetaData[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowNotifications_MetaData[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPlaySounds_MetaData[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowVFX_MetaData[] = {
		{ "Category", "Fusion Settings" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionRecipes_MetaData[] = {
		{ "Category", "Fusion Recipes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Receitas de fus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Receitas de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveFusions_MetaData[] = {
		{ "Category", "Fusion State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fus\xc3\xb5""es ativas\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fus\xc3\xb5""es ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionStarted_MetaData[] = {
		{ "Category", "Fusion Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de fus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionCompleted_MetaData[] = {
		{ "Category", "Fusion Events" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionProgress_MetaData[] = {
		{ "Category", "Fusion Events" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionCancelled_MetaData[] = {
		{ "Category", "Fusion Events" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFusionNotification_MetaData[] = {
		{ "Category", "Fusion Events" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilCreated_MetaData[] = {
		{ "Category", "Fusion Events" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSigilConsumed_MetaData[] = {
		{ "Category", "Fusion Events" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXManager_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Refer\xc3\xaancias para VFX e \xc3\xa1udio\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias para VFX e \xc3\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionStartSound_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionCompleteSound_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionFailSound_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationWidgetClass_MetaData[] = {
		{ "Category", "UI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Widget de notifica\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Widget de notifica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDebugMode_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de depura\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogFusionEvents_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/Fusion/SigilFusionSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAutomaticFusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticFusion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFusionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSimultaneousFusions;
	static void NewProp_bShowNotifications_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowNotifications;
	static void NewProp_bPlaySounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPlaySounds;
	static void NewProp_bShowVFX_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowVFX;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionRecipes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FusionRecipes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveFusions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveFusions;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionCancelled;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFusionNotification;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSigilConsumed;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VFXManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionStartSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionCompleteSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FusionFailSound;
	static const UECodeGen_Private::FClassPropertyParams NewProp_NotificationWidgetClass;
	static void NewProp_bDebugMode_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDebugMode;
	static void NewProp_bLogFusionEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogFusionEvents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilFusionSystem_AddFusionRecipe, "AddFusionRecipe" }, // 250963901
		{ &Z_Construct_UFunction_USigilFusionSystem_CancelAllFusions, "CancelAllFusions" }, // 439917908
		{ &Z_Construct_UFunction_USigilFusionSystem_CancelFusion, "CancelFusion" }, // 1340014275
		{ &Z_Construct_UFunction_USigilFusionSystem_CanStartFusion, "CanStartFusion" }, // 1630185758
		{ &Z_Construct_UFunction_USigilFusionSystem_DebugCancelAllFusions, "DebugCancelAllFusions" }, // 3608458885
		{ &Z_Construct_UFunction_USigilFusionSystem_DebugPrintActiveFusions, "DebugPrintActiveFusions" }, // 3916562310
		{ &Z_Construct_UFunction_USigilFusionSystem_DebugStartTestFusion, "DebugStartTestFusion" }, // 1225361806
		{ &Z_Construct_UFunction_USigilFusionSystem_FindBestRecipe, "FindBestRecipe" }, // 435119049
		{ &Z_Construct_UFunction_USigilFusionSystem_GetActiveFusionCount, "GetActiveFusionCount" }, // 3039148993
		{ &Z_Construct_UFunction_USigilFusionSystem_GetActiveFusions, "GetActiveFusions" }, // 588315851
		{ &Z_Construct_UFunction_USigilFusionSystem_GetAvailableRecipes, "GetAvailableRecipes" }, // 2212119283
		{ &Z_Construct_UFunction_USigilFusionSystem_GetFusionByID, "GetFusionByID" }, // 1361918960
		{ &Z_Construct_UFunction_USigilFusionSystem_GetFusionProgress, "GetFusionProgress" }, // 3339982984
		{ &Z_Construct_UFunction_USigilFusionSystem_HideAllNotifications, "HideAllNotifications" }, // 4083967766
		{ &Z_Construct_UFunction_USigilFusionSystem_LoadDefaultRecipes, "LoadDefaultRecipes" }, // 1237162765
		{ &Z_Construct_UFunction_USigilFusionSystem_RemoveFusionRecipe, "RemoveFusionRecipe" }, // 3034220835
		{ &Z_Construct_UFunction_USigilFusionSystem_SetAutomaticFusionEnabled, "SetAutomaticFusionEnabled" }, // 1468008473
		{ &Z_Construct_UFunction_USigilFusionSystem_SetDebugMode, "SetDebugMode" }, // 1155296815
		{ &Z_Construct_UFunction_USigilFusionSystem_SetDefaultFusionTime, "SetDefaultFusionTime" }, // 3085587788
		{ &Z_Construct_UFunction_USigilFusionSystem_SetMaxSimultaneousFusions, "SetMaxSimultaneousFusions" }, // 684196178
		{ &Z_Construct_UFunction_USigilFusionSystem_ShowFusionNotification, "ShowFusionNotification" }, // 2313005947
		{ &Z_Construct_UFunction_USigilFusionSystem_StartAutomaticFusion, "StartAutomaticFusion" }, // 1965844047
		{ &Z_Construct_UFunction_USigilFusionSystem_StartManualFusion, "StartManualFusion" }, // 3967264050
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilFusionSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bEnableAutomaticFusion_SetBit(void* Obj)
{
	((USigilFusionSystem*)Obj)->bEnableAutomaticFusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bEnableAutomaticFusion = { "bEnableAutomaticFusion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilFusionSystem), &Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bEnableAutomaticFusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticFusion_MetaData), NewProp_bEnableAutomaticFusion_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_DefaultFusionTime = { "DefaultFusionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, DefaultFusionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFusionTime_MetaData), NewProp_DefaultFusionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_MaxSimultaneousFusions = { "MaxSimultaneousFusions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, MaxSimultaneousFusions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSimultaneousFusions_MetaData), NewProp_MaxSimultaneousFusions_MetaData) };
void Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowNotifications_SetBit(void* Obj)
{
	((USigilFusionSystem*)Obj)->bShowNotifications = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowNotifications = { "bShowNotifications", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilFusionSystem), &Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowNotifications_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowNotifications_MetaData), NewProp_bShowNotifications_MetaData) };
void Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bPlaySounds_SetBit(void* Obj)
{
	((USigilFusionSystem*)Obj)->bPlaySounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bPlaySounds = { "bPlaySounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilFusionSystem), &Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bPlaySounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPlaySounds_MetaData), NewProp_bPlaySounds_MetaData) };
void Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowVFX_SetBit(void* Obj)
{
	((USigilFusionSystem*)Obj)->bShowVFX = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowVFX = { "bShowVFX", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilFusionSystem), &Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowVFX_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowVFX_MetaData), NewProp_bShowVFX_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionRecipes_Inner = { "FusionRecipes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionRecipe, METADATA_PARAMS(0, nullptr) }; // 598289628
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionRecipes = { "FusionRecipes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, FusionRecipes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionRecipes_MetaData), NewProp_FusionRecipes_MetaData) }; // 598289628
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_ActiveFusions_Inner = { "ActiveFusions", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSigilFusionInstance, METADATA_PARAMS(0, nullptr) }; // 3952472978
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_ActiveFusions = { "ActiveFusions", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, ActiveFusions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveFusions_MetaData), NewProp_ActiveFusions_MetaData) }; // 3952472978
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionStarted = { "OnFusionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnFusionStarted), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionStarted_MetaData), NewProp_OnFusionStarted_MetaData) }; // 4183821638
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionCompleted = { "OnFusionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnFusionCompleted), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionCompleted_MetaData), NewProp_OnFusionCompleted_MetaData) }; // 1511806315
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionProgress = { "OnFusionProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnFusionProgress), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionProgress_MetaData), NewProp_OnFusionProgress_MetaData) }; // 2747118632
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionCancelled = { "OnFusionCancelled", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnFusionCancelled), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionCancelled_MetaData), NewProp_OnFusionCancelled_MetaData) }; // 2278369885
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionNotification = { "OnFusionNotification", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnFusionNotification), Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFusionNotification_MetaData), NewProp_OnFusionNotification_MetaData) }; // 200758248
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnSigilCreated = { "OnSigilCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnSigilCreated), Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilCreated_MetaData), NewProp_OnSigilCreated_MetaData) }; // 3706080528
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnSigilConsumed = { "OnSigilConsumed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, OnSigilConsumed), Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSigilConsumed_MetaData), NewProp_OnSigilConsumed_MetaData) }; // 4106660433
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_VFXManager = { "VFXManager", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, VFXManager), Z_Construct_UClass_USigilVFXManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXManager_MetaData), NewProp_VFXManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionStartSound = { "FusionStartSound", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, FusionStartSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionStartSound_MetaData), NewProp_FusionStartSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionCompleteSound = { "FusionCompleteSound", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, FusionCompleteSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionCompleteSound_MetaData), NewProp_FusionCompleteSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionFailSound = { "FusionFailSound", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, FusionFailSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionFailSound_MetaData), NewProp_FusionFailSound_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_NotificationWidgetClass = { "NotificationWidgetClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilFusionSystem, NotificationWidgetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationWidgetClass_MetaData), NewProp_NotificationWidgetClass_MetaData) };
void Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bDebugMode_SetBit(void* Obj)
{
	((USigilFusionSystem*)Obj)->bDebugMode = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bDebugMode = { "bDebugMode", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilFusionSystem), &Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bDebugMode_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDebugMode_MetaData), NewProp_bDebugMode_MetaData) };
void Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bLogFusionEvents_SetBit(void* Obj)
{
	((USigilFusionSystem*)Obj)->bLogFusionEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bLogFusionEvents = { "bLogFusionEvents", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilFusionSystem), &Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bLogFusionEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogFusionEvents_MetaData), NewProp_bLogFusionEvents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilFusionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bEnableAutomaticFusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_DefaultFusionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_MaxSimultaneousFusions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowNotifications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bPlaySounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bShowVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionRecipes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionRecipes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_ActiveFusions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_ActiveFusions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionCancelled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnFusionNotification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnSigilCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_OnSigilConsumed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_VFXManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionStartSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionCompleteSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_FusionFailSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_NotificationWidgetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bDebugMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilFusionSystem_Statics::NewProp_bLogFusionEvents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilFusionSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilFusionSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilFusionSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilFusionSystem_Statics::ClassParams = {
	&USigilFusionSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_USigilFusionSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilFusionSystem_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilFusionSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilFusionSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilFusionSystem()
{
	if (!Z_Registration_Info_UClass_USigilFusionSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilFusionSystem.OuterSingleton, Z_Construct_UClass_USigilFusionSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilFusionSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilFusionSystem);
USigilFusionSystem::~USigilFusionSystem() {}
// ********** End Class USigilFusionSystem *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESigilFusionState_StaticEnum, TEXT("ESigilFusionState"), &Z_Registration_Info_UEnum_ESigilFusionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1854197481U) },
		{ ESigilFusionType_StaticEnum, TEXT("ESigilFusionType"), &Z_Registration_Info_UEnum_ESigilFusionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2685722532U) },
		{ ESigilFusionResult_StaticEnum, TEXT("ESigilFusionResult"), &Z_Registration_Info_UEnum_ESigilFusionResult, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2553120672U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSigilFusionRecipe::StaticStruct, Z_Construct_UScriptStruct_FSigilFusionRecipe_Statics::NewStructOps, TEXT("SigilFusionRecipe"), &Z_Registration_Info_UScriptStruct_FSigilFusionRecipe, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFusionRecipe), 598289628U) },
		{ FSigilFusionInstance::StaticStruct, Z_Construct_UScriptStruct_FSigilFusionInstance_Statics::NewStructOps, TEXT("SigilFusionInstance"), &Z_Registration_Info_UScriptStruct_FSigilFusionInstance, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFusionInstance), 3952472978U) },
		{ FSigilFusionNotification::StaticStruct, Z_Construct_UScriptStruct_FSigilFusionNotification_Statics::NewStructOps, TEXT("SigilFusionNotification"), &Z_Registration_Info_UScriptStruct_FSigilFusionNotification, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilFusionNotification), 1294010451U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilFusionSystem, USigilFusionSystem::StaticClass, TEXT("USigilFusionSystem"), &Z_Registration_Info_UClass_USigilFusionSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilFusionSystem), 118705767U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_2071681740(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Fusion_SigilFusionSystem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
