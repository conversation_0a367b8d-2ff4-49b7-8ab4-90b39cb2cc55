// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SigilGameplayEffects.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeSigilGameplayEffects() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_USigilAutoFusionEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilAutoFusionEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilEffectFactory();
AURACRON_API UClass* Z_Construct_UClass_USigilEffectFactory_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilForceFusionEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilForceFusionEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilFusionExecutionCalculation();
AURACRON_API UClass* Z_Construct_UClass_USigilFusionExecutionCalculation_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilGameplayEffectBase();
AURACRON_API UClass* Z_Construct_UClass_USigilGameplayEffectBase_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilObjectiveEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilObjectiveEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilReforgeEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilReforgeEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralExecutionCalculation();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralExecutionCalculation_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralFocusEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralFocusEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralPowerEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralPowerEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralResilienceEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralResilienceEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralVelocityEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilSpectralVelocityEffect_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_USigilTeamFightEffect();
AURACRON_API UClass* Z_Construct_UClass_USigilTeamFightEffect_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilRarity();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESigilType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_ESpectralEffectType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FSpectralEffectConfig();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffectExecutionCalculation();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESpectralEffectType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESpectralEffectType;
static UEnum* ESpectralEffectType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESpectralEffectType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESpectralEffectType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_ESpectralEffectType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("ESpectralEffectType"));
	}
	return Z_Registration_Info_UEnum_ESpectralEffectType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<ESpectralEffectType>()
{
	return ESpectralEffectType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CombatBonus.DisplayName", "Combat Bonus" },
		{ "CombatBonus.Name", "ESpectralEffectType::CombatBonus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de efeitos espectrais para s\xc3\xadgilos\n */" },
#endif
		{ "FocusBoost.DisplayName", "Focus Boost" },
		{ "FocusBoost.Name", "ESpectralEffectType::FocusBoost" },
		{ "FusionMultiplier.DisplayName", "Fusion Multiplier" },
		{ "FusionMultiplier.Name", "ESpectralEffectType::FusionMultiplier" },
		{ "MobilityBonus.DisplayName", "Mobility Bonus" },
		{ "MobilityBonus.Name", "ESpectralEffectType::MobilityBonus" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESpectralEffectType::None" },
		{ "ObjectiveBonus.DisplayName", "Objective Bonus" },
		{ "ObjectiveBonus.Name", "ESpectralEffectType::ObjectiveBonus" },
		{ "PowerBoost.DisplayName", "Power Boost" },
		{ "PowerBoost.Name", "ESpectralEffectType::PowerBoost" },
		{ "ResilienceBoost.DisplayName", "Resilience Boost" },
		{ "ResilienceBoost.Name", "ESpectralEffectType::ResilienceBoost" },
		{ "ResourceBonus.DisplayName", "Resource Bonus" },
		{ "ResourceBonus.Name", "ESpectralEffectType::ResourceBonus" },
		{ "TeamFightBonus.DisplayName", "Team Fight Bonus" },
		{ "TeamFightBonus.Name", "ESpectralEffectType::TeamFightBonus" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de efeitos espectrais para s\xc3\xadgilos" },
#endif
		{ "VelocityBoost.DisplayName", "Velocity Boost" },
		{ "VelocityBoost.Name", "ESpectralEffectType::VelocityBoost" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESpectralEffectType::None", (int64)ESpectralEffectType::None },
		{ "ESpectralEffectType::PowerBoost", (int64)ESpectralEffectType::PowerBoost },
		{ "ESpectralEffectType::ResilienceBoost", (int64)ESpectralEffectType::ResilienceBoost },
		{ "ESpectralEffectType::VelocityBoost", (int64)ESpectralEffectType::VelocityBoost },
		{ "ESpectralEffectType::FocusBoost", (int64)ESpectralEffectType::FocusBoost },
		{ "ESpectralEffectType::CombatBonus", (int64)ESpectralEffectType::CombatBonus },
		{ "ESpectralEffectType::MobilityBonus", (int64)ESpectralEffectType::MobilityBonus },
		{ "ESpectralEffectType::ResourceBonus", (int64)ESpectralEffectType::ResourceBonus },
		{ "ESpectralEffectType::TeamFightBonus", (int64)ESpectralEffectType::TeamFightBonus },
		{ "ESpectralEffectType::ObjectiveBonus", (int64)ESpectralEffectType::ObjectiveBonus },
		{ "ESpectralEffectType::FusionMultiplier", (int64)ESpectralEffectType::FusionMultiplier },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"ESpectralEffectType",
	"ESpectralEffectType",
	Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_ESpectralEffectType()
{
	if (!Z_Registration_Info_UEnum_ESpectralEffectType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESpectralEffectType.InnerSingleton, Z_Construct_UEnum_AURACRON_ESpectralEffectType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESpectralEffectType.InnerSingleton;
}
// ********** End Enum ESpectralEffectType *********************************************************

// ********** Begin ScriptStruct FSpectralEffectConfig *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSpectralEffectConfig;
class UScriptStruct* FSpectralEffectConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSpectralEffectConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSpectralEffectConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSpectralEffectConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("SpectralEffectConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSpectralEffectConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xa3o de efeito espectral\n */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de efeito espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectType_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do efeito espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do efeito espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseMagnitude_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Magnitude base do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Magnitude base do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityMultipliers_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador por raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador por raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do efeito (-1 para infinito) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito (-1 para infinito)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectTags_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags de gameplay associadas */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags de gameplay associadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanStack_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o efeito pode ser empilhado */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o efeito pode ser empilhado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxStacks_MetaData[] = {
		{ "Category", "Spectral Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\xa1ximo de empilhamentos */" },
#endif
		{ "EditCondition", "bCanStack" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa1ximo de empilhamentos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseMagnitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RarityMultipliers_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RarityMultipliers_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RarityMultipliers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RarityMultipliers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectTags;
	static void NewProp_bCanStack_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanStack;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxStacks;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSpectralEffectConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_EffectType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralEffectConfig, EffectType), Z_Construct_UEnum_AURACRON_ESpectralEffectType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectType_MetaData), NewProp_EffectType_MetaData) }; // 2655816111
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_BaseMagnitude = { "BaseMagnitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralEffectConfig, BaseMagnitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseMagnitude_MetaData), NewProp_BaseMagnitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers_ValueProp = { "RarityMultipliers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp = { "RarityMultipliers_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers = { "RarityMultipliers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralEffectConfig, RarityMultipliers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityMultipliers_MetaData), NewProp_RarityMultipliers_MetaData) }; // 3544987888
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralEffectConfig, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_EffectTags = { "EffectTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralEffectConfig, EffectTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectTags_MetaData), NewProp_EffectTags_MetaData) }; // 2104890724
void Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_bCanStack_SetBit(void* Obj)
{
	((FSpectralEffectConfig*)Obj)->bCanStack = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_bCanStack = { "bCanStack", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSpectralEffectConfig), &Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_bCanStack_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanStack_MetaData), NewProp_bCanStack_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_MaxStacks = { "MaxStacks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSpectralEffectConfig, MaxStacks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxStacks_MetaData), NewProp_MaxStacks_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_EffectType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_BaseMagnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_RarityMultipliers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_EffectTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_bCanStack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewProp_MaxStacks,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"SpectralEffectConfig",
	Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::PropPointers),
	sizeof(FSpectralEffectConfig),
	alignof(FSpectralEffectConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSpectralEffectConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSpectralEffectConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSpectralEffectConfig.InnerSingleton, Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSpectralEffectConfig.InnerSingleton;
}
// ********** End ScriptStruct FSpectralEffectConfig ***********************************************

// ********** Begin Class USigilGameplayEffectBase *************************************************
void USigilGameplayEffectBase::StaticRegisterNativesUSigilGameplayEffectBase()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilGameplayEffectBase;
UClass* USigilGameplayEffectBase::GetPrivateStaticClass()
{
	using TClass = USigilGameplayEffectBase;
	if (!Z_Registration_Info_UClass_USigilGameplayEffectBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilGameplayEffectBase"),
			Z_Registration_Info_UClass_USigilGameplayEffectBase.InnerSingleton,
			StaticRegisterNativesUSigilGameplayEffectBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilGameplayEffectBase.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilGameplayEffectBase_NoRegister()
{
	return USigilGameplayEffectBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilGameplayEffectBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GameplayEffect base para todos os efeitos de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffect base para todos os efeitos de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpectralConfig_MetaData[] = {
		{ "Category", "Sigil Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o do efeito espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o do efeito espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumRarity_MetaData[] = {
		{ "Category", "Sigil Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade m\xc3\xadnima necess\xc3\xa1ria para aplicar o efeito */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade m\xc3\xadnima necess\xc3\xa1ria para aplicar o efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredSigilType_MetaData[] = {
		{ "Category", "Sigil Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de sigilo necess\xc3\xa1rio (None = qualquer tipo) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de sigilo necess\xc3\xa1rio (None = qualquer tipo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFusionOnly_MetaData[] = {
		{ "Category", "Sigil Effect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o efeito \xc3\xa9 aplicado apenas durante fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o efeito \xc3\xa9 aplicado apenas durante fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamFightMultiplier_MetaData[] = {
		{ "Category", "MOBA" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador adicional durante team fights */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador adicional durante team fights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveMultiplier_MetaData[] = {
		{ "Category", "MOBA" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador adicional perto de objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador adicional perto de objetivos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpectralConfig;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumRarity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequiredSigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequiredSigilType;
	static void NewProp_bFusionOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFusionOnly;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeamFightMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilGameplayEffectBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_SpectralConfig = { "SpectralConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilGameplayEffectBase, SpectralConfig), Z_Construct_UScriptStruct_FSpectralEffectConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpectralConfig_MetaData), NewProp_SpectralConfig_MetaData) }; // 4082206754
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_MinimumRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_MinimumRarity = { "MinimumRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilGameplayEffectBase, MinimumRarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumRarity_MetaData), NewProp_MinimumRarity_MetaData) }; // 3544987888
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_RequiredSigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_RequiredSigilType = { "RequiredSigilType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilGameplayEffectBase, RequiredSigilType), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredSigilType_MetaData), NewProp_RequiredSigilType_MetaData) }; // 3758400079
void Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_bFusionOnly_SetBit(void* Obj)
{
	((USigilGameplayEffectBase*)Obj)->bFusionOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_bFusionOnly = { "bFusionOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilGameplayEffectBase), &Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_bFusionOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFusionOnly_MetaData), NewProp_bFusionOnly_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_TeamFightMultiplier = { "TeamFightMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilGameplayEffectBase, TeamFightMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamFightMultiplier_MetaData), NewProp_TeamFightMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_ObjectiveMultiplier = { "ObjectiveMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilGameplayEffectBase, ObjectiveMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveMultiplier_MetaData), NewProp_ObjectiveMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilGameplayEffectBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_SpectralConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_MinimumRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_MinimumRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_RequiredSigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_RequiredSigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_bFusionOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_TeamFightMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilGameplayEffectBase_Statics::NewProp_ObjectiveMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilGameplayEffectBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilGameplayEffectBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffect,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilGameplayEffectBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilGameplayEffectBase_Statics::ClassParams = {
	&USigilGameplayEffectBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilGameplayEffectBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilGameplayEffectBase_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilGameplayEffectBase_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilGameplayEffectBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilGameplayEffectBase()
{
	if (!Z_Registration_Info_UClass_USigilGameplayEffectBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilGameplayEffectBase.OuterSingleton, Z_Construct_UClass_USigilGameplayEffectBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilGameplayEffectBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilGameplayEffectBase);
USigilGameplayEffectBase::~USigilGameplayEffectBase() {}
// ********** End Class USigilGameplayEffectBase ***************************************************

// ********** Begin Class USigilAutoFusionEffect ***************************************************
void USigilAutoFusionEffect::StaticRegisterNativesUSigilAutoFusionEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilAutoFusionEffect;
UClass* USigilAutoFusionEffect::GetPrivateStaticClass()
{
	using TClass = USigilAutoFusionEffect;
	if (!Z_Registration_Info_UClass_USigilAutoFusionEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilAutoFusionEffect"),
			Z_Registration_Info_UClass_USigilAutoFusionEffect.InnerSingleton,
			StaticRegisterNativesUSigilAutoFusionEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilAutoFusionEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilAutoFusionEffect_NoRegister()
{
	return USigilAutoFusionEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilAutoFusionEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de fus\xc3\xa3o autom\xc3\xa1tica - aplicado ap\xc3\xb3s 6 minutos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de fus\xc3\xa3o autom\xc3\xa1tica - aplicado ap\xc3\xb3s 6 minutos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionTimeSeconds_MetaData[] = {
		{ "Category", "Auto Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo para fus\xc3\xa3o autom\xc3\xa1tica (padr\xc3\xa3o: 360 segundos = 6 minutos) */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo para fus\xc3\xa3o autom\xc3\xa1tica (padr\xc3\xa3o: 360 segundos = 6 minutos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionPowerMultiplier_MetaData[] = {
		{ "Category", "Auto Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de poder ap\xc3\xb3s fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de poder ap\xc3\xb3s fus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionMultipliersByRarity_MetaData[] = {
		{ "Category", "Auto Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicadores espec\xc3\xad""ficos por raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicadores espec\xc3\xad""ficos por raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPermanentFusion_MetaData[] = {
		{ "Category", "Auto Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a fus\xc3\xa3o \xc3\xa9 permanente */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a fus\xc3\xa3o \xc3\xa9 permanente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionTimeSeconds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionPowerMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionMultipliersByRarity_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FusionMultipliersByRarity_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FusionMultipliersByRarity_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_FusionMultipliersByRarity;
	static void NewProp_bPermanentFusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPermanentFusion;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilAutoFusionEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionTimeSeconds = { "FusionTimeSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAutoFusionEffect, FusionTimeSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionTimeSeconds_MetaData), NewProp_FusionTimeSeconds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionPowerMultiplier = { "FusionPowerMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAutoFusionEffect, FusionPowerMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionPowerMultiplier_MetaData), NewProp_FusionPowerMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity_ValueProp = { "FusionMultipliersByRarity", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity_Key_KeyProp = { "FusionMultipliersByRarity_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity = { "FusionMultipliersByRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilAutoFusionEffect, FusionMultipliersByRarity), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionMultipliersByRarity_MetaData), NewProp_FusionMultipliersByRarity_MetaData) }; // 3544987888
void Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_bPermanentFusion_SetBit(void* Obj)
{
	((USigilAutoFusionEffect*)Obj)->bPermanentFusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_bPermanentFusion = { "bPermanentFusion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(USigilAutoFusionEffect), &Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_bPermanentFusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPermanentFusion_MetaData), NewProp_bPermanentFusion_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilAutoFusionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionTimeSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionPowerMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_FusionMultipliersByRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilAutoFusionEffect_Statics::NewProp_bPermanentFusion,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAutoFusionEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilAutoFusionEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAutoFusionEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilAutoFusionEffect_Statics::ClassParams = {
	&USigilAutoFusionEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilAutoFusionEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilAutoFusionEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilAutoFusionEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilAutoFusionEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilAutoFusionEffect()
{
	if (!Z_Registration_Info_UClass_USigilAutoFusionEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilAutoFusionEffect.OuterSingleton, Z_Construct_UClass_USigilAutoFusionEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilAutoFusionEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilAutoFusionEffect);
USigilAutoFusionEffect::~USigilAutoFusionEffect() {}
// ********** End Class USigilAutoFusionEffect *****************************************************

// ********** Begin Class USigilForceFusionEffect **************************************************
void USigilForceFusionEffect::StaticRegisterNativesUSigilForceFusionEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilForceFusionEffect;
UClass* USigilForceFusionEffect::GetPrivateStaticClass()
{
	using TClass = USigilForceFusionEffect;
	if (!Z_Registration_Info_UClass_USigilForceFusionEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilForceFusionEffect"),
			Z_Registration_Info_UClass_USigilForceFusionEffect.InnerSingleton,
			StaticRegisterNativesUSigilForceFusionEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilForceFusionEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilForceFusionEffect_NoRegister()
{
	return USigilForceFusionEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilForceFusionEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de fus\xc3\xa3o for\xc3\xa7""ada - aplicado instantaneamente\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de fus\xc3\xa3o for\xc3\xa7""ada - aplicado instantaneamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaCost_MetaData[] = {
		{ "Category", "Force Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custo de mana para fus\xc3\xa3o for\xc3\xa7""ada */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custo de mana para fus\xc3\xa3o for\xc3\xa7""ada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownSeconds_MetaData[] = {
		{ "Category", "Force Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown ap\xc3\xb3s fus\xc3\xa3o for\xc3\xa7""ada */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown ap\xc3\xb3s fus\xc3\xa3o for\xc3\xa7""ada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ForcedFusionPenalty_MetaData[] = {
		{ "Category", "Force Fusion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador reduzido para fus\xc3\xa3o for\xc3\xa7""ada */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador reduzido para fus\xc3\xa3o for\xc3\xa7""ada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownSeconds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ForcedFusionPenalty;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilForceFusionEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilForceFusionEffect_Statics::NewProp_ManaCost = { "ManaCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilForceFusionEffect, ManaCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaCost_MetaData), NewProp_ManaCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilForceFusionEffect_Statics::NewProp_CooldownSeconds = { "CooldownSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilForceFusionEffect, CooldownSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownSeconds_MetaData), NewProp_CooldownSeconds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilForceFusionEffect_Statics::NewProp_ForcedFusionPenalty = { "ForcedFusionPenalty", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilForceFusionEffect, ForcedFusionPenalty), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ForcedFusionPenalty_MetaData), NewProp_ForcedFusionPenalty_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilForceFusionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilForceFusionEffect_Statics::NewProp_ManaCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilForceFusionEffect_Statics::NewProp_CooldownSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilForceFusionEffect_Statics::NewProp_ForcedFusionPenalty,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilForceFusionEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilForceFusionEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilForceFusionEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilForceFusionEffect_Statics::ClassParams = {
	&USigilForceFusionEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilForceFusionEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilForceFusionEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilForceFusionEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilForceFusionEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilForceFusionEffect()
{
	if (!Z_Registration_Info_UClass_USigilForceFusionEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilForceFusionEffect.OuterSingleton, Z_Construct_UClass_USigilForceFusionEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilForceFusionEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilForceFusionEffect);
USigilForceFusionEffect::~USigilForceFusionEffect() {}
// ********** End Class USigilForceFusionEffect ****************************************************

// ********** Begin Class USigilSpectralPowerEffect ************************************************
void USigilSpectralPowerEffect::StaticRegisterNativesUSigilSpectralPowerEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilSpectralPowerEffect;
UClass* USigilSpectralPowerEffect::GetPrivateStaticClass()
{
	using TClass = USigilSpectralPowerEffect;
	if (!Z_Registration_Info_UClass_USigilSpectralPowerEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilSpectralPowerEffect"),
			Z_Registration_Info_UClass_USigilSpectralPowerEffect.InnerSingleton,
			StaticRegisterNativesUSigilSpectralPowerEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilSpectralPowerEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilSpectralPowerEffect_NoRegister()
{
	return USigilSpectralPowerEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilSpectralPowerEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de aumento de Poder Espectral\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de aumento de Poder Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerIncrease_MetaData[] = {
		{ "Category", "Spectral Power" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento base de poder espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento base de poder espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerIncreasePercent_MetaData[] = {
		{ "Category", "Spectral Power" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento percentual adicional */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento percentual adicional" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerIncrease;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerIncreasePercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilSpectralPowerEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralPowerEffect_Statics::NewProp_PowerIncrease = { "PowerIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralPowerEffect, PowerIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerIncrease_MetaData), NewProp_PowerIncrease_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralPowerEffect_Statics::NewProp_PowerIncreasePercent = { "PowerIncreasePercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralPowerEffect, PowerIncreasePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerIncreasePercent_MetaData), NewProp_PowerIncreasePercent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilSpectralPowerEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralPowerEffect_Statics::NewProp_PowerIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralPowerEffect_Statics::NewProp_PowerIncreasePercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralPowerEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilSpectralPowerEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralPowerEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilSpectralPowerEffect_Statics::ClassParams = {
	&USigilSpectralPowerEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilSpectralPowerEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralPowerEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralPowerEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilSpectralPowerEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilSpectralPowerEffect()
{
	if (!Z_Registration_Info_UClass_USigilSpectralPowerEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilSpectralPowerEffect.OuterSingleton, Z_Construct_UClass_USigilSpectralPowerEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilSpectralPowerEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilSpectralPowerEffect);
USigilSpectralPowerEffect::~USigilSpectralPowerEffect() {}
// ********** End Class USigilSpectralPowerEffect **************************************************

// ********** Begin Class USigilSpectralResilienceEffect *******************************************
void USigilSpectralResilienceEffect::StaticRegisterNativesUSigilSpectralResilienceEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilSpectralResilienceEffect;
UClass* USigilSpectralResilienceEffect::GetPrivateStaticClass()
{
	using TClass = USigilSpectralResilienceEffect;
	if (!Z_Registration_Info_UClass_USigilSpectralResilienceEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilSpectralResilienceEffect"),
			Z_Registration_Info_UClass_USigilSpectralResilienceEffect.InnerSingleton,
			StaticRegisterNativesUSigilSpectralResilienceEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilSpectralResilienceEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilSpectralResilienceEffect_NoRegister()
{
	return USigilSpectralResilienceEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilSpectralResilienceEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de aumento de Resist\xc3\xaancia Espectral\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de aumento de Resist\xc3\xaancia Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResilienceIncrease_MetaData[] = {
		{ "Category", "Spectral Resilience" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento base de resist\xc3\xaancia espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento base de resist\xc3\xaancia espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageReductionPercent_MetaData[] = {
		{ "Category", "Spectral Resilience" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Redu\xc3\xa7\xc3\xa3o de dano percentual */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\xa7\xc3\xa3o de dano percentual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResilienceIncrease;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageReductionPercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilSpectralResilienceEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::NewProp_ResilienceIncrease = { "ResilienceIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralResilienceEffect, ResilienceIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResilienceIncrease_MetaData), NewProp_ResilienceIncrease_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::NewProp_DamageReductionPercent = { "DamageReductionPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralResilienceEffect, DamageReductionPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageReductionPercent_MetaData), NewProp_DamageReductionPercent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::NewProp_ResilienceIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::NewProp_DamageReductionPercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::ClassParams = {
	&USigilSpectralResilienceEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilSpectralResilienceEffect()
{
	if (!Z_Registration_Info_UClass_USigilSpectralResilienceEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilSpectralResilienceEffect.OuterSingleton, Z_Construct_UClass_USigilSpectralResilienceEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilSpectralResilienceEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilSpectralResilienceEffect);
USigilSpectralResilienceEffect::~USigilSpectralResilienceEffect() {}
// ********** End Class USigilSpectralResilienceEffect *********************************************

// ********** Begin Class USigilSpectralVelocityEffect *********************************************
void USigilSpectralVelocityEffect::StaticRegisterNativesUSigilSpectralVelocityEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilSpectralVelocityEffect;
UClass* USigilSpectralVelocityEffect::GetPrivateStaticClass()
{
	using TClass = USigilSpectralVelocityEffect;
	if (!Z_Registration_Info_UClass_USigilSpectralVelocityEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilSpectralVelocityEffect"),
			Z_Registration_Info_UClass_USigilSpectralVelocityEffect.InnerSingleton,
			StaticRegisterNativesUSigilSpectralVelocityEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilSpectralVelocityEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilSpectralVelocityEffect_NoRegister()
{
	return USigilSpectralVelocityEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilSpectralVelocityEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de aumento de Velocidade Espectral\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de aumento de Velocidade Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VelocityIncrease_MetaData[] = {
		{ "Category", "Spectral Velocity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento base de velocidade espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento base de velocidade espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedIncrease_MetaData[] = {
		{ "Category", "Spectral Velocity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento de velocidade de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento de velocidade de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReductionPercent_MetaData[] = {
		{ "Category", "Spectral Velocity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Redu\xc3\xa7\xc3\xa3o de cooldown percentual */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\xa7\xc3\xa3o de cooldown percentual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VelocityIncrease;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedIncrease;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReductionPercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilSpectralVelocityEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::NewProp_VelocityIncrease = { "VelocityIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralVelocityEffect, VelocityIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VelocityIncrease_MetaData), NewProp_VelocityIncrease_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::NewProp_MovementSpeedIncrease = { "MovementSpeedIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralVelocityEffect, MovementSpeedIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedIncrease_MetaData), NewProp_MovementSpeedIncrease_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::NewProp_CooldownReductionPercent = { "CooldownReductionPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralVelocityEffect, CooldownReductionPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReductionPercent_MetaData), NewProp_CooldownReductionPercent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::NewProp_VelocityIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::NewProp_MovementSpeedIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::NewProp_CooldownReductionPercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::ClassParams = {
	&USigilSpectralVelocityEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilSpectralVelocityEffect()
{
	if (!Z_Registration_Info_UClass_USigilSpectralVelocityEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilSpectralVelocityEffect.OuterSingleton, Z_Construct_UClass_USigilSpectralVelocityEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilSpectralVelocityEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilSpectralVelocityEffect);
USigilSpectralVelocityEffect::~USigilSpectralVelocityEffect() {}
// ********** End Class USigilSpectralVelocityEffect ***********************************************

// ********** Begin Class USigilSpectralFocusEffect ************************************************
void USigilSpectralFocusEffect::StaticRegisterNativesUSigilSpectralFocusEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilSpectralFocusEffect;
UClass* USigilSpectralFocusEffect::GetPrivateStaticClass()
{
	using TClass = USigilSpectralFocusEffect;
	if (!Z_Registration_Info_UClass_USigilSpectralFocusEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilSpectralFocusEffect"),
			Z_Registration_Info_UClass_USigilSpectralFocusEffect.InnerSingleton,
			StaticRegisterNativesUSigilSpectralFocusEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilSpectralFocusEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilSpectralFocusEffect_NoRegister()
{
	return USigilSpectralFocusEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilSpectralFocusEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de aumento de Foco Espectral\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de aumento de Foco Espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FocusIncrease_MetaData[] = {
		{ "Category", "Spectral Focus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento base de foco espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento base de foco espectral" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalChanceIncrease_MetaData[] = {
		{ "Category", "Spectral Focus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento de chance cr\xc3\xadtica */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento de chance cr\xc3\xadtica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegenIncrease_MetaData[] = {
		{ "Category", "Spectral Focus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aumento de regenera\xc3\xa7\xc3\xa3o de mana */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aumento de regenera\xc3\xa7\xc3\xa3o de mana" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FocusIncrease;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalChanceIncrease;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaRegenIncrease;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilSpectralFocusEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralFocusEffect_Statics::NewProp_FocusIncrease = { "FocusIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralFocusEffect, FocusIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FocusIncrease_MetaData), NewProp_FocusIncrease_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralFocusEffect_Statics::NewProp_CriticalChanceIncrease = { "CriticalChanceIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralFocusEffect, CriticalChanceIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalChanceIncrease_MetaData), NewProp_CriticalChanceIncrease_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilSpectralFocusEffect_Statics::NewProp_ManaRegenIncrease = { "ManaRegenIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilSpectralFocusEffect, ManaRegenIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegenIncrease_MetaData), NewProp_ManaRegenIncrease_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilSpectralFocusEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralFocusEffect_Statics::NewProp_FocusIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralFocusEffect_Statics::NewProp_CriticalChanceIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilSpectralFocusEffect_Statics::NewProp_ManaRegenIncrease,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralFocusEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilSpectralFocusEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralFocusEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilSpectralFocusEffect_Statics::ClassParams = {
	&USigilSpectralFocusEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilSpectralFocusEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralFocusEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralFocusEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilSpectralFocusEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilSpectralFocusEffect()
{
	if (!Z_Registration_Info_UClass_USigilSpectralFocusEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilSpectralFocusEffect.OuterSingleton, Z_Construct_UClass_USigilSpectralFocusEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilSpectralFocusEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilSpectralFocusEffect);
USigilSpectralFocusEffect::~USigilSpectralFocusEffect() {}
// ********** End Class USigilSpectralFocusEffect **************************************************

// ********** Begin Class USigilTeamFightEffect ****************************************************
void USigilTeamFightEffect::StaticRegisterNativesUSigilTeamFightEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilTeamFightEffect;
UClass* USigilTeamFightEffect::GetPrivateStaticClass()
{
	using TClass = USigilTeamFightEffect;
	if (!Z_Registration_Info_UClass_USigilTeamFightEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilTeamFightEffect"),
			Z_Registration_Info_UClass_USigilTeamFightEffect.InnerSingleton,
			StaticRegisterNativesUSigilTeamFightEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilTeamFightEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilTeamFightEffect_NoRegister()
{
	return USigilTeamFightEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilTeamFightEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de b\xc3\xb4nus em team fights\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de b\xc3\xb4nus em team fights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageBonus_MetaData[] = {
		{ "Category", "Team Fight" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de dano em team fights */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de dano em team fights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResistanceBonus_MetaData[] = {
		{ "Category", "Team Fight" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de resist\xc3\xaancia em team fights */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de resist\xc3\xaancia em team fights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamFightRadius_MetaData[] = {
		{ "Category", "Team Fight" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio para detectar team fight */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio para detectar team fight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinEnemiesForActivation_MetaData[] = {
		{ "Category", "Team Fight" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xadnimo de inimigos para ativar */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xadnimo de inimigos para ativar" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResistanceBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeamFightRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinEnemiesForActivation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilTeamFightEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_DamageBonus = { "DamageBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilTeamFightEffect, DamageBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageBonus_MetaData), NewProp_DamageBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_ResistanceBonus = { "ResistanceBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilTeamFightEffect, ResistanceBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResistanceBonus_MetaData), NewProp_ResistanceBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_TeamFightRadius = { "TeamFightRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilTeamFightEffect, TeamFightRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamFightRadius_MetaData), NewProp_TeamFightRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_MinEnemiesForActivation = { "MinEnemiesForActivation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilTeamFightEffect, MinEnemiesForActivation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinEnemiesForActivation_MetaData), NewProp_MinEnemiesForActivation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilTeamFightEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_DamageBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_ResistanceBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_TeamFightRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilTeamFightEffect_Statics::NewProp_MinEnemiesForActivation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilTeamFightEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilTeamFightEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilTeamFightEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilTeamFightEffect_Statics::ClassParams = {
	&USigilTeamFightEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilTeamFightEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilTeamFightEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilTeamFightEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilTeamFightEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilTeamFightEffect()
{
	if (!Z_Registration_Info_UClass_USigilTeamFightEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilTeamFightEffect.OuterSingleton, Z_Construct_UClass_USigilTeamFightEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilTeamFightEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilTeamFightEffect);
USigilTeamFightEffect::~USigilTeamFightEffect() {}
// ********** End Class USigilTeamFightEffect ******************************************************

// ********** Begin Class USigilObjectiveEffect ****************************************************
void USigilObjectiveEffect::StaticRegisterNativesUSigilObjectiveEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilObjectiveEffect;
UClass* USigilObjectiveEffect::GetPrivateStaticClass()
{
	using TClass = USigilObjectiveEffect;
	if (!Z_Registration_Info_UClass_USigilObjectiveEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilObjectiveEffect"),
			Z_Registration_Info_UClass_USigilObjectiveEffect.InnerSingleton,
			StaticRegisterNativesUSigilObjectiveEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilObjectiveEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilObjectiveEffect_NoRegister()
{
	return USigilObjectiveEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilObjectiveEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito de b\xc3\xb4nus perto de objetivos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de b\xc3\xb4nus perto de objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveDamageBonus_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de dano perto de objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de dano perto de objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSpeedBonus_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** B\xc3\xb4nus de velocidade de ataque */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "B\xc3\xb4nus de velocidade de ataque" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRadius_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio para detectar objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio para detectar objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidObjectiveTags_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags de objetivos v\xc3\xa1lidos */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags de objetivos v\xc3\xa1lidos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveDamageBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackSpeedBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidObjectiveTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilObjectiveEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_ObjectiveDamageBonus = { "ObjectiveDamageBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilObjectiveEffect, ObjectiveDamageBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveDamageBonus_MetaData), NewProp_ObjectiveDamageBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_AttackSpeedBonus = { "AttackSpeedBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilObjectiveEffect, AttackSpeedBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSpeedBonus_MetaData), NewProp_AttackSpeedBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_ObjectiveRadius = { "ObjectiveRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilObjectiveEffect, ObjectiveRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRadius_MetaData), NewProp_ObjectiveRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_ValidObjectiveTags = { "ValidObjectiveTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilObjectiveEffect, ValidObjectiveTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidObjectiveTags_MetaData), NewProp_ValidObjectiveTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilObjectiveEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_ObjectiveDamageBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_AttackSpeedBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_ObjectiveRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilObjectiveEffect_Statics::NewProp_ValidObjectiveTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilObjectiveEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilObjectiveEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilObjectiveEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilObjectiveEffect_Statics::ClassParams = {
	&USigilObjectiveEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilObjectiveEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilObjectiveEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilObjectiveEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilObjectiveEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilObjectiveEffect()
{
	if (!Z_Registration_Info_UClass_USigilObjectiveEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilObjectiveEffect.OuterSingleton, Z_Construct_UClass_USigilObjectiveEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilObjectiveEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilObjectiveEffect);
USigilObjectiveEffect::~USigilObjectiveEffect() {}
// ********** End Class USigilObjectiveEffect ******************************************************

// ********** Begin Class USigilReforgeEffect ******************************************************
void USigilReforgeEffect::StaticRegisterNativesUSigilReforgeEffect()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilReforgeEffect;
UClass* USigilReforgeEffect::GetPrivateStaticClass()
{
	using TClass = USigilReforgeEffect;
	if (!Z_Registration_Info_UClass_USigilReforgeEffect.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilReforgeEffect"),
			Z_Registration_Info_UClass_USigilReforgeEffect.InnerSingleton,
			StaticRegisterNativesUSigilReforgeEffect,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilReforgeEffect.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilReforgeEffect_NoRegister()
{
	return USigilReforgeEffect::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilReforgeEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Efeito aplicado durante reforge de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
		{ "PrioritizeCategories", "Status Duration GameplayEffect GameplayCues Stacking" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito aplicado durante reforge de s\xc3\xadgilos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeCooldown_MetaData[] = {
		{ "Category", "Reforge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown do reforge */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown do reforge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReforgeGoldCost_MetaData[] = {
		{ "Category", "Reforge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custo de ouro para reforge */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custo de ouro para reforge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RarityUpgradeChance_MetaData[] = {
		{ "Category", "Reforge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chance de melhorar raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chance de melhorar raridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KeepStatsChance_MetaData[] = {
		{ "Category", "Reforge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chance de manter estat\xc3\xadsticas atuais */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chance de manter estat\xc3\xadsticas atuais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReforgeCooldown;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReforgeGoldCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RarityUpgradeChance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_KeepStatsChance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilReforgeEffect>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_ReforgeCooldown = { "ReforgeCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReforgeEffect, ReforgeCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeCooldown_MetaData), NewProp_ReforgeCooldown_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_ReforgeGoldCost = { "ReforgeGoldCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReforgeEffect, ReforgeGoldCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReforgeGoldCost_MetaData), NewProp_ReforgeGoldCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_RarityUpgradeChance = { "RarityUpgradeChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReforgeEffect, RarityUpgradeChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RarityUpgradeChance_MetaData), NewProp_RarityUpgradeChance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_KeepStatsChance = { "KeepStatsChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(USigilReforgeEffect, KeepStatsChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KeepStatsChance_MetaData), NewProp_KeepStatsChance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_USigilReforgeEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_ReforgeCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_ReforgeGoldCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_RarityUpgradeChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_USigilReforgeEffect_Statics::NewProp_KeepStatsChance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReforgeEffect_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_USigilReforgeEffect_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USigilGameplayEffectBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReforgeEffect_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilReforgeEffect_Statics::ClassParams = {
	&USigilReforgeEffect::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_USigilReforgeEffect_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_USigilReforgeEffect_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilReforgeEffect_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilReforgeEffect_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilReforgeEffect()
{
	if (!Z_Registration_Info_UClass_USigilReforgeEffect.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilReforgeEffect.OuterSingleton, Z_Construct_UClass_USigilReforgeEffect_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilReforgeEffect.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilReforgeEffect);
USigilReforgeEffect::~USigilReforgeEffect() {}
// ********** End Class USigilReforgeEffect ********************************************************

// ********** Begin Class USigilFusionExecutionCalculation *****************************************
void USigilFusionExecutionCalculation::StaticRegisterNativesUSigilFusionExecutionCalculation()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilFusionExecutionCalculation;
UClass* USigilFusionExecutionCalculation::GetPrivateStaticClass()
{
	using TClass = USigilFusionExecutionCalculation;
	if (!Z_Registration_Info_UClass_USigilFusionExecutionCalculation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilFusionExecutionCalculation"),
			Z_Registration_Info_UClass_USigilFusionExecutionCalculation.InnerSingleton,
			StaticRegisterNativesUSigilFusionExecutionCalculation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilFusionExecutionCalculation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilFusionExecutionCalculation_NoRegister()
{
	return USigilFusionExecutionCalculation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilFusionExecutionCalculation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para fus\xc3\xa3o de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para fus\xc3\xa3o de s\xc3\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilFusionExecutionCalculation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilFusionExecutionCalculation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffectExecutionCalculation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilFusionExecutionCalculation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilFusionExecutionCalculation_Statics::ClassParams = {
	&USigilFusionExecutionCalculation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilFusionExecutionCalculation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilFusionExecutionCalculation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilFusionExecutionCalculation()
{
	if (!Z_Registration_Info_UClass_USigilFusionExecutionCalculation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilFusionExecutionCalculation.OuterSingleton, Z_Construct_UClass_USigilFusionExecutionCalculation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilFusionExecutionCalculation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilFusionExecutionCalculation);
USigilFusionExecutionCalculation::~USigilFusionExecutionCalculation() {}
// ********** End Class USigilFusionExecutionCalculation *******************************************

// ********** Begin Class USigilSpectralExecutionCalculation ***************************************
void USigilSpectralExecutionCalculation::StaticRegisterNativesUSigilSpectralExecutionCalculation()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilSpectralExecutionCalculation;
UClass* USigilSpectralExecutionCalculation::GetPrivateStaticClass()
{
	using TClass = USigilSpectralExecutionCalculation;
	if (!Z_Registration_Info_UClass_USigilSpectralExecutionCalculation.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilSpectralExecutionCalculation"),
			Z_Registration_Info_UClass_USigilSpectralExecutionCalculation.InnerSingleton,
			StaticRegisterNativesUSigilSpectralExecutionCalculation,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilSpectralExecutionCalculation.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilSpectralExecutionCalculation_NoRegister()
{
	return USigilSpectralExecutionCalculation::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para efeitos espectrais\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "C\xc3\xa1lculo de execu\xc3\xa7\xc3\xa3o para efeitos espectrais" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilSpectralExecutionCalculation>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayEffectExecutionCalculation,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics::ClassParams = {
	&USigilSpectralExecutionCalculation::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilSpectralExecutionCalculation()
{
	if (!Z_Registration_Info_UClass_USigilSpectralExecutionCalculation.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilSpectralExecutionCalculation.OuterSingleton, Z_Construct_UClass_USigilSpectralExecutionCalculation_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilSpectralExecutionCalculation.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilSpectralExecutionCalculation);
USigilSpectralExecutionCalculation::~USigilSpectralExecutionCalculation() {}
// ********** End Class USigilSpectralExecutionCalculation *****************************************

// ********** Begin Class USigilEffectFactory Function CreateAutoFusionEffect **********************
struct Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics
{
	struct SigilEffectFactory_eventCreateAutoFusionEffect_Parms
	{
		ESigilRarity Rarity;
		ESigilType Type;
		TSubclassOf<UGameplayEffect> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar efeito de fus\xc3\xa3o autom\xc3\xa1tica */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar efeito de fus\xc3\xa3o autom\xc3\xa1tica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateAutoFusionEffect_Parms, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateAutoFusionEffect_Parms, Type), Z_Construct_UEnum_AURACRON_ESigilType, METADATA_PARAMS(0, nullptr) }; // 3758400079
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateAutoFusionEffect_Parms, ReturnValue), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "CreateAutoFusionEffect", Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::SigilEffectFactory_eventCreateAutoFusionEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::SigilEffectFactory_eventCreateAutoFusionEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execCreateAutoFusionEffect)
{
	P_GET_ENUM(ESigilRarity,Z_Param_Rarity);
	P_GET_ENUM(ESigilType,Z_Param_Type);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TSubclassOf<UGameplayEffect>*)Z_Param__Result=USigilEffectFactory::CreateAutoFusionEffect(ESigilRarity(Z_Param_Rarity),ESigilType(Z_Param_Type));
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function CreateAutoFusionEffect ************************

// ********** Begin Class USigilEffectFactory Function CreateObjectiveEffect ***********************
struct Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics
{
	struct SigilEffectFactory_eventCreateObjectiveEffect_Parms
	{
		float DamageBonus;
		float AttackSpeedBonus;
		TSubclassOf<UGameplayEffect> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar efeito de objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar efeito de objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackSpeedBonus;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::NewProp_DamageBonus = { "DamageBonus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateObjectiveEffect_Parms, DamageBonus), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::NewProp_AttackSpeedBonus = { "AttackSpeedBonus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateObjectiveEffect_Parms, AttackSpeedBonus), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateObjectiveEffect_Parms, ReturnValue), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::NewProp_DamageBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::NewProp_AttackSpeedBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "CreateObjectiveEffect", Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::SigilEffectFactory_eventCreateObjectiveEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::SigilEffectFactory_eventCreateObjectiveEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execCreateObjectiveEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageBonus);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AttackSpeedBonus);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TSubclassOf<UGameplayEffect>*)Z_Param__Result=USigilEffectFactory::CreateObjectiveEffect(Z_Param_DamageBonus,Z_Param_AttackSpeedBonus);
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function CreateObjectiveEffect *************************

// ********** Begin Class USigilEffectFactory Function CreateSpectralEffect ************************
struct Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics
{
	struct SigilEffectFactory_eventCreateSpectralEffect_Parms
	{
		ESpectralEffectType EffectType;
		ESigilRarity Rarity;
		float Magnitude;
		TSubclassOf<UGameplayEffect> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar efeito espectral */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar efeito espectral" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Magnitude;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_EffectType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateSpectralEffect_Parms, EffectType), Z_Construct_UEnum_AURACRON_ESpectralEffectType, METADATA_PARAMS(0, nullptr) }; // 2655816111
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateSpectralEffect_Parms, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_Magnitude = { "Magnitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateSpectralEffect_Parms, Magnitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateSpectralEffect_Parms, ReturnValue), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_EffectType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_Magnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "CreateSpectralEffect", Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::SigilEffectFactory_eventCreateSpectralEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::SigilEffectFactory_eventCreateSpectralEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execCreateSpectralEffect)
{
	P_GET_ENUM(ESpectralEffectType,Z_Param_EffectType);
	P_GET_ENUM(ESigilRarity,Z_Param_Rarity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Magnitude);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TSubclassOf<UGameplayEffect>*)Z_Param__Result=USigilEffectFactory::CreateSpectralEffect(ESpectralEffectType(Z_Param_EffectType),ESigilRarity(Z_Param_Rarity),Z_Param_Magnitude);
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function CreateSpectralEffect **************************

// ********** Begin Class USigilEffectFactory Function CreateTeamFightEffect ***********************
struct Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics
{
	struct SigilEffectFactory_eventCreateTeamFightEffect_Parms
	{
		float DamageBonus;
		float ResistanceBonus;
		TSubclassOf<UGameplayEffect> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar efeito de team fight */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar efeito de team fight" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResistanceBonus;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::NewProp_DamageBonus = { "DamageBonus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateTeamFightEffect_Parms, DamageBonus), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::NewProp_ResistanceBonus = { "ResistanceBonus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateTeamFightEffect_Parms, ResistanceBonus), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventCreateTeamFightEffect_Parms, ReturnValue), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::NewProp_DamageBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::NewProp_ResistanceBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "CreateTeamFightEffect", Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::SigilEffectFactory_eventCreateTeamFightEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::SigilEffectFactory_eventCreateTeamFightEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execCreateTeamFightEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageBonus);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ResistanceBonus);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TSubclassOf<UGameplayEffect>*)Z_Param__Result=USigilEffectFactory::CreateTeamFightEffect(Z_Param_DamageBonus,Z_Param_ResistanceBonus);
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function CreateTeamFightEffect *************************

// ********** Begin Class USigilEffectFactory Function GetDefaultFusionDuration ********************
struct Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics
{
	struct SigilEffectFactory_eventGetDefaultFusionDuration_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter dura\xc3\xa7\xc3\xa3o padr\xc3\xa3o de fus\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter dura\xc3\xa7\xc3\xa3o padr\xc3\xa3o de fus\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventGetDefaultFusionDuration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "GetDefaultFusionDuration", Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::SigilEffectFactory_eventGetDefaultFusionDuration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::SigilEffectFactory_eventGetDefaultFusionDuration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execGetDefaultFusionDuration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=USigilEffectFactory::GetDefaultFusionDuration();
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function GetDefaultFusionDuration **********************

// ********** Begin Class USigilEffectFactory Function GetDefaultRarityMultiplier ******************
struct Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics
{
	struct SigilEffectFactory_eventGetDefaultRarityMultiplier_Parms
	{
		ESigilRarity Rarity;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter multiplicador padr\xc3\xa3o por raridade */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter multiplicador padr\xc3\xa3o por raridade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventGetDefaultRarityMultiplier_Parms, Rarity), Z_Construct_UEnum_AURACRON_ESigilRarity, METADATA_PARAMS(0, nullptr) }; // 3544987888
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventGetDefaultRarityMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "GetDefaultRarityMultiplier", Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::SigilEffectFactory_eventGetDefaultRarityMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::SigilEffectFactory_eventGetDefaultRarityMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execGetDefaultRarityMultiplier)
{
	P_GET_ENUM(ESigilRarity,Z_Param_Rarity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=USigilEffectFactory::GetDefaultRarityMultiplier(ESigilRarity(Z_Param_Rarity));
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function GetDefaultRarityMultiplier ********************

// ********** Begin Class USigilEffectFactory Function ValidateEffectConfig ************************
struct Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics
{
	struct SigilEffectFactory_eventValidateEffectConfig_Parms
	{
		FSpectralEffectConfig Config;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validar configura\xc3\xa7\xc3\xa3o de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar configura\xc3\xa7\xc3\xa3o de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SigilEffectFactory_eventValidateEffectConfig_Parms, Config), Z_Construct_UScriptStruct_FSpectralEffectConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 4082206754
void Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SigilEffectFactory_eventValidateEffectConfig_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SigilEffectFactory_eventValidateEffectConfig_Parms), &Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_USigilEffectFactory, nullptr, "ValidateEffectConfig", Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::SigilEffectFactory_eventValidateEffectConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::SigilEffectFactory_eventValidateEffectConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(USigilEffectFactory::execValidateEffectConfig)
{
	P_GET_STRUCT_REF(FSpectralEffectConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=USigilEffectFactory::ValidateEffectConfig(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class USigilEffectFactory Function ValidateEffectConfig **************************

// ********** Begin Class USigilEffectFactory ******************************************************
void USigilEffectFactory::StaticRegisterNativesUSigilEffectFactory()
{
	UClass* Class = USigilEffectFactory::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateAutoFusionEffect", &USigilEffectFactory::execCreateAutoFusionEffect },
		{ "CreateObjectiveEffect", &USigilEffectFactory::execCreateObjectiveEffect },
		{ "CreateSpectralEffect", &USigilEffectFactory::execCreateSpectralEffect },
		{ "CreateTeamFightEffect", &USigilEffectFactory::execCreateTeamFightEffect },
		{ "GetDefaultFusionDuration", &USigilEffectFactory::execGetDefaultFusionDuration },
		{ "GetDefaultRarityMultiplier", &USigilEffectFactory::execGetDefaultRarityMultiplier },
		{ "ValidateEffectConfig", &USigilEffectFactory::execValidateEffectConfig },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_USigilEffectFactory;
UClass* USigilEffectFactory::GetPrivateStaticClass()
{
	using TClass = USigilEffectFactory;
	if (!Z_Registration_Info_UClass_USigilEffectFactory.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("SigilEffectFactory"),
			Z_Registration_Info_UClass_USigilEffectFactory.InnerSingleton,
			StaticRegisterNativesUSigilEffectFactory,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_USigilEffectFactory.InnerSingleton;
}
UClass* Z_Construct_UClass_USigilEffectFactory_NoRegister()
{
	return USigilEffectFactory::GetPrivateStaticClass();
}
struct Z_Construct_UClass_USigilEffectFactory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Factory para criar GameplayEffects de s\xc3\xadgilos\n */" },
#endif
		{ "IncludePath", "Sigils/SigilGameplayEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Sigils/SigilGameplayEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory para criar GameplayEffects de s\xc3\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_USigilEffectFactory_CreateAutoFusionEffect, "CreateAutoFusionEffect" }, // 351791068
		{ &Z_Construct_UFunction_USigilEffectFactory_CreateObjectiveEffect, "CreateObjectiveEffect" }, // 1354283856
		{ &Z_Construct_UFunction_USigilEffectFactory_CreateSpectralEffect, "CreateSpectralEffect" }, // 2092381054
		{ &Z_Construct_UFunction_USigilEffectFactory_CreateTeamFightEffect, "CreateTeamFightEffect" }, // 2149015625
		{ &Z_Construct_UFunction_USigilEffectFactory_GetDefaultFusionDuration, "GetDefaultFusionDuration" }, // 2058981036
		{ &Z_Construct_UFunction_USigilEffectFactory_GetDefaultRarityMultiplier, "GetDefaultRarityMultiplier" }, // 2450716794
		{ &Z_Construct_UFunction_USigilEffectFactory_ValidateEffectConfig, "ValidateEffectConfig" }, // 817076539
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<USigilEffectFactory>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_USigilEffectFactory_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_USigilEffectFactory_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_USigilEffectFactory_Statics::ClassParams = {
	&USigilEffectFactory::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_USigilEffectFactory_Statics::Class_MetaDataParams), Z_Construct_UClass_USigilEffectFactory_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_USigilEffectFactory()
{
	if (!Z_Registration_Info_UClass_USigilEffectFactory.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_USigilEffectFactory.OuterSingleton, Z_Construct_UClass_USigilEffectFactory_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_USigilEffectFactory.OuterSingleton;
}
USigilEffectFactory::USigilEffectFactory(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(USigilEffectFactory);
USigilEffectFactory::~USigilEffectFactory() {}
// ********** End Class USigilEffectFactory ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESpectralEffectType_StaticEnum, TEXT("ESpectralEffectType"), &Z_Registration_Info_UEnum_ESpectralEffectType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2655816111U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSpectralEffectConfig::StaticStruct, Z_Construct_UScriptStruct_FSpectralEffectConfig_Statics::NewStructOps, TEXT("SpectralEffectConfig"), &Z_Registration_Info_UScriptStruct_FSpectralEffectConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSpectralEffectConfig), 4082206754U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_USigilGameplayEffectBase, USigilGameplayEffectBase::StaticClass, TEXT("USigilGameplayEffectBase"), &Z_Registration_Info_UClass_USigilGameplayEffectBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilGameplayEffectBase), 1442679070U) },
		{ Z_Construct_UClass_USigilAutoFusionEffect, USigilAutoFusionEffect::StaticClass, TEXT("USigilAutoFusionEffect"), &Z_Registration_Info_UClass_USigilAutoFusionEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilAutoFusionEffect), 1855966142U) },
		{ Z_Construct_UClass_USigilForceFusionEffect, USigilForceFusionEffect::StaticClass, TEXT("USigilForceFusionEffect"), &Z_Registration_Info_UClass_USigilForceFusionEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilForceFusionEffect), 1449231894U) },
		{ Z_Construct_UClass_USigilSpectralPowerEffect, USigilSpectralPowerEffect::StaticClass, TEXT("USigilSpectralPowerEffect"), &Z_Registration_Info_UClass_USigilSpectralPowerEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilSpectralPowerEffect), 749371391U) },
		{ Z_Construct_UClass_USigilSpectralResilienceEffect, USigilSpectralResilienceEffect::StaticClass, TEXT("USigilSpectralResilienceEffect"), &Z_Registration_Info_UClass_USigilSpectralResilienceEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilSpectralResilienceEffect), 1449522215U) },
		{ Z_Construct_UClass_USigilSpectralVelocityEffect, USigilSpectralVelocityEffect::StaticClass, TEXT("USigilSpectralVelocityEffect"), &Z_Registration_Info_UClass_USigilSpectralVelocityEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilSpectralVelocityEffect), 2271749389U) },
		{ Z_Construct_UClass_USigilSpectralFocusEffect, USigilSpectralFocusEffect::StaticClass, TEXT("USigilSpectralFocusEffect"), &Z_Registration_Info_UClass_USigilSpectralFocusEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilSpectralFocusEffect), 1992466144U) },
		{ Z_Construct_UClass_USigilTeamFightEffect, USigilTeamFightEffect::StaticClass, TEXT("USigilTeamFightEffect"), &Z_Registration_Info_UClass_USigilTeamFightEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilTeamFightEffect), 3370733693U) },
		{ Z_Construct_UClass_USigilObjectiveEffect, USigilObjectiveEffect::StaticClass, TEXT("USigilObjectiveEffect"), &Z_Registration_Info_UClass_USigilObjectiveEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilObjectiveEffect), 1941358581U) },
		{ Z_Construct_UClass_USigilReforgeEffect, USigilReforgeEffect::StaticClass, TEXT("USigilReforgeEffect"), &Z_Registration_Info_UClass_USigilReforgeEffect, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilReforgeEffect), 1514951910U) },
		{ Z_Construct_UClass_USigilFusionExecutionCalculation, USigilFusionExecutionCalculation::StaticClass, TEXT("USigilFusionExecutionCalculation"), &Z_Registration_Info_UClass_USigilFusionExecutionCalculation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilFusionExecutionCalculation), 3533664716U) },
		{ Z_Construct_UClass_USigilSpectralExecutionCalculation, USigilSpectralExecutionCalculation::StaticClass, TEXT("USigilSpectralExecutionCalculation"), &Z_Registration_Info_UClass_USigilSpectralExecutionCalculation, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilSpectralExecutionCalculation), 3918866760U) },
		{ Z_Construct_UClass_USigilEffectFactory, USigilEffectFactory::StaticClass, TEXT("USigilEffectFactory"), &Z_Registration_Info_UClass_USigilEffectFactory, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(USigilEffectFactory), 3437145359U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_2117634931(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Sigils_SigilGameplayEffects_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
