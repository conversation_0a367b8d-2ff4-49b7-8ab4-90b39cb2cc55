{"Version": "1.2", "Data": {"Source": "c:\\auracron\\source\\auracron\\private\\pcg\\auracronmapmeasurements.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronmapmeasurements.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronmapmeasurements.generated.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcrc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcrc.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\data\\pcgdataptrwrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdataptrwrapper.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgattributepropertyselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcgpointhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgattributepropertyselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadatacommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmetadatacommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdebug.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpin.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpin.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\elements\\pcgactorselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgactorselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgpreconfiguration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadataattributetraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\defaultvaluehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmetadataattributetraits.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpreconfiguration.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\tests\\determinism\\pcgdeterminismsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdeterminismsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\allof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgvolume.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgvolume.generated.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgtypes.h", "c:\\auracron\\source\\auracron\\public\\data\\auracronenums.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronenums.generated.h", "c:\\auracron\\source\\auracron\\public\\data\\auracronstructs.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspec.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\attributeset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\attributeset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspechandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspechandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffecttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffecthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\activegameplayeffecthandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectattributecapturedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffectattributecapturedefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffecttypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayprediction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\classes\\net\\serialization\\fastarrayserializer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\misc\\guidreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\netcoremodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\netcore\\uht\\fastarrayserializer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayprediction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\scalablefloat.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\source\\dataregistry\\public\\dataregistryid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\intermediate\\build\\win64\\unrealeditor\\inc\\dataregistry\\uht\\dataregistryid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\scalablefloat.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspec.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronstructs.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgtypes.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}