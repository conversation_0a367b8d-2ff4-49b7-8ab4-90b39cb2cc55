{"Version": "1.2", "Data": {"Source": "c:\\auracron\\source\\auracron\\private\\ui\\sigilwidgets.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\source\\auracron\\public\\ui\\sigilwidgets.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\userwidget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\umgsequenceplaymode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\umgsequenceplaymode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetchild.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetchild.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsavecontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\cookenums.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsaveoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\cooker\\cookdependency.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\slatewrappertypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\slatewrappertypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\widget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\binding\\states\\widgetstatebitfield.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetstatebitfield.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\fieldnotification\\public\\fieldnotificationdeclaration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\fieldnotification\\public\\ifieldnotificationclassdescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\fieldnotification\\public\\inotifyfieldvaluechanged.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\fieldnotification\\uht\\inotifyfieldvaluechanged.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\visual.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\visual.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\slate\\widgettransform.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgettransform.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetnavigation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slatecore\\public\\types\\navigationmetadata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetnavigation.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\namedslotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\namedslotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\layout\\anchors.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\slate\\uht\\anchors.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationevents.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetanimationevents.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetanimationhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\umgsequencetickmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenelatentactionmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\umgsequencetickmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneplayback.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenesequencetransform.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenefwd.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenefwd.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenetimetransform.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenetimetransform.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenetimewarping.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenetimewarping.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\variants\\moviescenetimewarpvariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\variants\\moviescenenumericvariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenenumericvariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenetimewarpvariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenesequencetransform.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenetimehelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneplaybackmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviescenesharedplaybackstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\compilation\\moviescenecompileddataid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviescenesequenceinstancehandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviescenecomponentdebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviesceneentitysystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviesceneentityids.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\inlinevalue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneevaluationoperand.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenesequenceid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenesequenceid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviesceneevaluationoperand.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneplaybackcapabilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\imoviesceneplaybackcapability.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\relativeptr.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenepreanimatedstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\userwidget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\dragdropoperation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\dragdropoperation.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\image.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\slate\\slatetextureatlasinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\slatetextureatlasinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\iostoreondemand.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\sharedstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandhostgroup.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandtoc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\experimental\\streamablemanagererror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\image.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\textblock.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\textwidgettypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\textwidgettypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\textblock.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\progressbar.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\notifications\\sprogressbar.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\slate\\uht\\sprogressbar.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\progressbar.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\button.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\contentwidget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\panelwidget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\panelslot.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\panelslot.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\panelwidget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\contentwidget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\button.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\border.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\border.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\canvaspanel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\layout\\sconstraintcanvas.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\canvaspanel.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\horizontalbox.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\horizontalbox.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\verticalbox.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\verticalbox.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\overlay.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\overlay.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\auracron\\source\\auracron\\public\\sigils\\sigilitem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysysteminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysysteminterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\attributeset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\attributeset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayprediction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\classes\\net\\serialization\\fastarrayserializer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\misc\\guidreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\netcoremodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\netcore\\uht\\fastarrayserializer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayprediction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplaycueinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffecttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffecthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\activegameplayeffecthandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectattributecapturedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffectattributecapturedefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffecttypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplaycueinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagassetinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagassetinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspec.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspechandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspechandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\scalablefloat.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\source\\dataregistry\\public\\dataregistryid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\intermediate\\build\\win64\\unrealeditor\\inc\\dataregistry\\uht\\dataregistryid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\scalablefloat.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspec.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffect.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectaggregator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffectiterator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffect.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytasks\\classes\\gameplaytaskscomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytasks\\classes\\gameplaytaskresource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytasks\\uht\\gameplaytaskresource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytasks\\uht\\gameplaytaskscomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilityrepanimmontage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityrepanimmontage.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilitytargettypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilitytargettypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayability.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilitytypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilitytypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayability.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysystemreplicationproxyinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysystemreplicationproxyinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\public\\gameplaytags.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagsmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagarasystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraassettagdefinitions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraassettagdefinitions.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetcompileddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstats.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpoolmethodenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponentpoolmethodenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaradefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarascalabilitystate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarascalabilitystate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratickbehaviorenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaratickbehaviorenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagaracore\\uht\\niagaracore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaratypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratyperegistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaradatasetcompileddata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetaccessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraeffecttype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ingameperformancetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraplatformset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraplatformset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraperfbaseline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstatsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraperfbaseline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationrule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravalidationrule.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationruleset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravalidationruleset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraeffecttype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraemitterhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraemitterhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaramessagestore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaramessagestore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparametercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracompilehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagaracore\\uht\\niagaracompilehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparametercollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparameterdefinitionssubscriber.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterdefinitionsdelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparameterdefinitionssubscriber.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarauserredirectionparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarauserredirectionparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\fxbudget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarasystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaravariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\emitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\emitter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\particlesystemstatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemstatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\unrealnetwork.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\propertyconditions\\propertyconditions.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\sigilitem.generated.h", "c:\\auracron\\source\\auracron\\public\\sigils\\sigilmanagercomponent.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\sigilmanagercomponent.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\sigilwidgets.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetblueprintlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetblueprintlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\canvaspanelslot.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\canvaspanelslot.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\horizontalboxslot.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\horizontalboxslot.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\verticalboxslot.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\verticalboxslot.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarafunctionlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpool.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponentpool.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\vectorvm\\public\\vectorvm.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\vectorvm\\uht\\vectorvm.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarafunctionlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialparametercollectioninstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialparametercollectioninstance.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}