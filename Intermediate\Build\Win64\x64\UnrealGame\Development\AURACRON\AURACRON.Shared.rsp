/I "."
/I "Runtime/Net/Core/Private"
/I "C:/AURACRON/Source/AURACRON/Private"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/NetCore/UHT"
/I "Runtime/Net/Core/Classes"
/I "Runtime/Net/Core/Public"
/I "Runtime/Core/Public"
/I "Runtime/TraceLog/Public"
/I "Runtime/AutoRTFM/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ImageCore/UHT"
/I "Runtime/ImageCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/CoreUObject/UHT"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/CoreUObject/VerseVMBytecode"
/I "Runtime/CoreUObject/Public"
/I "Runtime/CorePreciseFP/Public"
/I "Runtime/Net/Common/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/IrisCore/UHT"
/I "Runtime/Experimental/Iris/Core/Public"
/I "../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/UnrealGame/Inc/GameplayAbilities/UHT"
/I "../Plugins/Runtime/GameplayAbilities/Source"
/I "../Plugins/Runtime/GameplayAbilities/Source/GameplayAbilities/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Engine/UHT"
/I "Runtime/Engine/Classes"
/I "Runtime/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/CoreOnline/UHT"
/I "Runtime/CoreOnline/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/FieldNotification/UHT"
/I "Runtime/FieldNotification/Public"
/I "Runtime/Json/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/JsonUtilities/UHT"
/I "Runtime/JsonUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/SlateCore/UHT"
/I "Runtime/SlateCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/DeveloperSettings/UHT"
/I "Runtime/DeveloperSettings/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/InputCore/UHT"
/I "Runtime/InputCore/Classes"
/I "Runtime/InputCore/Public"
/I "Runtime/ApplicationCore/Public"
/I "Runtime/RHI/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Slate/UHT"
/I "Runtime/Slate/Public"
/I "Runtime/ImageWrapper/Public"
/I "Runtime/Messaging/Public"
/I "Runtime/MessagingCommon/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/RenderCore/UHT"
/I "Runtime/RenderCore/Public"
/I "Runtime/OpenGLDrv/Public"
/I "Runtime/Analytics/AnalyticsET/Public"
/I "Runtime/Analytics/Analytics/Public"
/I "Runtime/Sockets/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AssetRegistry/UHT"
/I "Runtime/AssetRegistry/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/EngineMessages/UHT"
/I "Runtime/EngineMessages/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/EngineSettings/UHT"
/I "Runtime/EngineSettings/Classes"
/I "Runtime/EngineSettings/Public"
/I "Runtime/SynthBenchmark/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/GameplayTags/UHT"
/I "Runtime/GameplayTags/Classes"
/I "Runtime/GameplayTags/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/PacketHandler/UHT"
/I "Runtime/PacketHandlers/PacketHandler/Classes"
/I "Runtime/PacketHandlers/PacketHandler/Public"
/I "Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioPlatformConfiguration/UHT"
/I "Runtime/AudioPlatformConfiguration/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/MeshDescription/UHT"
/I "Runtime/MeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/StaticMeshDescription/UHT"
/I "Runtime/StaticMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/SkeletalMeshDescription/UHT"
/I "Runtime/SkeletalMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AnimationCore/UHT"
/I "Runtime/AnimationCore/Public"
/I "Runtime/PakFile/Public"
/I "Runtime/RSA/Public"
/I "Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/PhysicsCore/UHT"
/I "Runtime/PhysicsCore/Public"
/I "Runtime/Experimental/ChaosCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Chaos/UHT"
/I "Runtime/Experimental/Chaos/Public"
/I "Runtime/Experimental/Voronoi/Public"
/I "Runtime/GeometryCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ChaosVDRuntime/UHT"
/I "Runtime/Experimental/ChaosVisualDebugger/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/NNE/UHT"
/I "Runtime/NNE/Public"
/I "Runtime/SignalProcessing/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/StateStream/UHT"
/I "Runtime/StateStream/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioExtensions/UHT"
/I "Runtime/AudioExtensions/Public"
/I "Runtime/AudioMixerCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioMixer/UHT"
/I "Runtime/AudioMixer/Classes"
/I "Runtime/AudioMixer/Public"
/I "Developer/TargetPlatform/Public"
/I "Developer/TextureFormat/Public"
/I "Developer/DesktopPlatform/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioLinkEngine/UHT"
/I "Runtime/AudioLink/AudioLinkEngine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioLinkCore/UHT"
/I "Runtime/AudioLink/AudioLinkCore/Public"
/I "Runtime/Networking/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ClothSysRuntimeIntrfc/UHT"
/I "Runtime/ClothingSystemRuntimeInterface/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/MovieSceneCapture/UHT"
/I "Runtime/MovieSceneCapture/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Renderer/UHT"
/I "Runtime/Renderer/Public"
/I "../Shaders/Public"
/I "../Shaders/Shared"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/TypedElementFramework/UHT"
/I "Runtime/TypedElementFramework/Tests"
/I "Runtime/TypedElementFramework/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/TypedElementRuntime/UHT"
/I "Runtime/TypedElementRuntime/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/GameplayTasks/UHT"
/I "Runtime/GameplayTasks/Classes"
/I "Runtime/GameplayTasks/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/MovieScene/UHT"
/I "Runtime/MovieScene/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/TimeManagement/UHT"
/I "Runtime/TimeManagement/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/UniversalObjectLocator/UHT"
/I "Runtime/UniversalObjectLocator/Public"
/I "../Plugins/Runtime/DataRegistry/Intermediate/Build/Win64/UnrealGame/Inc/DataRegistry/UHT"
/I "../Plugins/Runtime/DataRegistry/Source"
/I "../Plugins/Runtime/DataRegistry/Source/DataRegistry/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/UMG/UHT"
/I "Runtime/UMG/Public"
/I "Runtime/Online/HTTP/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/MovieSceneTracks/UHT"
/I "Runtime/MovieSceneTracks/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Constraints/UHT"
/I "Runtime/Experimental/Animation/Constraints/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/PropertyPath/UHT"
/I "Runtime/PropertyPath/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealGame/Inc/Niagara/UHT"
/I "../Plugins/FX/Niagara/Source"
/I "../Plugins/FX/Niagara/Source/Niagara/Classes"
/I "../Plugins/FX/Niagara/Source/Niagara/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealGame/Inc/NiagaraCore/UHT"
/I "../Plugins/FX/Niagara/Source/NiagaraCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/VectorVM/UHT"
/I "Runtime/VectorVM/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealGame/Inc/NiagaraShader/UHT"
/I "../Plugins/FX/Niagara/Shaders/Shared"
/I "../Plugins/FX/Niagara/Source/NiagaraShader/Public"
/I "Runtime/Projects/Public"
/I "../Plugins/FX/Niagara/Source/NiagaraVertexFactories/Public"
/I "../Plugins/Runtime/ReplicationGraph/Intermediate/Build/Win64/UnrealGame/Inc/ReplicationGraph/UHT"
/I "../Plugins/Runtime/ReplicationGraph/Source"
/I "../Plugins/Runtime/ReplicationGraph/Source/Public"
/I "../Plugins/Online/OnlineSubsystem/Intermediate/Build/Win64/UnrealGame/Inc/OnlineSubsystem/UHT"
/I "../Plugins/Online/OnlineSubsystem/Source/Test"
/I "../Plugins/Online/OnlineSubsystem/Source"
/I "../Plugins/Online/OnlineSubsystem/Source/Public"
/I "../Plugins/Online/OnlineBase/Source"
/I "../Plugins/Online/OnlineBase/Source/Public"
/I "../Plugins/Runtime/NetworkPrediction/Intermediate/Build/Win64/UnrealGame/Inc/NetworkPrediction/UHT"
/I "../Plugins/Runtime/NetworkPrediction/Source"
/I "../Plugins/Runtime/NetworkPrediction/Source/NetworkPrediction/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ToolMenus/UHT"
/I "Developer/ToolMenus/Public"
/I "C:/AURACRON/Intermediate/Build/Win64/UnrealGame/Inc/AURACRON/UHT"
/I "C:/AURACRON/Source/AURACRON/Public"
/I "C:/AURACRON/Source/AURACRON/Public/Sigils"
/I "C:/AURACRON/Source/AURACRON/Public/UI"
/I "C:/AURACRON/Source/AURACRON/Public/Effects"
/I "C:/AURACRON/Source/AURACRON/Public/Data"
/I "C:/AURACRON/Source"
/I "../Plugins/EnhancedInput/Intermediate/Build/Win64/UnrealGame/Inc/EnhancedInput/UHT"
/I "../Plugins/EnhancedInput/Source"
/I "../Plugins/EnhancedInput/Source/EnhancedInput/Public"
/I "../Plugins/PCG/Intermediate/Build/Win64/UnrealGame/Inc/PCG/UHT"
/I "../Plugins/PCG/Source"
/I "../Plugins/PCG/Source/PCG/Public"
/I "../Plugins/Runtime/ComputeFramework/Intermediate/Build/Win64/UnrealGame/Inc/ComputeFramework/UHT"
/I "../Plugins/Runtime/ComputeFramework/Source"
/I "../Plugins/Runtime/ComputeFramework/Source/ComputeFramework/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Landscape/UHT"
/I "Runtime/Landscape/Classes"
/I "Runtime/Landscape/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Foliage/UHT"
/I "Runtime/Foliage/Public"
/I "../Plugins/PCG/Source/PCGCompute/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/NavigationSystem/UHT"
/I "Runtime/NavigationSystem/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/GeometryCollectionEngine/UHT"
/I "Runtime/Experimental/GeometryCollectionEngine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ChaosSolverEngine/UHT"
/I "Runtime/Experimental/ChaosSolverEngine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/DataflowCore/UHT"
/I "Runtime/Experimental/Dataflow/Core/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/DataflowEngine/UHT"
/I "Runtime/Experimental/Dataflow/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/DataflowSimulation/UHT"
/I "Runtime/Experimental/Dataflow/Simulation/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/FieldSystemEngine/UHT"
/I "Runtime/Experimental/FieldSystem/Source/FieldSystemEngine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ISMPool/UHT"
/I "Runtime/Experimental/ISMPool/Public"
/I "C:/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine"
/external:W0
/external:I "ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
/external:I "ThirdParty/AtomicQueue"
/external:I "ThirdParty/RapidJSON/1.1.0"
/external:I "ThirdParty/LibTiff/Source/Win64"
/external:I "ThirdParty/LibTiff/Source"
/external:I "ThirdParty/OpenGL"
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"