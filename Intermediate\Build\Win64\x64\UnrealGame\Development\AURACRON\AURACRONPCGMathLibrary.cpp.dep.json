{"Version": "1.2", "Data": {"Source": "c:\\auracron\\source\\auracron\\private\\pcg\\auracronpcgmathlibrary.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracron\\development\\engine\\sharedpch.engine.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgmathlibrary.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronmapmeasurements.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronmapmeasurements.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcgmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}