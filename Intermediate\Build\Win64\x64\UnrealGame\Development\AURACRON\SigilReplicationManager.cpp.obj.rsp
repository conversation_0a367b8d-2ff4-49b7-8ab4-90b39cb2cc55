"C:/AURACRON/Source/AURACRON/Private/Multiplayer/SigilReplicationManager.cpp"
@"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealGame/Development/AURACRON/AURACRON.Shared.rsp"
/FI"C:/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h"
/FI"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealGame/Development/AURACRON/Definitions.AURACRON.h"
/Yu"C:/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h"
/Fp"C:/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h.pch"
/Fo"C:/AURACRON/Intermediate/Build/Win64/x64/UnrealGame/Development/AURACRON/SigilReplicationManager.cpp.obj"
/experimental:log "C:/AURACRON/Intermediate/Build/Win64/x64/UnrealGame/Development/AURACRON/SigilReplicationManager.cpp.sarif"
/sourceDependencies "C:/AURACRON/Intermediate/Build/Win64/x64/UnrealGame/Development/AURACRON/SigilReplicationManager.cpp.dep.json"
/Zc:inline
/nologo
/Oi
/FC
/diagnostics:caret
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/D_HAS_EXCEPTIONS=0
/DPLATFORM_EXCEPTIONS_DISABLED=1
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/W4
/we4456
/we4458
/we4459
/wd4244
/wd4838
/TP
/GR-
/std:c++20
/Zc:preprocessor
/wd5054