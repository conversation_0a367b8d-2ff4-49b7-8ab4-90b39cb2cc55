﻿Log file open, 09/03/25 01:01:12
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=13864)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.5074] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:\AURACRON\AURACRON.uproject""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.751543
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-57E1FB9E47211CE9FFD8E0927CEE699B
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../AURACRON/Binaries/Win64/*.target
LogAssetRegistry: Display: No AssetDiscovery cache present at ../../../../../../AURACRON/Intermediate/CachedAssetRegistryDiscovery.bin. AssetRegistry discovery of files will be uncached.
LogConfig: Display: Loading VulkanPC ini files took 0.07 seconds
LogConfig: Display: Loading Mac ini files took 0.07 seconds
LogConfig: Display: Loading Windows ini files took 0.08 seconds
LogConfig: Display: Loading Unix ini files took 0.08 seconds
LogConfig: Display: Loading Linux ini files took 0.09 seconds
LogConfig: Display: Loading Android ini files took 0.09 seconds
LogConfig: Display: Loading IOS ini files took 0.09 seconds
LogConfig: Display: Loading TVOS ini files took 0.09 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../AURACRON/Binaries/Win64/*.target
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin Iris
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin ObjectMixer
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
Launching UnrealBuildTool... [C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/Build.bat -Mode=QueryTargets -Project="C:/AURACRON/AURACRON.uproject" -Output="C:/AURACRON/Intermediate/TargetInfo.json" -IncludeAllTargets -DontIncludeParentAssembly]
Running C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/Build.bat Development Win64 -Project="C:/AURACRON/AURACRON.uproject" -TargetType=Editor -Progress -NoEngineChanges -NoHotReloadFromIDE
Using bundled DotNet SDK version: 8.0.300 win-x64
Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" Development Win64 -Project="C:/AURACRON/AURACRON.uproject" -TargetType=Editor -Progress -NoEngineChanges -NoHotReloadFromIDE
Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
Available x64 toolchains (1):
 * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
    (Family=14.44.35207, FamilyRank=1, Version=14.44.35215, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
Visual Studio 2022 compiler version 14.44.35215 is not a preferred version. Please use the latest preferred version 14.38.33130
Creating makefile for AURACRONEditor (no existing makefile)
@progress push 5%
Parsing headers for AURACRONEditor
  Running Internal UnrealHeaderTool C:\AURACRON\AURACRON.uproject C:\AURACRON\Intermediate\Build\Win64\AURACRONEditor\Development\AURACRONEditor.uhtmanifest -WarningsAsErrors -installed
Total of 22 written
Reflection code generated for AURACRONEditor in 1.9268141 seconds
@progress pop
Building AURACRONEditor...
Using Visual Studio 2022 14.44.35215 toolchain (C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207) and Windows 10.0.26100.0 SDK (C:\Program Files (x86)\Windows Kits\10).
Warning: Visual Studio 2022 compiler is not a preferred version
Determining max actions to execute in parallel (10 physical cores, 12 logical cores)
  Executing up to 10 processes, one per physical core
  Requested 1.5 GB memory per action, 12.68 GB available: limiting max parallel actions to 8
Using Unreal Build Accelerator local executor to run 21 action(s)
  Storage capacity 40Gb
---- Starting trace: 250903_010128 ----
UbaServer - Listening on 0.0.0.0:1345
------ Building 21 action(s) started ------
[1/21] Resource Default.rc2
[2/21] Compile [x64] SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.cpp
[3/21] Compile [x64] PerModuleInline.gen.cpp
[4/21] Compile [x64] AURACRON.cpp
C:\AURACRON\Source\AURACRON\Private\AURACRON.cpp(12,1): error C2061: erro de sintaxe: identificador 'FAURACRONModule'
IMPLEMENT_PRIMARY_GAME_MODULE(FAURACRONModule, AURACRON, "AURACRON");
^
C:\AURACRON\Source\AURACRON\Private\AURACRON.cpp(15,7): error C4273: 'GInternalProjectName': v?nculo de dll inconsistente
TCHAR GInternalProjectName[64] = TEXT("AURACRON");
      ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\CoreGlobals.h(456,23): note: ver a defini?? anterior de "GInternalProjectName"
extern CORE_API TCHAR GInternalProjectName[64];
                      ^
C:\AURACRON\Source\AURACRON\Private\AURACRON.cpp(16,20): error C2373: 'GForeignEngineDir': redefini??; modificadores de tipo diferentes
const TCHAR* const GForeignEngineDir = TEXT("");
                   ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\CoreGlobals.h(457,30): note: consulte a declara?? de 'GForeignEngineDir'
extern CORE_API const TCHAR* GForeignEngineDir;
                             ^
C:\AURACRON\Source\AURACRON\Private\AURACRON.cpp(17,6): error C4273: 'GIsGameAgnosticExe': v?nculo de dll inconsistente
bool GIsGameAgnosticExe = false;
     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\CoreGlobals.h(145,22): note: ver a defini?? anterior de "GIsGameAgnosticExe"
extern CORE_API bool GIsGameAgnosticExe;
                     ^
[5/21] Compile [x64] SigilAttributeSet.cpp
[6/21] Compile [x64] Module.AURACRON.gen.2.cpp
[7/21] Compile [x64] Module.AURACRON.gen.1.cpp
[8/21] Compile [x64] SigilGameplayEffects.cpp
[9/21] Compile [x64] SigilNetworkConfig.cpp
[10/21] Compile [x64] SigilFusionSystem.cpp
[11/21] Compile [x64] Module.AURACRON.gen.4.cpp
[12/21] Compile [x64] SigilItem.cpp
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(28,5): warning C4996: 'AActor::NetUpdateFrequency': Public access to NetUpdateFrequency has been deprecated. Use SetNetUpdateFrequency() and GetNetUpdateFrequency() instead. - Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    NetUpdateFrequency = 10.0f;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(29,5): warning C4996: 'AActor::MinNetUpdateFrequency': Public access MinNetUpdateFrequency has been deprecated. Use SetMinNetUpdateFrequency() and GetMinNetUpdateFrequency() instead. - Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    MinNetUpdateFrequency = 2.0f;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(42,5): error C2065: 'CollisionComponent': identificador n? declarado
    CollisionComponent = CreateDefaultSubobject<USphereComponent>(TEXT("CollisionComponent"));
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(43,5): error C2065: 'CollisionComponent': identificador n? declarado
    CollisionComponent->SetupAttachment(RootComponent);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(44,5): error C2065: 'CollisionComponent': identificador n? declarado
    CollisionComponent->SetSphereRadius(50.0f);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(45,5): error C2065: 'CollisionComponent': identificador n? declarado
    CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(46,5): error C2065: 'CollisionComponent': identificador n? declarado
    CollisionComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(47,5): error C2065: 'CollisionComponent': identificador n? declarado
    CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(60,5): error C2065: 'OwningActor': identificador n? declarado
    OwningActor = nullptr;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(62,5): error C2065: 'bIsEquipped': identificador n? declarado
    bIsEquipped = false;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(63,5): error C2065: 'bIsFused': identificador n? declarado
    bIsFused = false;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(64,5): error C2065: 'LastReforgeTime': identificador n? declarado
    LastReforgeTime = 0.0f;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(68,15): error C2039: ' SigilRarity': n? ? um membro de 'FSigilData'
    SigilData.SigilRarity = ESigilRarity::Common;
              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(69,15): error C2039: ' SigilState': n? ? um membro de 'FSigilData'
    SigilData.SigilState = ESigilState::Unequipped;
              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(84,17): error C2065: 'bIsEquipped': identificador n? declarado
            if (bIsEquipped)
                ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(95,13): error C2065: 'ValidationTimerHandle': identificador n? declarado
            ValidationTimerHandle,
            ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(98,17): error C3861: 'ValidateSigilState': identificador n? encontrado
                ValidateSigilState();
                ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(114,50): error C2065: 'ValidationTimerHandle': identificador n? declarado
        GetWorld()->GetTimerManager().ClearTimer(ValidationTimerHandle);
                                                 ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(117,13): error C2065: 'bIsEquipped': identificador n? declarado
        if (bIsEquipped && AbilitySystemComponent)
            ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(133,101): error C2039: ' OnGameplayEffectApplied': n? ? um membro de 'ASigilItem'
        AbilitySystemComponent->OnGameplayEffectAppliedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectApplied);
                                                                                                    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(188,20): note: consulte a declara?? de 'ASigilItem'
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(133,101): error C2065: 'OnGameplayEffectApplied': identificador n? declarado
        AbilitySystemComponent->OnGameplayEffectAppliedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectApplied);
                                                                                                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(134,33): error C2039: ' OnGameplayEffectRemovedDelegateToSelf': n? ? um membro de 'UAbilitySystemComponent'
        AbilitySystemComponent->OnGameplayEffectRemovedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectRemoved);
                                ^
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GameplayAbilities\Source\GameplayAbilities\Public\AbilitySystemComponent.h(109,7): note: consulte a declara?? de 'UAbilitySystemComponent'
class UAbilitySystemComponent : public UGameplayTasksComponent, public IGameplayTagAssetInterface, public IAbilitySystemReplicationProxyInterface
      ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(134,101): error C2039: ' OnGameplayEffectRemoved': n? ? um membro de 'ASigilItem'
        AbilitySystemComponent->OnGameplayEffectRemovedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectRemoved);
                                                                                                    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(188,20): note: consulte a declara?? de 'ASigilItem'
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(134,101): error C2065: 'OnGameplayEffectRemoved': identificador n? declarado
        AbilitySystemComponent->OnGameplayEffectRemovedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectRemoved);
                                                                                                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(144,5): error C2039: ' OwningActor': n? ? um membro de 'ASigilItem'
    DOREPLIFETIME(ASigilItem, OwningActor);
    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(188,20): note: consulte a declara?? de 'ASigilItem'
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(144,5): error C2672: 'UEAsserts_Private::GetMemberNameCheckedJunk': nenhuma fun?? sobrecarregada correspondente encontrada
    DOREPLIFETIME(ASigilItem, OwningActor);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(486,7): note: poderia ser 'bool UEAsserts_Private::GetMemberNameCheckedJunk(R (__cdecl *)(Args...))'
	bool GetMemberNameCheckedJunk(R(*)(Args...));
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(484,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(volatile const T &)'
	bool GetMemberNameCheckedJunk(const volatile T&);
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(482,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(const T &)'
	bool GetMemberNameCheckedJunk(const T&);
	     ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(144,5): error C2660: 'GetReplicatedProperty': fun?? n? recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, OwningActor);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Public\Net\UnrealNetwork.h(221,19): note: consulte a declara?? de 'GetReplicatedProperty'
inline FProperty* GetReplicatedProperty(const UClass* CallingClass, const UClass* PropClass, const FName& PropName)
                  ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(144,5): note: ao tentar corresponder a lista de argumentos '(UClass *, UClass *)'
    DOREPLIFETIME(ASigilItem, OwningActor);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(144,5): error C2661: 'RegisterReplicatedLifetimeProperty': nenhuma fun?? sobrecarregada recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, OwningActor);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(144,5): note: ao tentar corresponder a lista de argumentos '(FProperty *, TArray<FLifetimeProperty,FDefaultAllocator>)'
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(146,5): error C2039: ' bIsEquipped': n? ? um membro de 'ASigilItem'
    DOREPLIFETIME(ASigilItem, bIsEquipped);
    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(188,20): note: consulte a declara?? de 'ASigilItem'
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(146,5): error C2672: 'UEAsserts_Private::GetMemberNameCheckedJunk': nenhuma fun?? sobrecarregada correspondente encontrada
    DOREPLIFETIME(ASigilItem, bIsEquipped);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(486,7): note: poderia ser 'bool UEAsserts_Private::GetMemberNameCheckedJunk(R (__cdecl *)(Args...))'
	bool GetMemberNameCheckedJunk(R(*)(Args...));
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(484,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(volatile const T &)'
	bool GetMemberNameCheckedJunk(const volatile T&);
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(482,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(const T &)'
	bool GetMemberNameCheckedJunk(const T&);
	     ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(146,5): error C2660: 'GetReplicatedProperty': fun?? n? recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, bIsEquipped);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Public\Net\UnrealNetwork.h(221,19): note: consulte a declara?? de 'GetReplicatedProperty'
inline FProperty* GetReplicatedProperty(const UClass* CallingClass, const UClass* PropClass, const FName& PropName)
                  ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(146,5): note: ao tentar corresponder a lista de argumentos '(UClass *, UClass *)'
    DOREPLIFETIME(ASigilItem, bIsEquipped);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(146,5): error C2661: 'RegisterReplicatedLifetimeProperty': nenhuma fun?? sobrecarregada recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, bIsEquipped);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(146,5): note: ao tentar corresponder a lista de argumentos '(FProperty *, TArray<FLifetimeProperty,FDefaultAllocator>)'
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(147,5): error C2039: ' bIsFused': n? ? um membro de 'ASigilItem'
    DOREPLIFETIME(ASigilItem, bIsFused);
    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(188,20): note: consulte a declara?? de 'ASigilItem'
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(147,5): error C2672: 'UEAsserts_Private::GetMemberNameCheckedJunk': nenhuma fun?? sobrecarregada correspondente encontrada
    DOREPLIFETIME(ASigilItem, bIsFused);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(486,7): note: poderia ser 'bool UEAsserts_Private::GetMemberNameCheckedJunk(R (__cdecl *)(Args...))'
	bool GetMemberNameCheckedJunk(R(*)(Args...));
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(484,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(volatile const T &)'
	bool GetMemberNameCheckedJunk(const volatile T&);
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(482,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(const T &)'
	bool GetMemberNameCheckedJunk(const T&);
	     ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(147,5): error C2660: 'GetReplicatedProperty': fun?? n? recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, bIsFused);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Public\Net\UnrealNetwork.h(221,19): note: consulte a declara?? de 'GetReplicatedProperty'
inline FProperty* GetReplicatedProperty(const UClass* CallingClass, const UClass* PropClass, const FName& PropName)
                  ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(147,5): note: ao tentar corresponder a lista de argumentos '(UClass *, UClass *)'
    DOREPLIFETIME(ASigilItem, bIsFused);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(147,5): error C2661: 'RegisterReplicatedLifetimeProperty': nenhuma fun?? sobrecarregada recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, bIsFused);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(147,5): note: ao tentar corresponder a lista de argumentos '(FProperty *, TArray<FLifetimeProperty,FDefaultAllocator>)'
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(148,5): error C2039: ' LastReforgeTime': n? ? um membro de 'ASigilItem'
    DOREPLIFETIME(ASigilItem, LastReforgeTime);
    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(188,20): note: consulte a declara?? de 'ASigilItem'
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(148,5): error C2672: 'UEAsserts_Private::GetMemberNameCheckedJunk': nenhuma fun?? sobrecarregada correspondente encontrada
    DOREPLIFETIME(ASigilItem, LastReforgeTime);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(486,7): note: poderia ser 'bool UEAsserts_Private::GetMemberNameCheckedJunk(R (__cdecl *)(Args...))'
	bool GetMemberNameCheckedJunk(R(*)(Args...));
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(484,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(volatile const T &)'
	bool GetMemberNameCheckedJunk(const volatile T&);
	     ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public\Misc\AssertionMacros.h(482,7): note: ou       'bool UEAsserts_Private::GetMemberNameCheckedJunk(const T &)'
	bool GetMemberNameCheckedJunk(const T&);
	     ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(148,5): error C2660: 'GetReplicatedProperty': fun?? n? recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, LastReforgeTime);
    ^
C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Public\Net\UnrealNetwork.h(221,19): note: consulte a declara?? de 'GetReplicatedProperty'
inline FProperty* GetReplicatedProperty(const UClass* CallingClass, const UClass* PropClass, const FName& PropName)
                  ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(148,5): note: ao tentar corresponder a lista de argumentos '(UClass *, UClass *)'
    DOREPLIFETIME(ASigilItem, LastReforgeTime);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(148,5): error C2661: 'RegisterReplicatedLifetimeProperty': nenhuma fun?? sobrecarregada recebe 2 argumentos
    DOREPLIFETIME(ASigilItem, LastReforgeTime);
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(148,5): note: ao tentar corresponder a lista de argumentos '(FProperty *, TArray<FLifetimeProperty,FDefaultAllocator>)'
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(179,28): error C2039: ' IsSlotAvailable': n? ? um membro de 'USigilManagerComponent'
        if (!SigilManager->IsSlotAvailable(TargetSlotIndex))
                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(185,13): error C2065: 'bIsEquipped': identificador n? declarado
        if (bIsEquipped && OwningActor)
            ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(185,28): error C2065: 'OwningActor': identificador n? declarado
        if (bIsEquipped && OwningActor)
                           ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(191,9): error C2065: 'OwningActor': identificador n? declarado
        OwningActor = TargetActor;
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(193,9): error C2065: 'bIsEquipped': identificador n? declarado
        bIsEquipped = true;
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(194,19): error C2039: ' SigilState': n? ? um membro de 'FSigilData'
        SigilData.SigilState = ESigilState::Equipped;
                  ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(200,23): error C2039: ' OnSigilEquipped': n? ? um membro de 'USigilManagerComponent'
        SigilManager->OnSigilEquipped(this, TargetSlotIndex);
                      ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(234,9): error C2065: 'OwningActor': identificador n? declarado
    if (OwningActor)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(236,52): error C2065: 'OwningActor': identificador n? declarado
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
                                                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(238,27): error C2039: ' OnSigilUnequipped': n? ? um membro de 'USigilManagerComponent'
            SigilManager->OnSigilUnequipped(this, SlotIndex);
                          ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(243,5): error C2065: 'OwningActor': identificador n? declarado
    OwningActor = nullptr;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(245,5): error C2065: 'bIsEquipped': identificador n? declarado
    bIsEquipped = false;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(246,5): error C2065: 'bIsFused': identificador n? declarado
    bIsFused = false;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(247,15): error C2039: ' SigilState': n? ? um membro de 'FSigilData'
    SigilData.SigilState = ESigilState::Unequipped;
              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(272,24): error C2039: ' HasAvailableSlots': n? ? um membro de 'USigilManagerComponent'
    if (!SigilManager->HasAvailableSlots())
                       ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(278,19): error C2039: ' RequiredLevel': n? ? um membro de 'FSigilData'
    if (SigilData.RequiredLevel > 1)
                  ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(290,9): error C2065: 'bIsFused': identificador n? declarado
    if (bIsFused)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(297,50): error C2065: 'LastReforgeTime': identificador n? declarado
    const float TimeSinceReforge = CurrentTime - LastReforgeTime;
                                                 ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(297,17): error C2737: 'TimeSinceReforge': objetoconst deve ser inicializado
    const float TimeSinceReforge = CurrentTime - LastReforgeTime;
                ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(303,12): error C2065: 'bIsEquipped': identificador n? declarado
    return bIsEquipped;
           ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(324,10): error C2065: 'OwningActor': identificador n? declarado
    if (!OwningActor)
         ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(329,44): error C2065: 'OwningActor': identificador n? declarado
    USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>();
                                           ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(336,23): error C2039: ' TriggerFusionForSigil': n? ? um membro de 'USigilManagerComponent'
    if (SigilManager->TriggerFusionForSigil(this))
                      ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(338,9): error C2065: 'bIsFused': identificador n? declarado
        bIsFused = true;
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(339,19): error C2039: ' SigilState': n? ? um membro de 'FSigilData'
        SigilData.SigilState = ESigilState::Fused;
                  ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(356,10): error C2065: 'bIsEquipped': identificador n? declarado
    if (!bIsEquipped || bIsFused)
         ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(356,25): error C2065: 'bIsFused': identificador n? declarado
    if (!bIsEquipped || bIsFused)
                        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(362,19): error C2039: ' CurrentLevel': n? ? um membro de 'FSigilData'
    if (SigilData.CurrentLevel < 2)
                  ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(368,9): error C2065: 'OwningActor': identificador n? declarado
    if (OwningActor)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(370,52): error C2065: 'OwningActor': identificador n? declarado
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
                                                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(372,34): error C2039: ' HasCompatibleSigilsForFusion': n? ? um membro de 'USigilManagerComponent'
            return SigilManager->HasCompatibleSigilsForFusion(this);
                                 ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(396,27): error C2039: ' CurrentLevel': n? ? um membro de 'FSigilData'
                SigilData.CurrentLevel,
                          ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(394,76): error C2660: 'UAbilitySystemComponent::MakeOutgoingSpec': fun?? n? recebe 2 argumentos
            FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
                                                                           ^
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GameplayAbilities\Source\GameplayAbilities\Public\AbilitySystemComponent.h(363,43): note: consulte a declara?? de 'UAbilitySystemComponent::MakeOutgoingSpec'
	UE_API virtual FGameplayEffectSpecHandle MakeOutgoingSpec(TSubclassOf<UGameplayEffect> GameplayEffectClass, float Level, FGameplayEffectContextHandle Context) const;
	                                         ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(394,76): note: ao tentar corresponder a lista de argumentos '(const TSubclassOf<UGameplayEffect>, FGameplayEffectContextHandle)'
            FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
                                                                           ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(428,5): error C2065: 'bIsFused': identificador n? declarado
    bIsFused = false;
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(430,9): error C2065: 'bIsEquipped': identificador n? declarado
    if (bIsEquipped)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(432,19): error C2039: ' SigilState': n? ? um membro de 'FSigilData'
        SigilData.SigilState = ESigilState::Equipped;
                  ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(436,19): error C2039: ' SigilState': n? ? um membro de 'FSigilData'
        SigilData.SigilState = ESigilState::Unequipped;
                  ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(460,9): error C2065: 'OwningActor': identificador n? declarado
    if (OwningActor)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(462,52): error C2065: 'OwningActor': identificador n? declarado
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
                                                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(464,32): error C2039: ' CanAffordReforge': n? ? um membro de 'USigilManagerComponent'
            if (!SigilManager->CanAffordReforge(this))
                               ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(470,27): error C2039: ' ConsumeReforgeResources': n? ? um membro de 'USigilManagerComponent'
            SigilManager->ConsumeReforgeResources(this);
                          ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(475,9): error C2065: 'bIsEquipped': identificador n? declarado
    if (bIsEquipped)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(482,5): error C3861: 'RegenerateSigilProperties': identificador n? encontrado
    RegenerateSigilProperties();
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(485,9): error C2065: 'bIsEquipped': identificador n? declarado
    if (bIsEquipped)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(488,13): error C2065: 'bIsFused': identificador n? declarado
        if (bIsFused)
            ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(495,5): error C2065: 'LastReforgeTime': identificador n? declarado
    LastReforgeTime = GetWorld()->GetTimeSeconds();
    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(506,10): error C2065: 'bIsEquipped': identificador n? declarado
    if (!bIsEquipped)
         ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(513,54): error C2065: 'LastReforgeTime': identificador n? declarado
    const float TimeSinceLastReforge = CurrentTime - LastReforgeTime;
                                                     ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(513,17): error C2737: 'TimeSinceLastReforge': objetoconst deve ser inicializado
    const float TimeSinceLastReforge = CurrentTime - LastReforgeTime;
                ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(522,9): error C2065: 'bIsFused': identificador n? declarado
    if (bIsFused)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(528,9): error C2065: 'OwningActor': identificador n? declarado
    if (OwningActor)
        ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(530,52): error C2065: 'OwningActor': identificador n? declarado
        if (USigilManagerComponent* SigilManager = OwningActor->FindComponentByClass<USigilManagerComponent>())
                                                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(532,34): error C2039: ' CanAffordReforge': n? ? um membro de 'USigilManagerComponent'
            return SigilManager->CanAffordReforge(this);
                                 ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilManagerComponent.h(176,20): note: consulte a declara?? de 'USigilManagerComponent'
class AURACRON_API USigilManagerComponent : public UActorComponent
                   ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(550,15): error C2039: ' CurrentExperience': n? ? um membro de 'FSigilData'
    SigilData.CurrentExperience += ExperienceAmount;
              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(558,5): error C2039: ' CurrentExperience': n? ? um membro de 'FSigilData'
    UE_LOG(LogTemp, Log, TEXT("Added %f experience to sigil %s (Total: %f)"),
    ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilItem.h(74,21): note: consulte a declara?? de 'FSigilData'
struct AURACRON_API FSigilData
                    ^
C:\AURACRON\Source\AURACRON\Private\Sigils\SigilItem.cpp(558,5): fatal error C1003: contador de erros excede 100; interrompendo compila??
    UE_LOG(LogTemp, Log, TEXT("Added %f experience to sigil %s (Total: %f)"),
    ^
[13/21] Compile [x64] SigilDebugCommands.cpp
[14/21] Compile [x64] Module.AURACRON.gen.3.cpp
[15/21] Compile [x64] SigilManagerComponent.cpp
[16/21] Compile [x64] SigilVFXManager.cpp
[17/21] Compile [x64] SigilReplicationManager.cpp
[18/21] Compile [x64] SigilWidgets.cpp
Trace written to file C:/Users/<USER>/AppData/Local/UnrealBuildTool/Log.uba with size 19.6kb
Total time in Unreal Build Accelerator local executor: 56.30 seconds

Result: Failed (OtherCompilationError)
Total execution time: 61.84 seconds
LogCore: Engine exit requested (reason: EngineExit() was called)
LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: )...
LogAudio: Display: Audio Device Manager Shutdown
LogExit: Preparing to exit.
LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
LogPakFile: Destroying PakPlatformFile
LogExit: Exiting.
Log file closed, 09/03/25 01:02:46
