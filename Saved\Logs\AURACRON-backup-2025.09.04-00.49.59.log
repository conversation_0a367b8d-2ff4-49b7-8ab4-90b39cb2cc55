﻿Log file open, 09/03/25 21:49:47
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=18808)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 38979
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.5074] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:/AURACRON/AURACRON.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=8d6311d1065c4fdcb7f874696a83c878 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.267315
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-2F6D8572489D44D87ABA548AD26AABA7
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogAssetRegistry: Display: No AssetDiscovery cache present at ../../../../../../AURACRON/Intermediate/CachedAssetRegistryDiscovery.bin. AssetRegistry discovery of files will be uncached.
LogPluginManager: Unable to find target receipt in path: ../../../../../../AURACRON/Binaries/Win64/*.target
LogConfig: Display: Loading Mac ini files took 0.09 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.09 seconds
LogConfig: Display: Loading IOS ini files took 0.09 seconds
LogConfig: Display: Loading Android ini files took 0.10 seconds
LogConfig: Display: Loading Unix ini files took 0.11 seconds
LogConfig: Display: Loading TVOS ini files took 0.11 seconds
LogConfig: Display: Loading Windows ini files took 0.11 seconds
LogConfig: Display: Loading Linux ini files took 0.12 seconds
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../AURACRON/Binaries/Win64/*.target
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin Iris
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
Running C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/Build.bat Development Win64 -Project="C:/AURACRON/AURACRON.uproject" -TargetType=Editor -Progress -NoEngineChanges -NoHotReloadFromIDE
Using bundled DotNet SDK version: 8.0.300 win-x64
Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" Development Win64 -Project="C:/AURACRON/AURACRON.uproject" -TargetType=Editor -Progress -NoEngineChanges -NoHotReloadFromIDE
Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
Available x64 toolchains (1):
 * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
    (Family=14.44.35207, FamilyRank=1, Version=14.44.35215, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
Visual Studio 2022 compiler version 14.44.35215 is not a preferred version. Please use the latest preferred version 14.38.33130
@progress push 5%
@progress pop
Building AURACRONEditor...
Using Visual Studio 2022 14.44.35215 toolchain (C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207) and Windows 10.0.26100.0 SDK (C:\Program Files (x86)\Windows Kits\10).
Warning: Visual Studio 2022 compiler is not a preferred version
Determining max actions to execute in parallel (10 physical cores, 12 logical cores)
  Executing up to 10 processes, one per physical core
  Requested 1.5 GB memory per action, 9.82 GB available: limiting max parallel actions to 6
Using Unreal Build Accelerator local executor to run 4 action(s)
  Storage capacity 40Gb
---- Starting trace: 250903_214952 ----
UbaSessionServer - Disable remote execution (remote sessions will finish current processes)
------ Building 4 action(s) started ------
[1/4] Compile [x64] Module.AURACRON.gen.1.cpp
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(31,44): error C2370: 'SigilAbilityTags::Ability_Sigil': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag Ability_Sigil;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(39,31): note: consulte a declara?? de 'SigilAbilityTags::Ability_Sigil'
    static const FGameplayTag Ability_Sigil = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(34,44): error C2370: 'SigilAbilityTags::Ability_Sigil_Aegis_Murallion': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag Ability_Sigil_Aegis_Murallion;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(42,31): note: consulte a declara?? de 'SigilAbilityTags::Ability_Sigil_Aegis_Murallion'
    static const FGameplayTag Ability_Sigil_Aegis_Murallion = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Aegis.Murallion"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(35,44): error C2370: 'SigilAbilityTags::Ability_Sigil_Ruin_FracassoPrismal': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag Ability_Sigil_Ruin_FracassoPrismal;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(43,31): note: consulte a declara?? de 'SigilAbilityTags::Ability_Sigil_Ruin_FracassoPrismal'
    static const FGameplayTag Ability_Sigil_Ruin_FracassoPrismal = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Ruin.FracassoPrismal"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(36,44): error C2370: 'SigilAbilityTags::Ability_Sigil_Vesper_SoproDeFluxo': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag Ability_Sigil_Vesper_SoproDeFluxo;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(44,31): note: consulte a declara?? de 'SigilAbilityTags::Ability_Sigil_Vesper_SoproDeFluxo'
    static const FGameplayTag Ability_Sigil_Vesper_SoproDeFluxo = FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Vesper.SoproDeFluxo"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(39,44): error C2370: 'SigilAbilityTags::State_Barrier': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag State_Barrier;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(47,31): note: consulte a declara?? de 'SigilAbilityTags::State_Barrier'
    static const FGameplayTag State_Barrier = FGameplayTag::RequestGameplayTag(FName("State.Barrier"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(40,44): error C2370: 'SigilAbilityTags::State_CooldownReset': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag State_CooldownReset;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(48,31): note: consulte a declara?? de 'SigilAbilityTags::State_CooldownReset'
    static const FGameplayTag State_CooldownReset = FGameplayTag::RequestGameplayTag(FName("State.CooldownReset"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(41,44): error C2370: 'SigilAbilityTags::State_Dash': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag State_Dash;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(50,31): note: consulte a declara?? de 'SigilAbilityTags::State_Dash'
    static const FGameplayTag State_Dash = FGameplayTag::RequestGameplayTag(FName("State.Dash"));
                              ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h(42,44): error C2370: 'SigilAbilityTags::State_Shield': redefini??; classe de armazenamento diferente
    AURACRON_API extern const FGameplayTag State_Shield;
                                           ^
C:\AURACRON\Source\AURACRON\Public\Sigils\SigilAbilities.h(49,31): note: consulte a declara?? de 'SigilAbilityTags::State_Shield'
    static const FGameplayTag State_Shield = FGameplayTag::RequestGameplayTag(FName("State.Shield"));
                              ^
Trace written to file C:/Users/<USER>/AppData/Local/UnrealBuildTool/Log.uba with size 7.2kb
Total time in Unreal Build Accelerator local executor: 3.21 seconds

Result: Failed (OtherCompilationError)
Total execution time: 4.04 seconds
LogCore: Engine exit requested (reason: EngineExit() was called)
LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: )...
LogAudio: Display: Audio Device Manager Shutdown
LogExit: Preparing to exit.
LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
LogPakFile: Destroying PakPlatformFile
LogExit: Exiting.
Log file closed, 09/03/25 21:49:59
