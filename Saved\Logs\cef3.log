[0903/222650.838:ERROR:gpu_init.cc(426)] Passthrough is not supported, GL is disabled
[0903/222846.429:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(102,1-6): warning X4000: use of potentially uninitialized variable (dyn_index_vec4_float4)

[0903/222846.776:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(135,1): warning X4000: use of potentially uninitialized variable (f_i)
C:\fakepath(137,1): warning X4000: use of potentially uninitialized variable (f_i)
C:\fakepath(173,1): warning X4000: use of potentially uninitialized variable (f_u)

[0903/222848.801:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(165,1): warning X4000: use of potentially uninitialized variable (f_renderMipMapLevel)

[0903/222849.172:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(381,10-38): warning X4008: floating point division by zero
C:\fakepath(381,10-38): warning X4008: floating point division by zero

[0903/222850.039:ERROR:gl_utils.cc(314)] [.WebGL-000002945C36C880] GL_INVALID_OPERATION: Two textures of different types use the same sampler location.
[0903/222850.336:ERROR:gl_utils.cc(314)] [.WebGL-000002945C36C880] GL_INVALID_OPERATION: Two textures of different types use the same sampler location.
[0903/224501.827:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(102,1-6): warning X4000: use of potentially uninitialized variable (dyn_index_vec4_float4)

[0903/224502.049:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(135,1): warning X4000: use of potentially uninitialized variable (f_i)
C:\fakepath(137,1): warning X4000: use of potentially uninitialized variable (f_i)
C:\fakepath(173,1): warning X4000: use of potentially uninitialized variable (f_u)

[0903/224503.834:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(165,1): warning X4000: use of potentially uninitialized variable (f_renderMipMapLevel)

[0903/224503.835:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(165,1): warning X4000: use of potentially uninitialized variable (f_renderMipMapLevel)

[0903/224503.841:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(165,1): warning X4000: use of potentially uninitialized variable (f_renderMipMapLevel)

[0903/224503.875:WARNING:angle_platform_impl.cc(48)] HLSLCompiler.cpp:257 (compileToBinary): 
C:\fakepath(165,1): warning X4000: use of potentially uninitialized variable (f_renderMipMapLevel)

