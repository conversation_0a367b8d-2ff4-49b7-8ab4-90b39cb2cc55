// AURACRON.Target.cs
// Sistema de Sígilos AURACRON - Target Configuration UE 5.6
// Configuração para build de jogo (Shipping/Development)

using UnrealBuildTool;

public class AURACRONTarget : TargetRules
{
    public AURACRONTarget(TargetInfo Target) : base(Target)
    {
        Type = TargetType.Game;
        DefaultBuildSettings = BuildSettingsVersion.V5;
        IncludeOrderVersion = EngineIncludeOrderVersion.Unreal5_6;
        bOverrideBuildEnvironment = true;
        
        // Módulos principais
        ExtraModuleNames.AddRange(new string[] { "AURACRON" });
        
        // Configurações de otimização para MOBA multiplayer
        bUseUnityBuild = true;
        bUsePCHFiles = true;
        
        // Configurações específicas para diferentes builds
        if (Configuration == UnrealTargetConfiguration.Shipping)
        {
            bUseLoggingInShipping = false;
            bUseChecksInShipping = false;
            bCompileWithStatsWithoutEngine = false;
            bCompileWithPluginSupport = true;
        }
        else
        {
            bUseLoggingInShipping = true;
            bUseChecksInShipping = true;
            bCompileWithStatsWithoutEngine = true;
            bCompileWithPluginSupport = true;
        }
        
        // Configurações de rede para multiplayer - UE 5.6 APIs ROBUSTAS
        bWithPushModel = true;          // ROBUSTO: Implementação funcionando corretamente
        bUseIris = true;                // ROBUSTO: Implementação funcionando corretamente com Iris
    }
}