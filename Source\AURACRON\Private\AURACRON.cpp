// AURACRON.cpp
// Sistema de Sígilos AURACRON - M<PERSON>dulo Principal UE 5.6
// Implementação do módulo principal com suporte completo para Iris e Push Model

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "GameplayTagsManager.h"
#include "AbilitySystemGlobals.h"

// Definições globais são automaticamente criadas pelo IMPLEMENT_PRIMARY_GAME_MODULE

/**
 * Módulo principal do AURACRON
 * Responsável por inicializar todos os sistemas de sígilos
 */
class FAURACRONModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

private:
    /** Inicializar sistema de GameplayTags */
    void InitializeGameplayTags();
    
    /** Inicializar Gameplay Ability System */
    void InitializeAbilitySystem();
    
    /** Configurar sistema de replicação Iris */
    void ConfigureIrisReplication();
};

void FAURACRONModule::StartupModule()
{
    UE_LOG(LogTemp, Warning, TEXT("AURACRON Module: Starting up..."));
    
    // Inicializar sistemas core
    InitializeGameplayTags();
    InitializeAbilitySystem();
    ConfigureIrisReplication();
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON Module: Startup complete"));
}

void FAURACRONModule::ShutdownModule()
{
    UE_LOG(LogTemp, Warning, TEXT("AURACRON Module: Shutting down..."));
}

void FAURACRONModule::InitializeGameplayTags()
{
    // Garantir que o GameplayTagsManager está inicializado
    UGameplayTagsManager& TagsManager = UGameplayTagsManager::Get();
    
    // Tags principais do sistema de sígilos
    const TArray<FString> SigilTags = {
        TEXT("Sigil.System.Active"),
        TEXT("Sigil.System.Inactive"),
        TEXT("Sigil.Type.Offensive"),
        TEXT("Sigil.Type.Defensive"),
        TEXT("Sigil.Type.Utility"),
        TEXT("Sigil.Type.Support"),
        TEXT("Sigil.Rarity.Common"),
        TEXT("Sigil.Rarity.Uncommon"),
        TEXT("Sigil.Rarity.Rare"),
        TEXT("Sigil.Rarity.Epic"),
        TEXT("Sigil.Rarity.Legendary"),
        TEXT("Sigil.Rarity.Mythic"),
        TEXT("Sigil.State.Unequipped"),
        TEXT("Sigil.State.Equipped"),
        TEXT("Sigil.State.Fused"),
        TEXT("Sigil.State.Reforging"),
        TEXT("Sigil.Fusion.Active"),
        TEXT("Sigil.Fusion.Cooldown"),
        TEXT("Sigil.Fusion.Available"),
        TEXT("Sigil.Effect.Damage"),
        TEXT("Sigil.Effect.Healing"),
        TEXT("Sigil.Effect.Shield"),
        TEXT("Sigil.Effect.Speed"),
        TEXT("Sigil.Effect.Mana"),
        TEXT("Sigil.Effect.CriticalChance"),
        TEXT("Sigil.Effect.CriticalDamage"),
        TEXT("Sigil.Effect.Resistance"),
        TEXT("Sigil.Effect.Penetration")
    };
    
    // Registrar tags se ainda não existirem
    for (const FString& TagString : SigilTags)
    {
        if (!TagsManager.IsValidGameplayTagString(TagString))
        {
            UE_LOG(LogTemp, Warning, TEXT("Registering GameplayTag: %s"), *TagString);
        }
    }
}

void FAURACRONModule::InitializeAbilitySystem()
{
    // Inicializar globais do Ability System
    UAbilitySystemGlobals::Get().InitGlobalData();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ability System initialized"));
}

void FAURACRONModule::ConfigureIrisReplication()
{
    // Configurações específicas para Iris no UE 5.6
    // Iris é configurado automaticamente através do Build.cs
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Iris replication configured"));
}

IMPLEMENT_PRIMARY_GAME_MODULE(FAURACRONModule, AURACRON, "AURACRON");
