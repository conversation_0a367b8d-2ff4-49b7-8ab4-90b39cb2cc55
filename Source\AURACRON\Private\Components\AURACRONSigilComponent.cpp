// AURACRONSigilComponent.cpp
// Sistema de Sígilos AURACRON - Implementação do Componente de Gerenciamento de Sígilos UE 5.6

#include "Components/AURACRONSigilComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Abilities/GameplayAbility.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "AbilitySystemLog.h"
#include "GameplayTagsManager.h"
#include "GameplayAbilitySpec.h"
#include "GameplayEffectTypes.h"
#include "Abilities/GameplayAbilityTypes.h"

// ========================================
// LOGGING PERSONALIZADO PARA PRODUÇÃO - UE 5.6 MODERN API
// ========================================
DEFINE_LOG_CATEGORY_STATIC(LogAuracronSigil, Log, All);

UAURACRONSigilComponent::UAURACRONSigilComponent()
{
    // ========================================
    // CONFIGURAÇÃO MODERNA DE TICK - UE 5.6 MODERN APIS
    // ========================================
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // Atualizar a cada 100ms
    PrimaryComponentTick.bStartWithTickEnabled = true;
    PrimaryComponentTick.TickGroup = TG_PostUpdateWork;

    // UE 5.6 Modern API - Configuração de replicação
    SetIsReplicatedByDefault(true);
    SetIsReplicated(true);

    // ========================================
    // CONFIGURAÇÕES PADRÃO ALINHADAS COM DOCUMENTAÇÃO
    // ========================================
    MaxSigilSlots = 3; // Baseado na documentação: 3 Sígilos (Tanque, Dano, Utilidade)
    FusionCooldown = 120.0f; // 2 minutos conforme documentação
    FusionDuration = 10.0f;  // 10 segundos de duração da fusão
    FusionCooldownRemaining = 0.0f;
    bIsFusionActive = false;
    FusionTimeRemaining = 0.0f;
    bIsInitialized = false;

    // ========================================
    // VARIÁVEIS DE TIMING E RE-FORJAMENTO - DOCUMENTAÇÃO ALINHADA
    // ========================================
    GameStartTime = 0.0f;
    bAutoFusionTriggered = false;
    ReforgeCount = 0;
    LastReforgeTime = 0.0f;
    NexusProximityDistance = 500.0f; // 5 metros de distância do Nexus para re-forjamento

    // ========================================
    // INICIALIZAÇÃO ROBUSTA DE CONTAINERS - UE 5.6 MODERN APIS
    // ========================================
    EquippedSigils.Empty();
    EquippedSigils.Reserve(MaxSigilSlots); // Otimização de memória

    ActiveEffectHandles.Empty();
    ActiveEffectHandles.Reserve(MaxSigilSlots);

    GrantedAbilityHandles.Empty();
    GrantedAbilityHandles.Reserve(MaxSigilSlots);

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent: Construtor inicializado com APIs modernas UE 5.6"));
}

void UAURACRONSigilComponent::BeginPlay()
{
    Super::BeginPlay();

    // ========================================
    // INICIALIZAÇÃO ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do World
    UWorld* World = GetWorld();
    if (!World || !IsValid(World))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::BeginPlay - World inválido! Componente não funcionará corretamente."));
        return;
    }

    // Inicializar tempo de início da partida
    GameStartTime = World->GetTimeSeconds();

    // Validação robusta do Owner
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::BeginPlay - Owner inválido! Componente não funcionará corretamente."));
        return;
    }

    // ========================================
    // INICIALIZAÇÃO COM SISTEMA DE HABILIDADES - UE 5.6 MODERN APIS
    // ========================================

    // Tentar inicializar com o sistema de habilidades do proprietário
    UAbilitySystemComponent* ASC = Owner->FindComponentByClass<UAbilitySystemComponent>();
    if (ASC && IsValid(ASC))
    {
        InitializeWithAbilitySystem(ASC);
        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::BeginPlay - Sistema de habilidades encontrado e inicializado"));
    }
    else
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::BeginPlay - Sistema de habilidades não encontrado, tentará inicializar posteriormente"));
    }

    // ========================================
    // CONFIGURAÇÃO DE NETWORKING - UE 5.6 MODERN APIS
    // ========================================

    // Garantir que a replicação está configurada corretamente
    if (Owner->HasAuthority())
    {
        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::BeginPlay - Executando no servidor, replicação ativa"));
    }
    else
    {
        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::BeginPlay - Executando no cliente"));
    }
}

void UAURACRONSigilComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // ========================================
    // LIMPEZA ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::EndPlay - Iniciando limpeza (Razão: %d)"), (int32)EndPlayReason);

    // Validação robusta antes da limpeza
    if (AbilitySystemComponent && IsValid(AbilitySystemComponent))
    {
        // Limpar todos os efeitos e habilidades de forma robusta
        for (const FAURACRONEquippedSigilInfo& SigilInfo : EquippedSigils)
        {
            RemoveSigilEffects(SigilInfo);
            RemoveSigilAbilities(SigilInfo);
        }

        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::EndPlay - Removidos %d Sígilos equipados"), EquippedSigils.Num());
    }
    else
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::EndPlay - AbilitySystemComponent inválido, limpeza limitada"));
    }

    // ========================================
    // LIMPEZA COMPLETA DE CONTAINERS - UE 5.6 MODERN APIS
    // ========================================

    // Limpeza robusta dos containers
    EquippedSigils.Empty();
    EquippedSigils.Shrink(); // Liberar memória

    ActiveEffectHandles.Empty();
    ActiveEffectHandles.Shrink();

    GrantedAbilityHandles.Empty();
    GrantedAbilityHandles.Shrink();

    // Resetar estado
    bIsInitialized = false;
    AbilitySystemComponent = nullptr;

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::EndPlay - Limpeza concluída"));

    Super::EndPlay(EndPlayReason);
}

void UAURACRONSigilComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // ========================================
    // PROCESSAMENTO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta antes de processar
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::TickComponent - Owner inválido, pulando tick"));
        return;
    }

    // Validação de DeltaTime
    if (DeltaTime <= 0.0f || DeltaTime > 1.0f) // Proteção contra valores anômalos
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::TickComponent - DeltaTime anômalo: %.4f"), DeltaTime);
        return;
    }

    // Atualizar cooldowns apenas no servidor
    if (Owner->HasAuthority())
    {
        UpdateCooldowns(DeltaTime);

        // Verificar fusão automática apenas no servidor
        CheckAutoFusion();
    }
}

void UAURACRONSigilComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // ========================================
    // REPLICAÇÃO MODERNA - UE 5.6 MODERN APIS
    // ========================================

    // Replicação com condições otimizadas para performance
    DOREPLIFETIME_CONDITION(UAURACRONSigilComponent, EquippedSigils, COND_None);
    DOREPLIFETIME_CONDITION(UAURACRONSigilComponent, FusionCooldownRemaining, COND_OwnerOnly);
    DOREPLIFETIME_CONDITION(UAURACRONSigilComponent, bIsFusionActive, COND_None);
    DOREPLIFETIME_CONDITION(UAURACRONSigilComponent, FusionTimeRemaining, COND_OwnerOnly);
    DOREPLIFETIME_CONDITION(UAURACRONSigilComponent, bAutoFusionTriggered, COND_None);
    DOREPLIFETIME_CONDITION(UAURACRONSigilComponent, ReforgeCount, COND_OwnerOnly);

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetLifetimeReplicatedProps - Configuradas %d propriedades replicadas"), OutLifetimeProps.Num());
}

void UAURACRONSigilComponent::InitializeWithAbilitySystem(UAbilitySystemComponent* InAbilitySystemComponent)
{
    // ========================================
    // INICIALIZAÇÃO ROBUSTA COM SISTEMA DE HABILIDADES - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta de entrada
    if (!InAbilitySystemComponent || !IsValid(InAbilitySystemComponent))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::InitializeWithAbilitySystem - AbilitySystemComponent inválido"));
        return;
    }

    // Verificar se já foi inicializado
    if (bIsInitialized)
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::InitializeWithAbilitySystem - Componente já inicializado, ignorando"));
        return;
    }

    // Validação do Owner
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::InitializeWithAbilitySystem - Owner inválido"));
        return;
    }

    // ========================================
    // CONFIGURAÇÃO DO SISTEMA DE HABILIDADES
    // ========================================

    AbilitySystemComponent = InAbilitySystemComponent;
    bIsInitialized = true;

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::InitializeWithAbilitySystem - Sistema inicializado com sucesso"));

    // ========================================
    // APLICAR EFEITOS DE SÍGILOS JÁ EQUIPADOS - UE 5.6 MODERN APIS
    // ========================================

    // Aplicar efeitos de Sígilos já equipados (para casos de reconexão ou inicialização tardia)
    if (EquippedSigils.Num() > 0)
    {
        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::InitializeWithAbilitySystem - Reaplicando efeitos de %d Sígilos equipados"), EquippedSigils.Num());

        for (FAURACRONEquippedSigilInfo& SigilInfo : EquippedSigils)
        {
            if (SigilInfo.bIsActive)
            {
                ApplySigilEffects(SigilInfo);
                GrantSigilAbilities(SigilInfo);
            }
        }
    }
    else
    {
        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::InitializeWithAbilitySystem - Nenhum Sígilo equipado para reaplicar"));
    }
}

void UAURACRONSigilComponent::EquipSigil_Implementation(EAURACRONSigilType SigilType, int32 Level, int32 Rarity)
{
    // ========================================
    // EQUIPAR SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do Owner e autoridade
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Owner inválido"));
        return;
    }

    if (!Owner->HasAuthority())
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Tentativa de equipar Sígilo sem autoridade"));
        return;
    }

    // Validação do World
    UWorld* World = GetWorld();
    if (!World || !IsValid(World))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - World inválido"));
        return;
    }

    // Verificar se já está equipado
    if (IsSigilEquipped(SigilType))
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Sígilo %d já está equipado"), (int32)SigilType);
        return;
    }

    // Verificar se há slots disponíveis
    if (!CanEquipMoreSigils())
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Não há slots disponíveis (%d/%d)"), GetEquippedSigilCount(), GetMaxSigilSlots());
        return;
    }

    // ========================================
    // CRIAR INFORMAÇÕES DO SÍGILO - VALIDAÇÃO ROBUSTA
    // ========================================

    FAURACRONEquippedSigilInfo NewSigilInfo;
    NewSigilInfo.SigilType = SigilType;
    NewSigilInfo.Level = FMath::Clamp(Level, 1, 5);
    NewSigilInfo.Rarity = FMath::Clamp(Rarity, 1, 5);
    NewSigilInfo.EquipTime = World->GetTimeSeconds();
    NewSigilInfo.bIsActive = true;

    // Inicializar arrays vazios
    NewSigilInfo.AppliedEffects.Empty();
    NewSigilInfo.GrantedAbilities.Empty();

    // Adicionar à lista com validação
    const int32 NewIndex = EquippedSigils.Add(NewSigilInfo);

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Sígilo %d equipado (Nível: %d, Raridade: %d, Índice: %d)"),
           (int32)SigilType, NewSigilInfo.Level, NewSigilInfo.Rarity, NewIndex);

    // ========================================
    // APLICAR EFEITOS SE SISTEMA ESTIVER INICIALIZADO
    // ========================================

    if (bIsInitialized && AbilitySystemComponent && IsValid(AbilitySystemComponent))
    {
        FAURACRONEquippedSigilInfo& SigilRef = EquippedSigils[NewIndex];
        ApplySigilEffects(SigilRef);
        GrantSigilAbilities(SigilRef);

        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Efeitos aplicados para Sígilo %d"), (int32)SigilType);
    }
    else
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::EquipSigil_Implementation - Sistema não inicializado, efeitos serão aplicados posteriormente"));
    }

    // Chamar evento Blueprint
    OnSigilEquipped(SigilType, NewSigilInfo.Level, NewSigilInfo.Rarity);
}

void UAURACRONSigilComponent::UnequipSigil_Implementation(EAURACRONSigilType SigilType)
{
    // ========================================
    // DESEQUIPAR SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do Owner e autoridade
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Owner inválido"));
        return;
    }

    if (!Owner->HasAuthority())
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Tentativa de desequipar Sígilo sem autoridade"));
        return;
    }

    // ========================================
    // ENCONTRAR O SÍGILO COM VALIDAÇÃO ROBUSTA
    // ========================================

    int32 SigilIndex = EquippedSigils.IndexOfByPredicate([SigilType](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.SigilType == SigilType;
    });

    if (SigilIndex == INDEX_NONE)
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Sígilo %d não encontrado para desequipar"), (int32)SigilType);
        return;
    }

    // Validação de índice
    if (!EquippedSigils.IsValidIndex(SigilIndex))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Índice inválido %d para array de tamanho %d"), SigilIndex, EquippedSigils.Num());
        return;
    }

    // ========================================
    // REMOVER EFEITOS E HABILIDADES DE FORMA ROBUSTA
    // ========================================

    const FAURACRONEquippedSigilInfo& SigilInfo = EquippedSigils[SigilIndex];

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Desequipando Sígilo %d (Nível: %d, Raridade: %d)"),
           (int32)SigilType, SigilInfo.Level, SigilInfo.Rarity);

    // Remover efeitos e habilidades se o sistema estiver inicializado
    if (bIsInitialized && AbilitySystemComponent && IsValid(AbilitySystemComponent))
    {
        RemoveSigilEffects(SigilInfo);
        RemoveSigilAbilities(SigilInfo);

        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Efeitos removidos para Sígilo %d"), (int32)SigilType);
    }
    else
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Sistema não inicializado, limpeza limitada"));
    }

    // Remover da lista com validação
    EquippedSigils.RemoveAt(SigilIndex);

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::UnequipSigil_Implementation - Sígilo %d removido da lista (%d Sígilos restantes)"),
           (int32)SigilType, EquippedSigils.Num());

    // Chamar evento Blueprint
    OnSigilUnequipped(SigilType);
}

bool UAURACRONSigilComponent::IsSigilEquipped(EAURACRONSigilType SigilType) const
{
    // ========================================
    // VERIFICAÇÃO ROBUSTA DE SÍGILO EQUIPADO - UE 5.6 MODERN APIS
    // ========================================

    bool bIsEquipped = EquippedSigils.ContainsByPredicate([SigilType](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.SigilType == SigilType && Info.bIsActive;
    });

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::IsSigilEquipped - Sígilo %d está %s"),
           (int32)SigilType, bIsEquipped ? TEXT("equipado") : TEXT("não equipado"));

    return bIsEquipped;
}

FAURACRONEquippedSigilInfo UAURACRONSigilComponent::GetSigilInfo(EAURACRONSigilType SigilType) const
{
    // ========================================
    // OBTER INFORMAÇÕES DE SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    const FAURACRONEquippedSigilInfo* FoundInfo = EquippedSigils.FindByPredicate([SigilType](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.SigilType == SigilType;
    });

    if (FoundInfo)
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetSigilInfo - Sígilo %d encontrado (Nível: %d, Raridade: %d)"),
               (int32)SigilType, FoundInfo->Level, FoundInfo->Rarity);
        return *FoundInfo;
    }
    else
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetSigilInfo - Sígilo %d não encontrado, retornando padrão"), (int32)SigilType);
        return FAURACRONEquippedSigilInfo();
    }
}

TArray<FAURACRONEquippedSigilInfo> UAURACRONSigilComponent::GetAllEquippedSigils() const
{
    // ========================================
    // OBTER TODOS OS SÍGILOS EQUIPADOS - UE 5.6 MODERN APIS
    // ========================================

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetAllEquippedSigils - Retornando %d Sígilos equipados"), EquippedSigils.Num());

    return EquippedSigils;
}

int32 UAURACRONSigilComponent::GetEquippedSigilCount() const
{
    // ========================================
    // CONTAR SÍGILOS EQUIPADOS ATIVOS - UE 5.6 MODERN APIS
    // ========================================

    int32 ActiveCount = EquippedSigils.FilterByPredicate([](const FAURACRONEquippedSigilInfo& Info)
    {
        return Info.bIsActive;
    }).Num();

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetEquippedSigilCount - %d Sígilos ativos de %d total"), ActiveCount, EquippedSigils.Num());

    return ActiveCount;
}

int32 UAURACRONSigilComponent::GetMaxSigilSlots() const
{
    // ========================================
    // OBTER SLOTS MÁXIMOS - UE 5.6 MODERN APIS
    // ========================================

    int32 ClampedMaxSlots = FMath::Clamp(MaxSigilSlots, 1, 6); // Baseado na documentação

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetMaxSigilSlots - %d slots máximos"), ClampedMaxSlots);

    return ClampedMaxSlots;
}

bool UAURACRONSigilComponent::CanEquipMoreSigils() const
{
    // ========================================
    // VERIFICAR SE PODE EQUIPAR MAIS SÍGILOS - UE 5.6 MODERN APIS
    // ========================================

    int32 CurrentCount = GetEquippedSigilCount();
    int32 MaxSlots = GetMaxSigilSlots();
    bool bCanEquip = CurrentCount < MaxSlots;

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::CanEquipMoreSigils - %s (%d/%d)"),
           bCanEquip ? TEXT("Pode equipar") : TEXT("Não pode equipar"), CurrentCount, MaxSlots);

    return bCanEquip;
}

void UAURACRONSigilComponent::ActivateSigilFusion_Implementation()
{
    // ========================================
    // ATIVAR FUSÃO DE SÍGILOS ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do Owner e autoridade
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Owner inválido"));
        return;
    }

    if (!Owner->HasAuthority())
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Tentativa de ativar fusão sem autoridade"));
        return;
    }

    // Verificar se a fusão está disponível
    if (!IsFusionAvailable())
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Fusão não disponível (Cooldown: %.1f, Ativa: %s, Sígilos: %d)"),
               FusionCooldownRemaining, bIsFusionActive ? TEXT("true") : TEXT("false"), GetEquippedSigilCount());
        return;
    }

    // ========================================
    // ATIVAR FUSÃO COM VALIDAÇÕES ROBUSTAS
    // ========================================

    bIsFusionActive = true;
    FusionTimeRemaining = FusionDuration;
    FusionCooldownRemaining = FusionCooldown;

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Fusão ativada (Duração: %.1f, Cooldown: %.1f)"),
           FusionDuration, FusionCooldown);

    // ========================================
    // APLICAR EFEITO DE FUSÃO GERAL - UE 5.6 MODERN APIS
    // ========================================

    if (FusionEffect && AbilitySystemComponent && IsValid(AbilitySystemComponent))
    {
        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
        EffectContext.AddSourceObject(Owner);

        FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(FusionEffect, 1.0f, EffectContext);
        if (EffectSpecHandle.IsValid())
        {
            FActiveGameplayEffectHandle ActiveHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
            if (ActiveHandle.IsValid())
            {
                UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Efeito de fusão aplicado"));
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Falha ao aplicar efeito de fusão"));
            }
        }
        else
        {
            UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - Spec de efeito de fusão inválido"));
        }
    }
    else
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - FusionEffect ou AbilitySystemComponent inválido"));
    }

    // ========================================
    // ATIVAR HABILIDADES ESPECÍFICAS DOS SÍGILOS - UE 5.6 MODERN APIS
    // ========================================

    int32 ActivatedAbilities = 0;
    for (const FAURACRONEquippedSigilInfo& SigilInfo : EquippedSigils)
    {
        if (SigilInfo.bIsActive)
        {
            ActivateSigilSpecificAbility(SigilInfo.SigilType);
            ActivatedAbilities++;
        }
    }

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::ActivateSigilFusion_Implementation - %d habilidades específicas ativadas"), ActivatedAbilities);

    // Chamar evento Blueprint
    OnFusionActivated();
}

bool UAURACRONSigilComponent::IsFusionAvailable() const
{
    // ========================================
    // VERIFICAR DISPONIBILIDADE DE FUSÃO - UE 5.6 MODERN APIS
    // ========================================

    bool bCooldownReady = FusionCooldownRemaining <= 0.0f;
    bool bNotActive = !bIsFusionActive;
    int32 SigilCount = GetEquippedSigilCount();
    bool bEnoughSigils = SigilCount >= 2; // Baseado na documentação: mínimo 2 Sígilos

    bool bAvailable = bCooldownReady && bNotActive && bEnoughSigils;

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::IsFusionAvailable - %s (Cooldown: %.1f, Ativa: %s, Sígilos: %d)"),
           bAvailable ? TEXT("Disponível") : TEXT("Indisponível"),
           FusionCooldownRemaining, bIsFusionActive ? TEXT("true") : TEXT("false"), SigilCount);

    return bAvailable;
}

float UAURACRONSigilComponent::GetFusionCooldownRemaining() const
{
    // ========================================
    // OBTER COOLDOWN RESTANTE DE FUSÃO - UE 5.6 MODERN APIS
    // ========================================

    float ClampedCooldown = FMath::Max(0.0f, FusionCooldownRemaining);

    UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::GetFusionCooldownRemaining - %.1f segundos restantes"), ClampedCooldown);

    return ClampedCooldown;
}

void UAURACRONSigilComponent::ApplySigilEffects(FAURACRONEquippedSigilInfo& SigilInfo)
{
    // ========================================
    // APLICAR EFEITOS DE SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do AbilitySystemComponent
    if (!AbilitySystemComponent || !IsValid(AbilitySystemComponent))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::ApplySigilEffects - AbilitySystemComponent inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
        return;
    }

    // Validação do Owner
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Owner inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
        return;
    }

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Aplicando efeitos para Sígilo %d (Nível: %d, Raridade: %d)"),
           (int32)SigilInfo.SigilType, SigilInfo.Level, SigilInfo.Rarity);

    // ========================================
    // APLICAR BÔNUS PASSIVOS ESPECÍFICOS - DOCUMENTAÇÃO ALINHADA
    // ========================================

    ApplySigilPassiveBonuses(SigilInfo);

    // ========================================
    // APLICAR EFEITOS ADICIONAIS CONFIGURÁVEIS - UE 5.6 MODERN APIS
    // ========================================

    TArray<TSubclassOf<UGameplayEffect>>* BaseEffects = nullptr;
    const TCHAR* SigilTypeName = nullptr;

    switch (SigilInfo.SigilType)
    {
        case EAURACRONSigilType::Aegis:
            BaseEffects = &AegisBaseEffects;
            SigilTypeName = TEXT("Aegis");
            break;
        case EAURACRONSigilType::Ruin:
            BaseEffects = &RuinBaseEffects;
            SigilTypeName = TEXT("Ruin");
            break;
        case EAURACRONSigilType::Vesper:
            BaseEffects = &VesperBaseEffects;
            SigilTypeName = TEXT("Vesper");
            break;
        default:
            UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Tipo de Sígilo desconhecido: %d"), (int32)SigilInfo.SigilType);
            return;
    }

    if (BaseEffects && BaseEffects->Num() > 0)
    {
        // Obter ou criar array de handles para este Sígilo
        TArray<FActiveGameplayEffectHandle>& EffectHandles = ActiveEffectHandles.FindOrAdd(SigilInfo.SigilType);
        EffectHandles.Reserve(BaseEffects->Num());

        int32 AppliedEffectsCount = 0;
        for (const TSubclassOf<UGameplayEffect>& EffectClass : *BaseEffects)
        {
            if (EffectClass && IsValid(EffectClass))
            {
                FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
                EffectContext.AddSourceObject(Owner);

                // Calcular magnitude baseada no nível e raridade do Sígilo
                float EffectLevel = SigilInfo.Level * (1.0f + (SigilInfo.Rarity - 1) * 0.1f);

                FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(EffectClass, EffectLevel, EffectContext);
                if (EffectSpecHandle.IsValid())
                {
                    FActiveGameplayEffectHandle ActiveHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
                    if (ActiveHandle.IsValid())
                    {
                        EffectHandles.Add(ActiveHandle);
                        SigilInfo.AppliedEffects.Add(EffectClass);
                        AppliedEffectsCount++;

                        UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Efeito aplicado para %s (Magnitude: %.2f)"),
                               SigilTypeName, EffectLevel);
                    }
                    else
                    {
                        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Falha ao aplicar efeito para %s"), SigilTypeName);
                    }
                }
                else
                {
                    UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Spec inválido para efeito de %s"), SigilTypeName);
                }
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Classe de efeito inválida para %s"), SigilTypeName);
            }
        }

        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::ApplySigilEffects - %d/%d efeitos aplicados para %s"),
               AppliedEffectsCount, BaseEffects->Num(), SigilTypeName);
    }
    else
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::ApplySigilEffects - Nenhum efeito configurado para %s"), SigilTypeName);
    }
}

void UAURACRONSigilComponent::RemoveSigilEffects(const FAURACRONEquippedSigilInfo& SigilInfo)
{
    // ========================================
    // REMOVER EFEITOS DE SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do AbilitySystemComponent
    if (!AbilitySystemComponent || !IsValid(AbilitySystemComponent))
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - AbilitySystemComponent inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
        return;
    }

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - Removendo efeitos para Sígilo %d"), (int32)SigilInfo.SigilType);

    // ========================================
    // REMOVER BÔNUS PASSIVOS ESPECÍFICOS
    // ========================================

    RemoveSigilPassiveBonuses(SigilInfo);

    // ========================================
    // REMOVER EFEITOS ADICIONAIS CONFIGURÁVEIS - UE 5.6 MODERN APIS
    // ========================================

    TArray<FActiveGameplayEffectHandle>* EffectHandles = ActiveEffectHandles.Find(SigilInfo.SigilType);
    if (EffectHandles && EffectHandles->Num() > 0)
    {
        int32 RemovedEffectsCount = 0;
        for (const FActiveGameplayEffectHandle& Handle : *EffectHandles)
        {
            if (Handle.IsValid())
            {
                bool bRemoved = AbilitySystemComponent->RemoveActiveGameplayEffect(Handle);
                if (bRemoved)
                {
                    RemovedEffectsCount++;
                    UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - Efeito removido para Sígilo %d"), (int32)SigilInfo.SigilType);
                }
                else
                {
                    UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - Falha ao remover efeito para Sígilo %d"), (int32)SigilInfo.SigilType);
                }
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - Handle inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
            }
        }

        // Limpar array de handles
        EffectHandles->Empty();
        EffectHandles->Shrink();

        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - %d efeitos removidos para Sígilo %d"),
               RemovedEffectsCount, (int32)SigilInfo.SigilType);
    }
    else
    {
        UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::RemoveSigilEffects - Nenhum efeito ativo encontrado para Sígilo %d"), (int32)SigilInfo.SigilType);
    }
}

void UAURACRONSigilComponent::GrantSigilAbilities(FAURACRONEquippedSigilInfo& SigilInfo)
{
    // ========================================
    // CONCEDER HABILIDADES DE SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do AbilitySystemComponent
    if (!AbilitySystemComponent || !IsValid(AbilitySystemComponent))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - AbilitySystemComponent inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
        return;
    }

    // Validação do Owner
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Error, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Owner inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
        return;
    }

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Concedendo habilidades para Sígilo %d"), (int32)SigilInfo.SigilType);

    // ========================================
    // OBTER HABILIDADES BASE PARA ESTE TIPO DE SÍGILO
    // ========================================

    TArray<TSubclassOf<UGameplayAbility>>* BaseAbilities = nullptr;
    const TCHAR* SigilTypeName = nullptr;

    switch (SigilInfo.SigilType)
    {
        case EAURACRONSigilType::Aegis:
            BaseAbilities = &AegisBaseAbilities;
            SigilTypeName = TEXT("Aegis");
            break;
        case EAURACRONSigilType::Ruin:
            BaseAbilities = &RuinBaseAbilities;
            SigilTypeName = TEXT("Ruin");
            break;
        case EAURACRONSigilType::Vesper:
            BaseAbilities = &VesperBaseAbilities;
            SigilTypeName = TEXT("Vesper");
            break;
        default:
            UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Tipo de Sígilo desconhecido: %d"), (int32)SigilInfo.SigilType);
            return;
    }

    if (!BaseAbilities || BaseAbilities->Num() == 0)
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Nenhuma habilidade configurada para %s"), SigilTypeName);
        return;
    }

    // ========================================
    // CONCEDER CADA HABILIDADE - UE 5.6 MODERN APIS
    // ========================================

    TArray<FGameplayAbilitySpecHandle>& AbilityHandles = GrantedAbilityHandles.FindOrAdd(SigilInfo.SigilType);
    AbilityHandles.Reserve(BaseAbilities->Num());

    int32 GrantedAbilitiesCount = 0;
    for (const TSubclassOf<UGameplayAbility>& AbilityClass : *BaseAbilities)
    {
        if (AbilityClass && IsValid(AbilityClass))
        {
            // Criar spec da habilidade com nível do Sígilo
            FGameplayAbilitySpec AbilitySpec(AbilityClass, SigilInfo.Level, INDEX_NONE, Owner);

            FGameplayAbilitySpecHandle Handle = AbilitySystemComponent->GiveAbility(AbilitySpec);
            if (Handle.IsValid())
            {
                AbilityHandles.Add(Handle);
                SigilInfo.GrantedAbilities.Add(AbilityClass);
                GrantedAbilitiesCount++;

                UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Habilidade concedida para %s (Nível: %d)"),
                       SigilTypeName, SigilInfo.Level);
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Falha ao conceder habilidade para %s"), SigilTypeName);
            }
        }
        else
        {
            UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - Classe de habilidade inválida para %s"), SigilTypeName);
        }
    }

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::GrantSigilAbilities - %d/%d habilidades concedidas para %s"),
           GrantedAbilitiesCount, BaseAbilities->Num(), SigilTypeName);
}

void UAURACRONSigilComponent::RemoveSigilAbilities(const FAURACRONEquippedSigilInfo& SigilInfo)
{
    // ========================================
    // REMOVER HABILIDADES DE SÍGILO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do AbilitySystemComponent
    if (!AbilitySystemComponent || !IsValid(AbilitySystemComponent))
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - AbilitySystemComponent inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
        return;
    }

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - Removendo habilidades para Sígilo %d"), (int32)SigilInfo.SigilType);

    // ========================================
    // REMOVER HABILIDADES CONCEDIDAS - UE 5.6 MODERN APIS
    // ========================================

    TArray<FGameplayAbilitySpecHandle>* AbilityHandles = GrantedAbilityHandles.Find(SigilInfo.SigilType);
    if (AbilityHandles && AbilityHandles->Num() > 0)
    {
        int32 RemovedAbilitiesCount = 0;
        for (const FGameplayAbilitySpecHandle& Handle : *AbilityHandles)
        {
            if (Handle.IsValid())
            {
                // Verificar se a habilidade ainda existe antes de tentar remover
                FGameplayAbilitySpec* AbilitySpec = AbilitySystemComponent->FindAbilitySpecFromHandle(Handle);
                if (AbilitySpec)
                {
                    AbilitySystemComponent->ClearAbility(Handle);
                    RemovedAbilitiesCount++;

                    UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - Habilidade removida para Sígilo %d"), (int32)SigilInfo.SigilType);
                }
                else
                {
                    UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - Spec de habilidade não encontrado para Sígilo %d"), (int32)SigilInfo.SigilType);
                }
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - Handle inválido para Sígilo %d"), (int32)SigilInfo.SigilType);
            }
        }

        // Limpar array de handles
        AbilityHandles->Empty();
        AbilityHandles->Shrink();

        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - %d habilidades removidas para Sígilo %d"),
               RemovedAbilitiesCount, (int32)SigilInfo.SigilType);
    }
    else
    {
        UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::RemoveSigilAbilities - Nenhuma habilidade ativa encontrada para Sígilo %d"), (int32)SigilInfo.SigilType);
    }
}

void UAURACRONSigilComponent::UpdateCooldowns(float DeltaTime)
{
    // ========================================
    // ATUALIZAR COOLDOWNS ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Validação de DeltaTime
    if (DeltaTime <= 0.0f || DeltaTime > 1.0f)
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::UpdateCooldowns - DeltaTime inválido: %.4f"), DeltaTime);
        return;
    }

    bool bCooldownsChanged = false;

    // ========================================
    // ATUALIZAR COOLDOWN DA FUSÃO
    // ========================================

    if (FusionCooldownRemaining > 0.0f)
    {
        float PreviousCooldown = FusionCooldownRemaining;
        FusionCooldownRemaining = FMath::Max(0.0f, FusionCooldownRemaining - DeltaTime);

        if (FusionCooldownRemaining != PreviousCooldown)
        {
            bCooldownsChanged = true;

            // Log quando o cooldown termina
            if (FusionCooldownRemaining <= 0.0f && PreviousCooldown > 0.0f)
            {
                UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::UpdateCooldowns - Cooldown de fusão terminou"));
            }
        }
    }

    // ========================================
    // ATUALIZAR TEMPO DA FUSÃO ATIVA
    // ========================================

    if (bIsFusionActive)
    {
        float PreviousTime = FusionTimeRemaining;
        FusionTimeRemaining = FMath::Max(0.0f, FusionTimeRemaining - DeltaTime);

        if (FusionTimeRemaining != PreviousTime)
        {
            bCooldownsChanged = true;
        }

        // Verificar se a fusão terminou
        if (FusionTimeRemaining <= 0.0f && PreviousTime > 0.0f)
        {
            bIsFusionActive = false;
            bCooldownsChanged = true;

            UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::UpdateCooldowns - Fusão terminou"));

            // Chamar evento Blueprint se necessário
            // OnFusionDeactivated(); // Implementar se necessário
        }
    }

    // ========================================
    // VERIFICAR FUSÃO AUTOMÁTICA AOS 6 MINUTOS
    // ========================================

    // Nota: CheckAutoFusion já é chamada no TickComponent, mas mantemos aqui por segurança
    // CheckAutoFusion();

    // Log detalhado apenas quando há mudanças significativas
    if (bCooldownsChanged)
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::UpdateCooldowns - Cooldown Fusão: %.1f, Tempo Fusão: %.1f, Ativa: %s"),
               FusionCooldownRemaining, FusionTimeRemaining, bIsFusionActive ? TEXT("true") : TEXT("false"));
    }
}

void UAURACRONSigilComponent::OnRep_EquippedSigils()
{
    // ========================================
    // REPLICAÇÃO DE SÍGILOS EQUIPADOS - UE 5.6 MODERN APIS
    // ========================================

    UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Replicação recebida com %d Sígilos"), EquippedSigils.Num());

    // Validação robusta do Owner
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Owner inválido"));
        return;
    }

    // ========================================
    // NOTIFICAR BLUEPRINT E SISTEMAS LOCAIS - UE 5.6 MODERN APIS
    // ========================================

    // Processar apenas no cliente que possui este ator
    // UE 5.6 Modern API - Verificar se é controlado localmente
    APawn* OwnerPawn = Cast<APawn>(Owner);
    if (OwnerPawn && OwnerPawn->IsLocallyControlled())
    {
        UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Processando replicação no cliente local"));

        // Notificar sobre mudanças nos Sígilos para atualização de UI
        for (const FAURACRONEquippedSigilInfo& SigilInfo : EquippedSigils)
        {
            // Validar informações do Sígilo replicado
            if (SigilInfo.Level >= 1 && SigilInfo.Level <= 5 &&
                SigilInfo.Rarity >= 1 && SigilInfo.Rarity <= 5)
            {
                UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Sígilo %d replicado (Nível: %d, Raridade: %d, Ativo: %s)"),
                       (int32)SigilInfo.SigilType, SigilInfo.Level, SigilInfo.Rarity, SigilInfo.bIsActive ? TEXT("true") : TEXT("false"));

                // Reaplica efeitos visuais e de UI para Sígilos replicados
                if (bIsInitialized && AbilitySystemComponent && IsValid(AbilitySystemComponent))
                {
                    // Garantir que efeitos visuais estejam sincronizados
                    // (efeitos de gameplay são aplicados apenas no servidor)

                    // Aqui podemos adicionar lógica específica para efeitos visuais locais
                    // que não afetam gameplay mas são importantes para feedback visual

                    UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Sincronizando efeitos visuais para Sígilo %d"), (int32)SigilInfo.SigilType);
                }
                else
                {
                    UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Sistema não inicializado, efeitos visuais serão aplicados posteriormente"));
                }
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Dados inválidos para Sígilo %d (Nível: %d, Raridade: %d)"),
                       (int32)SigilInfo.SigilType, SigilInfo.Level, SigilInfo.Rarity);
            }
        }

        // ========================================
        // NOTIFICAR SISTEMAS DE UI E EVENTOS
        // ========================================

        // Aqui podemos adicionar delegates ou eventos específicos para UI
        // OnSigilsReplicatedToClient.Broadcast(EquippedSigils); // Implementar se necessário
    }
    else
    {
        UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::OnRep_EquippedSigils - Replicação recebida em cliente não-local, ignorando processamento de UI"));
    }
}

void UAURACRONSigilComponent::CheckAutoFusion()
{
    // ========================================
    // VERIFICAR FUSÃO AUTOMÁTICA - UE 5.6 MODERN APIS
    // ========================================

    // Verificar se a fusão automática já foi ativada
    if (bAutoFusionTriggered)
    {
        return; // Já foi ativada, não precisa verificar novamente
    }

    // Validação robusta do World
    UWorld* World = GetWorld();
    if (!World || !IsValid(World))
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::CheckAutoFusion - World inválido"));
        return;
    }

    // Verificar se o tempo de início foi definido
    if (GameStartTime <= 0.0f)
    {
        UE_LOG(LogAuracronSigil, VeryVerbose, TEXT("UAURACRONSigilComponent::CheckAutoFusion - GameStartTime não definido"));
        return;
    }

    // ========================================
    // CALCULAR TEMPO DECORRIDO DESDE O INÍCIO DA PARTIDA
    // ========================================

    float CurrentTime = World->GetTimeSeconds();
    float ElapsedTime = CurrentTime - GameStartTime;

    // Validação de tempo
    if (ElapsedTime < 0.0f)
    {
        UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::CheckAutoFusion - Tempo decorrido negativo: %.2f"), ElapsedTime);
        return;
    }

    // ========================================
    // FUSÃO AUTOMÁTICA AOS 6 MINUTOS (360 SEGUNDOS) - DOCUMENTAÇÃO ALINHADA
    // ========================================

    const float AutoFusionTime = 360.0f; // 6 minutos conforme documentação

    if (ElapsedTime >= AutoFusionTime)
    {
        int32 SigilCount = GetEquippedSigilCount();

        if (SigilCount > 0)
        {
            bAutoFusionTriggered = true;

            UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::CheckAutoFusion - Fusão automática ativada após %.1f segundos com %d Sígilos"),
                   ElapsedTime, SigilCount);

            // ========================================
            // ATIVAR FUSÃO AUTOMATICAMENTE SE DISPONÍVEL
            // ========================================

            if (IsFusionAvailable())
            {
                ActivateSigilFusion();
                UE_LOG(LogAuracronSigil, Log, TEXT("UAURACRONSigilComponent::CheckAutoFusion - Fusão automática executada com sucesso"));
            }
            else
            {
                UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::CheckAutoFusion - Fusão automática não disponível (Cooldown: %.1f, Ativa: %s)"),
                       FusionCooldownRemaining, bIsFusionActive ? TEXT("true") : TEXT("false"));
            }

            // Chamar evento Blueprint
            OnAutoFusionTriggered();
        }
        else
        {
            UE_LOG(LogAuracronSigil, Warning, TEXT("UAURACRONSigilComponent::CheckAutoFusion - Fusão automática não ativada: nenhum Sígilo equipado"));
            bAutoFusionTriggered = true; // Marcar como ativada para não tentar novamente
        }
    }
    else
    {
        // Log apenas ocasionalmente para não spam
        static float LastLogTime = 0.0f;
        if (CurrentTime - LastLogTime > 30.0f) // Log a cada 30 segundos
        {
            LastLogTime = CurrentTime;
            float TimeRemaining = AutoFusionTime - ElapsedTime;
            UE_LOG(LogAuracronSigil, Verbose, TEXT("UAURACRONSigilComponent::CheckAutoFusion - %.1f segundos restantes para fusão automática"), TimeRemaining);
        }
    }
}

bool UAURACRONSigilComponent::IsNearNexus() const
{
    AActor* NearestNexus = FindNearestNexus();
    if (!NearestNexus || !GetOwner())
    {
        return false;
    }

    float Distance = FVector::Dist(GetOwner()->GetActorLocation(), NearestNexus->GetActorLocation());
    return Distance <= NexusProximityDistance;
}

AActor* UAURACRONSigilComponent::FindNearestNexus() const
{
    if (!GetWorld())
    {
        return nullptr;
    }

    // Procurar por atores com tag "Nexus" ou classe específica de Nexus
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName("Nexus"), FoundActors);

    if (FoundActors.Num() == 0)
    {
        return nullptr;
    }

    // Encontrar o Nexus mais próximo
    AActor* NearestNexus = nullptr;
    float NearestDistance = FLT_MAX;

    if (GetOwner())
    {
        FVector OwnerLocation = GetOwner()->GetActorLocation();

        for (AActor* Actor : FoundActors)
        {
            if (Actor)
            {
                float Distance = FVector::Dist(OwnerLocation, Actor->GetActorLocation());
                if (Distance < NearestDistance)
                {
                    NearestDistance = Distance;
                    NearestNexus = Actor;
                }
            }
        }
    }

    return NearestNexus;
}

bool UAURACRONSigilComponent::CanReforge() const
{
    // Verificar se ainda pode re-forjar (máximo 1 vez por partida)
    if (ReforgeCount >= 1)
    {
        return false;
    }

    // Verificar cooldown de re-forjamento (2 minutos = 120 segundos)
    if (GetWorld() && LastReforgeTime > 0.0f)
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        float TimeSinceLastReforge = CurrentTime - LastReforgeTime;
        if (TimeSinceLastReforge < 120.0f)
        {
            return false;
        }
    }

    // Verificar se está próximo ao Nexus
    if (!IsNearNexus())
    {
        return false;
    }

    // Verificar se tem pelo menos um Sígilo equipado
    return GetEquippedSigilCount() > 0;
}

void UAURACRONSigilComponent::ReforgeSigil_Implementation(EAURACRONSigilType OldSigilType, EAURACRONSigilType NewSigilType)
{
    // Verificar se pode re-forjar
    if (!CanReforge())
    {
        return;
    }

    // Verificar se o Sígilo antigo está equipado
    if (!IsSigilEquipped(OldSigilType))
    {
        return;
    }

    // Verificar se o novo Sígilo é diferente do antigo
    if (OldSigilType == NewSigilType)
    {
        return;
    }

    // Obter informações do Sígilo antigo
    FAURACRONEquippedSigilInfo OldSigilInfo = GetSigilInfo(OldSigilType);

    // Remover o Sígilo antigo
    UnequipSigil(OldSigilType);

    // Equipar o novo Sígilo com o mesmo nível e raridade
    EquipSigil(NewSigilType, OldSigilInfo.Level, OldSigilInfo.Rarity);

    // Atualizar contadores
    ReforgeCount++;
    if (GetWorld())
    {
        LastReforgeTime = GetWorld()->GetTimeSeconds();
    }

    // Chamar evento
    OnSigilReforged(OldSigilType, NewSigilType);
}

void UAURACRONSigilComponent::ActivateSigilSpecificAbility_Implementation(EAURACRONSigilType SigilType)
{
    if (!AbilitySystemComponent || !IsSigilEquipped(SigilType))
    {
        return;
    }

    // Determinar qual habilidade específica ativar baseado no tipo de Sígilo
    TSubclassOf<UGameplayAbility> SpecificAbility = nullptr;

    switch (SigilType)
    {
        case EAURACRONSigilType::Aegis:
            // Murallion - cria barreira circular por 3s
            SpecificAbility = AegisMurallionAbility;
            break;

        case EAURACRONSigilType::Ruin:
            // Fracasso Prismal - reset parcial de recarga
            SpecificAbility = RuinFracassoPrismalAbility;
            break;

        case EAURACRONSigilType::Vesper:
            // Sopro de Fluxo - dash aliado + escudo
            SpecificAbility = VesperSoproDeFluxoAbility;
            break;
    }

    // Ativar a habilidade específica se disponível
    if (SpecificAbility)
    {
        // Criar spec da habilidade
        FAURACRONEquippedSigilInfo SigilInfo = GetSigilInfo(SigilType);
        FGameplayAbilitySpec AbilitySpec(SpecificAbility, SigilInfo.Level, INDEX_NONE, GetOwner());

        // Tentar ativar a habilidade
        FGameplayAbilitySpecHandle Handle = AbilitySystemComponent->GiveAbility(AbilitySpec);
        if (Handle.IsValid())
        {
            AbilitySystemComponent->TryActivateAbility(Handle);

            // Remover a habilidade após uso (habilidades específicas são de uso único)
            AbilitySystemComponent->ClearAbility(Handle);
        }

        // Chamar evento
        OnSigilSpecificAbilityActivated(SigilType);
    }
}

void UAURACRONSigilComponent::ApplySigilPassiveBonuses(FAURACRONEquippedSigilInfo& SigilInfo)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Aplicar bônus passivos específicos baseados na documentação
    TArray<TSubclassOf<UGameplayEffect>> PassiveEffects;

    switch (SigilInfo.SigilType)
    {
        case EAURACRONSigilType::Aegis:
            // +15% HP, Armadura adaptativa
            PassiveEffects = AegisBaseEffects;
            break;

        case EAURACRONSigilType::Ruin:
            // +12% ATK/AP adaptativo
            PassiveEffects = RuinBaseEffects;
            break;

        case EAURACRONSigilType::Vesper:
            // +10% Vel. Move + 8% Recarga
            PassiveEffects = VesperBaseEffects;
            break;
    }

    // Aplicar cada efeito passivo
    TArray<FActiveGameplayEffectHandle>& PassiveHandles = ActiveEffectHandles.FindOrAdd(SigilInfo.SigilType);

    for (const TSubclassOf<UGameplayEffect>& EffectClass : PassiveEffects)
    {
        if (EffectClass)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(GetOwner());

            // Usar nível e raridade do Sígilo para calcular magnitude
            float EffectLevel = SigilInfo.Level * (1.0f + (SigilInfo.Rarity - 1) * 0.1f);

            FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(EffectClass, EffectLevel, EffectContext);
            if (EffectSpecHandle.IsValid())
            {
                FActiveGameplayEffectHandle ActiveHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
                PassiveHandles.Add(ActiveHandle);
                SigilInfo.AppliedEffects.Add(EffectClass);
            }
        }
    }
}

void UAURACRONSigilComponent::RemoveSigilPassiveBonuses(const FAURACRONEquippedSigilInfo& SigilInfo)
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Remover efeitos passivos específicos
    TArray<FActiveGameplayEffectHandle>* PassiveHandles = ActiveEffectHandles.Find(SigilInfo.SigilType);
    if (PassiveHandles)
    {
        for (const FActiveGameplayEffectHandle& Handle : *PassiveHandles)
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(Handle);
        }
        PassiveHandles->Empty();
    }
}
