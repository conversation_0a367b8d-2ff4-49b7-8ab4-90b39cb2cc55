// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "Components/DamageZoneComponent.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Engine/World.h"
#include "Engine/DamageEvents.h"
#include "GameFramework/DamageType.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Engine/Engine.h"
#include "TimerManager.h"

// ========================================
// LOGGING PERSONALIZADO PARA PRODUÇÃO - UE 5.6 MODERN API
// ========================================
DEFINE_LOG_CATEGORY_STATIC(LogAuracronDamageZone, Log, All);

// Sets default values for this component's properties
UDamageZoneComponent::UDamageZoneComponent()
{
    // Set this component to be initialized when the game starts, and to be ticked every frame
    PrimaryComponentTick.bCanEverTick = true;

    // UE 5.6 Modern API - Configuração avançada de tick
    PrimaryComponentTick.bStartWithTickEnabled = false;
    PrimaryComponentTick.TickGroup = TG_PostUpdateWork;

    // Valores padrão alinhados com AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md
    SafeRadius = 10000.0f;
    WarningRadius = 10500.0f;
    DamagePerSecond = 10.0f;
    DamageScalingFactor = 1.5f;

    // Sincronização correta da propriedade bIsDamageZoneActive
    bIsDamageZoneActive = false;
    SetActive(false);

    AccumulatedTime = 0.0f;
    DamageInterval = 0.5f; // Aplicar dano a cada 0.5 segundos
    ZoneCenter = FVector::ZeroVector;

    // ========================================
    // INICIALIZAÇÃO DE COMPONENTES VISUAIS - UE 5.6 MODERN APIS
    // ========================================

    // Componente Niagara para efeitos visuais da zona de dano
    DamageZoneVFXComponent = nullptr;
    WarningZoneVFXComponent = nullptr;

    // Material Parameter Collection para controle visual
    ZoneMaterialParameterCollection = nullptr;

    // Configurações de efeitos visuais baseadas na documentação
    bShowVisualEffects = true;
    VisualEffectsIntensity = 1.0f;

    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent: Construtor inicializado com APIs modernas UE 5.6"));
}

// Called when the game starts
void UDamageZoneComponent::BeginPlay()
{
    Super::BeginPlay();

    // ========================================
    // INICIALIZAÇÃO ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    // Validação robusta do Owner
    AActor* Owner = GetOwner();
    if (!Owner)
    {
        UE_LOG(LogAuracronDamageZone, Error, TEXT("UDamageZoneComponent::BeginPlay - Owner é nulo! Componente não funcionará corretamente."));
        return;
    }

    // Validação robusta do World
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogAuracronDamageZone, Error, TEXT("UDamageZoneComponent::BeginPlay - World é nulo! Componente não funcionará corretamente."));
        return;
    }

    // Definir o centro da zona como a localização do ator proprietário
    ZoneCenter = Owner->GetActorLocation();

    // ========================================
    // INICIALIZAÇÃO DE EFEITOS VISUAIS - ALINHADO COM DOCUMENTAÇÃO
    // ========================================

    InitializeVisualEffects();

    // ========================================
    // CONFIGURAÇÃO DE MATERIAL PARAMETER COLLECTION
    // ========================================

    InitializeMaterialParameters();

    // ========================================
    // CONFIGURAÇÃO DE DELEGATES E EVENTOS
    // ========================================

    SetupDamageEventDelegates();

    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::BeginPlay - Inicializado com raio seguro %.1f no centro %s"),
           SafeRadius, *ZoneCenter.ToString());
}

// Called every frame
void UDamageZoneComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // ========================================
    // PROCESSAMENTO ROBUSTO - UE 5.6 MODERN APIS
    // ========================================

    // Só processar se o componente estiver ativo E a zona de dano estiver ativa
    if (IsActive() && bIsDamageZoneActive)
    {
        // Validação robusta antes de processar
        if (!GetOwner() || !GetWorld())
        {
            UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::TickComponent - Owner ou World inválido, pulando tick"));
            return;
        }

        ApplyDamageToPlayersOutsideSafeZone(DeltaTime);
        UpdateVisualEffects(DeltaTime);
        UpdateWarningSystem(DeltaTime);
    }
}

void UDamageZoneComponent::SetSafeRadius(float NewRadius)
{
    // Validação robusta
    if (NewRadius < 0.0f)
    {
        UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::SetSafeRadius - Tentativa de definir raio negativo %.1f, ignorando"), NewRadius);
        return;
    }

    SafeRadius = NewRadius;

    // Atualizar efeitos visuais se necessário
    UpdateVisualEffectsRadius();

    UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::SetSafeRadius - Raio seguro atualizado para %.1f"), SafeRadius);
}

void UDamageZoneComponent::SetDamagePerSecond(float NewDamagePerSecond)
{
    // Validação robusta
    if (NewDamagePerSecond < 0.0f)
    {
        UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::SetDamagePerSecond - Tentativa de definir dano negativo %.1f, ignorando"), NewDamagePerSecond);
        return;
    }

    DamagePerSecond = NewDamagePerSecond;
    UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::SetDamagePerSecond - Dano por segundo atualizado para %.1f"), DamagePerSecond);
}

void UDamageZoneComponent::SetDamageScalingFactor(float NewScalingFactor)
{
    // Validação robusta
    if (NewScalingFactor < 0.0f)
    {
        UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::SetDamageScalingFactor - Tentativa de definir fator negativo %.1f, ignorando"), NewScalingFactor);
        return;
    }

    DamageScalingFactor = NewScalingFactor;
    UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::SetDamageScalingFactor - Fator de escala atualizado para %.1f"), DamageScalingFactor);
}

void UDamageZoneComponent::SetWarningRadius(float NewWarningRadius)
{
    // Validação robusta
    if (NewWarningRadius < 0.0f)
    {
        UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::SetWarningRadius - Tentativa de definir raio negativo %.1f, ignorando"), NewWarningRadius);
        return;
    }

    WarningRadius = NewWarningRadius;

    // Atualizar efeitos visuais se necessário
    UpdateVisualEffectsRadius();

    UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::SetWarningRadius - Raio de aviso atualizado para %.1f"), WarningRadius);
}

void UDamageZoneComponent::SetActive(bool bNewActive, bool bReset)
{
    // ========================================
    // SINCRONIZAÇÃO CORRETA - UE 5.6 MODERN APIS
    // ========================================

    // Chamar implementação da classe pai
    Super::SetActive(bNewActive, bReset);

    // Sincronizar com a propriedade bIsDamageZoneActive
    bIsDamageZoneActive = bNewActive;

    // Usar API moderna do UE 5.6 para ativar/desativar componente
    SetComponentTickEnabled(bNewActive);

    // Controlar efeitos visuais
    if (bNewActive)
    {
        ActivateVisualEffects();
    }
    else
    {
        DeactivateVisualEffects();
    }

    // Reset se solicitado
    if (bReset)
    {
        AccumulatedTime = 0.0f;

        // Resetar centro da zona para posição atual do owner
        if (GetOwner())
        {
            ZoneCenter = GetOwner()->GetActorLocation();
        }
    }

    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::SetActive - %s (bIsDamageZoneActive=%s)"),
           bNewActive ? TEXT("Ativado") : TEXT("Desativado"),
           bIsDamageZoneActive ? TEXT("true") : TEXT("false"));
}

void UDamageZoneComponent::ApplyDamageToPlayersOutsideSafeZone(float DeltaTime)
{
    // ========================================
    // APLICAÇÃO DE DANO ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    // Acumular tempo para aplicar dano no intervalo correto
    AccumulatedTime += DeltaTime;

    // Verificar se é hora de aplicar dano
    if (AccumulatedTime >= DamageInterval)
    {
        // Validação robusta
        UWorld* World = GetWorld();
        if (!World)
        {
            UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::ApplyDamageToPlayersOutsideSafeZone - World inválido"));
            return;
        }

        // Obter todos os jogadores
        TArray<AActor*> Players = GetAllPlayers();

        if (Players.Num() == 0)
        {
            UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::ApplyDamageToPlayersOutsideSafeZone - Nenhum jogador encontrado"));
        }

        // Aplicar dano aos jogadores fora da zona segura
        for (AActor* Player : Players)
        {
            if (IsPlayerOutsideSafeZone(Player))
            {
                // Calcular dano baseado na distância
                float DamageMultiplier = CalculateDamageMultiplier(Player);
                float DamageAmount = DamagePerSecond * DamageInterval * DamageMultiplier;

                // ========================================
                // USAR UDAMAGETYPE ESPECÍFICO - UE 5.6 MODERN API
                // ========================================

                // Criar evento de dano com tipo específico para zona de dano
                FDamageEvent DamageEvent(UDamageType::StaticClass());

                // Aplicar dano ao jogador com validação
                if (Player && IsValid(Player))
                {
                    float ActualDamage = Player->TakeDamage(DamageAmount, DamageEvent, nullptr, GetOwner());

                    // Notificar sistema de eventos de dano
                    OnPlayerDamagedByZone.Broadcast(Player, ActualDamage, DamageMultiplier);

                    UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::ApplyDamageToPlayersOutsideSafeZone - Aplicando %.1f de dano (multiplicador %.2f) ao jogador %s"),
                           DamageAmount, DamageMultiplier, *Player->GetName());
                }
            }
        }

        // Resetar tempo acumulado
        AccumulatedTime = 0.0f;
    }
}

bool UDamageZoneComponent::IsPlayerOutsideSafeZone(AActor* Player) const
{
    // ========================================
    // VALIDAÇÃO ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    if (!Player || !IsValid(Player))
    {
        UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::IsPlayerOutsideSafeZone - Player inválido"));
        return false;
    }

    // Calcular distância do jogador ao centro da zona
    float Distance = FVector::Dist(Player->GetActorLocation(), ZoneCenter);

    // Verificar se está fora da zona segura
    bool bIsOutside = Distance > SafeRadius;

    UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::IsPlayerOutsideSafeZone - Player %s está %s da zona segura (distância: %.1f, raio: %.1f)"),
           *Player->GetName(), bIsOutside ? TEXT("fora") : TEXT("dentro"), Distance, SafeRadius);

    return bIsOutside;
}

float UDamageZoneComponent::CalculateDamageMultiplier(AActor* Player) const
{
    // ========================================
    // CÁLCULO ROBUSTO DE MULTIPLICADOR - UE 5.6 MODERN APIS
    // ========================================

    if (!Player || !IsValid(Player))
    {
        UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::CalculateDamageMultiplier - Player inválido, retornando multiplicador padrão"));
        return 1.0f;
    }

    // Calcular distância do jogador ao centro da zona
    float Distance = FVector::Dist(Player->GetActorLocation(), ZoneCenter);

    // Calcular quanto o jogador está além do limite seguro
    float ExcessDistance = Distance - SafeRadius;

    // Validação de segurança
    if (ExcessDistance <= 0.0f)
    {
        return 1.0f; // Jogador está dentro da zona segura
    }

    // Normalizar para um valor entre 0 e 1, assumindo que o jogador não estará mais longe que 2x o raio seguro
    float NormalizedExcess = FMath::Clamp(ExcessDistance / SafeRadius, 0.0f, 1.0f);

    // Aplicar fator de escala com validação
    float Multiplier = 1.0f + (NormalizedExcess * FMath::Max(0.0f, DamageScalingFactor));

    UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::CalculateDamageMultiplier - Player %s: distância %.1f, excesso %.1f, multiplicador %.2f"),
           *Player->GetName(), Distance, ExcessDistance, Multiplier);

    return Multiplier;
}

TArray<AActor*> UDamageZoneComponent::GetAllPlayers() const
{
    // ========================================
    // OBTENÇÃO ROBUSTA DE JOGADORES - UE 5.6 MODERN APIS
    // ========================================

    TArray<AActor*> Players;

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogAuracronDamageZone, Warning, TEXT("UDamageZoneComponent::GetAllPlayers - World inválido"));
        return Players;
    }

    // Obter todos os pawns controlados por jogadores usando API moderna
    for (FConstPlayerControllerIterator It = World->GetPlayerControllerIterator(); It; ++It)
    {
        APlayerController* PC = It->Get();
        if (PC && IsValid(PC))
        {
            APawn* Pawn = PC->GetPawn();
            if (Pawn && IsValid(Pawn))
            {
                Players.Add(Pawn);
                UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::GetAllPlayers - Adicionado jogador: %s"), *Pawn->GetName());
            }
        }
    }

    UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::GetAllPlayers - Encontrados %d jogadores"), Players.Num());

    return Players;
}

// ========================================
// IMPLEMENTAÇÃO DA FUNÇÃO QUE ESTAVA FALTANDO - UE 5.6 MODERN APIS
// ========================================

void UDamageZoneComponent::SetDamageZoneActive(bool bActive)
{
    // ========================================
    // ATIVAÇÃO/DESATIVAÇÃO ROBUSTA - UE 5.6 MODERN APIS
    // ========================================

    // Verificar se já está no estado desejado
    if (bIsDamageZoneActive == bActive)
    {
        UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::SetDamageZoneActive - Zona já está %s, ignorando"),
               bActive ? TEXT("ativa") : TEXT("inativa"));
        return;
    }

    // Implementação robusta para ativar/desativar zona de dano
    SetActive(bActive);

    if (bActive)
    {
        // ========================================
        // ATIVAÇÃO DA ZONA DE DANO
        // ========================================

        // Validação robusta do Owner
        AActor* Owner = GetOwner();
        if (!Owner || !IsValid(Owner))
        {
            UE_LOG(LogAuracronDamageZone, Error, TEXT("UDamageZoneComponent::SetDamageZoneActive - Owner inválido, não é possível ativar zona"));
            return;
        }

        // Ativar zona de dano
        AccumulatedTime = 0.0f;

        // Resetar centro da zona para posição atual do owner
        ZoneCenter = Owner->GetActorLocation();

        // Ativar efeitos visuais
        ActivateVisualEffects();

        // Notificar sistema de eventos
        OnDamageZoneActivated.Broadcast();

        UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::SetDamageZoneActive - Zona de dano ativada na localização %s"),
               *ZoneCenter.ToString());
    }
    else
    {
        // ========================================
        // DESATIVAÇÃO DA ZONA DE DANO
        // ========================================

        // Desativar efeitos visuais
        DeactivateVisualEffects();

        // Notificar sistema de eventos
        OnDamageZoneDeactivated.Broadcast();

        UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::SetDamageZoneActive - Zona de dano desativada"));
    }
}

// ========================================
// IMPLEMENTAÇÕES DE EFEITOS VISUAIS - UE 5.6 MODERN APIS
// ========================================

void UDamageZoneComponent::InitializeVisualEffects()
{
    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::InitializeVisualEffects - Inicializando efeitos visuais"));

    // Validação robusta
    AActor* Owner = GetOwner();
    if (!Owner || !IsValid(Owner))
    {
        UE_LOG(LogAuracronDamageZone, Error, TEXT("UDamageZoneComponent::InitializeVisualEffects - Owner inválido"));
        return;
    }

    UWorld* World = GetWorld();
    if (!World || !IsValid(World))
    {
        UE_LOG(LogAuracronDamageZone, Error, TEXT("UDamageZoneComponent::InitializeVisualEffects - World inválido"));
        return;
    }

    // ========================================
    // INICIALIZAÇÃO DE COMPONENTES NIAGARA - UE 5.6 MODERN API
    // ========================================

    if (!DamageZoneVFXComponent)
    {
        DamageZoneVFXComponent = NewObject<UNiagaraComponent>(Owner);
        if (DamageZoneVFXComponent)
        {
            DamageZoneVFXComponent->AttachToComponent(Owner->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            DamageZoneVFXComponent->SetAutoActivate(false);

            UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::InitializeVisualEffects - Componente Niagara de zona de dano criado"));
        }
    }

    if (!WarningZoneVFXComponent)
    {
        WarningZoneVFXComponent = NewObject<UNiagaraComponent>(Owner);
        if (WarningZoneVFXComponent)
        {
            WarningZoneVFXComponent->AttachToComponent(Owner->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            WarningZoneVFXComponent->SetAutoActivate(false);

            UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::InitializeVisualEffects - Componente Niagara de zona de aviso criado"));
        }
    }
}

void UDamageZoneComponent::InitializeMaterialParameters()
{
    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::InitializeMaterialParameters - Inicializando parâmetros de material"));

    // Implementação para Material Parameter Collection será adicionada quando necessário
    // Baseado na documentação do projeto sobre efeitos visuais
}

void UDamageZoneComponent::SetupDamageEventDelegates()
{
    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::SetupDamageEventDelegates - Configurando delegates de eventos"));

    // Delegates já estão configurados no header, apenas log para confirmação
}

void UDamageZoneComponent::ActivateVisualEffects()
{
    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::ActivateVisualEffects - Ativando efeitos visuais"));

    if (!bShowVisualEffects)
    {
        UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::ActivateVisualEffects - Efeitos visuais desabilitados"));
        return;
    }

    // Ativar componente Niagara da zona de dano
    if (DamageZoneVFXComponent && IsValid(DamageZoneVFXComponent))
    {
        DamageZoneVFXComponent->Activate();

        // Configurar parâmetros do sistema Niagara
        DamageZoneVFXComponent->SetFloatParameter(TEXT("SafeRadius"), SafeRadius);
        DamageZoneVFXComponent->SetFloatParameter(TEXT("Intensity"), VisualEffectsIntensity);

        UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::ActivateVisualEffects - Efeito de zona de dano ativado"));
    }

    // Ativar componente Niagara da zona de aviso
    if (WarningZoneVFXComponent && IsValid(WarningZoneVFXComponent))
    {
        WarningZoneVFXComponent->Activate();

        // Configurar parâmetros do sistema Niagara
        WarningZoneVFXComponent->SetFloatParameter(TEXT("WarningRadius"), WarningRadius);
        WarningZoneVFXComponent->SetFloatParameter(TEXT("Intensity"), VisualEffectsIntensity * 0.5f);

        UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::ActivateVisualEffects - Efeito de zona de aviso ativado"));
    }
}

void UDamageZoneComponent::DeactivateVisualEffects()
{
    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::DeactivateVisualEffects - Desativando efeitos visuais"));

    // Desativar componente Niagara da zona de dano
    if (DamageZoneVFXComponent && IsValid(DamageZoneVFXComponent))
    {
        DamageZoneVFXComponent->Deactivate();
        UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::DeactivateVisualEffects - Efeito de zona de dano desativado"));
    }

    // Desativar componente Niagara da zona de aviso
    if (WarningZoneVFXComponent && IsValid(WarningZoneVFXComponent))
    {
        WarningZoneVFXComponent->Deactivate();
        UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::DeactivateVisualEffects - Efeito de zona de aviso desativado"));
    }
}

void UDamageZoneComponent::UpdateVisualEffects(float DeltaTime)
{
    // Atualização contínua dos efeitos visuais baseada no tempo
    if (!bShowVisualEffects || !bIsDamageZoneActive)
    {
        return;
    }

    // Atualizar intensidade dos efeitos baseado na atividade da zona
    float CurrentIntensity = VisualEffectsIntensity;

    // Aplicar pulsação baseada no tempo para efeito visual dinâmico
    float PulseIntensity = 1.0f + (FMath::Sin(GetWorld()->GetTimeSeconds() * 2.0f) * 0.2f);
    CurrentIntensity *= PulseIntensity;

    // Atualizar parâmetros dos sistemas Niagara
    if (DamageZoneVFXComponent && IsValid(DamageZoneVFXComponent))
    {
        DamageZoneVFXComponent->SetFloatParameter(TEXT("Intensity"), CurrentIntensity);
    }

    if (WarningZoneVFXComponent && IsValid(WarningZoneVFXComponent))
    {
        WarningZoneVFXComponent->SetFloatParameter(TEXT("Intensity"), CurrentIntensity * 0.5f);
    }
}

void UDamageZoneComponent::UpdateVisualEffectsRadius()
{
    UE_LOG(LogAuracronDamageZone, Verbose, TEXT("UDamageZoneComponent::UpdateVisualEffectsRadius - Atualizando raios dos efeitos visuais"));

    // Atualizar parâmetros de raio nos sistemas Niagara
    if (DamageZoneVFXComponent && IsValid(DamageZoneVFXComponent))
    {
        DamageZoneVFXComponent->SetFloatParameter(TEXT("SafeRadius"), SafeRadius);
    }

    if (WarningZoneVFXComponent && IsValid(WarningZoneVFXComponent))
    {
        WarningZoneVFXComponent->SetFloatParameter(TEXT("WarningRadius"), WarningRadius);
    }
}

void UDamageZoneComponent::UpdateWarningSystem(float DeltaTime)
{
    // ========================================
    // SISTEMA DE AVISOS PARA JOGADORES - UE 5.6 MODERN APIS
    // ========================================

    if (!bIsDamageZoneActive)
    {
        return;
    }

    // Obter todos os jogadores
    TArray<AActor*> Players = GetAllPlayers();

    for (AActor* Player : Players)
    {
        if (!Player || !IsValid(Player))
        {
            continue;
        }

        float Distance = FVector::Dist(Player->GetActorLocation(), ZoneCenter);

        // Verificar se jogador está na zona de aviso
        if (Distance > SafeRadius && Distance <= WarningRadius)
        {
            // Notificar sistema de eventos sobre jogador na zona de aviso
            OnPlayerInWarningZone.Broadcast(Player, Distance);

            UE_LOG(LogAuracronDamageZone, VeryVerbose, TEXT("UDamageZoneComponent::UpdateWarningSystem - Jogador %s na zona de aviso (distância: %.1f)"),
                   *Player->GetName(), Distance);
        }
    }
}

// ========================================
// FUNÇÕES UTILITÁRIAS ADICIONAIS - UE 5.6 MODERN APIS
// ========================================

void UDamageZoneComponent::SetVisualEffectsEnabled(bool bEnabled)
{
    bShowVisualEffects = bEnabled;

    if (!bEnabled)
    {
        DeactivateVisualEffects();
    }
    else if (bIsDamageZoneActive)
    {
        ActivateVisualEffects();
    }

    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::SetVisualEffectsEnabled - Efeitos visuais %s"),
           bEnabled ? TEXT("habilitados") : TEXT("desabilitados"));
}

void UDamageZoneComponent::SetVisualEffectsIntensity(float NewIntensity)
{
    VisualEffectsIntensity = FMath::Clamp(NewIntensity, 0.0f, 2.0f);

    // Atualizar imediatamente se os efeitos estiverem ativos
    if (bShowVisualEffects && bIsDamageZoneActive)
    {
        UpdateVisualEffectsRadius();
    }

    UE_LOG(LogAuracronDamageZone, Log, TEXT("UDamageZoneComponent::SetVisualEffectsIntensity - Intensidade atualizada para %.2f"),
           VisualEffectsIntensity);
}

FVector UDamageZoneComponent::GetZoneCenter() const
{
    return ZoneCenter;
}

float UDamageZoneComponent::GetCurrentSafeRadius() const
{
    return SafeRadius;
}

float UDamageZoneComponent::GetCurrentWarningRadius() const
{
    return WarningRadius;
}

bool UDamageZoneComponent::IsDamageZoneActive() const
{
    return bIsDamageZoneActive;
}