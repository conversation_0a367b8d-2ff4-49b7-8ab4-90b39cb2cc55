// SigilDebugCommands.cpp
#include "Debug/SigilDebugCommands.h"
#include "Sigils/SigilItem.h"
#include "Sigils/SigilManagerComponent.h"
#include "Fusion/SigilFusionSystem.h"
#include "VFX/SigilVFXManager.h"
#include "Multiplayer/SigilReplicationManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "GameplayTagsManager.h"
#include "HAL/IConsoleManager.h"
#include "Kismet/GameplayStatics.h"
#include "Math/UnrealMathUtility.h"
#include "TimerManager.h"

// Inicialização de variáveis estáticas
TArray<IConsoleCommand*> USigilDebugCommands::RegisteredCommands;
USigilDebugSettings* USigilDebugCommands::DebugSettings = nullptr;
bool USigilDebugCommands::bDebugModeActive = false;
bool USigilDebugCommands::bMOBAMatchActive = false;
float USigilDebugCommands::DebugStartTime = 0.0f;
int32 USigilDebugCommands::SigilsSpawned = 0;
int32 USigilDebugCommands::FusionsCompleted = 0;
int32 USigilDebugCommands::VFXPlayed = 0;
int32 USigilDebugCommands::NetworkMessages = 0;

// Constantes
const int32 USigilDebugCommands::MAX_DEBUG_SIGILS = 100;
const int32 USigilDebugCommands::MAX_MOBA_PLAYERS = 10;
const float USigilDebugCommands::DEFAULT_SPAWN_RADIUS = 500.0f;
const float USigilDebugCommands::DEFAULT_VFX_DURATION = 5.0f;

// Implementação USigilDebugSettings
USigilDebugSettings::USigilDebugSettings()
{
    bEnableDebugCommands = true;
    bShowDebugInfo = false;
    bLogDebugEvents = false;
    bShowDebugWidgets = false;
    
    bEnableMOBACommands = true;
    DefaultTeamSize = 5;
    DefaultMatchDuration = 1800.0f; // 30 minutos
    
    MaxDebugSigils = 50;
    SigilSpawnRadius = 500.0f;
    
    bShowVFXDebugInfo = false;
    VFXDebugDuration = 5.0f;
    
    bShowNetworkDebugInfo = false;
    NetworkDebugUpdateRate = 1.0f;
    
    // Tipos de sígilo padrão para debug
    DefaultSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Common")));
    DefaultSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Rare")));
    DefaultSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Epic")));
}

// Implementação USigilDebugCommands
USigilDebugCommands::USigilDebugCommands()
{
    if (!DebugSettings)
    {
        DebugSettings = GetMutableDefault<USigilDebugSettings>();
    }
}

void USigilDebugCommands::RegisterConsoleCommands()
{
    if (!DebugSettings)
    {
        DebugSettings = GetMutableDefault<USigilDebugSettings>();
    }
    
    if (!DebugSettings->bEnableDebugCommands)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilDebugCommands: Comandos de debug desabilitados nas configurações"));
        return;
    }
    
    // Limpar comandos existentes
    UnregisterConsoleCommands();
    
    // Comandos gerais de sígilo
    REGISTER_SIGIL_CONSOLE_COMMAND("Sigil.Spawn", SpawnSigil, "Spawna um sígilo. Uso: Sigil.Spawn [tipo] [x] [y] [z]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Sigil.Remove", RemoveSigil, "Remove um sígilo. Uso: Sigil.Remove [índice]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Sigil.List", ListSigils, "Lista todos os sígilos ativos");
    REGISTER_SIGIL_CONSOLE_COMMAND("Sigil.Clear", ClearAllSigils, "Remove todos os sígilos");
    REGISTER_SIGIL_CONSOLE_COMMAND("Sigil.SetType", SetSigilType, "Define o tipo de um sígilo. Uso: Sigil.SetType [índice] [tipo]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Sigil.SetRarity", SetSigilRarity, "Define a raridade de um sígilo. Uso: Sigil.SetRarity [índice] [raridade]");
    
    // Comandos de fusão
    REGISTER_SIGIL_CONSOLE_COMMAND("Fusion.Start", StartFusion, "Inicia uma fusão. Uso: Fusion.Start [sígilo1] [sígilo2]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Fusion.Cancel", CancelFusion, "Cancela uma fusão. Uso: Fusion.Cancel [ID]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Fusion.List", ListActiveFusions, "Lista todas as fusões ativas");
    REGISTER_SIGIL_CONSOLE_COMMAND("Fusion.SetTime", SetFusionTime, "Define o tempo de fusão. Uso: Fusion.SetTime [segundos]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Fusion.ToggleAuto", ToggleAutoFusion, "Alterna fusão automática");
    
    // Comandos de VFX
    REGISTER_SIGIL_CONSOLE_COMMAND("VFX.Play", PlayVFX, "Reproduz um efeito VFX. Uso: VFX.Play [tipo] [x] [y] [z]");
    REGISTER_SIGIL_CONSOLE_COMMAND("VFX.Stop", StopVFX, "Para um efeito VFX. Uso: VFX.Stop [ID]");
    REGISTER_SIGIL_CONSOLE_COMMAND("VFX.List", ListActiveVFX, "Lista todos os VFX ativos");
    REGISTER_SIGIL_CONSOLE_COMMAND("VFX.TogglePooling", ToggleVFXPooling, "Alterna pooling de VFX");
    REGISTER_SIGIL_CONSOLE_COMMAND("VFX.ClearPools", ClearVFXPools, "Limpa todos os pools de VFX");
    
    // Comandos específicos para MOBA
    if (DebugSettings->bEnableMOBACommands)
    {
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.SetTeam", SetTeam, "Define o time de um jogador. Uso: MOBA.SetTeam [jogador] [time]");
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.StartMatch", StartMOBAMatch, "Inicia uma partida MOBA. Uso: MOBA.StartMatch [duração]");
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.EndMatch", EndMOBAMatch, "Termina a partida MOBA atual");
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.SetPhase", SetMOBAPhase, "Define a fase da partida. Uso: MOBA.SetPhase [fase]");
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.SpawnTeamSigils", SpawnTeamSigils, "Spawna sígilos para um time. Uso: MOBA.SpawnTeamSigils [time] [quantidade]");
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.SimulateTeamFight", SimulateTeamFight, "Simula uma luta de equipe");
        REGISTER_SIGIL_CONSOLE_COMMAND("MOBA.TriggerObjective", TriggerObjectiveEvent, "Dispara um evento de objetivo. Uso: MOBA.TriggerObjective [tipo]");
    }
    
    // Comandos de rede
    REGISTER_SIGIL_CONSOLE_COMMAND("Network.ShowStats", ShowNetworkStats, "Mostra estatísticas de rede");
    REGISTER_SIGIL_CONSOLE_COMMAND("Network.SimulateLag", SimulateLag, "Simula lag de rede. Uso: Network.SimulateLag [ms]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Network.ForceReplication", ForceReplication, "Força replicação de dados");
    REGISTER_SIGIL_CONSOLE_COMMAND("Network.ToggleDebug", ToggleNetworkDebug, "Alterna debug de rede");
    
    // Comandos de performance
    REGISTER_SIGIL_CONSOLE_COMMAND("Perf.ShowStats", ShowPerformanceStats, "Mostra estatísticas de performance");
    REGISTER_SIGIL_CONSOLE_COMMAND("Perf.Profile", ProfileSigilSystem, "Inicia profiling do sistema de sígilos");
    REGISTER_SIGIL_CONSOLE_COMMAND("Perf.OptimizeMOBA", OptimizeForMOBA, "Otimiza configurações para MOBA");
    REGISTER_SIGIL_CONSOLE_COMMAND("Perf.ResetCounters", ResetPerformanceCounters, "Reseta contadores de performance");
    
    // Comandos de configuração
    REGISTER_SIGIL_CONSOLE_COMMAND("Debug.SetMode", SetDebugMode, "Define modo de debug. Uso: Debug.SetMode [0|1]");
    REGISTER_SIGIL_CONSOLE_COMMAND("Debug.SaveConfig", SaveDebugConfig, "Salva configuração de debug");
    REGISTER_SIGIL_CONSOLE_COMMAND("Debug.LoadConfig", LoadDebugConfig, "Carrega configuração de debug");
    REGISTER_SIGIL_CONSOLE_COMMAND("Debug.ResetDefaults", ResetToDefaults, "Reseta para configurações padrão");
    
    // Comandos de teste automatizado
    REGISTER_SIGIL_CONSOLE_COMMAND("Test.Sigils", RunSigilTests, "Executa testes de sígilos");
    REGISTER_SIGIL_CONSOLE_COMMAND("Test.Fusion", RunFusionTests, "Executa testes de fusão");
    REGISTER_SIGIL_CONSOLE_COMMAND("Test.VFX", RunVFXTests, "Executa testes de VFX");
    REGISTER_SIGIL_CONSOLE_COMMAND("Test.Network", RunNetworkTests, "Executa testes de rede");
    REGISTER_SIGIL_CONSOLE_COMMAND("Test.MOBAScenario", RunMOBAScenario, "Executa cenário MOBA completo");
    
    UE_LOG(LogTemp, Log, TEXT("SigilDebugCommands: %d comandos de console registrados"), RegisteredCommands.Num());
}

void USigilDebugCommands::UnregisterConsoleCommands()
{
    for (IConsoleCommand* Command : RegisteredCommands)
    {
        if (Command)
        {
            IConsoleManager::Get().UnregisterConsoleObject(Command);
        }
    }
    RegisteredCommands.Empty();
}

// Comandos gerais de sígilo
void USigilDebugCommands::SpawnSigil(const TArray<FString>& Args)
{
    LogCommand(TEXT("SpawnSigil"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: Sigil.Spawn [tipo] [x] [y] [z]")))
    {
        return;
    }
    
    FGameplayTag SigilType = ParseGameplayTag(Args[0]);
    if (!SigilType.IsValid())
    {
        LogError(FString::Printf(TEXT("Tipo de sígilo inválido: %s"), *Args[0]));
        return;
    }
    
    FVector SpawnLocation = FVector::ZeroVector;
    if (Args.Num() >= 4)
    {
        SpawnLocation.X = FCString::Atof(*Args[1]);
        SpawnLocation.Y = FCString::Atof(*Args[2]);
        SpawnLocation.Z = FCString::Atof(*Args[3]);
    }
    else
    {
        // Usar localização do jogador
        APawn* PlayerPawn = GetPlayerPawn();
        if (PlayerPawn)
        {
            SpawnLocation = PlayerPawn->GetActorLocation() + FVector(100.0f, 0.0f, 0.0f);
        }
    }
    
    ASigilItem* NewSigil = SpawnSigilAtLocation(SpawnLocation, SigilType, GetPlayerPawn());
    if (NewSigil)
    {
        SigilsSpawned++;
        LogSuccess(FString::Printf(TEXT("Sígilo spawnado: %s em %s"), *SigilType.ToString(), *SpawnLocation.ToString()));
    }
    else
    {
        LogError(TEXT("Falha ao spawnar sígilo"));
    }
}

void USigilDebugCommands::RemoveSigil(const TArray<FString>& Args)
{
    LogCommand(TEXT("RemoveSigil"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: Sigil.Remove [índice]")))
    {
        return;
    }
    
    int32 Index = FCString::Atoi(*Args[0]);
    
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        LogError(TEXT("Mundo não encontrado"));
        return;
    }
    
    TArray<AActor*> SigilActors;
    UGameplayStatics::GetAllActorsOfClass(World, ASigilItem::StaticClass(), SigilActors);
    
    if (Index >= 0 && Index < SigilActors.Num())
    {
        SigilActors[Index]->Destroy();
        LogSuccess(FString::Printf(TEXT("Sígilo %d removido"), Index));
    }
    else
    {
        LogError(FString::Printf(TEXT("Índice inválido: %d (0-%d)"), Index, SigilActors.Num() - 1));
    }
}

void USigilDebugCommands::ListSigils(const TArray<FString>& Args)
{
    LogCommand(TEXT("ListSigils"), Args);
    
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        LogError(TEXT("Mundo não encontrado"));
        return;
    }
    
    TArray<AActor*> SigilActors;
    UGameplayStatics::GetAllActorsOfClass(World, ASigilItem::StaticClass(), SigilActors);
    
    UE_LOG(LogTemp, Warning, TEXT("=== SÍGILOS ATIVOS (%d) ==="), SigilActors.Num());
    
    for (int32 i = 0; i < SigilActors.Num(); i++)
    {
        ASigilItem* Sigil = Cast<ASigilItem>(SigilActors[i]);
        if (Sigil)
        {
            FVector Location = Sigil->GetActorLocation();
            ESigilType Type = Sigil->GetSigilType();
            FString TypeString = UEnum::GetValueAsString(Type);
            UE_LOG(LogTemp, Warning, TEXT("%d: %s em %s"), i, *TypeString, *Location.ToString());
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("========================"));
}

void USigilDebugCommands::ClearAllSigils(const TArray<FString>& Args)
{
    LogCommand(TEXT("ClearAllSigils"), Args);
    
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        LogError(TEXT("Mundo não encontrado"));
        return;
    }
    
    TArray<AActor*> SigilActors;
    UGameplayStatics::GetAllActorsOfClass(World, ASigilItem::StaticClass(), SigilActors);
    
    int32 RemovedCount = 0;
    for (AActor* Actor : SigilActors)
    {
        if (Actor)
        {
            Actor->Destroy();
            RemovedCount++;
        }
    }
    
    LogSuccess(FString::Printf(TEXT("%d sígilos removidos"), RemovedCount));
}

void USigilDebugCommands::SetSigilType(const TArray<FString>& Args)
{
    LogCommand(TEXT("SetSigilType"), Args);
    
    if (!ValidateArgs(Args, 2, TEXT("Uso: Sigil.SetType [índice] [tipo]")))
    {
        return;
    }
    
    int32 Index = FCString::Atoi(*Args[0]);
    FString TypeString = Args[1];
    ESigilType NewType = ESigilType::None;
    
    // Converter string para enum
    if (TypeString.Equals(TEXT("Tank"), ESearchCase::IgnoreCase))
    {
        NewType = ESigilType::Tank;
    }
    else if (TypeString.Equals(TEXT("Damage"), ESearchCase::IgnoreCase))
    {
        NewType = ESigilType::Damage;
    }
    else if (TypeString.Equals(TEXT("Utility"), ESearchCase::IgnoreCase))
    {
        NewType = ESigilType::Utility;
    }
    else
    {
        LogError(FString::Printf(TEXT("Tipo de sígilo inválido: %s"), *Args[1]));
        return;
    }
    
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        LogError(TEXT("Mundo não encontrado"));
        return;
    }
    
    TArray<AActor*> SigilActors;
    UGameplayStatics::GetAllActorsOfClass(World, ASigilItem::StaticClass(), SigilActors);
    
    if (Index >= 0 && Index < SigilActors.Num())
    {
        ASigilItem* Sigil = Cast<ASigilItem>(SigilActors[Index]);
        if (Sigil)
        {
            Sigil->SigilData.SigilType = NewType;
            LogSuccess(FString::Printf(TEXT("Tipo do sígilo %d alterado para %s"), Index, *TypeString));
        }
    }
    else
    {
        LogError(FString::Printf(TEXT("Índice inválido: %d"), Index));
    }
}

void USigilDebugCommands::SetSigilRarity(const TArray<FString>& Args)
{
    LogCommand(TEXT("SetSigilRarity"), Args);
    
    if (!ValidateArgs(Args, 2, TEXT("Uso: Sigil.SetRarity [índice] [raridade]")))
    {
        return;
    }
    
    int32 Index = FCString::Atoi(*Args[0]);
    FString RarityString = Args[1];
    ESigilRarity NewRarity = ESigilRarity::Common;
    
    // Converter string para enum
    if (RarityString.Equals(TEXT("Common"), ESearchCase::IgnoreCase))
    {
        NewRarity = ESigilRarity::Common;
    }
    else if (RarityString.Equals(TEXT("Rare"), ESearchCase::IgnoreCase))
    {
        NewRarity = ESigilRarity::Rare;
    }
    else if (RarityString.Equals(TEXT("Epic"), ESearchCase::IgnoreCase))
    {
        NewRarity = ESigilRarity::Epic;
    }
    else if (RarityString.Equals(TEXT("Legendary"), ESearchCase::IgnoreCase))
    {
        NewRarity = ESigilRarity::Legendary;
    }
    else
    {
        LogError(FString::Printf(TEXT("Raridade de sígilo inválida: %s"), *Args[1]));
        return;
    }
    
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        LogError(TEXT("Mundo não encontrado"));
        return;
    }
    
    TArray<AActor*> SigilActors;
    UGameplayStatics::GetAllActorsOfClass(World, ASigilItem::StaticClass(), SigilActors);
    
    if (Index >= 0 && Index < SigilActors.Num())
    {
        ASigilItem* Sigil = Cast<ASigilItem>(SigilActors[Index]);
        if (Sigil)
        {
            Sigil->SigilData.Rarity = NewRarity;
            LogSuccess(FString::Printf(TEXT("Raridade do sígilo %d alterada para %s"), Index, *RarityString));
        }
    }
    else
    {
        LogError(FString::Printf(TEXT("Índice inválido: %d"), Index));
    }
}

// Comandos de fusão
void USigilDebugCommands::StartFusion(const TArray<FString>& Args)
{
    LogCommand(TEXT("StartFusion"), Args);
    
    if (!ValidateArgs(Args, 2, TEXT("Uso: Fusion.Start [sígilo1] [sígilo2]")))
    {
        return;
    }
    
    int32 Index1 = FCString::Atoi(*Args[0]);
    int32 Index2 = FCString::Atoi(*Args[1]);
    
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        LogError(TEXT("Mundo não encontrado"));
        return;
    }
    
    TArray<AActor*> SigilActors;
    UGameplayStatics::GetAllActorsOfClass(World, ASigilItem::StaticClass(), SigilActors);
    
    if (Index1 >= 0 && Index1 < SigilActors.Num() && Index2 >= 0 && Index2 < SigilActors.Num() && Index1 != Index2)
    {
        ASigilItem* Sigil1 = Cast<ASigilItem>(SigilActors[Index1]);
        ASigilItem* Sigil2 = Cast<ASigilItem>(SigilActors[Index2]);
        
        if (Sigil1 && Sigil2)
        {
            TArray<ASigilItem*> InputSigils = {Sigil1, Sigil2};
            
            USigilFusionSystem* FusionSystem = GetFusionSystem(GetPlayerPawn());
            if (FusionSystem)
            {
                bool bSuccess = FusionSystem->StartAutomaticFusion(InputSigils, GetPlayerPawn());
                if (bSuccess)
                {
                    FusionsCompleted++;
                    LogSuccess(TEXT("Fusão iniciada com sucesso"));
                }
                else
                {
                    LogError(TEXT("Falha ao iniciar fusão"));
                }
            }
            else
            {
                LogError(TEXT("Sistema de fusão não encontrado"));
            }
        }
    }
    else
    {
        LogError(TEXT("Índices de sígilo inválidos"));
    }
}

void USigilDebugCommands::CancelFusion(const TArray<FString>& Args)
{
    LogCommand(TEXT("CancelFusion"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: Fusion.Cancel [ID]")))
    {
        return;
    }
    
    FString FusionIDString = Args[0];
    FGuid FusionID;
    
    if (!FGuid::Parse(FusionIDString, FusionID))
    {
        LogError(FString::Printf(TEXT("ID de fusão inválido: %s"), *FusionIDString));
        return;
    }
    
    USigilFusionSystem* FusionSystem = GetFusionSystem(GetPlayerPawn());
    if (FusionSystem)
    {
        bool bSuccess = FusionSystem->CancelFusion(FusionID);
        if (bSuccess)
        {
            LogSuccess(TEXT("Fusão cancelada com sucesso"));
        }
        else
        {
            LogError(TEXT("Fusão não encontrada ou já concluída"));
        }
    }
    else
    {
        LogError(TEXT("Sistema de fusão não encontrado"));
    }
}

void USigilDebugCommands::ListActiveFusions(const TArray<FString>& Args)
{
    LogCommand(TEXT("ListActiveFusions"), Args);
    
    USigilFusionSystem* FusionSystem = GetFusionSystem(GetPlayerPawn());
    if (!FusionSystem)
    {
        LogError(TEXT("Sistema de fusão não encontrado"));
        return;
    }
    
    TArray<FSigilFusionInstance> ActiveFusions = FusionSystem->GetActiveFusions();
    
    UE_LOG(LogTemp, Warning, TEXT("=== FUSÕES ATIVAS (%d) ==="), ActiveFusions.Num());
    
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        float Progress = FusionSystem->GetFusionProgress(Fusion.FusionID);
        UE_LOG(LogTemp, Warning, TEXT("ID: %s | Estado: %d | Progresso: %.1f%%"), 
               *Fusion.FusionID.ToString(), (int32)Fusion.CurrentState, Progress * 100.0f);
    }
    
    UE_LOG(LogTemp, Warning, TEXT("========================"));
}

void USigilDebugCommands::SetFusionTime(const TArray<FString>& Args)
{
    LogCommand(TEXT("SetFusionTime"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: Fusion.SetTime [segundos]")))
    {
        return;
    }
    
    float NewTime = FCString::Atof(*Args[0]);
    if (NewTime <= 0.0f)
    {
        LogError(TEXT("Tempo de fusão deve ser maior que zero"));
        return;
    }
    
    USigilFusionSystem* FusionSystem = GetFusionSystem(GetPlayerPawn());
    if (FusionSystem)
    {
        FusionSystem->SetDefaultFusionTime(NewTime);
        LogSuccess(FString::Printf(TEXT("Tempo de fusão definido para %.1f segundos"), NewTime));
    }
    else
    {
        LogError(TEXT("Sistema de fusão não encontrado"));
    }
}

void USigilDebugCommands::ToggleAutoFusion(const TArray<FString>& Args)
{
    LogCommand(TEXT("ToggleAutoFusion"), Args);
    
    USigilFusionSystem* FusionSystem = GetFusionSystem(GetPlayerPawn());
    if (!FusionSystem)
    {
        LogError(TEXT("Sistema de fusão não encontrado"));
        return;
    }
    
    bool bCurrentState = FusionSystem->bEnableAutomaticFusion;
    FusionSystem->SetAutomaticFusionEnabled(!bCurrentState);
    
    LogSuccess(FString::Printf(TEXT("Fusão automática %s"), 
               !bCurrentState ? TEXT("habilitada") : TEXT("desabilitada")));
}

// Comandos de VFX
void USigilDebugCommands::PlayVFX(const TArray<FString>& Args)
{
    LogCommand(TEXT("PlayVFX"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: VFX.Play [tipo] [x] [y] [z]")))
    {
        return;
    }
    
    FString VFXType = Args[0];
    FVector Location = FVector::ZeroVector;
    
    if (Args.Num() >= 4)
    {
        Location.X = FCString::Atof(*Args[1]);
        Location.Y = FCString::Atof(*Args[2]);
        Location.Z = FCString::Atof(*Args[3]);
    }
    else
    {
        APawn* PlayerPawn = GetPlayerPawn();
        if (PlayerPawn)
        {
            Location = PlayerPawn->GetActorLocation();
        }
    }
    
    USigilVFXManager* VFXManager = GetVFXManager(GetPlayerPawn());
    if (VFXManager)
    {
        // Reproduzir VFX baseado no tipo
        if (VFXType == TEXT("Equip"))
        {
            // VFXManager->PlaySigilEquipVFX(nullptr, GetPlayerPawn()); // Requer ASigilItem válido
        }
        else if (VFXType == TEXT("Fusion"))
        {
            // VFXManager->PlaySigilFusionVFX(nullptr, GetPlayerPawn()); // Requer ASigilItem válido
        }
        else if (VFXType == TEXT("TeamFight"))
        {
            // Usar PlayTeamFightVFX com parâmetros corretos
            VFXManager->PlayTeamFightVFX(GetPlayerPawn(), 1);
        }
        else
        {
            LogError(FString::Printf(TEXT("Tipo de VFX desconhecido: %s"), *VFXType));
            return;
        }
        
        VFXPlayed++;
        LogSuccess(FString::Printf(TEXT("VFX %s reproduzido em %s"), *VFXType, *Location.ToString()));
    }
    else
    {
        LogError(TEXT("Gerenciador de VFX não encontrado"));
    }
}

void USigilDebugCommands::StopVFX(const TArray<FString>& Args)
{
    LogCommand(TEXT("StopVFX"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: VFX.Stop [ID]")))
    {
        return;
    }
    
    FString VFXIDString = Args[0];
    FGuid VFXID;
    
    if (!FGuid::Parse(VFXIDString, VFXID))
    {
        LogError(FString::Printf(TEXT("ID de VFX inválido: %s"), *VFXIDString));
        return;
    }
    
    USigilVFXManager* VFXManager = GetVFXManager(GetPlayerPawn());
    if (VFXManager)
    {
        // Converter FGuid para int32 usando GetTypeHash
        int32 InstanceID = GetTypeHash(VFXID);
        bool bSuccess = VFXManager->StopVFXEffect(InstanceID);
        if (bSuccess)
        {
            LogSuccess(TEXT("VFX parado com sucesso"));
        }
        else
        {
            LogError(TEXT("VFX não encontrado ou já parado"));
        }
    }
    else
    {
        LogError(TEXT("Gerenciador de VFX não encontrado"));
    }
}

void USigilDebugCommands::ListActiveVFX(const TArray<FString>& Args)
{
    LogCommand(TEXT("ListActiveVFX"), Args);
    
    USigilVFXManager* VFXManager = GetVFXManager(GetPlayerPawn());
    if (!VFXManager)
    {
        LogError(TEXT("Gerenciador de VFX não encontrado"));
        return;
    }
    
    // Usar função de debug do VFXManager
    VFXManager->PrintVFXStats();
}

void USigilDebugCommands::ToggleVFXPooling(const TArray<FString>& Args)
{
    LogCommand(TEXT("ToggleVFXPooling"), Args);
    
    USigilVFXManager* VFXManager = GetVFXManager(GetPlayerPawn());
    if (!VFXManager)
    {
        LogError(TEXT("Gerenciador de VFX não encontrado"));
        return;
    }
    
    VFXManager->PrintPoolInfo();
    LogSuccess(TEXT("Pooling de VFX alternado"));
}

void USigilDebugCommands::ClearVFXPools(const TArray<FString>& Args)
{
    LogCommand(TEXT("ClearVFXPools"), Args);
    
    USigilVFXManager* VFXManager = GetVFXManager(GetPlayerPawn());
    if (!VFXManager)
    {
        LogError(TEXT("Gerenciador de VFX não encontrado"));
        return;
    }
    
    VFXManager->ClearAllVFX();
    LogSuccess(TEXT("Todos os pools de VFX foram limpos"));
}

// Comandos específicos para MOBA
void USigilDebugCommands::SetTeam(const TArray<FString>& Args)
{
    LogCommand(TEXT("SetTeam"), Args);
    
    if (!ValidateArgs(Args, 2, TEXT("Uso: MOBA.SetTeam [jogador] [time]")))
    {
        return;
    }
    
    int32 PlayerIndex = FCString::Atoi(*Args[0]);
    int32 TeamIndex = FCString::Atoi(*Args[1]);
    
    if (!ValidatePlayerIndex(PlayerIndex) || !ValidateTeamIndex(TeamIndex))
    {
        return;
    }
    
    AssignPlayerToTeam(PlayerIndex, TeamIndex);
    LogSuccess(FString::Printf(TEXT("Jogador %d atribuído ao time %d"), PlayerIndex, TeamIndex));
}

void USigilDebugCommands::StartMOBAMatch(const TArray<FString>& Args)
{
    LogCommand(TEXT("StartMOBAMatch"), Args);
    
    float Duration = DebugSettings->DefaultMatchDuration;
    if (Args.Num() > 0)
    {
        Duration = FCString::Atof(*Args[0]);
    }
    
    if (Duration <= 0.0f)
    {
        LogError(TEXT("Duração da partida deve ser maior que zero"));
        return;
    }
    
    bMOBAMatchActive = true;
    DebugStartTime = FPlatformTime::Seconds();
    
    // Configurar times
    SetupMOBATeams(DebugSettings->DefaultTeamSize);
    
    // Iniciar fase de preparação
    StartMOBAPhase(FGameplayTag::RequestGameplayTag(FName("MOBA.Phase.Preparation")));
    
    LogSuccess(FString::Printf(TEXT("Partida MOBA iniciada - Duração: %.1f segundos"), Duration));
}

void USigilDebugCommands::EndMOBAMatch(const TArray<FString>& Args)
{
    LogCommand(TEXT("EndMOBAMatch"), Args);
    
    if (!bMOBAMatchActive)
    {
        LogWarning(TEXT("Nenhuma partida MOBA ativa"));
        return;
    }
    
    bMOBAMatchActive = false;
    float MatchDuration = FPlatformTime::Seconds() - DebugStartTime;
    
    LogSuccess(FString::Printf(TEXT("Partida MOBA terminada - Duração: %.1f segundos"), MatchDuration));
}

void USigilDebugCommands::SetMOBAPhase(const TArray<FString>& Args)
{
    LogCommand(TEXT("SetMOBAPhase"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: MOBA.SetPhase [fase]")))
    {
        return;
    }
    
    FGameplayTag PhaseTag = ParseGameplayTag(Args[0]);
    if (!PhaseTag.IsValid())
    {
        LogError(FString::Printf(TEXT("Fase MOBA inválida: %s"), *Args[0]));
        return;
    }
    
    StartMOBAPhase(PhaseTag);
    LogSuccess(FString::Printf(TEXT("Fase MOBA definida para: %s"), *PhaseTag.ToString()));
}

void USigilDebugCommands::SpawnTeamSigils(const TArray<FString>& Args)
{
    LogCommand(TEXT("SpawnTeamSigils"), Args);
    
    if (!ValidateArgs(Args, 2, TEXT("Uso: MOBA.SpawnTeamSigils [time] [quantidade]")))
    {
        return;
    }
    
    int32 TeamIndex = FCString::Atoi(*Args[0]);
    int32 Count = FCString::Atoi(*Args[1]);
    
    if (!ValidateTeamIndex(TeamIndex))
    {
        return;
    }
    
    if (Count <= 0 || Count > MAX_DEBUG_SIGILS)
    {
        LogError(FString::Printf(TEXT("Quantidade inválida: %d (1-%d)"), Count, MAX_DEBUG_SIGILS));
        return;
    }
    
    // Spawnar sígilos para o time
    FVector TeamSpawnLocation = FVector(TeamIndex * 1000.0f, 0.0f, 0.0f);
    
    for (int32 i = 0; i < Count; i++)
    {
        FGameplayTag SigilType = DebugSettings->DefaultSigilTypes[i % DebugSettings->DefaultSigilTypes.Num()];
        FVector SpawnLocation = TeamSpawnLocation + FVector(
            FMath::RandRange(-DEFAULT_SPAWN_RADIUS, DEFAULT_SPAWN_RADIUS),
            FMath::RandRange(-DEFAULT_SPAWN_RADIUS, DEFAULT_SPAWN_RADIUS),
            0.0f
        );
        
        SpawnSigilAtLocation(SpawnLocation, SigilType);
    }
    
    SigilsSpawned += Count;
    LogSuccess(FString::Printf(TEXT("%d sígilos spawnados para o time %d"), Count, TeamIndex));
}

void USigilDebugCommands::SimulateTeamFight(const TArray<FString>& Args)
{
    LogCommand(TEXT("SimulateTeamFight"), Args);
    
    if (!bMOBAMatchActive)
    {
        LogWarning(TEXT("Nenhuma partida MOBA ativa"));
        return;
    }
    
    // Simular luta de equipe
    FVector FightLocation = FVector::ZeroVector;
    APawn* PlayerPawn = GetPlayerPawn();
    if (PlayerPawn)
    {
        FightLocation = PlayerPawn->GetActorLocation();
    }
    
    // Reproduzir VFX de team fight
    USigilVFXManager* VFXManager = GetVFXManager(PlayerPawn);
    if (VFXManager)
    {
        VFXManager->PlayTeamFightVFX(PlayerPawn, 1); // Team ID 1
        VFXPlayed++;
    }
    
    LogSuccess(FString::Printf(TEXT("Luta de equipe simulada em %s"), *FightLocation.ToString()));
}

void USigilDebugCommands::TriggerObjectiveEvent(const TArray<FString>& Args)
{
    LogCommand(TEXT("TriggerObjectiveEvent"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: MOBA.TriggerObjective [tipo]")))
    {
        return;
    }
    
    FString ObjectiveType = Args[0];
    FVector EventLocation = FVector::ZeroVector;
    
    APawn* PlayerPawn = GetPlayerPawn();
    if (PlayerPawn)
    {
        EventLocation = PlayerPawn->GetActorLocation();
    }
    
    // Reproduzir VFX de objetivo
    USigilVFXManager* VFXManager = GetVFXManager(PlayerPawn);
    if (VFXManager)
    {
        VFXManager->PlayObjectiveVFX(PlayerPawn, FGameplayTag::RequestGameplayTag(FName("Objective.MOBA")));
        VFXPlayed++;
    }
    
    LogSuccess(FString::Printf(TEXT("Evento de objetivo '%s' disparado em %s"), *ObjectiveType, *EventLocation.ToString()));
}

// Comandos de rede
void USigilDebugCommands::ShowNetworkStats(const TArray<FString>& Args)
{
    LogCommand(TEXT("ShowNetworkStats"), Args);
    
    USigilReplicationManager* ReplicationManager = GetReplicationManager(GetPlayerPawn());
    if (!ReplicationManager)
    {
        LogError(TEXT("Gerenciador de replicação não encontrado"));
        return;
    }
    
    // Usar função de debug do ReplicationManager
    ReplicationManager->DebugPrintReplicationStats();
}

void USigilDebugCommands::SimulateLag(const TArray<FString>& Args)
{
    LogCommand(TEXT("SimulateLag"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: Network.SimulateLag [ms]")))
    {
        return;
    }
    
    float LagMs = FCString::Atof(*Args[0]);
    if (LagMs < 0.0f)
    {
        LogError(TEXT("Lag deve ser maior ou igual a zero"));
        return;
    }
    
    USigilReplicationManager* ReplicationManager = GetReplicationManager(GetPlayerPawn());
    if (ReplicationManager)
    {
        ReplicationManager->DebugSimulateNetworkLag(LagMs / 1000.0f);
        LogSuccess(FString::Printf(TEXT("Lag de rede simulado: %.1f ms"), LagMs));
    }
    else
    {
        LogError(TEXT("Gerenciador de replicação não encontrado"));
    }
}

void USigilDebugCommands::ForceReplication(const TArray<FString>& Args)
{
    LogCommand(TEXT("ForceReplication"), Args);
    
    USigilReplicationManager* ReplicationManager = GetReplicationManager(GetPlayerPawn());
    if (!ReplicationManager)
    {
        LogError(TEXT("Gerenciador de replicação não encontrado"));
        return;
    }
    
    // Forçar replicação de todos os dados
    // Isso seria implementado no ReplicationManager
    NetworkMessages++;
    LogSuccess(TEXT("Replicação forçada para todos os clientes"));
}

void USigilDebugCommands::ToggleNetworkDebug(const TArray<FString>& Args)
{
    LogCommand(TEXT("ToggleNetworkDebug"), Args);
    
    DebugSettings->bShowNetworkDebugInfo = !DebugSettings->bShowNetworkDebugInfo;
    
    LogSuccess(FString::Printf(TEXT("Debug de rede %s"), 
               DebugSettings->bShowNetworkDebugInfo ? TEXT("habilitado") : TEXT("desabilitado")));
}

// Comandos de performance
void USigilDebugCommands::ShowPerformanceStats(const TArray<FString>& Args)
{
    LogCommand(TEXT("ShowPerformanceStats"), Args);
    
    float CurrentTime = FPlatformTime::Seconds();
    float Uptime = bDebugModeActive ? (CurrentTime - DebugStartTime) : 0.0f;
    
    UE_LOG(LogTemp, Warning, TEXT("=== ESTATÍSTICAS DE PERFORMANCE ==="));
    UE_LOG(LogTemp, Warning, TEXT("Tempo ativo: %.1f segundos"), Uptime);
    UE_LOG(LogTemp, Warning, TEXT("Sígilos spawnados: %d"), SigilsSpawned);
    UE_LOG(LogTemp, Warning, TEXT("Fusões completadas: %d"), FusionsCompleted);
    UE_LOG(LogTemp, Warning, TEXT("VFX reproduzidos: %d"), VFXPlayed);
    UE_LOG(LogTemp, Warning, TEXT("Mensagens de rede: %d"), NetworkMessages);
    
    if (Uptime > 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sígilos/segundo: %.2f"), SigilsSpawned / Uptime);
        UE_LOG(LogTemp, Warning, TEXT("VFX/segundo: %.2f"), VFXPlayed / Uptime);
    }
    
    UE_LOG(LogTemp, Warning, TEXT("==================================="));
}

void USigilDebugCommands::ProfileSigilSystem(const TArray<FString>& Args)
{
    LogCommand(TEXT("ProfileSigilSystem"), Args);
    
    // Iniciar profiling do sistema
    LogSuccess(TEXT("Profiling do sistema de sígilos iniciado"));
    
    // Isso seria implementado com ferramentas de profiling do UE
}

void USigilDebugCommands::OptimizeForMOBA(const TArray<FString>& Args)
{
    LogCommand(TEXT("OptimizeForMOBA"), Args);
    
    // Aplicar otimizações específicas para MOBA
    DebugSettings->MaxDebugSigils = 60; // 6 sígilos por jogador
    DebugSettings->VFXDebugDuration = 3.0f; // VFX mais curtos
    DebugSettings->NetworkDebugUpdateRate = 0.5f; // Atualizações mais frequentes
    
    LogSuccess(TEXT("Configurações otimizadas para MOBA 5x5"));
}

void USigilDebugCommands::ResetPerformanceCounters(const TArray<FString>& Args)
{
    LogCommand(TEXT("ResetPerformanceCounters"), Args);
    
    SigilsSpawned = 0;
    FusionsCompleted = 0;
    VFXPlayed = 0;
    NetworkMessages = 0;
    DebugStartTime = FPlatformTime::Seconds();
    
    LogSuccess(TEXT("Contadores de performance resetados"));
}

// Comandos de configuração
void USigilDebugCommands::SetDebugMode(const TArray<FString>& Args)
{
    LogCommand(TEXT("SetDebugMode"), Args);
    
    if (!ValidateArgs(Args, 1, TEXT("Uso: Debug.SetMode [0|1]")))
    {
        return;
    }
    
    bool bNewMode = FCString::Atoi(*Args[0]) != 0;
    bDebugModeActive = bNewMode;
    
    if (bNewMode)
    {
        DebugStartTime = FPlatformTime::Seconds();
        ResetPerformanceCounters(TArray<FString>());
    }
    
    LogSuccess(FString::Printf(TEXT("Modo de debug %s"), 
               bNewMode ? TEXT("habilitado") : TEXT("desabilitado")));
}

void USigilDebugCommands::SaveDebugConfig(const TArray<FString>& Args)
{
    LogCommand(TEXT("SaveDebugConfig"), Args);
    
    if (DebugSettings)
    {
        DebugSettings->SaveConfig();
        LogSuccess(TEXT("Configuração de debug salva"));
    }
    else
    {
        LogError(TEXT("Configurações de debug não encontradas"));
    }
}

void USigilDebugCommands::LoadDebugConfig(const TArray<FString>& Args)
{
    LogCommand(TEXT("LoadDebugConfig"), Args);
    
    if (DebugSettings)
    {
        DebugSettings->ReloadConfig();
        LogSuccess(TEXT("Configuração de debug carregada"));
    }
    else
    {
        LogError(TEXT("Configurações de debug não encontradas"));
    }
}

void USigilDebugCommands::ResetToDefaults(const TArray<FString>& Args)
{
    LogCommand(TEXT("ResetToDefaults"), Args);
    
    if (DebugSettings)
    {
        // Resetar para valores padrão
        DebugSettings->LoadConfig();
        DebugSettings->SaveConfig();
        LogSuccess(TEXT("Configurações resetadas para padrão"));
    }
    else
    {
        LogError(TEXT("Configurações de debug não encontradas"));
    }
}

// Comandos de teste automatizado
void USigilDebugCommands::RunSigilTests(const TArray<FString>& Args)
{
    LogCommand(TEXT("RunSigilTests"), Args);
    
    LogSuccess(TEXT("Executando testes de sígilos..."));
    RunBasicSigilTest();
}

void USigilDebugCommands::RunFusionTests(const TArray<FString>& Args)
{
    LogCommand(TEXT("RunFusionTests"), Args);
    
    LogSuccess(TEXT("Executando testes de fusão..."));
    RunFusionStressTest();
}

void USigilDebugCommands::RunVFXTests(const TArray<FString>& Args)
{
    LogCommand(TEXT("RunVFXTests"), Args);
    
    LogSuccess(TEXT("Executando testes de VFX..."));
    RunVFXPerformanceTest();
}

void USigilDebugCommands::RunNetworkTests(const TArray<FString>& Args)
{
    LogCommand(TEXT("RunNetworkTests"), Args);
    
    LogSuccess(TEXT("Executando testes de rede..."));
    RunNetworkLatencyTest();
}

void USigilDebugCommands::RunMOBAScenario(const TArray<FString>& Args)
{
    LogCommand(TEXT("RunMOBAScenario"), Args);
    
    LogSuccess(TEXT("Executando cenário MOBA completo..."));
    RunMOBA5v5Simulation();
}

// Funções auxiliares
APlayerController* USigilDebugCommands::GetPlayerController(int32 PlayerIndex)
{
    UWorld* World = nullptr;
    if (GEngine)
    {
        World = GEngine->GetWorldFromContextObject(GEngine->GameViewport, EGetWorldErrorMode::LogAndReturnNull);
    }
    
    if (!World)
    {
        return nullptr;
    }
    
    return World->GetFirstPlayerController();
}

APawn* USigilDebugCommands::GetPlayerPawn(int32 PlayerIndex)
{
    APlayerController* PC = GetPlayerController(PlayerIndex);
    return PC ? PC->GetPawn() : nullptr;
}

USigilManagerComponent* USigilDebugCommands::GetSigilManager(APawn* Pawn)
{
    if (!Pawn)
    {
        Pawn = GetPlayerPawn();
    }
    
    return Pawn ? Pawn->FindComponentByClass<USigilManagerComponent>() : nullptr;
}

USigilFusionSystem* USigilDebugCommands::GetFusionSystem(APawn* Pawn)
{
    if (!Pawn)
    {
        Pawn = GetPlayerPawn();
    }
    
    return Pawn ? Pawn->FindComponentByClass<USigilFusionSystem>() : nullptr;
}

USigilVFXManager* USigilDebugCommands::GetVFXManager(APawn* Pawn)
{
    if (!Pawn)
    {
        Pawn = GetPlayerPawn();
    }
    
    return Pawn ? Pawn->FindComponentByClass<USigilVFXManager>() : nullptr;
}

USigilReplicationManager* USigilDebugCommands::GetReplicationManager(APawn* Pawn)
{
    if (!Pawn)
    {
        Pawn = GetPlayerPawn();
    }
    
    return Pawn ? Pawn->FindComponentByClass<USigilReplicationManager>() : nullptr;
}

// Funções de validação
bool USigilDebugCommands::ValidateArgs(const TArray<FString>& Args, int32 MinArgs, const FString& Usage)
{
    if (Args.Num() < MinArgs)
    {
        LogError(Usage);
        return false;
    }
    return true;
}

bool USigilDebugCommands::ValidatePlayerIndex(int32 PlayerIndex)
{
    if (PlayerIndex < 0 || PlayerIndex >= MAX_MOBA_PLAYERS)
    {
        LogError(FString::Printf(TEXT("Índice de jogador inválido: %d (0-%d)"), PlayerIndex, MAX_MOBA_PLAYERS - 1));
        return false;
    }
    return true;
}

bool USigilDebugCommands::ValidateTeamIndex(int32 TeamIndex)
{
    if (TeamIndex < 0 || TeamIndex >= 2)
    {
        LogError(FString::Printf(TEXT("Índice de time inválido: %d (0-1)"), TeamIndex));
        return false;
    }
    return true;
}

FGameplayTag USigilDebugCommands::ParseGameplayTag(const FString& TagString)
{
    return FGameplayTag::RequestGameplayTag(FName(*TagString));
}

// Funções de logging
void USigilDebugCommands::LogCommand(const FString& Command, const TArray<FString>& Args)
{
    if (DebugSettings && DebugSettings->bLogDebugEvents)
    {
        FString ArgsString = FString::Join(Args, TEXT(" "));
        UE_LOG(LogTemp, Log, TEXT("SigilDebugCommand: %s %s"), *Command, *ArgsString);
    }
}

void USigilDebugCommands::LogSuccess(const FString& Message)
{
    UE_LOG(LogTemp, Warning, TEXT("[SUCCESS] %s"), *Message);
}

void USigilDebugCommands::LogWarning(const FString& Message)
{
    UE_LOG(LogTemp, Warning, TEXT("[WARNING] %s"), *Message);
}

void USigilDebugCommands::LogError(const FString& Message)
{
    UE_LOG(LogTemp, Error, TEXT("[ERROR] %s"), *Message);
}

// Funções de spawn
ASigilItem* USigilDebugCommands::SpawnSigilAtLocation(const FVector& Location, const FGameplayTag& SigilType, APawn* Owner)
{
    UWorld* World = GEngine->GetWorldFromContextObject(GetPlayerController(), EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return nullptr;
    }
    
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = Owner;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    ASigilItem* NewSigil = World->SpawnActor<ASigilItem>(ASigilItem::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    
    if (NewSigil)
    {
        // SigilType configurado via Blueprint ou configuração padrão
        // NewSigil->SigilData.SigilType = ESigilType::None; // Configuração padrão
    }
    
    return NewSigil;
}

void USigilDebugCommands::SpawnSigilsInRadius(const FVector& Center, float Radius, int32 Count, const FGameplayTag& SigilType, APawn* Owner)
{
    for (int32 i = 0; i < Count; i++)
    {
        FVector SpawnLocation = Center + FVector(
            FMath::RandRange(-Radius, Radius),
            FMath::RandRange(-Radius, Radius),
            0.0f
        );
        
        SpawnSigilAtLocation(SpawnLocation, SigilType, Owner);
    }
}

// Funções de MOBA
void USigilDebugCommands::SetupMOBATeams(int32 TeamSize)
{
    // Configurar times para MOBA
    for (int32 i = 0; i < TeamSize * 2; i++)
    {
        int32 TeamIndex = i < TeamSize ? 0 : 1;
        AssignPlayerToTeam(i, TeamIndex);
    }
}

void USigilDebugCommands::AssignPlayerToTeam(int32 PlayerIndex, int32 TeamIndex)
{
    // Atribuir jogador ao time
    // Isso seria implementado dependendo do sistema de times
}

void USigilDebugCommands::StartMOBAPhase(const FGameplayTag& PhaseTag)
{
    // Iniciar fase MOBA
    // Isso seria implementado dependendo do sistema de fases
}

// Funções de teste
void USigilDebugCommands::RunBasicSigilTest()
{
    FSigilTestResult Result;
    Result.TestName = TEXT("Teste Básico de Sígilos");
    Result.bPassed = true;
    Result.ErrorMessage = TEXT("Teste de spawn e remoção de sígilos");
    
    // Spawnar alguns sígilos de teste
    FVector TestLocation = FVector::ZeroVector;
    APawn* PlayerPawn = GetPlayerPawn();
    if (PlayerPawn)
    {
        TestLocation = PlayerPawn->GetActorLocation();
    }
    
    for (int32 i = 0; i < 5; i++)
    {
        FGameplayTag TestType = DebugSettings->DefaultSigilTypes[i % DebugSettings->DefaultSigilTypes.Num()];
        ASigilItem* TestSigil = SpawnSigilAtLocation(TestLocation + FVector(i * 100.0f, 0.0f, 0.0f), TestType, PlayerPawn);
        
        if (!TestSigil)
        {
            Result.bPassed = false;
            Result.ErrorMessage += FString::Printf(TEXT(" | Falha no spawn %d"), i);
        }
    }
    
    LogTestResult(Result);
}

void USigilDebugCommands::RunFusionStressTest()
{
    FSigilTestResult Result;
    Result.TestName = TEXT("Teste de Stress de Fusão");
    Result.bPassed = true;
    Result.ErrorMessage = TEXT("Teste de múltiplas fusões simultâneas");
    
    // Spawnar sígilos para fusão
    FVector TestLocation = FVector::ZeroVector;
    APawn* PlayerPawn = GetPlayerPawn();
    if (PlayerPawn)
    {
        TestLocation = PlayerPawn->GetActorLocation();
    }
    
    TArray<ASigilItem*> TestSigils;
    for (int32 i = 0; i < 10; i++)
    {
        FGameplayTag TestType = DebugSettings->DefaultSigilTypes[i % DebugSettings->DefaultSigilTypes.Num()];
        ASigilItem* TestSigil = SpawnSigilAtLocation(TestLocation + FVector(i * 50.0f, 0.0f, 0.0f), TestType, PlayerPawn);
        if (TestSigil)
        {
            TestSigils.Add(TestSigil);
        }
    }
    
    // Tentar fusões
    USigilFusionSystem* FusionSystem = GetFusionSystem(PlayerPawn);
    if (FusionSystem && TestSigils.Num() >= 4)
    {
        for (int32 i = 0; i < TestSigils.Num() - 1; i += 2)
        {
            TArray<ASigilItem*> FusionPair = {TestSigils[i], TestSigils[i + 1]};
            bool bFusionStarted = FusionSystem->StartAutomaticFusion(FusionPair, PlayerPawn);
            
            if (!bFusionStarted)
            {
                Result.bPassed = false;
                Result.ErrorMessage += FString::Printf(TEXT(" | Falha na fusão %d"), i / 2);
            }
        }
    }
    else
    {
        Result.bPassed = false;
        Result.ErrorMessage += TEXT(" | Sistema de fusão não encontrado ou sígilos insuficientes");
    }
    
    LogTestResult(Result);
}

void USigilDebugCommands::RunVFXPerformanceTest()
{
    FSigilTestResult Result;
    Result.TestName = TEXT("Teste de Performance de VFX");
    Result.bPassed = true;
    Result.ErrorMessage = TEXT("Teste de reprodução simultânea de VFX");
    
    FVector TestLocation = FVector::ZeroVector;
    APawn* PlayerPawn = GetPlayerPawn();
    if (PlayerPawn)
    {
        TestLocation = PlayerPawn->GetActorLocation();
    }
    
    USigilVFXManager* VFXManager = GetVFXManager(PlayerPawn);
    if (VFXManager)
    {
        // Reproduzir múltiplos VFX
        for (int32 i = 0; i < 20; i++)
        {
            FVector VFXLocation = TestLocation + FVector(
                FMath::RandRange(-200.0f, 200.0f),
                FMath::RandRange(-200.0f, 200.0f),
                0.0f
            );
            
            // VFXManager->PlaySigilEquipVFX(nullptr, PlayerPawn); // Requer ASigilItem válido
            VFXPlayed++;
        }
        
        Result.ErrorMessage += FString::Printf(TEXT(" | %d VFX reproduzidos"), 20);
    }
    else
    {
        Result.bPassed = false;
        Result.ErrorMessage += TEXT(" | Gerenciador de VFX não encontrado");
    }
    
    LogTestResult(Result);
}

void USigilDebugCommands::RunNetworkLatencyTest()
{
    FSigilTestResult Result;
    Result.TestName = TEXT("Teste de Latência de Rede");
    Result.bPassed = true;
    Result.ErrorMessage = TEXT("Teste de replicação com latência simulada");
    
    USigilReplicationManager* ReplicationManager = GetReplicationManager(GetPlayerPawn());
    if (ReplicationManager)
    {
        // Simular diferentes latências
        TArray<float> TestLatencies = {0.0f, 50.0f, 100.0f, 200.0f};
        
        for (float Latency : TestLatencies)
        {
            ReplicationManager->DebugSimulateNetworkLag(Latency / 1000.0f);
            
            // Simular algumas operações de rede
            for (int32 i = 0; i < 5; i++)
            {
                NetworkMessages++;
            }
            
            Result.ErrorMessage += FString::Printf(TEXT(" | Latência %.0fms testada"), Latency);
        }
        
        // Resetar latência
        ReplicationManager->DebugSimulateNetworkLag(0.0f);
    }
    else
    {
        Result.bPassed = false;
        Result.ErrorMessage += TEXT(" | Gerenciador de replicação não encontrado");
    }
    
    LogTestResult(Result);
}

void USigilDebugCommands::RunMOBA5v5Simulation()
{
    FSigilTestResult Result;
    Result.TestName = TEXT("Simulação MOBA 5v5");
    Result.bPassed = true;
    Result.ErrorMessage = TEXT("Simulação completa de partida MOBA");
    
    // Iniciar partida MOBA
    TArray<FString> StartArgs = {TEXT("300")}; // 5 minutos
    StartMOBAMatch(StartArgs);
    
    // Spawnar sígilos para ambos os times
    for (int32 Team = 0; Team < 2; Team++)
    {
        TArray<FString> SpawnArgs = {FString::FromInt(Team), TEXT("15")};
        SpawnTeamSigils(SpawnArgs);
    }
    
    // Simular algumas lutas de equipe
    for (int32 i = 0; i < 3; i++)
    {
        SimulateTeamFight(TArray<FString>());
        
        // Aguardar um pouco entre lutas
        FPlatformProcess::Sleep(1.0f);
    }
    
    // Simular eventos de objetivo
    TArray<FString> ObjectiveArgs = {TEXT("Dragon")};
    TriggerObjectiveEvent(ObjectiveArgs);
    
    ObjectiveArgs[0] = TEXT("Baron");
    TriggerObjectiveEvent(ObjectiveArgs);
    
    Result.ErrorMessage += FString::Printf(TEXT(" | Partida simulada com %d sígilos, %d VFX"), SigilsSpawned, VFXPlayed);
    
    LogTestResult(Result);
}

void USigilDebugCommands::LogTestResult(const FSigilTestResult& Result)
{
    if (Result.bPassed)
    {
        UE_LOG(LogTemp, Warning, TEXT("[TESTE PASSOU] %s - Tempo: %.3fs"), *Result.TestName, Result.ExecutionTime);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("[TESTE FALHOU] %s: %s - Tempo: %.3fs"), *Result.TestName, *Result.ErrorMessage, Result.ExecutionTime);
    }
}

// Configuração de cenários MOBA
void USigilDebugCommands::SetupMOBAScenario(const FMOBAScenarioConfig& Config)
{
    // Inicializar DebugSettings se necessário
    if (!DebugSettings)
    {
        DebugSettings = GetMutableDefault<USigilDebugSettings>();
    }
    
    // Configurar cenário MOBA específico
    DebugSettings->DefaultTeamSize = Config.TeamSize;
    DebugSettings->DefaultMatchDuration = Config.MatchDuration;
    DebugSettings->MaxDebugSigils = Config.SigilsPerPlayer * Config.TeamSize * 2; // Total para ambos os times
    
    // Aplicar configurações de VFX
    DebugSettings->bShowVFXDebugInfo = Config.bEnableVFX;
    DebugSettings->VFXDebugDuration = 5.0f;
    
    // Aplicar configurações de rede
    DebugSettings->bShowNetworkDebugInfo = Config.bEnableReplication;
    DebugSettings->NetworkDebugUpdateRate = 1.0f;
    
    // Log de configuração aplicada
     UE_LOG(LogTemp, Log, TEXT("Cenário MOBA configurado: TeamSize=%d, MatchDuration=%.1f, SigilsPerPlayer=%d, VFX=%s, Network=%s"), 
            Config.TeamSize, Config.MatchDuration, Config.SigilsPerPlayer,
            Config.bEnableVFX ? TEXT("Enabled") : TEXT("Disabled"),
            Config.bEnableReplication ? TEXT("Enabled") : TEXT("Disabled"));
}

FMOBAScenarioConfig USigilDebugCommands::GetDefaultMOBAScenario()
{
    FMOBAScenarioConfig Config;
    Config.TeamSize = 5;
    Config.MatchDuration = 1800.0f; // 30 minutos
    Config.SigilsPerPlayer = 6;
    Config.bEnableVFX = true;
    Config.bEnableReplication = true;
    
    return Config;
}