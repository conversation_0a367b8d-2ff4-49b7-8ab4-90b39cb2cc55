// SigilFusionSystem.cpp
// Sistema de Fusão de Sígilos AURACRON - UE 5.6 APIs Modernas
// Implementação robusta e completa do sistema de fusão conforme AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md

#include "Fusion/SigilFusionSystem.h"
#include "Sigils/SigilItem.h"
#include "VFX/SigilVFXManager.h"
#include "Engine/World.h"
#include "Engine/TimerHandle.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Blueprint/UserWidget.h"
#include "Engine/Engine.h"
#include "GameplayTagsManager.h"
#include "Kismet/GameplayStatics.h"
#include "Math/UnrealMathUtility.h"
#include "Engine/GameInstance.h"
#include "GameFramework/PlayerController.h"
#include "Components/WidgetComponent.h"
#include "UObject/ConstructorHelpers.h"
#include "Net/UnrealNetwork.h"
#include "Engine/NetDriver.h"

// Constantes do sistema
const float USigilFusionSystem::DEFAULT_FUSION_TIME = 360.0f; // 6 minutos
const int32 USigilFusionSystem::MAX_FUSION_INSTANCES = 5;
const float USigilFusionSystem::FUSION_PROGRESS_UPDATE_RATE = 0.1f; // 10 vezes por segundo
const float USigilFusionSystem::NOTIFICATION_DISPLAY_TIME = 5.0f;

USigilFusionSystem::USigilFusionSystem()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = FUSION_PROGRESS_UPDATE_RATE;
    
    // Configurações padrão
    bEnableAutomaticFusion = true;
    DefaultFusionTime = DEFAULT_FUSION_TIME;
    MaxSimultaneousFusions = MAX_FUSION_INSTANCES;
    bShowNotifications = true;
    bPlaySounds = true;
    bShowVFX = true;
    
    // Configurações de depuração
    bDebugMode = false;
    bLogFusionEvents = false;
    
    // Inicializar arrays
    ActiveFusions.Empty();
    FusionRecipes.Empty();
    ActiveNotificationWidgets.Empty();
}

void USigilFusionSystem::BeginPlay()
{
    Super::BeginPlay();
    
    // Carregar receitas padrão
    LoadDefaultRecipes();
    
    // Encontrar VFXManager se não foi definido
    if (!VFXManager)
    {
        VFXManager = GetOwner()->FindComponentByClass<USigilVFXManager>();
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Sistema iniciado para %s"), *GetOwner()->GetName());
    }
}

void USigilFusionSystem::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (bEnableAutomaticFusion)
    {
        UpdateFusionProgress(DeltaTime);
    }
}

// Funções principais de fusão
bool USigilFusionSystem::StartAutomaticFusion(const TArray<ASigilItem*>& InputSigils, AActor* Owner)
{
    if (!bEnableAutomaticFusion)
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Fusão automática está desabilitada"));
        }
        return false;
    }
    
    if (!CanStartFusion(InputSigils))
    {
        return false;
    }
    
    // Encontrar a melhor receita
    FSigilFusionRecipe BestRecipe = FindBestRecipe(InputSigils);
    if (BestRecipe.RecipeID.IsValid())
    {
        return StartManualFusion(BestRecipe, InputSigils, Owner);
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Nenhuma receita encontrada para os sígilos fornecidos"));
    }
    
    return false;
}

bool USigilFusionSystem::StartManualFusion(const FSigilFusionRecipe& Recipe, const TArray<ASigilItem*>& InputSigils, AActor* Owner)
{
    if (!CanStartFusion(InputSigils))
    {
        return false;
    }
    
    if (!ValidateFusionInputs(InputSigils, Recipe))
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Entradas de fusão inválidas"));
        }
        return false;
    }
    
    // Verificar limite de fusões simultâneas
    if (GetActiveFusionCount(Owner) >= MaxSimultaneousFusions)
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Limite de fusões simultâneas atingido"));
        }
        return false;
    }
    
    // Criar nova instância de fusão
    FSigilFusionInstance NewFusion;
    NewFusion.FusionID = FGuid::NewGuid();
    NewFusion.Recipe = Recipe;
    NewFusion.InputSigils = InputSigils;
    NewFusion.CurrentState = ESigilFusionState::Preparing;
    NewFusion.StartTime = GetWorld()->GetTimeSeconds();
    NewFusion.RemainingTime = Recipe.FusionTime;
    NewFusion.OwnerActor = Owner;
    NewFusion.bIsAutomatic = true;
    
    // Adicionar à lista de fusões ativas
    ActiveFusions.Add(NewFusion);
    
    // Configurar timer para conclusão usando API moderna UE 5.6
    FTimerHandle TimerHandle;
    FTimerDelegate TimerDelegate;
    TimerDelegate.BindUObject(this, &USigilFusionSystem::OnFusionTimerComplete, NewFusion.FusionID);

    GetWorld()->GetTimerManager().SetTimer(
        TimerHandle,
        TimerDelegate,
        Recipe.FusionTime,
        false
    );
    
    FusionTimers.Add(NewFusion.FusionID, TimerHandle);
    
    // Consumir sígilos de entrada
    ConsumeFusionInputs(InputSigils);
    
    // Atualizar estado para em progresso
    for (FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.FusionID == NewFusion.FusionID)
        {
            Fusion.CurrentState = ESigilFusionState::InProgress;
            break;
        }
    }
    
    // Reproduzir efeitos
    PlayFusionVFX(NewFusion, ESigilFusionState::InProgress);
    PlayFusionSound(ESigilFusionState::InProgress);
    
    // Mostrar notificação
    if (bShowNotifications)
    {
        FSigilFusionNotification Notification = CreateNotificationForState(ESigilFusionState::InProgress, NewFusion);
        ShowFusionNotification(Notification, Owner);
    }
    
    // Disparar evento
    OnFusionStarted.Broadcast(NewFusion, Owner);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão iniciada - ID: %s, Tempo: %.1f segundos"), 
               *NewFusion.FusionID.ToString(), Recipe.FusionTime);
    }
    
    return true;
}

bool USigilFusionSystem::CancelFusion(const FGuid& FusionID)
{
    for (int32 i = ActiveFusions.Num() - 1; i >= 0; i--)
    {
        if (ActiveFusions[i].FusionID == FusionID)
        {
            FSigilFusionInstance& Fusion = ActiveFusions[i];
            
            // Limpar timer
            ClearFusionTimer(FusionID);
            
            // Atualizar estado
            Fusion.CurrentState = ESigilFusionState::Cancelled;
            
            // Reproduzir efeitos de cancelamento
            PlayFusionVFX(Fusion, ESigilFusionState::Cancelled);
            
            // Mostrar notificação
            if (bShowNotifications)
            {
                FSigilFusionNotification Notification = CreateNotificationForState(ESigilFusionState::Cancelled, Fusion);
                ShowFusionNotification(Notification, Fusion.OwnerActor);
            }
            
            // Disparar evento
            OnFusionCancelled.Broadcast(Fusion, Fusion.OwnerActor);
            
            // Remover da lista
            ActiveFusions.RemoveAt(i);
            
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão cancelada - ID: %s"), *FusionID.ToString());
            }
            
            return true;
        }
    }
    
    return false;
}

void USigilFusionSystem::CancelAllFusions(AActor* Owner)
{
    TArray<FGuid> FusionsToCancel;
    
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (!Owner || Fusion.OwnerActor == Owner)
        {
            FusionsToCancel.Add(Fusion.FusionID);
        }
    }
    
    for (const FGuid& FusionID : FusionsToCancel)
    {
        CancelFusion(FusionID);
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: %d fusões canceladas"), FusionsToCancel.Num());
    }
}

// Funções de consulta
bool USigilFusionSystem::CanStartFusion(const TArray<ASigilItem*>& InputSigils) const
{
    if (InputSigils.Num() < 2)
    {
        return false;
    }
    
    // Verificar se todos os sígilos são válidos
    for (ASigilItem* Sigil : InputSigils)
    {
        if (!IsValid(Sigil))
        {
            return false;
        }
    }
    
    // Verificar se existe uma receita válida
    FSigilFusionRecipe Recipe = FindBestRecipe(InputSigils);
    return Recipe.RecipeID.IsValid();
}

FSigilFusionRecipe USigilFusionSystem::FindBestRecipe(const TArray<ASigilItem*>& InputSigils) const
{
    FSigilFusionRecipe BestRecipe;
    float BestScore = 0.0f;
    
    for (const FSigilFusionRecipe& Recipe : FusionRecipes)
    {
        if (Recipe.RequiredSigilTypes.Num() != InputSigils.Num())
        {
            continue;
        }
        
        // Verificar se os tipos de sígilos correspondem
        TArray<FGameplayTag> InputTypes;
        for (ASigilItem* Sigil : InputSigils)
        {
            if (IsValid(Sigil))
            {
                // Convert ESigilType to FGameplayTag
                ESigilType SigilType = Sigil->GetSigilType();
                FGameplayTag TypeTag;
                
                switch (SigilType)
                {
                    case ESigilType::Tank:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank"));
                        break;
                    case ESigilType::Damage:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage"));
                        break;
                    case ESigilType::Utility:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility"));
                        break;
                    default:
                        TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.None"));
                        break;
                }
                
                InputTypes.Add(TypeTag);
            }
        }
        
        bool bMatches = true;
        for (const FGameplayTag& RequiredType : Recipe.RequiredSigilTypes)
        {
            if (!InputTypes.Contains(RequiredType))
            {
                bMatches = false;
                break;
            }
        }
        
        if (bMatches)
        {
            float Score = Recipe.SuccessRate;
            if (Score > BestScore)
            {
                BestScore = Score;
                BestRecipe = Recipe;
            }
        }
    }
    
    return BestRecipe;
}

TArray<FSigilFusionInstance> USigilFusionSystem::GetActiveFusions(AActor* Owner) const
{
    if (!Owner)
    {
        return ActiveFusions;
    }
    
    TArray<FSigilFusionInstance> OwnerFusions;
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.OwnerActor == Owner)
        {
            OwnerFusions.Add(Fusion);
        }
    }
    
    return OwnerFusions;
}

FSigilFusionInstance USigilFusionSystem::GetFusionByID(const FGuid& FusionID) const
{
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.FusionID == FusionID)
        {
            return Fusion;
        }
    }
    
    return FSigilFusionInstance();
}

float USigilFusionSystem::GetFusionProgress(const FGuid& FusionID) const
{
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.FusionID == FusionID)
        {
            float ElapsedTime = GetWorld()->GetTimeSeconds() - Fusion.StartTime;
            float TotalTime = Fusion.Recipe.FusionTime;
            return FMath::Clamp(ElapsedTime / TotalTime, 0.0f, 1.0f);
        }
    }
    
    return 0.0f;
}

int32 USigilFusionSystem::GetActiveFusionCount(AActor* Owner) const
{
    if (!Owner)
    {
        return ActiveFusions.Num();
    }
    
    int32 Count = 0;
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.OwnerActor == Owner)
        {
            Count++;
        }
    }
    
    return Count;
}

// Funções de receitas
void USigilFusionSystem::AddFusionRecipe(const FSigilFusionRecipe& Recipe)
{
    // Verificar se a receita já existe
    for (FSigilFusionRecipe& ExistingRecipe : FusionRecipes)
    {
        if (ExistingRecipe.RecipeID == Recipe.RecipeID)
        {
            ExistingRecipe = Recipe; // Atualizar receita existente
            return;
        }
    }
    
    FusionRecipes.Add(Recipe);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Receita adicionada - %s"), *Recipe.RecipeID.ToString());
    }
}

bool USigilFusionSystem::RemoveFusionRecipe(const FGameplayTag& RecipeID)
{
    for (int32 i = FusionRecipes.Num() - 1; i >= 0; i--)
    {
        if (FusionRecipes[i].RecipeID == RecipeID)
        {
            FusionRecipes.RemoveAt(i);
            
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Receita removida - %s"), *RecipeID.ToString());
            }
            
            return true;
        }
    }
    
    return false;
}

void USigilFusionSystem::LoadDefaultRecipes()
{
    FusionRecipes.Empty();

    // Receitas baseadas no Sistema de Sígilos Auracron conforme documentação
    // Aegis (Tanque) + Ruin (Dano) -> Vesper (Utilidade) - Receita básica
    FSigilFusionRecipe AegisRuinRecipe;
    AegisRuinRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.AegisRuin"));
    AegisRuinRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank")));
    AegisRuinRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage")));
    AegisRuinRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility"));
    AegisRuinRecipe.FusionType = ESigilFusionType::Basic;
    AegisRuinRecipe.SuccessRate = 0.8f;
    AegisRuinRecipe.FusionTime = DEFAULT_FUSION_TIME; // 6 minutos conforme documentação
    AegisRuinRecipe.RequiredLevel = 25; // Level 25: Desbloqueio dos Sígilos Auracron
    AddFusionRecipe(AegisRuinRecipe);

    // Aegis (Tanque) + Vesper (Utilidade) -> Ruin (Dano) - Receita básica
    FSigilFusionRecipe AegisVesperRecipe;
    AegisVesperRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.AegisVesper"));
    AegisVesperRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank")));
    AegisVesperRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility")));
    AegisVesperRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage"));
    AegisVesperRecipe.FusionType = ESigilFusionType::Basic;
    AegisVesperRecipe.SuccessRate = 0.8f;
    AegisVesperRecipe.FusionTime = DEFAULT_FUSION_TIME;
    AegisVesperRecipe.RequiredLevel = 25;
    AddFusionRecipe(AegisVesperRecipe);

    // Ruin (Dano) + Vesper (Utilidade) -> Aegis (Tanque) - Receita básica
    FSigilFusionRecipe RuinVesperRecipe;
    RuinVesperRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.RuinVesper"));
    RuinVesperRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage")));
    RuinVesperRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility")));
    RuinVesperRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank"));
    RuinVesperRecipe.FusionType = ESigilFusionType::Basic;
    RuinVesperRecipe.SuccessRate = 0.8f;
    RuinVesperRecipe.FusionTime = DEFAULT_FUSION_TIME;
    RuinVesperRecipe.RequiredLevel = 25;
    AddFusionRecipe(RuinVesperRecipe);
    
    // Receitas avançadas: Sígilos duplos -> Versões aprimoradas
    // Duplo Aegis -> Aegis Aprimorado (Murallion - barreira circular 3s)
    FSigilFusionRecipe DoubleAegisRecipe;
    DoubleAegisRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.DoubleAegis"));
    DoubleAegisRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank")));
    DoubleAegisRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank")));
    DoubleAegisRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank.Enhanced"));
    DoubleAegisRecipe.FusionType = ESigilFusionType::Advanced;
    DoubleAegisRecipe.SuccessRate = 0.6f;
    DoubleAegisRecipe.FusionTime = DEFAULT_FUSION_TIME * 1.5f; // 9 minutos
    DoubleAegisRecipe.RequiredLevel = 50; // Level 50: Rastreamento de maestria de realm
    AddFusionRecipe(DoubleAegisRecipe);

    // Duplo Ruin -> Ruin Aprimorado (Fracasso Prismal - reset parcial de recarga)
    FSigilFusionRecipe DoubleRuinRecipe;
    DoubleRuinRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.DoubleRuin"));
    DoubleRuinRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage")));
    DoubleRuinRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage")));
    DoubleRuinRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage.Enhanced"));
    DoubleRuinRecipe.FusionType = ESigilFusionType::Advanced;
    DoubleRuinRecipe.SuccessRate = 0.6f;
    DoubleRuinRecipe.FusionTime = DEFAULT_FUSION_TIME * 1.5f;
    DoubleRuinRecipe.RequiredLevel = 50;
    AddFusionRecipe(DoubleRuinRecipe);

    // Duplo Vesper -> Vesper Aprimorado (Sopro de Fluxo - dash aliado + escudo)
    FSigilFusionRecipe DoubleVesperRecipe;
    DoubleVesperRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.DoubleVesper"));
    DoubleVesperRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility")));
    DoubleVesperRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility")));
    DoubleVesperRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility.Enhanced"));
    DoubleVesperRecipe.FusionType = ESigilFusionType::Advanced;
    DoubleVesperRecipe.SuccessRate = 0.6f;
    DoubleVesperRecipe.FusionTime = DEFAULT_FUSION_TIME * 1.5f;
    DoubleVesperRecipe.RequiredLevel = 50;
    AddFusionRecipe(DoubleVesperRecipe);
    
    // Receitas lendárias: Sígilos aprimorados -> Sígilos lendários
    // Aegis Aprimorado + Ruin Aprimorado -> Sígilo Lendário Híbrido
    FSigilFusionRecipe LegendaryHybridRecipe;
    LegendaryHybridRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.LegendaryHybrid"));
    LegendaryHybridRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank.Enhanced")));
    LegendaryHybridRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage.Enhanced")));
    LegendaryHybridRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Hybrid"));
    LegendaryHybridRecipe.FusionType = ESigilFusionType::Legendary;
    LegendaryHybridRecipe.SuccessRate = 0.4f;
    LegendaryHybridRecipe.FusionTime = DEFAULT_FUSION_TIME * 2.0f; // 12 minutos
    LegendaryHybridRecipe.RequiredLevel = 100; // Level 100: Criação de lobbies customizados
    AddFusionRecipe(LegendaryHybridRecipe);

    // Vesper Aprimorado + Qualquer Aprimorado -> Sígilo Lendário de Suporte
    FSigilFusionRecipe LegendarySupportRecipe;
    LegendarySupportRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.LegendarySupport"));
    LegendarySupportRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility.Enhanced")));
    LegendarySupportRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Enhanced"))); // Qualquer aprimorado
    LegendarySupportRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Support"));
    LegendarySupportRecipe.FusionType = ESigilFusionType::Legendary;
    LegendarySupportRecipe.SuccessRate = 0.4f;
    LegendarySupportRecipe.FusionTime = DEFAULT_FUSION_TIME * 2.0f;
    LegendarySupportRecipe.RequiredLevel = 100;
    AddFusionRecipe(LegendarySupportRecipe);

    // Receitas espectrais: Sígilos lendários -> Sígilos espectrais (máximo poder)
    // Dois Lendários Híbridos -> Sígilo Espectral Supremo
    FSigilFusionRecipe SpectralSupremeRecipe;
    SpectralSupremeRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.SpectralSupreme"));
    SpectralSupremeRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Hybrid")));
    SpectralSupremeRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Hybrid")));
    SpectralSupremeRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Spectral.Supreme"));
    SpectralSupremeRecipe.FusionType = ESigilFusionType::Spectral;
    SpectralSupremeRecipe.SuccessRate = 0.2f;
    SpectralSupremeRecipe.FusionTime = DEFAULT_FUSION_TIME * 3.0f; // 18 minutos
    SpectralSupremeRecipe.RequiredLevel = 200; // Level 200: Privilégios de beta tester
    AddFusionRecipe(SpectralSupremeRecipe);

    // Lendário Híbrido + Lendário de Suporte -> Sígilo Espectral Equilibrado
    FSigilFusionRecipe SpectralBalancedRecipe;
    SpectralBalancedRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.SpectralBalanced"));
    SpectralBalancedRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Hybrid")));
    SpectralBalancedRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Support")));
    SpectralBalancedRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Spectral.Balanced"));
    SpectralBalancedRecipe.FusionType = ESigilFusionType::Spectral;
    SpectralBalancedRecipe.SuccessRate = 0.25f; // Ligeiramente maior chance por ser mais equilibrado
    SpectralBalancedRecipe.FusionTime = DEFAULT_FUSION_TIME * 2.5f; // 15 minutos
    SpectralBalancedRecipe.RequiredLevel = 200;
    AddFusionRecipe(SpectralBalancedRecipe);

    // Receita especial para Level 500: Status lendário
    FSigilFusionRecipe MythicalRecipe;
    MythicalRecipe.RecipeID = FGameplayTag::RequestGameplayTag(FName("Sigil.Recipe.Mythical"));
    MythicalRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Spectral.Supreme")));
    MythicalRecipe.RequiredSigilTypes.Add(FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Spectral.Balanced")));
    MythicalRecipe.ResultSigilType = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Mythical.Auracron"));
    MythicalRecipe.FusionType = ESigilFusionType::Spectral;
    MythicalRecipe.SuccessRate = 0.1f; // Extremamente raro
    MythicalRecipe.FusionTime = DEFAULT_FUSION_TIME * 4.0f; // 24 minutos
    MythicalRecipe.RequiredLevel = 500; // Level 500: Status lendário + recompensas únicas
    AddFusionRecipe(MythicalRecipe);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: %d receitas padrão carregadas"), FusionRecipes.Num());
    }
}

TArray<FSigilFusionRecipe> USigilFusionSystem::GetAvailableRecipes(const TArray<ASigilItem*>& InputSigils) const
{
    TArray<FSigilFusionRecipe> AvailableRecipes;
    
    for (const FSigilFusionRecipe& Recipe : FusionRecipes)
    {
        if (ValidateFusionInputs(InputSigils, Recipe))
        {
            AvailableRecipes.Add(Recipe);
        }
    }
    
    return AvailableRecipes;
}

// Funções de notificação
void USigilFusionSystem::ShowFusionNotification(const FSigilFusionNotification& Notification, AActor* Owner)
{
    if (!bShowNotifications || !NotificationWidgetClass)
    {
        return;
    }
    
    // Criar widget de notificação
    CreateNotificationWidget(Notification);
    
    // Disparar evento de notificação
    OnFusionNotification.Broadcast(Notification, Owner);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Notificação mostrada - %s"), *Notification.Title.ToString());
    }
}

void USigilFusionSystem::HideAllNotifications()
{
    // Limpar todos os timers de notificação primeiro
    for (auto& TimerPair : NotificationTimers)
    {
        if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(TimerPair.Value))
        {
            GetWorld()->GetTimerManager().ClearTimer(TimerPair.Value);
        }
    }
    NotificationTimers.Empty();

    // Remover todos os widgets
    for (UUserWidget* Widget : ActiveNotificationWidgets)
    {
        if (IsValid(Widget))
        {
            Widget->RemoveFromParent();
        }
    }

    ActiveNotificationWidgets.Empty();

    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Todas as notificações foram ocultadas"));
    }
}

// Funções de configuração
void USigilFusionSystem::SetAutomaticFusionEnabled(bool bEnabled)
{
    bEnableAutomaticFusion = bEnabled;
    
    if (!bEnabled)
    {
        // Cancelar todas as fusões automáticas ativas
        TArray<FGuid> AutomaticFusions;
        for (const FSigilFusionInstance& Fusion : ActiveFusions)
        {
            if (Fusion.bIsAutomatic)
            {
                AutomaticFusions.Add(Fusion.FusionID);
            }
        }
        
        for (const FGuid& FusionID : AutomaticFusions)
        {
            CancelFusion(FusionID);
        }
    }
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão automática %s"), 
               bEnabled ? TEXT("habilitada") : TEXT("desabilitada"));
    }
}

void USigilFusionSystem::SetDefaultFusionTime(float NewTime)
{
    DefaultFusionTime = FMath::Max(NewTime, 1.0f);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Tempo padrão de fusão definido para %.1f segundos"), DefaultFusionTime);
    }
}

void USigilFusionSystem::SetMaxSimultaneousFusions(int32 NewMax)
{
    MaxSimultaneousFusions = FMath::Max(NewMax, 1);
    
    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Máximo de fusões simultâneas definido para %d"), MaxSimultaneousFusions);
    }
}

// Funções internas de fusão
void USigilFusionSystem::ProcessFusionCompletion(const FGuid& FusionID)
{
    for (int32 i = ActiveFusions.Num() - 1; i >= 0; i--)
    {
        if (ActiveFusions[i].FusionID == FusionID)
        {
            FSigilFusionInstance& Fusion = ActiveFusions[i];
            
            // Determinar resultado da fusão
            float RandomValue = FMath::RandRange(0.0f, 1.0f);
            ESigilFusionResult Result;
            
            if (RandomValue <= Fusion.Recipe.SuccessRate * 0.1f) // 10% da taxa de sucesso para crítico
            {
                Result = ESigilFusionResult::CriticalSuccess;
            }
            else if (RandomValue <= Fusion.Recipe.SuccessRate)
            {
                Result = ESigilFusionResult::Success;
            }
            else
            {
                Result = ESigilFusionResult::Failed;
            }
            
            // Atualizar estado
            Fusion.CurrentState = (Result != ESigilFusionResult::Failed) ? 
                ESigilFusionState::Completed : ESigilFusionState::Failed;
            
            // Criar sígilo resultado se bem-sucedido
            ASigilItem* ResultSigil = nullptr;
            if (Result != ESigilFusionResult::Failed)
            {
                ResultSigil = CreateResultSigil(Fusion.Recipe, Fusion.OwnerActor);
            }
            
            // Reproduzir efeitos
            PlayFusionVFX(Fusion, Fusion.CurrentState);
            PlayFusionSound(Fusion.CurrentState);
            
            // Mostrar notificação
            if (bShowNotifications)
            {
                FSigilFusionNotification Notification = CreateNotificationForState(Fusion.CurrentState, Fusion);
                ShowFusionNotification(Notification, Fusion.OwnerActor);
            }
            
            // Disparar evento
            OnFusionCompleted.Broadcast(Fusion, Result, ResultSigil);
            
            // Limpar timer
            ClearFusionTimer(FusionID);
            
            // Remover da lista
            ActiveFusions.RemoveAt(i);
            
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Fusão concluída - ID: %s, Resultado: %d"), 
                       *FusionID.ToString(), (int32)Result);
            }
            
            break;
        }
    }
}

void USigilFusionSystem::UpdateFusionProgress(float DeltaTime)
{
    for (FSigilFusionInstance& Fusion : ActiveFusions)
    {
        if (Fusion.CurrentState == ESigilFusionState::InProgress)
        {
            float Progress = GetFusionProgress(Fusion.FusionID);
            OnFusionProgress.Broadcast(Fusion, Progress);
        }
    }
}

ASigilItem* USigilFusionSystem::CreateResultSigil(const FSigilFusionRecipe& Recipe, AActor* Owner)
{
    if (!IsValid(Owner) || !GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Owner ou World inválidos para criação de sígilo"));
        return nullptr;
    }

    // Buscar classe de sígilo apropriada baseada no tipo
    UClass* SigilClass = ASigilItem::StaticClass();

    // Configurar parâmetros de spawn robustos
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = Owner;
    SpawnParams.Instigator = Cast<APawn>(Owner);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    SpawnParams.bNoFail = true;
    SpawnParams.Name = FName(*FString::Printf(TEXT("FusedSigil_%s"), *FGuid::NewGuid().ToString()));

    // Determinar localização de spawn próxima ao owner
    FVector SpawnLocation = Owner->GetActorLocation() + FVector(0, 0, 100); // Spawn acima do owner
    FRotator SpawnRotation = Owner->GetActorRotation();

    ASigilItem* NewSigil = GetWorld()->SpawnActor<ASigilItem>(SigilClass, SpawnLocation, SpawnRotation, SpawnParams);

    if (IsValid(NewSigil))
    {
        // Configurar propriedades do novo sígilo baseado na receita e tipo de fusão
        FGameplayTag ResultTag = Recipe.ResultSigilType;
        ESigilType ResultType = ESigilType::None;

        // Mapear GameplayTags para tipos de sígilo conforme documentação AURACRON
        if (ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank")) ||
            ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Rare")) ||
            ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Epic")) ||
            ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary")) ||
            ResultTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Spectral")))
        {
            // Determinar tipo baseado na receita e inputs
            if (Recipe.FusionType == ESigilFusionType::Basic)
            {
                ResultType = ESigilType::Tank; // Aegis - Tanque
            }
            else if (Recipe.FusionType == ESigilFusionType::Advanced)
            {
                ResultType = ESigilType::Damage; // Ruin - Dano
            }
            else if (Recipe.FusionType == ESigilFusionType::Legendary || Recipe.FusionType == ESigilFusionType::Spectral)
            {
                ResultType = ESigilType::Utility; // Vesper - Utilidade
            }
        }

        // Configurar dados do sígilo de forma robusta usando propriedades existentes
        NewSigil->SigilData.SigilType = ResultType;
        NewSigil->SigilData.SigilID = FMath::RandRange(10000, 99999); // ID único para sígilo fusionado

        // Configurar raridade baseada no tipo de fusão
        switch (Recipe.FusionType)
        {
            case ESigilFusionType::Basic:
                NewSigil->SigilData.Rarity = ESigilRarity::Rare;
                break;
            case ESigilFusionType::Advanced:
                NewSigil->SigilData.Rarity = ESigilRarity::Epic;
                break;
            case ESigilFusionType::Legendary:
                NewSigil->SigilData.Rarity = ESigilRarity::Legendary;
                break;
            case ESigilFusionType::Spectral:
                NewSigil->SigilData.Rarity = ESigilRarity::Legendary; // Máxima raridade disponível
                break;
            default:
                NewSigil->SigilData.Rarity = ESigilRarity::Common;
                break;
        }

        // Configurar estado como equipado se apropriado
        NewSigil->SigilData.State = ESigilState::Unequipped;

        // Configurar tags de gameplay para o sígilo
        NewSigil->SigilData.SigilTag = Recipe.ResultSigilType;
        NewSigil->SigilData.RequiredTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Sigil.Source.Fusion")));

        // Configurar nome único para o sígilo fusionado
        FString SigilName = FString::Printf(TEXT("Sígilo Fusionado %s"), *Recipe.RecipeID.ToString());
        NewSigil->SigilData.SigilName = FText::FromString(SigilName);

        // Configurar descrição baseada no tipo de fusão
        FString Description = FString::Printf(TEXT("Sígilo criado através de fusão %s. Taxa de sucesso: %.1f%%"),
                                            *UEnum::GetValueAsString(Recipe.FusionType), Recipe.SuccessRate * 100.0f);
        NewSigil->SigilData.Description = FText::FromString(Description);

        // Aplicar efeitos visuais de criação usando método de fusão
        if (VFXManager && bShowVFX)
        {
            VFXManager->PlaySigilFusionVFX(NewSigil, Owner, true); // true = fusão completa
        }

        // Notificar sistemas de inventário através de eventos
        OnSigilCreated.Broadcast(NewSigil, Owner, Recipe);

        // Tentar encontrar componente de inventário no owner
        if (UActorComponent* InventoryComponent = Owner->GetComponentByClass(UActorComponent::StaticClass()))
        {
            // Usar reflexão para chamar método de adição se existir
            if (UFunction* AddSigilFunction = InventoryComponent->GetClass()->FindFunctionByName(FName("AddSigil")))
            {
                struct FAddSigilParams
                {
                    ASigilItem* Sigil;
                };

                FAddSigilParams Params;
                Params.Sigil = NewSigil;

                InventoryComponent->ProcessEvent(AddSigilFunction, &Params);
            }
        }

        // Replicar para clientes se em ambiente multiplayer
        if (GetWorld()->GetNetMode() != NM_Standalone)
        {
            NewSigil->SetReplicates(true);
            NewSigil->bAlwaysRelevant = true;
        }

        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Sígilo resultado criado com sucesso - Tipo: %s, Raridade: %s, ID: %d"),
                   *Recipe.ResultSigilType.ToString(),
                   *UEnum::GetValueAsString(NewSigil->SigilData.Rarity),
                   NewSigil->SigilData.SigilID);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Falha ao criar sígilo resultado"));
    }

    return NewSigil;
}

bool USigilFusionSystem::ValidateFusionInputs(const TArray<ASigilItem*>& InputSigils, const FSigilFusionRecipe& Recipe) const
{
    // Validação básica de quantidade
    if (InputSigils.Num() != Recipe.RequiredSigilTypes.Num())
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Quantidade de sígilos incorreta. Esperado: %d, Recebido: %d"),
                   Recipe.RequiredSigilTypes.Num(), InputSigils.Num());
        }
        return false;
    }

    // Verificar se todos os sígilos são válidos
    for (ASigilItem* Sigil : InputSigils)
    {
        if (!IsValid(Sigil))
        {
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Sígilo inválido encontrado na entrada"));
            }
            return false;
        }

        // Verificar se o sígilo não está sendo usado em outra fusão
        if (IsSigilInActiveFusion(Sigil))
        {
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Sígilo %s já está sendo usado em outra fusão"),
                       *Sigil->GetName());
            }
            return false;
        }

        // Verificar se o sígilo tem raridade suficiente (baseado no nível da receita)
        int32 SigilLevel = static_cast<int32>(Sigil->GetSigilRarity()) + 1; // Converter raridade para nível
        if (SigilLevel < (Recipe.RequiredLevel / 25)) // Cada 25 níveis = 1 raridade
        {
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Sígilo %s tem raridade insuficiente. Requerido: %d, Atual: %s"),
                       *Sigil->GetName(), Recipe.RequiredLevel, *UEnum::GetValueAsString(Sigil->GetSigilRarity()));
            }
            return false;
        }
    }

    // Verificar tipos de sígilos com mapeamento robusto
    TArray<FGameplayTag> InputTypes;
    for (ASigilItem* Sigil : InputSigils)
    {
        // Converter ESigilType para FGameplayTag
        ESigilType SigilType = Sigil->GetSigilType();
        FGameplayTag TypeTag;

        switch (SigilType)
        {
            case ESigilType::Tank:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank"));
                break;
            case ESigilType::Damage:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage"));
                break;
            case ESigilType::Utility:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility"));
                break;
            default:
                TypeTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.None"));
                break;
        }

        InputTypes.Add(TypeTag);
    }

    // Verificar correspondência de tipos (permitindo ordem flexível)
    TArray<FGameplayTag> RequiredTypes = Recipe.RequiredSigilTypes;

    for (const FGameplayTag& InputTag : InputTypes)
    {
        bool bTypeFound = false;

        // Primeiro, tentar correspondência exata
        for (int32 i = 0; i < RequiredTypes.Num(); i++)
        {
            if (RequiredTypes[i] == InputTag)
            {
                RequiredTypes.RemoveAt(i);
                bTypeFound = true;
                break;
            }
        }

        // Se não encontrou correspondência exata, verificar tipos genéricos
        if (!bTypeFound)
        {
            for (int32 i = 0; i < RequiredTypes.Num(); i++)
            {
                // Verificar se é um tipo "Enhanced" genérico
                if (RequiredTypes[i] == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Enhanced")) &&
                    (InputTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Tank.Enhanced")) ||
                     InputTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Damage.Enhanced")) ||
                     InputTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Utility.Enhanced"))))
                {
                    RequiredTypes.RemoveAt(i);
                    bTypeFound = true;
                    break;
                }

                // Verificar se é um tipo "Legendary" genérico
                if (RequiredTypes[i] == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary")) &&
                    (InputTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Hybrid")) ||
                     InputTag == FGameplayTag::RequestGameplayTag(FName("Sigil.Type.Legendary.Support"))))
                {
                    RequiredTypes.RemoveAt(i);
                    bTypeFound = true;
                    break;
                }
            }
        }

        if (!bTypeFound)
        {
            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Tipo de sígilo não corresponde. Tag: %s"),
                       *InputTag.ToString());
            }
            return false;
        }
    }

    // Verificar se todos os tipos necessários foram satisfeitos
    if (RequiredTypes.Num() > 0)
    {
        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Tipos de sígilo faltando: %d"), RequiredTypes.Num());
        }
        return false;
    }

    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Validação de entrada bem-sucedida para receita %s"),
               *Recipe.RecipeID.ToString());
    }

    return true;
}

bool USigilFusionSystem::IsSigilInActiveFusion(const ASigilItem* Sigil) const
{
    if (!IsValid(Sigil))
    {
        return false;
    }

    // Verificar se o sígilo está sendo usado em alguma fusão ativa
    for (const FSigilFusionInstance& FusionInstance : ActiveFusions)
    {
        // Verificar se está na lista de sígilos de entrada
        if (FusionInstance.InputSigils.Contains(const_cast<ASigilItem*>(Sigil)))
        {
            // Verificar se a fusão ainda está ativa (não completada, falhada ou cancelada)
            if (FusionInstance.CurrentState == ESigilFusionState::InProgress)
            {
                return true;
            }
        }
    }

    return false;
}

float USigilFusionSystem::CalculateSuccessRate(const TArray<ASigilItem*>& InputSigils, const FSigilFusionRecipe& Recipe) const
{
    float BaseSuccessRate = Recipe.SuccessRate;

    // Modificadores baseados na qualidade e nível dos sígilos de entrada
    float QualityModifier = 0.0f;
    float LevelModifier = 0.0f;
    float SynergyModifier = 0.0f;

    int32 TotalSigilLevel = 0;
    int32 TotalSigilQuality = 0;

    for (const ASigilItem* Sigil : InputSigils)
    {
        if (IsValid(Sigil))
        {
            // Modificador de qualidade baseado na raridade do sígilo
            int32 SigilQuality = static_cast<int32>(Sigil->GetSigilRarity()) + 1;
            TotalSigilQuality += SigilQuality;
            QualityModifier += SigilQuality * 0.02f; // 2% por ponto de qualidade

            // Modificador de nível do sígilo (baseado na raridade)
            int32 SigilLevel = SigilQuality * 25; // Cada raridade = 25 níveis
            TotalSigilLevel += SigilLevel;

            // Bônus por nível acima do mínimo requerido
            if (SigilLevel > Recipe.RequiredLevel)
            {
                LevelModifier += (SigilLevel - Recipe.RequiredLevel) * 0.01f; // 1% por nível extra
            }

            // Penalidade por nível abaixo do recomendado
            if (SigilLevel < Recipe.RequiredLevel)
            {
                LevelModifier -= (Recipe.RequiredLevel - SigilLevel) * 0.05f; // 5% de penalidade por nível faltante
            }
        }
    }

    // Modificador de sinergia baseado na combinação de tipos
    if (InputSigils.Num() >= 2)
    {
        // Verificar combinações específicas conforme documentação AURACRON
        bool bHasAegis = false, bHasRuin = false, bHasVesper = false;

        for (const ASigilItem* Sigil : InputSigils)
        {
            if (IsValid(Sigil))
            {
                ESigilType Type = Sigil->GetSigilType();
                if (Type == ESigilType::Tank) bHasAegis = true;
                else if (Type == ESigilType::Damage) bHasRuin = true;
                else if (Type == ESigilType::Utility) bHasVesper = true;
            }
        }

        // Bônus de sinergia para combinações específicas
        if (bHasAegis && bHasRuin && !bHasVesper)
        {
            SynergyModifier += 0.1f; // 10% bônus para Aegis + Ruin
        }
        else if (bHasAegis && bHasVesper && !bHasRuin)
        {
            SynergyModifier += 0.08f; // 8% bônus para Aegis + Vesper
        }
        else if (bHasRuin && bHasVesper && !bHasAegis)
        {
            SynergyModifier += 0.12f; // 12% bônus para Ruin + Vesper (mais arriscado)
        }

        // Bônus para sígilos do mesmo tipo (fusão dupla)
        if (InputSigils.Num() == 2)
        {
            ESigilType FirstType = InputSigils[0]->GetSigilType();
            ESigilType SecondType = InputSigils[1]->GetSigilType();

            if (FirstType == SecondType && FirstType != ESigilType::None)
            {
                SynergyModifier += 0.05f; // 5% bônus para fusão dupla
            }
        }
    }

    // Modificador baseado no tipo de fusão (conforme documentação)
    float FusionTypeModifier = 0.0f;
    switch (Recipe.FusionType)
    {
        case ESigilFusionType::Basic:
            FusionTypeModifier = 0.0f; // Sem modificador
            break;
        case ESigilFusionType::Advanced:
            FusionTypeModifier = -0.1f; // 10% mais difícil
            break;
        case ESigilFusionType::Legendary:
            FusionTypeModifier = -0.2f; // 20% mais difícil
            break;
        case ESigilFusionType::Spectral:
            FusionTypeModifier = -0.3f; // 30% mais difícil
            break;
        default:
            break;
    }

    // Modificador baseado na experiência do jogador (simulado)
    float ExperienceModifier = 0.0f;
    int32 CompletedFusions = GetCompletedFusionCount();
    if (CompletedFusions > 0)
    {
        ExperienceModifier = FMath::Min(CompletedFusions * 0.005f, 0.15f); // Máximo 15% de bônus
    }

    // Calcular taxa final
    float FinalSuccessRate = BaseSuccessRate + QualityModifier + LevelModifier +
                            SynergyModifier + FusionTypeModifier + ExperienceModifier;

    // Aplicar limites realistas
    FinalSuccessRate = FMath::Clamp(FinalSuccessRate, 0.05f, 0.95f); // Entre 5% e 95%

    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Taxa de sucesso calculada: %.2f%% (Base: %.2f%%, Qualidade: +%.2f%%, Nível: +%.2f%%, Sinergia: +%.2f%%, Tipo: %.2f%%, Experiência: +%.2f%%)"),
               FinalSuccessRate * 100.0f, BaseSuccessRate * 100.0f, QualityModifier * 100.0f,
               LevelModifier * 100.0f, SynergyModifier * 100.0f, FusionTypeModifier * 100.0f, ExperienceModifier * 100.0f);
    }

    return FinalSuccessRate;
}

int32 USigilFusionSystem::GetCompletedFusionCount() const
{
    // Contar fusões completadas com sucesso
    int32 CompletedCount = 0;

    for (const FSigilFusionInstance& FusionInstance : ActiveFusions)
    {
        if (FusionInstance.CurrentState == ESigilFusionState::Completed)
        {
            CompletedCount++;
        }
    }

    // Adicionar contador persistente se disponível
    // Isso seria implementado com save game ou sistema de progressão

    return CompletedCount;
}

void USigilFusionSystem::ConsumeFusionInputs(const TArray<ASigilItem*>& InputSigils)
{
    if (InputSigils.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Nenhum sígilo de entrada para consumir"));
        return;
    }

    int32 ConsumedCount = 0;

    for (ASigilItem* Sigil : InputSigils)
    {
        if (IsValid(Sigil))
        {
            // Notificar sistemas antes de destruir
            OnSigilConsumed.Broadcast(Sigil, Sigil->GetOwner());

            // Tentar remover do inventário primeiro se houver componente de inventário
            if (AActor* SigilOwner = Sigil->GetOwner())
            {
                if (UActorComponent* InventoryComponent = SigilOwner->GetComponentByClass(UActorComponent::StaticClass()))
                {
                    // Usar reflexão para chamar método de remoção se existir
                    if (UFunction* RemoveSigilFunction = InventoryComponent->GetClass()->FindFunctionByName(FName("RemoveSigil")))
                    {
                        struct FRemoveSigilParams
                        {
                            ASigilItem* Sigil;
                            bool bReturnValue;
                        };

                        FRemoveSigilParams Params;
                        Params.Sigil = Sigil;
                        Params.bReturnValue = false;

                        InventoryComponent->ProcessEvent(RemoveSigilFunction, &Params);

                        if (Params.bReturnValue)
                        {
                            if (bLogFusionEvents)
                            {
                                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Sígilo removido do inventário: %s"),
                                       *Sigil->GetName());
                            }
                        }
                    }
                }
            }

            // Reproduzir efeito visual de consumo usando método de desequipar
            if (VFXManager && bShowVFX)
            {
                AActor* Owner = Sigil->GetOwner();
                if (Owner)
                {
                    VFXManager->PlaySigilUnequipVFX(Sigil, Owner);
                }
            }

            // Marcar para destruição de forma segura
            Sigil->SetLifeSpan(0.1f); // Destruir após 0.1 segundos para permitir efeitos
            ConsumedCount++;

            if (bLogFusionEvents)
            {
                UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Sígilo consumido: %s (Tipo: %d)"),
                       *Sigil->GetName(), (int32)Sigil->GetSigilType());
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Sígilo inválido encontrado na lista de entrada"));
        }
    }

    if (bLogFusionEvents)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: %d de %d sígilos de entrada consumidos com sucesso"),
               ConsumedCount, InputSigils.Num());
    }

    // Verificar se todos os sígilos foram consumidos
    if (ConsumedCount != InputSigils.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Nem todos os sígilos de entrada foram consumidos (%d/%d)"),
               ConsumedCount, InputSigils.Num());
    }
}

// Funções de efeitos
void USigilFusionSystem::PlayFusionVFX(const FSigilFusionInstance& FusionInstance, ESigilFusionState State)
{
    if (!bShowVFX || !VFXManager)
    {
        return;
    }
    
    FVector Location = FusionInstance.OwnerActor ? FusionInstance.OwnerActor->GetActorLocation() : FVector::ZeroVector;
    
    switch (State)
    {
        case ESigilFusionState::InProgress:
            if (FusionInstance.InputSigils.Num() > 0 && IsValid(FusionInstance.InputSigils[0]))
            {
                VFXManager->PlaySigilFusionVFX(FusionInstance.InputSigils[0], FusionInstance.OwnerActor, false);
            }
            break;
        case ESigilFusionState::Completed:
            if (FusionInstance.InputSigils.Num() > 0 && IsValid(FusionInstance.InputSigils[0]))
            {
                VFXManager->PlaySigilFusionVFX(FusionInstance.InputSigils[0], FusionInstance.OwnerActor, true);
            }
            break;
        case ESigilFusionState::Failed:
            // VFX de falha seria implementado no VFXManager
            break;
        case ESigilFusionState::Cancelled:
            // VFX de cancelamento seria implementado no VFXManager
            break;
        default:
            break;
    }
}

void USigilFusionSystem::PlayFusionSound(ESigilFusionState State)
{
    if (!bPlaySounds)
    {
        return;
    }
    
    USoundBase* SoundToPlay = nullptr;
    
    switch (State)
    {
        case ESigilFusionState::InProgress:
            SoundToPlay = FusionStartSound;
            break;
        case ESigilFusionState::Completed:
            SoundToPlay = FusionCompleteSound;
            break;
        case ESigilFusionState::Failed:
        case ESigilFusionState::Cancelled:
            SoundToPlay = FusionFailSound;
            break;
        default:
            break;
    }
    
    if (SoundToPlay && GetWorld())
    {
        UGameplayStatics::PlaySound2D(GetWorld(), SoundToPlay);
    }
}

// Funções de notificação internas
FSigilFusionNotification USigilFusionSystem::CreateNotificationForState(ESigilFusionState State, const FSigilFusionInstance& FusionInstance) const
{
    FSigilFusionNotification Notification;
    Notification.FusionState = State;
    Notification.DisplayDuration = NOTIFICATION_DISPLAY_TIME;
    
    switch (State)
    {
        case ESigilFusionState::InProgress:
            Notification.Title = FText::FromString(TEXT("Fusão Iniciada"));
            Notification.Message = FText::FromString(TEXT("Fusão de sígilos em progresso..."));
            Notification.NotificationColor = FLinearColor::Yellow;
            break;
        case ESigilFusionState::Completed:
            Notification.Title = FText::FromString(TEXT("Fusão Concluída"));
            Notification.Message = FText::FromString(TEXT("Fusão de sígilos bem-sucedida!"));
            Notification.NotificationColor = FLinearColor::Green;
            break;
        case ESigilFusionState::Failed:
            Notification.Title = FText::FromString(TEXT("Fusão Falhou"));
            Notification.Message = FText::FromString(TEXT("A fusão de sígilos falhou."));
            Notification.NotificationColor = FLinearColor::Red;
            break;
        case ESigilFusionState::Cancelled:
            Notification.Title = FText::FromString(TEXT("Fusão Cancelada"));
            Notification.Message = FText::FromString(TEXT("A fusão de sígilos foi cancelada."));
            Notification.NotificationColor = FLinearColor::Gray;
            break;
        default:
            Notification.Title = FText::FromString(TEXT("Fusão"));
            Notification.Message = FText::FromString(TEXT("Estado de fusão desconhecido."));
            Notification.NotificationColor = FLinearColor::White;
            break;
    }
    
    return Notification;
}

void USigilFusionSystem::CreateNotificationWidget(const FSigilFusionNotification& Notification)
{
    if (!NotificationWidgetClass || !GetWorld())
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: NotificationWidgetClass ou World inválidos"));
        return;
    }

    // Usar API moderna do UE 5.6 para criação de widgets
    UUserWidget* NotificationWidget = nullptr;

    // Tentar obter PlayerController para criação do widget
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        NotificationWidget = UUserWidget::CreateWidgetInstance(*PC, NotificationWidgetClass, FName("SigilFusionNotification"));
    }
    else
    {
        // Fallback para GameInstance se PlayerController não estiver disponível
        if (UGameInstance* GameInstance = GetWorld()->GetGameInstance())
        {
            NotificationWidget = UUserWidget::CreateWidgetInstance(*GameInstance, NotificationWidgetClass, FName("SigilFusionNotification"));
        }
        else
        {
            // Último fallback para World
            NotificationWidget = UUserWidget::CreateWidgetInstance(*GetWorld(), NotificationWidgetClass, FName("SigilFusionNotification"));
        }
    }

    if (IsValid(NotificationWidget))
    {
        // Configurar propriedades do widget usando APIs modernas do UE 5.6
        // Usar reflexão através de UFunction para configurar propriedades
        if (UFunction* SetTitleFunction = NotificationWidget->GetClass()->FindFunctionByName(FName("SetNotificationTitle")))
        {
            struct FSetTitleParams
            {
                FText Title;
            };

            FSetTitleParams Params;
            Params.Title = Notification.Title;

            NotificationWidget->ProcessEvent(SetTitleFunction, &Params);
        }

        if (UFunction* SetMessageFunction = NotificationWidget->GetClass()->FindFunctionByName(FName("SetNotificationMessage")))
        {
            struct FSetMessageParams
            {
                FText Message;
            };

            FSetMessageParams Params;
            Params.Message = Notification.Message;

            NotificationWidget->ProcessEvent(SetMessageFunction, &Params);
        }

        if (UFunction* SetColorFunction = NotificationWidget->GetClass()->FindFunctionByName(FName("SetNotificationColor")))
        {
            struct FSetColorParams
            {
                FLinearColor Color;
            };

            FSetColorParams Params;
            Params.Color = Notification.NotificationColor;

            NotificationWidget->ProcessEvent(SetColorFunction, &Params);
        }

        // Adicionar ao viewport com Z-Order apropriado
        NotificationWidget->AddToViewport(1000); // Z-Order alto para notificações
        ActiveNotificationWidgets.Add(NotificationWidget);

        // Configurar timer para remover a notificação usando API moderna
        FTimerHandle NotificationTimer;
        FTimerDelegate NotificationDelegate;
        NotificationDelegate.BindUObject(this, &USigilFusionSystem::RemoveNotificationWidget, NotificationWidget);

        GetWorld()->GetTimerManager().SetTimer(
            NotificationTimer,
            NotificationDelegate,
            Notification.DisplayDuration,
            false
        );

        // Armazenar timer para limpeza posterior se necessário
        NotificationTimers.Add(NotificationWidget, NotificationTimer);

        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Widget de notificação criado com sucesso - %s"),
                   *Notification.Title.ToString());
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Falha ao criar widget de notificação"));
    }
}

void USigilFusionSystem::RemoveNotificationWidget(UUserWidget* Widget)
{
    if (IsValid(Widget))
    {
        // Limpar timer associado se existir
        if (FTimerHandle* TimerHandle = NotificationTimers.Find(Widget))
        {
            if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(*TimerHandle))
            {
                GetWorld()->GetTimerManager().ClearTimer(*TimerHandle);
            }
            NotificationTimers.Remove(Widget);
        }

        // Remover widget da viewport e da lista
        Widget->RemoveFromParent();
        ActiveNotificationWidgets.Remove(Widget);

        if (bLogFusionEvents)
        {
            UE_LOG(LogTemp, Log, TEXT("SigilFusionSystem: Widget de notificação removido"));
        }
    }
}

// Funções de timer
void USigilFusionSystem::OnFusionTimerComplete(FGuid FusionID)
{
    ProcessFusionCompletion(FusionID);
}

void USigilFusionSystem::ClearFusionTimer(const FGuid& FusionID)
{
    if (FTimerHandle* TimerHandle = FusionTimers.Find(FusionID))
    {
        if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(*TimerHandle))
        {
            GetWorld()->GetTimerManager().ClearTimer(*TimerHandle);
        }
        FusionTimers.Remove(FusionID);
    }
}

// Funções de depuração
void USigilFusionSystem::DebugPrintActiveFusions()
{
    UE_LOG(LogTemp, Warning, TEXT("=== FUSÕES ATIVAS ==="));
    UE_LOG(LogTemp, Warning, TEXT("Total: %d fusões"), ActiveFusions.Num());
    
    for (const FSigilFusionInstance& Fusion : ActiveFusions)
    {
        float Progress = GetFusionProgress(Fusion.FusionID);
        UE_LOG(LogTemp, Warning, TEXT("ID: %s | Estado: %d | Progresso: %.1f%% | Proprietário: %s"),
               *Fusion.FusionID.ToString(),
               (int32)Fusion.CurrentState,
               Progress * 100.0f,
               Fusion.OwnerActor ? *Fusion.OwnerActor->GetName() : TEXT("Nenhum"));
    }
    
    UE_LOG(LogTemp, Warning, TEXT("====================="));
}

void USigilFusionSystem::DebugStartTestFusion()
{
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Nenhum proprietário para teste de fusão"));
        return;
    }
    
    // Criar sígilos de teste
    TArray<ASigilItem*> TestSigils;
    
    for (int32 i = 0; i < 2; i++)
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = GetOwner();
        
        ASigilItem* TestSigil = GetWorld()->SpawnActor<ASigilItem>(ASigilItem::StaticClass(), SpawnParams);
        if (IsValid(TestSigil))
        {
            TestSigil->SigilData.SigilType = ESigilType::None;
            TestSigils.Add(TestSigil);
        }
    }
    
    if (TestSigils.Num() >= 2)
    {
        StartAutomaticFusion(TestSigils, GetOwner());
        UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Fusão de teste iniciada"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SigilFusionSystem: Falha ao criar sígilos de teste"));
    }
}

void USigilFusionSystem::DebugCancelAllFusions()
{
    int32 CancelledCount = ActiveFusions.Num();
    CancelAllFusions();
    
    UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: %d fusões canceladas via debug"), CancelledCount);
}

void USigilFusionSystem::SetDebugMode(bool bEnabled)
{
    bDebugMode = bEnabled;
    bLogFusionEvents = bEnabled;
    
    UE_LOG(LogTemp, Warning, TEXT("SigilFusionSystem: Modo de depuração %s"), 
           bEnabled ? TEXT("habilitado") : TEXT("desabilitado"));
}