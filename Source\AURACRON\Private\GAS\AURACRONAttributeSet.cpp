// AURACRONAttributeSet.cpp
// Sistema de Sígilos AURACRON - Implementação do Conjunto de Atributos UE 5.6
// Implementação robusta e completa com APIs modernas do UE 5.6

#include "GAS/AURACRONAttributeSet.h"
#include "Net/UnrealNetwork.h"
#include "GameplayEffect.h"
#include "GameplayEffectExtension.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameFramework/Character.h"
#include "Engine/Engine.h"
#include "GameplayTags.h"
#include "GameplayEffectTypes.h"
#include "AbilitySystemGlobals.h"
#include "GameplayEffectComponent.h"
#include "TickableAttributeSetInterface.h"
#include "Logging/LogMacros.h"
#include "GameFramework/PlayerController.h"
#include "Engine/World.h"
#include "TimerManager.h"

// Definir categoria de log para o sistema de atributos AURACRON
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONAttributes, Log, All);

// Definir tags de gameplay para o sistema de Sígilos
namespace AURACRONGameplayTags
{
    // Tags de Sígilos
    const FGameplayTag Sigil_Aegis = FGameplayTag::RequestGameplayTag(FName("Sigil.Aegis"));
    const FGameplayTag Sigil_Ruin = FGameplayTag::RequestGameplayTag(FName("Sigil.Ruin"));
    const FGameplayTag Sigil_Vesper = FGameplayTag::RequestGameplayTag(FName("Sigil.Vesper"));

    // Tags de Estado
    const FGameplayTag State_Dead = FGameplayTag::RequestGameplayTag(FName("State.Dead"));
    const FGameplayTag State_Invulnerable = FGameplayTag::RequestGameplayTag(FName("State.Invulnerable"));
    const FGameplayTag State_Regenerating = FGameplayTag::RequestGameplayTag(FName("State.Regenerating"));

    // Tags de Ambiente (baseado na documentação)
    const FGameplayTag Environment_Solar = FGameplayTag::RequestGameplayTag(FName("Environment.Solar"));
    const FGameplayTag Environment_Lunar = FGameplayTag::RequestGameplayTag(FName("Environment.Lunar"));
    const FGameplayTag Environment_Axis = FGameplayTag::RequestGameplayTag(FName("Environment.Axis"));
}

UAURACRONAttributeSet::UAURACRONAttributeSet()
{
    // Inicializar valores padrão dos atributos primários
    InitHealth(100.0f);
    InitMaxHealth(100.0f);
    InitMana(100.0f);
    InitMaxMana(100.0f);

    // Inicializar atributos de combate
    InitAttackDamage(50.0f);
    InitAbilityPower(50.0f);
    InitArmor(20.0f);
    InitMagicResistance(20.0f);
    InitAttackSpeed(1.0f);
    InitCriticalChance(0.05f);
    InitCriticalDamage(1.5f);
    InitAccuracy(1.0f);

    // Inicializar atributos de movimento
    InitMovementSpeed(500.0f);

    // Inicializar atributos de regeneração
    InitHealthRegeneration(2.0f);
    InitManaRegeneration(5.0f);

    // Inicializar atributos de cooldown
    InitCooldownReduction(0.0f);

    // Inicializar atributos específicos do sistema de Sígilos AURACRON
    InitSigilEfficiency(1.0f);
    InitMaxSigilSlots(3.0f); // Padrão: 3 slots, máximo 6 conforme documentação

    // Inicializar atributos de progressão
    InitExperienceMultiplier(1.0f);

    // Inicializar meta-atributos (usados para processamento de dano/cura)
    InitIncomingDamage(0.0f);
    InitIncomingHealing(0.0f);
    InitIncomingShield(0.0f);

    // Inicializar timer para regeneração automática
    RegenerationTimerHandle = FTimerHandle();
    LastMovementSpeedValidation = 0.0f;

    // Log de inicialização
    UE_LOG(LogAURACRONAttributes, Log, TEXT("AURACRONAttributeSet inicializado com valores padrão"));
}

void UAURACRONAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar atributos primários
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Health, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MaxHealth, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Mana, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MaxMana, COND_None, REPNOTIFY_Always);

    // Replicar atributos de combate
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, AttackDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, AbilityPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Armor, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MagicResistance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, AttackSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, CriticalChance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, CriticalDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, Accuracy, COND_None, REPNOTIFY_Always);

    // Replicar atributos de movimento
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MovementSpeed, COND_None, REPNOTIFY_Always);

    // Replicar atributos de regeneração
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, HealthRegeneration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, ManaRegeneration, COND_None, REPNOTIFY_Always);

    // Replicar atributos de cooldown
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, CooldownReduction, COND_None, REPNOTIFY_Always);

    // Replicar atributos específicos do sistema de Sígilos AURACRON
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, SigilEfficiency, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, MaxSigilSlots, COND_None, REPNOTIFY_Always);

    // Replicar atributos de progressão
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, ExperienceMultiplier, COND_None, REPNOTIFY_Always);

    // Replicar meta-atributos para processamento server-side
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, IncomingDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, IncomingHealing, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAURACRONAttributeSet, IncomingShield, COND_None, REPNOTIFY_Always);
}

void UAURACRONAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
    Super::PreAttributeChange(Attribute, NewValue);

    // Obter o valor atual para comparação e logging
    const float CurrentValue = Attribute.GetNumericValue(this);

    // Validações robustas para atributos primários
    if (Attribute == GetHealthAttribute())
    {
        const float MaxHealthValue = GetMaxHealth();
        NewValue = FMath::Clamp(NewValue, 0.0f, MaxHealthValue);

        // Log mudanças críticas de vida
        if (NewValue <= 0.0f && CurrentValue > 0.0f)
        {
            UE_LOG(LogAURACRONAttributes, Warning, TEXT("Personagem está morrendo: Health %f -> %f"), CurrentValue, NewValue);
        }
        else if (FMath::Abs(NewValue - CurrentValue) > MaxHealthValue * 0.5f)
        {
            UE_LOG(LogAURACRONAttributes, Log, TEXT("Mudança significativa de Health: %f -> %f"), CurrentValue, NewValue);
        }
    }
    else if (Attribute == GetManaAttribute())
    {
        const float MaxManaValue = GetMaxMana();
        NewValue = FMath::Clamp(NewValue, 0.0f, MaxManaValue);

        // Log mudanças significativas de mana
        if (FMath::Abs(NewValue - CurrentValue) > MaxManaValue * 0.3f)
        {
            UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Mudança significativa de Mana: %f -> %f"), CurrentValue, NewValue);
        }
    }
    else if (Attribute == GetMaxHealthAttribute())
    {
        // Vida máxima deve ser sempre positiva
        NewValue = FMath::Max(NewValue, 1.0f);

        // Validação anti-cheat: vida máxima não pode ser excessivamente alta
        const float MaxAllowedHealth = 10000.0f; // Limite baseado no design do jogo
        if (NewValue > MaxAllowedHealth)
        {
            UE_LOG(LogAURACRONAttributes, Error, TEXT("ANTI-CHEAT: Tentativa de definir MaxHealth muito alta: %f. Limitando a %f"), NewValue, MaxAllowedHealth);
            NewValue = MaxAllowedHealth;
        }
    }
    else if (Attribute == GetMaxManaAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);

        // Validação anti-cheat: mana máxima não pode ser excessivamente alta
        const float MaxAllowedMana = 5000.0f;
        if (NewValue > MaxAllowedMana)
        {
            UE_LOG(LogAURACRONAttributes, Error, TEXT("ANTI-CHEAT: Tentativa de definir MaxMana muito alta: %f. Limitando a %f"), NewValue, MaxAllowedMana);
            NewValue = MaxAllowedMana;
        }
    }
    // Validações para atributos de combate
    else if (Attribute == GetAttackDamageAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);

        // Validação anti-cheat: dano de ataque não pode ser excessivamente alto
        const float MaxAllowedAttackDamage = 2000.0f;
        if (NewValue > MaxAllowedAttackDamage)
        {
            UE_LOG(LogAURACRONAttributes, Error, TEXT("ANTI-CHEAT: Tentativa de definir AttackDamage muito alto: %f. Limitando a %f"), NewValue, MaxAllowedAttackDamage);
            NewValue = MaxAllowedAttackDamage;
        }
    }
    else if (Attribute == GetAbilityPowerAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);

        // Validação anti-cheat: poder de habilidade não pode ser excessivamente alto
        const float MaxAllowedAbilityPower = 2000.0f;
        if (NewValue > MaxAllowedAbilityPower)
        {
            UE_LOG(LogAURACRONAttributes, Error, TEXT("ANTI-CHEAT: Tentativa de definir AbilityPower muito alto: %f. Limitando a %f"), NewValue, MaxAllowedAbilityPower);
            NewValue = MaxAllowedAbilityPower;
        }
    }
    else if (Attribute == GetArmorAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);

        // Validação: armadura máxima baseada no design do jogo
        const float MaxAllowedArmor = 500.0f;
        if (NewValue > MaxAllowedArmor)
        {
            UE_LOG(LogAURACRONAttributes, Warning, TEXT("Armor muito alta: %f. Limitando a %f"), NewValue, MaxAllowedArmor);
            NewValue = MaxAllowedArmor;
        }
    }
    else if (Attribute == GetMagicResistanceAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);

        // Validação: resistência mágica máxima
        const float MaxAllowedMagicResistance = 500.0f;
        if (NewValue > MaxAllowedMagicResistance)
        {
            UE_LOG(LogAURACRONAttributes, Warning, TEXT("MagicResistance muito alta: %f. Limitando a %f"), NewValue, MaxAllowedMagicResistance);
            NewValue = MaxAllowedMagicResistance;
        }
    }
    else if (Attribute == GetAttackSpeedAttribute())
    {
        // Velocidade de ataque deve estar entre 0.1 e 5.0
        NewValue = FMath::Clamp(NewValue, 0.1f, 5.0f);

        if (NewValue > 3.0f)
        {
            UE_LOG(LogAURACRONAttributes, Log, TEXT("AttackSpeed alta detectada: %f"), NewValue);
        }
    }
    else if (Attribute == GetCriticalChanceAttribute())
    {
        // Chance crítica entre 0% e 100%
        NewValue = FMath::Clamp(NewValue, 0.0f, 1.0f);
    }
    else if (Attribute == GetCriticalDamageAttribute())
    {
        // Dano crítico mínimo de 100% (1.0), máximo de 500% (5.0)
        NewValue = FMath::Clamp(NewValue, 1.0f, 5.0f);
    }
    else if (Attribute == GetAccuracyAttribute())
    {
        // Precisão entre 0% e 100%
        NewValue = FMath::Clamp(NewValue, 0.0f, 1.0f);
    }
    // Validações para atributos de movimento (CRÍTICO PARA ANTI-CHEAT)
    else if (Attribute == GetMovementSpeedAttribute())
    {
        const float MinMovementSpeed = 100.0f;  // Velocidade mínima
        const float MaxMovementSpeed = 1500.0f; // Velocidade máxima baseada na documentação

        NewValue = FMath::Clamp(NewValue, MinMovementSpeed, MaxMovementSpeed);

        // Validação anti-cheat para speed hacks
        const float SpeedChange = FMath::Abs(NewValue - CurrentValue);
        if (SpeedChange > 200.0f)
        {
            UE_LOG(LogAURACRONAttributes, Warning, TEXT("ANTI-CHEAT: Mudança súbita de velocidade detectada: %f -> %f"), CurrentValue, NewValue);

            // Registrar timestamp para validação contínua
            LastMovementSpeedValidation = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
        }
    }
    // Validações para atributos de regeneração
    else if (Attribute == GetHealthRegenerationAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, 50.0f); // Máximo 50 HP/s
    }
    else if (Attribute == GetManaRegenerationAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, 100.0f); // Máximo 100 MP/s
    }
    else if (Attribute == GetCooldownReductionAttribute())
    {
        // Redução de cooldown máxima de 80% conforme documentação
        NewValue = FMath::Clamp(NewValue, 0.0f, 0.8f);
    }
    // Validações específicas do sistema de Sígilos AURACRON
    else if (Attribute == GetSigilEfficiencyAttribute())
    {
        // Eficiência mínima de 10%, máxima de 300%
        NewValue = FMath::Clamp(NewValue, 0.1f, 3.0f);

        UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("SigilEfficiency alterada: %f -> %f"), CurrentValue, NewValue);
    }
    else if (Attribute == GetMaxSigilSlotsAttribute())
    {
        // Entre 1 e 6 slots conforme documentação do AURACRON
        NewValue = FMath::Clamp(NewValue, 1.0f, 6.0f);

        // Arredondar para número inteiro
        NewValue = FMath::RoundToFloat(NewValue);

        UE_LOG(LogAURACRONAttributes, Log, TEXT("MaxSigilSlots alterado: %f -> %f"), CurrentValue, NewValue);
    }
    else if (Attribute == GetExperienceMultiplierAttribute())
    {
        // Multiplicador de experiência entre 0.1x e 5.0x
        NewValue = FMath::Clamp(NewValue, 0.1f, 5.0f);
    }
    // Validações para meta-atributos
    else if (Attribute == GetIncomingDamageAttribute())
    {
        // Dano recebido não pode ser negativo
        NewValue = FMath::Max(NewValue, 0.0f);

        // Log para dano muito alto (possível cheat)
        if (NewValue > 5000.0f)
        {
            UE_LOG(LogAURACRONAttributes, Error, TEXT("ANTI-CHEAT: Dano excessivamente alto detectado: %f"), NewValue);
        }
    }
    else if (Attribute == GetIncomingHealingAttribute())
    {
        // Cura recebida não pode ser negativa
        NewValue = FMath::Max(NewValue, 0.0f);
    }
    else if (Attribute == GetIncomingShieldAttribute())
    {
        // Escudo recebido não pode ser negativo
        NewValue = FMath::Max(NewValue, 0.0f);
    }
}

void UAURACRONAttributeSet::PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data)
{
    Super::PostGameplayEffectExecute(Data);

    // Obter contexto e informações do efeito
    FGameplayEffectContextHandle Context = Data.EffectSpec.GetContext();
    UAbilitySystemComponent* SourceASC = Context.GetOriginalInstigatorAbilitySystemComponent();
    UAbilitySystemComponent* TargetASC = &Data.Target;
    const FGameplayTagContainer& SourceTags = *Data.EffectSpec.CapturedSourceTags.GetAggregatedTags();
    const FGameplayTagContainer& TargetTags = *Data.EffectSpec.CapturedTargetTags.GetAggregatedTags();

    // Obter atores envolvidos
    AActor* TargetActor = nullptr;
    AActor* SourceActor = nullptr;
    ACharacter* TargetCharacter = nullptr;
    ACharacter* SourceCharacter = nullptr;

    if (Data.Target.AbilityActorInfo.IsValid() && Data.Target.AbilityActorInfo->AvatarActor.IsValid())
    {
        TargetActor = Data.Target.AbilityActorInfo->AvatarActor.Get();
        TargetCharacter = Cast<ACharacter>(TargetActor);
    }

    if (SourceASC && SourceASC->AbilityActorInfo.IsValid() && SourceASC->AbilityActorInfo->AvatarActor.IsValid())
    {
        SourceActor = SourceASC->AbilityActorInfo->AvatarActor.Get();
        SourceCharacter = Cast<ACharacter>(SourceActor);
    }

    // Processar meta-atributos primeiro (dano, cura, escudo)
    if (Data.EvaluatedData.Attribute == GetIncomingDamageAttribute())
    {
        ProcessIncomingDamage(Data, SourceASC, SourceTags, TargetTags);
    }
    else if (Data.EvaluatedData.Attribute == GetIncomingHealingAttribute())
    {
        ProcessIncomingHealing(Data, SourceASC, SourceTags, TargetTags);
    }
    else if (Data.EvaluatedData.Attribute == GetIncomingShieldAttribute())
    {
        ProcessIncomingShield(Data, SourceASC, SourceTags, TargetTags);
    }
    // Processar mudanças em atributos primários
    else if (Data.EvaluatedData.Attribute == GetHealthAttribute())
    {
        const float OldHealth = GetHealth() - Data.EvaluatedData.Magnitude;
        const float NewHealth = FMath::Clamp(GetHealth(), 0.0f, GetMaxHealth());
        SetHealth(NewHealth);

        // Verificar se o personagem morreu
        if (NewHealth <= 0.0f && OldHealth > 0.0f)
        {
            HandleDeath(SourceASC, SourceTags, TargetTags);
        }

        // Broadcast evento de mudança de vida
        OnHealthChanged.Broadcast(OldHealth, NewHealth, SourceASC);

        UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Health alterado: %f -> %f"), OldHealth, NewHealth);
    }
    else if (Data.EvaluatedData.Attribute == GetManaAttribute())
    {
        const float OldMana = GetMana() - Data.EvaluatedData.Magnitude;
        const float NewMana = FMath::Clamp(GetMana(), 0.0f, GetMaxMana());
        SetMana(NewMana);

        // Broadcast evento de mudança de mana
        OnManaChanged.Broadcast(OldMana, NewMana, SourceASC);

        UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Mana alterado: %f -> %f"), OldMana, NewMana);
    }
    else if (Data.EvaluatedData.Attribute == GetMaxHealthAttribute())
    {
        // Ajustar vida atual quando vida máxima muda
        const float OldMaxHealth = GetMaxHealth() - Data.EvaluatedData.Magnitude;
        AdjustAttributeForMaxChange(Health, MaxHealth, GetMaxHealth(), GetHealthAttribute());

        UE_LOG(LogAURACRONAttributes, Log, TEXT("MaxHealth alterado: %f -> %f"), OldMaxHealth, GetMaxHealth());
    }
    else if (Data.EvaluatedData.Attribute == GetMaxManaAttribute())
    {
        // Ajustar mana atual quando mana máxima muda
        const float OldMaxMana = GetMaxMana() - Data.EvaluatedData.Magnitude;
        AdjustAttributeForMaxChange(Mana, MaxMana, GetMaxMana(), GetManaAttribute());

        UE_LOG(LogAURACRONAttributes, Log, TEXT("MaxMana alterado: %f -> %f"), OldMaxMana, GetMaxMana());
    }
    // Processar mudanças em atributos de combate
    else if (Data.EvaluatedData.Attribute == GetAttackDamageAttribute())
    {
        ApplySigilBonuses(GetAttackDamageAttribute(), SourceTags, TargetTags);
        OnCombatAttributeChanged.Broadcast(GetAttackDamageAttribute(), GetAttackDamage(), SourceASC);
    }
    else if (Data.EvaluatedData.Attribute == GetAbilityPowerAttribute())
    {
        ApplySigilBonuses(GetAbilityPowerAttribute(), SourceTags, TargetTags);
        OnCombatAttributeChanged.Broadcast(GetAbilityPowerAttribute(), GetAbilityPower(), SourceASC);
    }
    else if (Data.EvaluatedData.Attribute == GetArmorAttribute())
    {
        ApplySigilBonuses(GetArmorAttribute(), SourceTags, TargetTags);
        OnCombatAttributeChanged.Broadcast(GetArmorAttribute(), GetArmor(), SourceASC);
    }
    else if (Data.EvaluatedData.Attribute == GetMagicResistanceAttribute())
    {
        ApplySigilBonuses(GetMagicResistanceAttribute(), SourceTags, TargetTags);
        OnCombatAttributeChanged.Broadcast(GetMagicResistanceAttribute(), GetMagicResistance(), SourceASC);
    }
    // Processar mudanças em velocidade de movimento (crítico para anti-cheat)
    else if (Data.EvaluatedData.Attribute == GetMovementSpeedAttribute())
    {
        ValidateMovementSpeed(SourceASC, SourceTags, TargetTags);
        ApplySigilBonuses(GetMovementSpeedAttribute(), SourceTags, TargetTags);
        OnMovementSpeedChanged.Broadcast(GetMovementSpeed(), SourceASC);
    }
    // Processar mudanças específicas do sistema de Sígilos
    else if (Data.EvaluatedData.Attribute == GetSigilEfficiencyAttribute())
    {
        OnSigilAttributeChanged.Broadcast(GetSigilEfficiencyAttribute(), GetSigilEfficiency(), SourceASC);
        UE_LOG(LogAURACRONAttributes, Log, TEXT("SigilEfficiency alterado para: %f"), GetSigilEfficiency());
    }
    else if (Data.EvaluatedData.Attribute == GetMaxSigilSlotsAttribute())
    {
        OnSigilAttributeChanged.Broadcast(GetMaxSigilSlotsAttribute(), GetMaxSigilSlots(), SourceASC);
        UE_LOG(LogAURACRONAttributes, Log, TEXT("MaxSigilSlots alterado para: %f"), GetMaxSigilSlots());
    }

    // Aplicar efeitos ambientais baseados na documentação AURACRON
    ApplyEnvironmentalEffects(SourceTags, TargetTags);

    // Iniciar regeneração automática se necessário
    StartRegenerationIfNeeded();
}

void UAURACRONAttributeSet::OnRep_Health(const FGameplayAttributeData& OldHealth)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Health, OldHealth);
}

void UAURACRONAttributeSet::OnRep_MaxHealth(const FGameplayAttributeData& OldMaxHealth)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MaxHealth, OldMaxHealth);
}

void UAURACRONAttributeSet::OnRep_Mana(const FGameplayAttributeData& OldMana)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Mana, OldMana);
}

void UAURACRONAttributeSet::OnRep_MaxMana(const FGameplayAttributeData& OldMaxMana)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MaxMana, OldMaxMana);
}

void UAURACRONAttributeSet::OnRep_AttackDamage(const FGameplayAttributeData& OldAttackDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, AttackDamage, OldAttackDamage);
}

void UAURACRONAttributeSet::OnRep_AbilityPower(const FGameplayAttributeData& OldAbilityPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, AbilityPower, OldAbilityPower);
}

void UAURACRONAttributeSet::OnRep_Armor(const FGameplayAttributeData& OldArmor)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Armor, OldArmor);
}

void UAURACRONAttributeSet::OnRep_MagicResistance(const FGameplayAttributeData& OldMagicResistance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MagicResistance, OldMagicResistance);
}

void UAURACRONAttributeSet::OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, AttackSpeed, OldAttackSpeed);
}

void UAURACRONAttributeSet::OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, CriticalChance, OldCriticalChance);
}

void UAURACRONAttributeSet::OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MovementSpeed, OldMovementSpeed);
}

void UAURACRONAttributeSet::OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, HealthRegeneration, OldHealthRegeneration);
}

void UAURACRONAttributeSet::OnRep_ManaRegeneration(const FGameplayAttributeData& OldManaRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, ManaRegeneration, OldManaRegeneration);
}

void UAURACRONAttributeSet::OnRep_CooldownReduction(const FGameplayAttributeData& OldCooldownReduction)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, CooldownReduction, OldCooldownReduction);
}

void UAURACRONAttributeSet::OnRep_SigilEfficiency(const FGameplayAttributeData& OldSigilEfficiency)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, SigilEfficiency, OldSigilEfficiency);
}

void UAURACRONAttributeSet::OnRep_MaxSigilSlots(const FGameplayAttributeData& OldMaxSigilSlots)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, MaxSigilSlots, OldMaxSigilSlots);
}

void UAURACRONAttributeSet::ProcessIncomingDamage(const FGameplayEffectModCallbackData& Data, UAbilitySystemComponent* SourceASC, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    const float DamageAmount = GetIncomingDamage();
    if (DamageAmount <= 0.0f)
    {
        return;
    }

    // Calcular resistências efetivas
    float PhysicalResistance = CalculateEffectiveResistance(GetArmor(), SourceTags.HasTagExact(FGameplayTag::RequestGameplayTag("Damage.Physical")));
    float MagicalResistance = CalculateEffectiveResistance(GetMagicResistance(), SourceTags.HasTagExact(FGameplayTag::RequestGameplayTag("Damage.Magical")));

    // Aplicar resistência baseada no tipo de dano
    float FinalDamage = DamageAmount;
    if (SourceTags.HasTagExact(FGameplayTag::RequestGameplayTag("Damage.Physical")))
    {
        FinalDamage *= (1.0f - PhysicalResistance);
    }
    else if (SourceTags.HasTagExact(FGameplayTag::RequestGameplayTag("Damage.Magical")))
    {
        FinalDamage *= (1.0f - MagicalResistance);
    }

    // Aplicar modificadores de Sígilos
    FinalDamage = ApplySigilDamageModifiers(FinalDamage, SourceTags, TargetTags);

    // Verificar se há invulnerabilidade
    if (TargetTags.HasTagExact(AURACRONGameplayTags::State_Invulnerable))
    {
        FinalDamage = 0.0f;
        UE_LOG(LogAURACRONAttributes, Log, TEXT("Dano bloqueado por invulnerabilidade"));
    }

    // Aplicar o dano final à vida
    const float NewHealth = FMath::Max(GetHealth() - FinalDamage, 0.0f);
    SetHealth(NewHealth);

    // Resetar o meta-atributo
    SetIncomingDamage(0.0f);

    // Broadcast evento de dano recebido
    OnDamageReceived.Broadcast(FinalDamage, SourceASC, SourceTags);

    UE_LOG(LogAURACRONAttributes, Log, TEXT("Dano processado: %f -> %f (Final: %f)"), DamageAmount, FinalDamage, NewHealth);
}

void UAURACRONAttributeSet::ProcessIncomingHealing(const FGameplayEffectModCallbackData& Data, UAbilitySystemComponent* SourceASC, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    const float HealingAmount = GetIncomingHealing();
    if (HealingAmount <= 0.0f)
    {
        return;
    }

    // Aplicar modificadores de Sígilos para cura
    float FinalHealing = ApplySigilHealingModifiers(HealingAmount, SourceTags, TargetTags);

    // Aplicar eficiência de Sígilos
    FinalHealing *= GetSigilEfficiency();

    // Aplicar a cura à vida (não pode exceder vida máxima)
    const float NewHealth = FMath::Min(GetHealth() + FinalHealing, GetMaxHealth());
    SetHealth(NewHealth);

    // Resetar o meta-atributo
    SetIncomingHealing(0.0f);

    // Broadcast evento de cura recebida
    OnHealingReceived.Broadcast(FinalHealing, SourceASC, SourceTags);

    UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Cura processada: %f -> %f"), HealingAmount, FinalHealing);
}

void UAURACRONAttributeSet::ProcessIncomingShield(const FGameplayEffectModCallbackData& Data, UAbilitySystemComponent* SourceASC, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    const float ShieldAmount = GetIncomingShield();
    if (ShieldAmount <= 0.0f)
    {
        return;
    }

    // Aplicar modificadores de Sígilos para escudo
    float FinalShield = ApplySigilShieldModifiers(ShieldAmount, SourceTags, TargetTags);

    // Aplicar eficiência de Sígilos
    FinalShield *= GetSigilEfficiency();

    // Aplicar escudo temporário (implementação específica do jogo)
    ApplyTemporaryShield(FinalShield, SourceASC);

    // Resetar o meta-atributo
    SetIncomingShield(0.0f);

    // Broadcast evento de escudo recebido
    OnShieldReceived.Broadcast(FinalShield, SourceASC, SourceTags);

    UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Escudo processado: %f"), FinalShield);
}

void UAURACRONAttributeSet::HandleDeath(UAbilitySystemComponent* SourceASC, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    // Adicionar tag de morte
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        ASC->AddLooseGameplayTag(AURACRONGameplayTags::State_Dead);

        // Parar regeneração
        StopRegeneration();

        // Broadcast evento de morte
        OnDeath.Broadcast(SourceASC, SourceTags);

        UE_LOG(LogAURACRONAttributes, Warning, TEXT("Personagem morreu"));
    }
}

void UAURACRONAttributeSet::AdjustAttributeForMaxChange(const FGameplayAttributeData& AffectedAttribute, const FGameplayAttributeData& MaxAttribute, float NewMaxValue, const FGameplayAttribute& AffectedAttributeProperty) const
{
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    const float CurrentMaxValue = MaxAttribute.GetCurrentValue();
    if (!FMath::IsNearlyEqual(CurrentMaxValue, NewMaxValue) && AbilityComp)
    {
        // Calcular a nova proporção
        const float CurrentValue = AffectedAttribute.GetCurrentValue();
        float NewDelta = (CurrentMaxValue > 0.f) ? (CurrentValue * NewMaxValue / CurrentMaxValue) - CurrentValue : NewMaxValue;

        AbilityComp->ApplyModToAttributeUnsafe(AffectedAttributeProperty, EGameplayModOp::Additive, NewDelta);

        UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Atributo ajustado para mudança de máximo: %f -> %f"), CurrentValue, CurrentValue + NewDelta);
    }
}

// Implementação das funções OnRep para os novos atributos

void UAURACRONAttributeSet::OnRep_CriticalDamage(const FGameplayAttributeData& OldCriticalDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, CriticalDamage, OldCriticalDamage);
}

void UAURACRONAttributeSet::OnRep_Accuracy(const FGameplayAttributeData& OldAccuracy)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, Accuracy, OldAccuracy);
}

void UAURACRONAttributeSet::OnRep_ExperienceMultiplier(const FGameplayAttributeData& OldExperienceMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, ExperienceMultiplier, OldExperienceMultiplier);
}

// Implementações das funções específicas do sistema de Sígilos AURACRON

float UAURACRONAttributeSet::CalculateEffectiveResistance(float BaseResistance, bool bIsCorrectDamageType) const
{
    // Fórmula de resistência: Resistência / (Resistência + 100)
    float EffectiveResistance = BaseResistance / (BaseResistance + 100.0f);

    // Aplicar bônus de Sígilo Aegis se aplicável
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Aegis))
        {
            // Aegis: Armadura adaptativa (+15% resistência efetiva)
            EffectiveResistance *= 1.15f;
        }
    }

    // Limitar resistência máxima a 80%
    return FMath::Clamp(EffectiveResistance, 0.0f, 0.8f);
}

float UAURACRONAttributeSet::ApplySigilDamageModifiers(float BaseDamage, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags) const
{
    float ModifiedDamage = BaseDamage;

    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Sígilo Ruin: +12% ATK/AP adaptativo
        if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Ruin))
        {
            if (SourceTags.HasTagExact(FGameplayTag::RequestGameplayTag("Damage.Physical")))
            {
                ModifiedDamage *= (1.0f + (GetAttackDamage() * 0.12f / 100.0f));
            }
            else if (SourceTags.HasTagExact(FGameplayTag::RequestGameplayTag("Damage.Magical")))
            {
                ModifiedDamage *= (1.0f + (GetAbilityPower() * 0.12f / 100.0f));
            }
        }

        // Aplicar eficiência de Sígilos
        ModifiedDamage *= GetSigilEfficiency();
    }

    return ModifiedDamage;
}

float UAURACRONAttributeSet::ApplySigilHealingModifiers(float BaseHealing, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags) const
{
    float ModifiedHealing = BaseHealing;

    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Sígilo Vesper: Bônus de cura baseado em utilidade
        if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Vesper))
        {
            ModifiedHealing *= 1.1f; // +10% de cura
        }

        // Aplicar eficiência de Sígilos
        ModifiedHealing *= GetSigilEfficiency();
    }

    return ModifiedHealing;
}

float UAURACRONAttributeSet::ApplySigilShieldModifiers(float BaseShield, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags) const
{
    float ModifiedShield = BaseShield;

    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Sígilo Aegis: Bônus de escudo
        if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Aegis))
        {
            ModifiedShield *= 1.2f; // +20% de escudo
        }

        // Aplicar eficiência de Sígilos
        ModifiedShield *= GetSigilEfficiency();
    }

    return ModifiedShield;
}

void UAURACRONAttributeSet::ApplySigilBonuses(const FGameplayAttribute& Attribute, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Aplicar bônus específicos de cada Sígilo conforme documentação

        // Sígilo Aegis (Tanque): +15% HP, Armadura adaptativa
        if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Aegis))
        {
            if (Attribute == GetMaxHealthAttribute())
            {
                // Bônus já aplicado via GameplayEffect, apenas log
                UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Bônus Aegis aplicado a MaxHealth"));
            }
        }

        // Sígilo Ruin (Dano): +12% ATK/AP adaptativo
        else if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Ruin))
        {
            if (Attribute == GetAttackDamageAttribute() || Attribute == GetAbilityPowerAttribute())
            {
                UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Bônus Ruin aplicado a atributo de dano"));
            }
        }

        // Sígilo Vesper (Utilidade): +10% Vel. Move + 8% Recarga
        else if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::Sigil_Vesper))
        {
            if (Attribute == GetMovementSpeedAttribute())
            {
                UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Bônus Vesper aplicado a MovementSpeed"));
            }
            else if (Attribute == GetCooldownReductionAttribute())
            {
                UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Bônus Vesper aplicado a CooldownReduction"));
            }
        }
    }
}

void UAURACRONAttributeSet::ValidateMovementSpeed(UAbilitySystemComponent* SourceASC, const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    // Validação anti-cheat crítica para velocidade de movimento
    const float CurrentSpeed = GetMovementSpeed();
    const float MaxAllowedSpeed = 1500.0f; // Baseado na documentação

    if (CurrentSpeed > MaxAllowedSpeed)
    {
        UE_LOG(LogAURACRONAttributes, Error, TEXT("ANTI-CHEAT: Velocidade excessiva detectada: %f. Limitando a %f"), CurrentSpeed, MaxAllowedSpeed);
        SetMovementSpeed(MaxAllowedSpeed);
    }

    // Verificar mudanças súbitas suspeitas
    if (GetWorld())
    {
        const float CurrentTime = GetWorld()->GetTimeSeconds();
        const float TimeSinceLastValidation = CurrentTime - LastMovementSpeedValidation;

        if (TimeSinceLastValidation < 0.1f && CurrentSpeed > 800.0f)
        {
            UE_LOG(LogAURACRONAttributes, Warning, TEXT("ANTI-CHEAT: Mudança de velocidade muito rápida detectada"));
        }

        LastMovementSpeedValidation = CurrentTime;
    }
}

void UAURACRONAttributeSet::ApplyEnvironmentalEffects(const FGameplayTagContainer& SourceTags, const FGameplayTagContainer& TargetTags)
{
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Efeitos dos trilhos baseados na documentação AURACRON

        // Solar Trilhos: boost de velocidade de movimento e regeneração de vida
        if (TargetTags.HasTagExact(AURACRONGameplayTags::Environment_Solar))
        {
            // Aplicar bônus de regeneração solar
            const float SolarRegenBonus = GetHealthRegeneration() * 0.5f; // +50% regeneração
            ApplyTemporaryAttributeBonus(GetHealthRegenerationAttribute(), SolarRegenBonus, 5.0f);

            UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Efeito Solar Trilhos aplicado"));
        }

        // Lunar Trilhos: furtividade e visão aprimorada
        else if (TargetTags.HasTagExact(AURACRONGameplayTags::Environment_Lunar))
        {
            // Aplicar bônus de velocidade lunar
            const float LunarSpeedBonus = GetMovementSpeed() * 0.1f; // +10% velocidade
            ApplyTemporaryAttributeBonus(GetMovementSpeedAttribute(), LunarSpeedBonus, 8.0f);

            UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Efeito Lunar Trilhos aplicado"));
        }

        // Axis Trilhos: transição instantânea entre ambientes
        else if (TargetTags.HasTagExact(AURACRONGameplayTags::Environment_Axis))
        {
            // Aplicar bônus de cooldown para transições
            const float AxisCooldownBonus = 0.1f; // +10% redução de cooldown
            ApplyTemporaryAttributeBonus(GetCooldownReductionAttribute(), AxisCooldownBonus, 3.0f);

            UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Efeito Axis Trilhos aplicado"));
        }
    }
}

void UAURACRONAttributeSet::ApplyTemporaryShield(float ShieldAmount, UAbilitySystemComponent* SourceASC)
{
    // Implementar sistema de escudo temporário
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Criar efeito de escudo temporário
        if (UGameplayEffect* ShieldEffect = NewObject<UGameplayEffect>())
        {
            ShieldEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            ShieldEffect->DurationMagnitude = FScalableFloat(10.0f); // 10 segundos

            // Aplicar o efeito
            FGameplayEffectContextHandle ContextHandle = ASC->MakeEffectContext();
            FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(ShieldEffect->GetClass(), 1.0f, ContextHandle);

            if (SpecHandle.IsValid())
            {
                ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
                UE_LOG(LogAURACRONAttributes, Log, TEXT("Escudo temporário aplicado: %f por 10 segundos"), ShieldAmount);
            }
        }
    }
}

void UAURACRONAttributeSet::ApplyTemporaryAttributeBonus(const FGameplayAttribute& Attribute, float BonusAmount, float Duration)
{
    // Implementar bônus temporário de atributo
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Criar efeito temporário
        if (UGameplayEffect* BonusEffect = NewObject<UGameplayEffect>())
        {
            BonusEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            BonusEffect->DurationMagnitude = FScalableFloat(Duration);

            // Aplicar o efeito
            FGameplayEffectContextHandle ContextHandle = ASC->MakeEffectContext();
            FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(BonusEffect->GetClass(), 1.0f, ContextHandle);

            if (SpecHandle.IsValid())
            {
                ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
                UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Bônus temporário aplicado ao atributo por %f segundos"), Duration);
            }
        }
    }
}

void UAURACRONAttributeSet::StartRegenerationIfNeeded()
{
    // Iniciar regeneração automática se não estiver morto
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        if (!ASC->HasMatchingGameplayTag(AURACRONGameplayTags::State_Dead))
        {
            if (GetHealth() < GetMaxHealth() || GetMana() < GetMaxMana())
            {
                if (UWorld* World = GetWorld())
                {
                    // Iniciar timer de regeneração se não estiver ativo
                    if (!World->GetTimerManager().IsTimerActive(RegenerationTimerHandle))
                    {
                        World->GetTimerManager().SetTimer(
                            RegenerationTimerHandle,
                            this,
                            &UAURACRONAttributeSet::ProcessRegeneration,
                            1.0f, // A cada 1 segundo
                            true  // Repetir
                        );

                        UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Regeneração automática iniciada"));
                    }
                }
            }
        }
    }
}

void UAURACRONAttributeSet::StopRegeneration()
{
    // Parar regeneração automática
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(RegenerationTimerHandle);
        UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Regeneração automática parada"));
    }
}

void UAURACRONAttributeSet::ProcessRegeneration()
{
    // Processar regeneração automática
    if (UAbilitySystemComponent* ASC = GetOwningAbilitySystemComponent())
    {
        // Não regenerar se estiver morto
        if (ASC->HasMatchingGameplayTag(AURACRONGameplayTags::State_Dead))
        {
            StopRegeneration();
            return;
        }

        bool bNeedsRegeneration = false;

        // Regenerar vida
        if (GetHealth() < GetMaxHealth() && GetHealthRegeneration() > 0.0f)
        {
            const float NewHealth = FMath::Min(GetHealth() + GetHealthRegeneration(), GetMaxHealth());
            SetHealth(NewHealth);
            bNeedsRegeneration = true;

            UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Regeneração de vida: %f"), GetHealthRegeneration());
        }

        // Regenerar mana
        if (GetMana() < GetMaxMana() && GetManaRegeneration() > 0.0f)
        {
            const float NewMana = FMath::Min(GetMana() + GetManaRegeneration(), GetMaxMana());
            SetMana(NewMana);
            bNeedsRegeneration = true;

            UE_LOG(LogAURACRONAttributes, VeryVerbose, TEXT("Regeneração de mana: %f"), GetManaRegeneration());
        }

        // Parar regeneração se não for mais necessária
        if (!bNeedsRegeneration)
        {
            StopRegeneration();
        }
    }
}

// Implementações das funções OnRep para os novos meta-atributos

void UAURACRONAttributeSet::OnRep_IncomingDamage(const FGameplayAttributeData& OldIncomingDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, IncomingDamage, OldIncomingDamage);
}

void UAURACRONAttributeSet::OnRep_IncomingHealing(const FGameplayAttributeData& OldIncomingHealing)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, IncomingHealing, OldIncomingHealing);
}

void UAURACRONAttributeSet::OnRep_IncomingShield(const FGameplayAttributeData& OldIncomingShield)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAURACRONAttributeSet, IncomingShield, OldIncomingShield);
}
