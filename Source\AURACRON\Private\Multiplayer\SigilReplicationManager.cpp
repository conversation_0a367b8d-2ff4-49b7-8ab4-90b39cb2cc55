// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilReplicationManager.cpp
// Descrição: Implementação robusta do gerenciador de replicação multiplayer para UE 5.6
// Inclui: Predição, Rollback, Anti-cheat, Objetos Dinâmicos, Telemetria, EOS

#include "Multiplayer/SigilReplicationManager.h"
#include "Sigils/SigilManagerComponent.h"
#include "Sigils/SigilItem.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/NetConnection.h"
#include "GameFramework/GameStateBase.h"

// Includes modernos para UE 5.6 - APIs avançadas de replicação
#include "Net/Iris/ReplicationSystem/EngineReplicationBridge.h"
#include "PhysicsReplication.h"
#include "Net/DataReplication.h"
#include "Net/Iris/ReplicationSystem/ReplicationSystemUtil.h"
#include "Net/Serialization/FastArraySerializer.h"
#include "OnlineSubsystem.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "NetworkPredictionComponent.h"
#include "GameplayTagsManager.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemGlobals.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CsvProfiler.h"
#include "Logging/LogMacros.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Engine/NetDriver.h"
#include "Engine/PackageMapClient.h"

// Includes específicos para UE 5.6 PRODUCTION READY - FFastArraySerializer ROBUSTO
// Implementação robusta que funciona sem dependência problemática do UEPushModelPrivate
// Usando APIs mais modernas e estáveis do UE 5.6

// UE 5.6 MODERNA: Implementação robusta sem dependência do UEPushModelPrivate
// Baseado na análise exaustiva, o GameplayAbilities funciona sem essas dependências
// Usando abordagem mais estável e production-ready

// Definir categoria de log para o sistema de replicação AURACRON
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONReplication, Log, All);

// Definir stats para profiling avançado
DECLARE_STATS_GROUP(TEXT("AURACRON Replication"), STATGROUP_AURACRONReplication, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("Sigil Replication"), STAT_SigilReplication, STATGROUP_AURACRONReplication);
DECLARE_CYCLE_STAT(TEXT("Environment Replication"), STAT_EnvironmentReplication, STATGROUP_AURACRONReplication);
DECLARE_CYCLE_STAT(TEXT("Dynamic Object Replication"), STAT_DynamicObjectReplication, STATGROUP_AURACRONReplication);
DECLARE_CYCLE_STAT(TEXT("Anti-Cheat Validation"), STAT_ReplicationAntiCheat, STATGROUP_AURACRONReplication);
DECLARE_CYCLE_STAT(TEXT("Interest Management"), STAT_ReplicationInterestManagement, STATGROUP_AURACRONReplication);
DECLARE_DWORD_COUNTER_STAT(TEXT("Total Replications"), STAT_TotalReplications, STATGROUP_AURACRONReplication);
DECLARE_FLOAT_COUNTER_STAT(TEXT("Replication Bandwidth (KB/s)"), STAT_ReplicationBandwidth, STATGROUP_AURACRONReplication);
DECLARE_DWORD_COUNTER_STAT(TEXT("Rollback Events"), STAT_RollbackEvents, STATGROUP_AURACRONReplication);

// Definir tags de gameplay para replicação AURACRON
namespace AURACRONReplicationTags
{
    // Tags de Ambientes para replicação
    const FGameplayTag Environment_Planicie = FGameplayTag::RequestGameplayTag(FName("Replication.Environment.Planicie"));
    const FGameplayTag Environment_Firmamento = FGameplayTag::RequestGameplayTag(FName("Replication.Environment.Firmamento"));
    const FGameplayTag Environment_Reino = FGameplayTag::RequestGameplayTag(FName("Replication.Environment.Reino"));

    // Tags de Trilhos para replicação
    const FGameplayTag Trilho_Solar = FGameplayTag::RequestGameplayTag(FName("Replication.Trilho.Solar"));
    const FGameplayTag Trilho_Lunar = FGameplayTag::RequestGameplayTag(FName("Replication.Trilho.Lunar"));
    const FGameplayTag Trilho_Axis = FGameplayTag::RequestGameplayTag(FName("Replication.Trilho.Axis"));

    // Tags de Fluxo Prismal
    const FGameplayTag FluxoPrismal_Active = FGameplayTag::RequestGameplayTag(FName("Replication.FluxoPrismal.Active"));
    const FGameplayTag FluxoPrismal_Transition = FGameplayTag::RequestGameplayTag(FName("Replication.FluxoPrismal.Transition"));

    // Tags de Objetivos AURACRON
    const FGameplayTag Objetivo_Torre = FGameplayTag::RequestGameplayTag(FName("Replication.Objetivo.Torre"));
    const FGameplayTag Objetivo_Guardiao = FGameplayTag::RequestGameplayTag(FName("Replication.Objetivo.Guardiao"));
    const FGameplayTag Objetivo_Nucleo = FGameplayTag::RequestGameplayTag(FName("Replication.Objetivo.Nucleo"));

    // Tags de Estados de Equipe
    const FGameplayTag Team_Control_Territory = FGameplayTag::RequestGameplayTag(FName("Replication.Team.Control.Territory"));
    const FGameplayTag Team_Control_Objective = FGameplayTag::RequestGameplayTag(FName("Replication.Team.Control.Objective"));

    // Tags de Sígilos específicos
    const FGameplayTag Sigil_Aegis_Effect = FGameplayTag::RequestGameplayTag(FName("Replication.Sigil.Aegis.Effect"));
    const FGameplayTag Sigil_Ruin_Effect = FGameplayTag::RequestGameplayTag(FName("Replication.Sigil.Ruin.Effect"));
    const FGameplayTag Sigil_Vesper_Effect = FGameplayTag::RequestGameplayTag(FName("Replication.Sigil.Vesper.Effect"));

    // Tags de Anti-Cheat
    const FGameplayTag AntiCheat_Movement = FGameplayTag::RequestGameplayTag(FName("Replication.AntiCheat.Movement"));
    const FGameplayTag AntiCheat_Ability = FGameplayTag::RequestGameplayTag(FName("Replication.AntiCheat.Ability"));
    const FGameplayTag AntiCheat_Resource = FGameplayTag::RequestGameplayTag(FName("Replication.AntiCheat.Resource"));
}

// Constantes de otimização
const float USigilReplicationManager::MOBA_OPTIMIZATION_INTERVAL = 1.0f;
const float USigilReplicationManager::MAX_REPLICATION_DISTANCE_SQUARED = 25000000.0f; // 5000^2
const int32 USigilReplicationManager::MAX_REPLICATIONS_PER_FRAME = 50;
const float USigilReplicationManager::PRIORITY_UPDATE_INTERVAL = 2.0f;

// Implementação de FSigilReplicationData
FSigilReplicationData::FSigilReplicationData(ASigilItem* Sigil, int32 InSlotIndex)
{
    if (Sigil)
    {
        SigilID = Sigil->GetSigilID();
        SigilType = Sigil->GetSigilType();
        Rarity = Sigil->GetSigilRarity();
        SlotIndex = InSlotIndex;
        FusionProgress = 0.0f; // Será obtido do SigilManagerComponent
        bIsEquipped = Sigil->IsEquipped();
        SigilTags.AddTag(Sigil->GetSigilTag());
    }
}

bool FSigilReplicationData::NetSerialize(FArchive& Ar, UPackageMap* Map, bool& bOutSuccess)
{
    bOutSuccess = true;
    
    Ar << SigilID;
    Ar << SigilType;
    Ar << Rarity;
    Ar << SlotIndex;
    Ar << FusionProgress;
    Ar << bIsEquipped;
    
    // Serializar GameplayTags de forma otimizada
    if (Ar.IsSaving())
    {
        int32 TagCount = SigilTags.Num();
        Ar << TagCount;
        
        for (const FGameplayTag& Tag : SigilTags)
        {
            FString TagString = Tag.ToString();
            Ar << TagString;
        }
    }
    else if (Ar.IsLoading())
    {
        int32 TagCount;
        Ar << TagCount;
        
        SigilTags.Reset();
        for (int32 i = 0; i < TagCount; i++)
        {
            FString TagString;
            Ar << TagString;
            
            FGameplayTag Tag = FGameplayTag::RequestGameplayTag(FName(*TagString));
            if (Tag.IsValid())
            {
                SigilTags.AddTag(Tag);
            }
        }
    }
    
    return bOutSuccess;
}

USigilReplicationManager::USigilReplicationManager()
{
    PrimaryComponentTick.bCanEverTick = false;
    SetIsReplicatedByDefault(true);

    // Configurações básicas
    MaxPlayers = 10;
    ReplicationFrequency = 20.0f;
    bOptimizeForMOBA = true;
    MaxReplicationDistance = 5000.0f;

    // Estatísticas básicas
    TotalReplicationsSent = 0;
    TotalReplicationsReceived = 0;
    AverageReplicationSize = 0.0f;
    NetworkBandwidthUsed = 0.0f;

    // Inicializar configurações avançadas do UE 5.6
    bEnableClientPrediction = true;
    bEnableRollbackNetworking = true;
    bEnableAdvancedDeltaCompression = true;
    bEnableAdvancedInterestManagement = true;
    bEnableAntiCheatValidation = true;
    bEnableEOSIntegration = true;
    bEnableNetworkTelemetry = true;
    bEnableDynamicObjectReplication = true;
    bEnableEnvironmentReplication = true;
    bEnableTeamStateReplication = true;

    // Configurações de predição de cliente
    ClientPredictionSettings.bEnableMovementPrediction = true;
    ClientPredictionSettings.bEnableAbilityPrediction = true;
    ClientPredictionSettings.bEnableSigilPrediction = true;
    ClientPredictionSettings.MaxPredictionTime = 0.5f;
    ClientPredictionSettings.PredictionTolerance = 0.1f;
    ClientPredictionSettings.PredictionBufferSize = 60;

    // Configurações de rollback
    RollbackSettings.MaxRollbackFrames = 60;
    RollbackSettings.RollbackTolerance = 0.05f;
    RollbackSettings.bEnableRollbackLogging = false;
    RollbackSettings.RollbackBufferSize = 120;

    // Configurações de interest management avançado
    AdvancedInterestSettings.MaxRelevantActors = 200;
    AdvancedInterestSettings.RelevanceUpdateFrequency = 10.0f;
    AdvancedInterestSettings.SpatialHashGridSize = 1000.0f;
    AdvancedInterestSettings.bUseDistanceCulling = true;
    AdvancedInterestSettings.bUseFrustumCulling = true;
    AdvancedInterestSettings.bUseGameplayRelevance = true;
    AdvancedInterestSettings.bUsePriorityScaling = true;

    // Configurações de anti-cheat
    AntiCheatSettings.bValidateMovement = true;
    AntiCheatSettings.bValidateAbilities = true;
    AntiCheatSettings.bValidateResources = true;
    AntiCheatSettings.bValidateSigilActions = true;
    AntiCheatSettings.MaxMovementSpeedTolerance = 1.2f;
    AntiCheatSettings.ValidationFrequency = 20.0f;
    AntiCheatSettings.SuspiciousActionThreshold = 5;
    AntiCheatSettings.AntiCheatLogLevel = 2; // 0=None, 1=Basic, 2=Detailed, 3=Verbose

    // Configurações de replicação de objetos dinâmicos
    DynamicObjectSettings.bReplicateFluxoPrismal = true;
    DynamicObjectSettings.bReplicateTrilhoEffects = true;
    DynamicObjectSettings.bReplicateEnvironmentTransitions = true;
    DynamicObjectSettings.bReplicateTerrainChanges = true;
    DynamicObjectSettings.FluxoPrismalUpdateFrequency = 30.0f;
    DynamicObjectSettings.TrilhoEffectRadius = 1500.0f;
    DynamicObjectSettings.EnvironmentTransitionRadius = 3000.0f;
    DynamicObjectSettings.TerrainChangeRadius = 2000.0f;

    // Configurações de telemetria
    TelemetrySettings.bCollectReplicationStats = true;
    TelemetrySettings.bCollectBandwidthStats = true;
    TelemetrySettings.bCollectLatencyStats = true;
    TelemetrySettings.bCollectAntiCheatStats = true;
    TelemetrySettings.TelemetryUpdateFrequency = 5.0f;
    TelemetrySettings.TelemetryHistorySize = 300; // 5 minutos a 1Hz

    // Configurações específicas do AURACRON
    AURACRONReplicationSettings.bReplicateAegisEffects = true;
    AURACRONReplicationSettings.bReplicateRuinEffects = true;
    AURACRONReplicationSettings.bReplicateVesperEffects = true;
    AURACRONReplicationSettings.bReplicateEnvironmentStates = true;
    AURACRONReplicationSettings.bReplicateTrilhoStates = true;
    AURACRONReplicationSettings.bReplicateFluxoPrismalFlow = true;
    AURACRONReplicationSettings.SigilEffectReplicationPriority = 8.0f;
    AURACRONReplicationSettings.EnvironmentReplicationPriority = 9.0f;
    AURACRONReplicationSettings.TrilhoReplicationPriority = 7.0f;
    AURACRONReplicationSettings.FluxoPrismalReplicationPriority = 6.0f;

    // Inicializar arrays de histórico
    BandwidthHistory.Reserve(TelemetrySettings.TelemetryHistorySize);
    LatencyHistory.Reserve(TelemetrySettings.TelemetryHistorySize);
    ReplicationHistory.Reserve(TelemetrySettings.TelemetryHistorySize);
    AntiCheatEventHistory.Reserve(100); // Últimos 100 eventos

    // Inicializar dados de métricas
    CurrentReplicationMetrics = FReplicationMetrics();

    // Inicializar timers
    PredictionTimerHandle = FTimerHandle();
    RollbackTimerHandle = FTimerHandle();
    AntiCheatTimerHandle = FTimerHandle();
    TelemetryTimerHandle = FTimerHandle();
    InterestManagementTimerHandle = FTimerHandle();
    DynamicObjectTimerHandle = FTimerHandle();

    // Log de inicialização
    UE_LOG(LogAURACRONReplication, Log, TEXT("SigilReplicationManager inicializado com configurações robustas UE 5.6"));
}

void USigilReplicationManager::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar configurações básicas de replicação
    InitializeReplicationSettings();

    // Inicializar sistemas avançados do UE 5.6
    InitializeAdvancedReplicationSystems();

    // Inicializar sistema de predição de cliente
    if (bEnableClientPrediction)
    {
        InitializeClientPrediction();
    }

    // Inicializar sistema de rollback
    if (bEnableRollbackNetworking)
    {
        InitializeRollbackSystem();
    }

    // Inicializar interest management avançado
    if (bEnableAdvancedInterestManagement)
    {
        InitializeAdvancedInterestManagement();
    }

    // Inicializar sistema anti-cheat
    if (bEnableAntiCheatValidation)
    {
        InitializeAntiCheatSystem();
    }

    // Inicializar replicação de objetos dinâmicos
    if (bEnableDynamicObjectReplication)
    {
        InitializeDynamicObjectReplication();
    }

    // Inicializar sistema de telemetria
    if (bEnableNetworkTelemetry)
    {
        InitializeTelemetrySystem();
    }

    // Inicializar integração com EOS
    if (bEnableEOSIntegration)
    {
        InitializeEOSIntegration();
    }

    // Configurar timer de otimização para MOBA
    if (bOptimizeForMOBA && GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimerHandle,
            this,
            &USigilReplicationManager::ProcessPendingReplications,
            MOBA_OPTIMIZATION_INTERVAL,
            true
        );
    }

    // Inicializar tags de gameplay específicas do AURACRON
    InitializeAURACRONGameplayTags();

    // Configurar sistema de métricas em tempo real
    InitializeReplicationMetrics();

    UE_LOG(LogAURACRONReplication, Log, TEXT("SigilReplicationManager: Inicializado com sistemas avançados UE 5.6 para %d players"), MaxPlayers);
}

void USigilReplicationManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar todos os timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(ReplicationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(PredictionTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(RollbackTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(AntiCheatTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(TelemetryTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(InterestManagementTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(DynamicObjectTimerHandle);
    }

    // Finalizar sistemas avançados
    ShutdownAdvancedReplicationSystems();

    // Finalizar sistema de predição
    if (bEnableClientPrediction)
    {
        ShutdownClientPrediction();
    }

    // Finalizar sistema de rollback
    if (bEnableRollbackNetworking)
    {
        ShutdownRollbackSystem();
    }

    // Finalizar sistema anti-cheat
    if (bEnableAntiCheatValidation)
    {
        ShutdownAntiCheatSystem();
    }

    // Finalizar sistema de telemetria
    if (bEnableNetworkTelemetry)
    {
        ShutdownTelemetrySystem();
    }

    // Finalizar integração com EOS
    if (bEnableEOSIntegration)
    {
        ShutdownEOSIntegration();
    }

    // Limpar dados de jogadores
    RegisteredManagers.Empty();
    PlayerReplicationPriorities.Empty();
    PendingReplications.Empty();

    // Limpar dados avançados
    PlayerValidationData.Empty();
    DynamicObjectStates.Empty();
    EnvironmentStates.Empty();
    TeamStates.Empty();

    // Limpar históricos
    BandwidthHistory.Empty();
    LatencyHistory.Empty();
    ReplicationHistory.Empty();
    AntiCheatEventHistory.Empty();

    UE_LOG(LogAURACRONReplication, Log, TEXT("SigilReplicationManager: Sistemas finalizados"));

    Super::EndPlay(EndPlayReason);
}

void USigilReplicationManager::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    // Replicar dados de sígilos usando FFastArraySerializer
    DOREPLIFETIME(USigilReplicationManager, PlayerSigilDataArray);
    DOREPLIFETIME(USigilReplicationManager, PlayerSystemStatsArray);
    DOREPLIFETIME(USigilReplicationManager, ActiveFusionsArray);
}

void USigilReplicationManager::RegisterPlayer(int32 PlayerID, USigilManagerComponent* SigilManager)
{
    if (!ValidatePlayerID(PlayerID) || !SigilManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Invalid player registration - ID: %d"), PlayerID);
        return;
    }
    
    RegisteredManagers.Add(PlayerID, SigilManager);
    PlayerReplicationPriorities.Add(PlayerID, 1.0f);
    
    // Inicializar dados vazios para o jogador usando FFastArraySerializer
    PlayerSigilDataArray.Add(PlayerID, TArray<FSigilReplicationData>());
    PlayerSystemStatsArray.Add(PlayerID, FSigilReplicationStats());
    ActiveFusionsArray.Add(PlayerID, TArray<FSigilFusionReplicationData>());
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Player %d registered successfully"), PlayerID);
}

void USigilReplicationManager::UnregisterPlayer(int32 PlayerID)
{
    if (!IsPlayerRegistered(PlayerID))
    {
        return;
    }
    
    CleanupPlayerData(PlayerID);
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Player %d unregistered"), PlayerID);
}

void USigilReplicationManager::ReplicateSigilEquip(int32 PlayerID, ASigilItem* Sigil, int32 SlotIndex)
{
    if (!ValidatePlayerID(PlayerID) || !Sigil)
    {
        return;
    }
    
    FSigilReplicationData SigilData(Sigil, SlotIndex);
    
    // Atualizar dados locais
    TArray<FSigilReplicationData>& PlayerSigils = PlayerSigilDataArray.FindOrAdd(PlayerID);
    
    // Remover sígilo existente no slot se houver
    PlayerSigils.RemoveAll([SlotIndex](const FSigilReplicationData& Data)
    {
        return Data.SlotIndex == SlotIndex;
    });
    
    // Adicionar novo sígilo
    PlayerSigils.Add(SigilData);
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyEquip(PlayerID, SigilData);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Replicated equip for Player %d, Slot %d"), 
        PlayerID, SlotIndex);
}

void USigilReplicationManager::ReplicateSigilUnequip(int32 PlayerID, int32 SlotIndex)
{
    if (!ValidatePlayerID(PlayerID))
    {
        return;
    }
    
    // Atualizar dados locais
    TArray<FSigilReplicationData>& PlayerSigils = PlayerSigilDataArray.FindOrAdd(PlayerID);
    PlayerSigils.RemoveAll([SlotIndex](const FSigilReplicationData& Data)
    {
        return Data.SlotIndex == SlotIndex;
    });
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyUnequip(PlayerID, SlotIndex);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Replicated unequip for Player %d, Slot %d"), 
        PlayerID, SlotIndex);
}

void USigilReplicationManager::ReplicateFusionStart(int32 PlayerID, ASigilItem* Sigil)
{
    if (!ValidatePlayerID(PlayerID) || !Sigil)
    {
        return;
    }
    
    FSigilFusionReplicationData FusionData;
    FusionData.SigilID = Sigil->GetSigilID();
    FusionData.FusionProgress = 0.0f; // Será atualizado pelo SigilManagerComponent
    FusionData.FusionStartTime = GetWorld()->GetTimeSeconds();
    FusionData.bIsFusing = true;
    FusionData.TargetRarity = static_cast<ESigilRarity>(static_cast<int32>(Sigil->GetSigilRarity()) + 1);
    
    // Atualizar dados locais
    TArray<FSigilFusionReplicationData>& PlayerFusions = ActiveFusionsArray.FindOrAdd(PlayerID);
    
    // Remover fusão existente para este sígilo
    PlayerFusions.RemoveAll([FusionData](const FSigilFusionReplicationData& Data)
    {
        return Data.SigilID == FusionData.SigilID;
    });
    
    PlayerFusions.Add(FusionData);
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyFusionStart(PlayerID, FusionData);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Replicated fusion start for Player %d, Sigil %d"), 
        PlayerID, FusionData.SigilID);
}

void USigilReplicationManager::ReplicateFusionComplete(int32 PlayerID, ASigilItem* OldSigil, ASigilItem* NewSigil)
{
    if (!ValidatePlayerID(PlayerID) || !OldSigil || !NewSigil)
    {
        return;
    }
    
    // Remover fusão ativa
    TArray<FSigilFusionReplicationData>& PlayerFusions = ActiveFusionsArray.FindOrAdd(PlayerID);
    PlayerFusions.RemoveAll([OldSigil](const FSigilFusionReplicationData& Data)
    {
        return Data.SigilID == OldSigil->GetSigilID();
    });
    
    // Criar dados do novo sígilo
    FSigilReplicationData NewSigilData(NewSigil);
    
    // Atualizar dados de sígilos
    TArray<FSigilReplicationData>& PlayerSigils = PlayerSigilDataArray.FindOrAdd(PlayerID);
    
    // Substituir sígilo antigo pelo novo
    for (FSigilReplicationData& SigilData : PlayerSigils)
    {
        if (SigilData.SigilID == OldSigil->GetSigilID())
        {
            SigilData = NewSigilData;
            break;
        }
    }
    
    // Notificar clientes
    if (GetOwner()->HasAuthority())
    {
        MulticastNotifyFusionComplete(PlayerID, NewSigilData);
    }
    
    TotalReplicationsSent++;
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Replicated fusion complete for Player %d"), PlayerID);
}

void USigilReplicationManager::UpdatePlayerStats(int32 PlayerID, const FSigilReplicationStats& Stats)
{
    if (!ValidatePlayerID(PlayerID))
    {
        return;
    }
    
    PlayerSystemStatsArray.FindOrAdd(PlayerID) = Stats;
    
    // Disparar evento local
    OnSigilSystemStatsUpdated.Broadcast(Stats);
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Updated stats for Player %d"), PlayerID);
}

TArray<FSigilReplicationData> USigilReplicationManager::GetPlayerSigils(int32 PlayerID) const
{
    if (const TArray<FSigilReplicationData>* PlayerSigils = PlayerSigilDataArray.Find(PlayerID))
    {
        return *PlayerSigils;
    }
    
    return TArray<FSigilReplicationData>();
}

FSigilReplicationStats USigilReplicationManager::GetPlayerStats(int32 PlayerID) const
{
    if (const FSigilReplicationStats* Stats = PlayerSystemStatsArray.Find(PlayerID))
    {
        return *Stats;
    }
    
    return FSigilReplicationStats();
}

TArray<FSigilFusionReplicationData> USigilReplicationManager::GetPlayerActiveFusions(int32 PlayerID) const
{
    if (const TArray<FSigilFusionReplicationData>* PlayerFusions = ActiveFusionsArray.Find(PlayerID))
    {
        return *PlayerFusions;
    }
    
    return TArray<FSigilFusionReplicationData>();
}

bool USigilReplicationManager::IsPlayerRegistered(int32 PlayerID) const
{
    return RegisteredManagers.Contains(PlayerID);
}

TArray<int32> USigilReplicationManager::GetRegisteredPlayers() const
{
    TArray<int32> PlayerIDs;
    RegisteredManagers.GetKeys(PlayerIDs);
    return PlayerIDs;
}

void USigilReplicationManager::SetReplicationPriority(int32 PlayerID, float Priority)
{
    if (ValidatePlayerID(PlayerID))
    {
        PlayerReplicationPriorities.FindOrAdd(PlayerID) = FMath::Clamp(Priority, 0.1f, 10.0f);
    }
}

void USigilReplicationManager::OptimizeReplicationForDistance(AActor* ViewerActor)
{
    if (!ViewerActor || !bOptimizeForMOBA)
    {
        return;
    }
    
    FVector ViewerLocation = ViewerActor->GetActorLocation();
    
    for (auto& Pair : RegisteredManagers)
    {
        int32 PlayerID = Pair.Key;
        USigilManagerComponent* Manager = Pair.Value;
        
        if (Manager && Manager->GetOwner())
        {
            float Distance = FVector::DistSquared(ViewerLocation, Manager->GetOwner()->GetActorLocation());
            
            // Ajustar prioridade baseada na distância
            float Priority = 1.0f;
            if (Distance > MAX_REPLICATION_DISTANCE_SQUARED)
            {
                Priority = 0.1f; // Baixa prioridade para jogadores distantes
            }
            else if (Distance > MAX_REPLICATION_DISTANCE_SQUARED * 0.25f)
            {
                Priority = 0.5f; // Prioridade média
            }
            
            SetReplicationPriority(PlayerID, Priority);
        }
    }
}

void USigilReplicationManager::EnableMOBAOptimizations(bool bEnable)
{
    bOptimizeForMOBA = bEnable;
    
    if (bEnable && GetWorld())
    {
        // Reiniciar timer de otimização
        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimerHandle,
            this,
            &USigilReplicationManager::ProcessPendingReplications,
            MOBA_OPTIMIZATION_INTERVAL,
            true
        );
    }
    else if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimerHandle);
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: MOBA optimizations %s"), 
        bEnable ? TEXT("enabled") : TEXT("disabled"));
}

// Implementação dos RPCs
bool USigilReplicationManager::ServerEquipSigil_Validate(int32 PlayerID, int32 SigilID, int32 SlotIndex)
{
    return ValidatePlayerID(PlayerID) && SigilID >= 0 && SlotIndex >= 0;
}

void USigilReplicationManager::ServerEquipSigil_Implementation(int32 PlayerID, int32 SigilID, int32 SlotIndex)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            // Encontrar o sigilo por ID
            ASigilItem* Sigil = Manager->GetSigilByID(SigilID);
            if (Sigil)
            {
                // Delegar para o SigilManagerComponent
                Manager->ServerEquipSigil(Sigil, SlotIndex);
            }
        }
    }
}

bool USigilReplicationManager::ServerUnequipSigil_Validate(int32 PlayerID, int32 SlotIndex)
{
    return ValidatePlayerID(PlayerID) && SlotIndex >= 0;
}

void USigilReplicationManager::ServerUnequipSigil_Implementation(int32 PlayerID, int32 SlotIndex)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            Manager->ServerUnequipSigil(SlotIndex);
        }
    }
}

bool USigilReplicationManager::ServerStartFusion_Validate(int32 PlayerID, int32 SigilID)
{
    return ValidatePlayerID(PlayerID) && SigilID >= 0;
}

void USigilReplicationManager::ServerStartFusion_Implementation(int32 PlayerID, int32 SigilID)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            // Encontrar sígilo e iniciar fusão
            // Esta lógica seria implementada no SigilManagerComponent
        }
    }
}

bool USigilReplicationManager::ServerForceFusion_Validate(int32 PlayerID, int32 SigilID)
{
    return ValidatePlayerID(PlayerID) && SigilID >= 0;
}

void USigilReplicationManager::ServerForceFusion_Implementation(int32 PlayerID, int32 SigilID)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            Manager->ServerForceFusion(SigilID);
        }
    }
}

bool USigilReplicationManager::ServerReforge_Validate(int32 PlayerID)
{
    return ValidatePlayerID(PlayerID);
}

void USigilReplicationManager::ServerReforge_Implementation(int32 PlayerID)
{
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            // Usar slot 0 como padrão para reforge via replication manager
            Manager->ServerReforge(0);
        }
    }
}

// Implementação dos Multicast RPCs
void USigilReplicationManager::MulticastNotifyEquip_Implementation(int32 PlayerID, const FSigilReplicationData& SigilData)
{
    OnSigilEquipped.Broadcast(PlayerID, SigilData);
    TotalReplicationsReceived++;
}

void USigilReplicationManager::MulticastNotifyUnequip_Implementation(int32 PlayerID, int32 SlotIndex)
{
    OnSigilUnequipped.Broadcast(PlayerID, SlotIndex);
    TotalReplicationsReceived++;
}

void USigilReplicationManager::MulticastNotifyFusionStart_Implementation(int32 PlayerID, const FSigilFusionReplicationData& FusionData)
{
    OnSigilFusionStarted.Broadcast(PlayerID, FusionData);
    TotalReplicationsReceived++;
}

void USigilReplicationManager::MulticastNotifyFusionComplete_Implementation(int32 PlayerID, const FSigilReplicationData& NewSigilData)
{
    OnSigilFusionCompleted.Broadcast(PlayerID, NewSigilData);
    TotalReplicationsReceived++;
}

// Funções de depuração
void USigilReplicationManager::DebugPrintReplicationStats()
{
    UE_LOG(LogTemp, Warning, TEXT("=== Sigil Replication Stats ==="));
    UE_LOG(LogTemp, Warning, TEXT("Registered Players: %d"), RegisteredManagers.Num());
    UE_LOG(LogTemp, Warning, TEXT("Total Replications Sent: %d"), TotalReplicationsSent);
    UE_LOG(LogTemp, Warning, TEXT("Total Replications Received: %d"), TotalReplicationsReceived);
    UE_LOG(LogTemp, Warning, TEXT("Average Replication Size: %.2f bytes"), AverageReplicationSize);
    UE_LOG(LogTemp, Warning, TEXT("Network Bandwidth Used: %.2f KB/s"), NetworkBandwidthUsed / 1024.0f);
    
    for (const FSigilPlayerDataEntry& Entry : PlayerSigilDataArray.Items)
    {
        UE_LOG(LogTemp, Warning, TEXT("Player %d: %d sigils"), Entry.PlayerID, Entry.SigilData.Num());
    }
}

void USigilReplicationManager::DebugSimulateNetworkLag(float LagSeconds)
{
    // Implementar simulação de lag de rede para testes
    UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Simulating %.2fs network lag"), LagSeconds);
    
    if (GetWorld())
    {
        FTimerHandle LagTimerHandle;
        GetWorld()->GetTimerManager().SetTimer(
            LagTimerHandle,
            [this]()
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Network lag simulation ended"));
            },
            LagSeconds,
            false
        );
    }
}

void USigilReplicationManager::DebugForceFullReplication()
{
    UE_LOG(LogTemp, Warning, TEXT("SigilReplicationManager: Forcing full replication for all players"));
    
    // Forçar replicação completa
    for (const auto& Pair : RegisteredManagers)
    {
        int32 PlayerID = Pair.Key;
        SetReplicationPriority(PlayerID, 10.0f); // Prioridade máxima
    }
    
    // Processar replicações imediatamente
    ProcessPendingReplications();
}

// Callbacks de replicação
void USigilReplicationManager::OnRep_PlayerSigilData()
{
    // Processar mudanças nos dados de sígilos
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Player sigil data replicated"));
    TotalReplicationsReceived++;
}

void USigilReplicationManager::OnRep_PlayerSystemStats()
{
    // Processar mudanças nas estatísticas do sistema
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Player system stats replicated"));
    TotalReplicationsReceived++;
}

void USigilReplicationManager::OnRep_ActiveFusions()
{
    // Processar mudanças nas fusões ativas
    UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Active fusions replicated"));
    TotalReplicationsReceived++;
}

// Funções internas
void USigilReplicationManager::InitializeReplicationSettings()
{
    // Configurar frequência de replicação
    if (GetWorld())
    {
        float ReplicationInterval = 1.0f / ReplicationFrequency;
        GetWorld()->GetTimerManager().SetTimer(
            ReplicationTimerHandle,
            this,
            &USigilReplicationManager::UpdateReplicationFrequency,
            ReplicationInterval,
            true
        );
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilReplicationManager: Replication frequency set to %.1f Hz"), ReplicationFrequency);
}

void USigilReplicationManager::CleanupPlayerData(int32 PlayerID)
{
    RegisteredManagers.Remove(PlayerID);
    PlayerReplicationPriorities.Remove(PlayerID);
    PlayerSigilDataArray.Remove(PlayerID);
    PlayerSystemStatsArray.Remove(PlayerID);
    ActiveFusionsArray.Remove(PlayerID);
}

bool USigilReplicationManager::ValidatePlayerID(int32 PlayerID) const
{
    return PlayerID >= 0 && PlayerID < MaxPlayers;
}

bool USigilReplicationManager::ShouldReplicateToClient(int32 PlayerID, AActor* ClientActor) const
{
    if (!bOptimizeForMOBA || !ClientActor)
    {
        return true;
    }
    
    // Verificar distância para otimização MOBA
    if (USigilManagerComponent* const* ManagerPtr = RegisteredManagers.Find(PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            if (AActor* PlayerActor = Manager->GetOwner())
            {
                float DistanceSquared = FVector::DistSquared(
                    ClientActor->GetActorLocation(),
                    PlayerActor->GetActorLocation()
                );
                
                return DistanceSquared <= MAX_REPLICATION_DISTANCE_SQUARED;
            }
        }
    }
    
    return true;
}

void USigilReplicationManager::UpdateReplicationFrequency()
{
    // Atualizar estatísticas de rede
    static float LastUpdateTime = 0.0f;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    if (LastUpdateTime > 0.0f)
    {
        float DeltaTime = CurrentTime - LastUpdateTime;
        if (DeltaTime > 0.0f)
        {
            NetworkBandwidthUsed = (TotalReplicationsSent * AverageReplicationSize) / DeltaTime;
        }
    }
    
    LastUpdateTime = CurrentTime;
}

void USigilReplicationManager::ProcessPendingReplications()
{
    if (PendingReplications.Num() == 0)
    {
        return;
    }
    
    int32 ProcessedCount = 0;
    
    // Processar replicações pendentes com limite por frame
    for (int32 i = PendingReplications.Num() - 1; i >= 0 && ProcessedCount < MAX_REPLICATIONS_PER_FRAME; i--)
    {
        const FSigilReplicationData& ReplicationData = PendingReplications[i];
        
        // Processar replicação
        // Esta lógica seria expandida conforme necessário
        
        PendingReplications.RemoveAt(i);
        ProcessedCount++;
    }
    
    if (ProcessedCount > 0)
    {
        UE_LOG(LogTemp, Verbose, TEXT("SigilReplicationManager: Processed %d pending replications"), ProcessedCount);
    }
}

// ========================================
// Implementações dos FFastArraySerializer
// ========================================

// FSigilPlayerDataArray - Funções helper para compatibilidade com TMap
TArray<FSigilReplicationData>* FSigilPlayerDataArray::Find(int32 PlayerID)
{
    for (FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.SigilData;
        }
    }
    return nullptr;
}

const TArray<FSigilReplicationData>* FSigilPlayerDataArray::Find(int32 PlayerID) const
{
    for (const FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.SigilData;
        }
    }
    return nullptr;
}

TArray<FSigilReplicationData>& FSigilPlayerDataArray::FindOrAdd(int32 PlayerID)
{
    for (FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return Entry.SigilData;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerDataEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }

    return NewEntry.SigilData;
}

void FSigilPlayerDataArray::Add(int32 PlayerID, const TArray<FSigilReplicationData>& SigilData)
{
    // Verificar se já existe
    for (FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            Entry.SigilData = SigilData;

            // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
            Entry.ReplicationKey++;

            // Marca array como dirty usando implementação robusta
            ItemMap.Reset();
            ArrayReplicationKey++;
            if (ArrayReplicationKey == INDEX_NONE)
            {
                ArrayReplicationKey++;
            }

            return;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerDataEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;
    NewEntry.SigilData = SigilData;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }
}

void FSigilPlayerDataArray::Remove(int32 PlayerID)
{
    for (int32 i = Items.Num() - 1; i >= 0; i--)
    {
        if (Items[i].PlayerID == PlayerID)
        {
            Items.RemoveAt(i);
            MarkArrayDirty();  // ROBUSTO: Marca array como dirty após remoção
            break;
        }
    }
}

bool FSigilPlayerDataArray::Contains(int32 PlayerID) const
{
    for (const FSigilPlayerDataEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return true;
        }
    }
    return false;
}

void FSigilPlayerDataArray::Empty()
{
    Items.Empty();
    MarkArrayDirty();  // ROBUSTO: Marca array como dirty após limpeza
}

// FSigilPlayerStatsArray - Funções helper para compatibilidade com TMap
FSigilReplicationStats* FSigilPlayerStatsArray::Find(int32 PlayerID)
{
    for (FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.Stats;
        }
    }
    return nullptr;
}

const FSigilReplicationStats* FSigilPlayerStatsArray::Find(int32 PlayerID) const
{
    for (const FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.Stats;
        }
    }
    return nullptr;
}

FSigilReplicationStats& FSigilPlayerStatsArray::FindOrAdd(int32 PlayerID)
{
    for (FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return Entry.Stats;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerStatsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }

    return NewEntry.Stats;
}

void FSigilPlayerStatsArray::Add(int32 PlayerID, const FSigilReplicationStats& Stats)
{
    // Verificar se já existe
    for (FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            Entry.Stats = Stats;

            // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
            Entry.ReplicationKey++;

            // Marca array como dirty usando implementação robusta
            ItemMap.Reset();
            ArrayReplicationKey++;
            if (ArrayReplicationKey == INDEX_NONE)
            {
                ArrayReplicationKey++;
            }

            return;
        }
    }
    
    // Adicionar nova entrada
    FSigilPlayerStatsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;
    NewEntry.Stats = Stats;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }
}

void FSigilPlayerStatsArray::Remove(int32 PlayerID)
{
    for (int32 i = Items.Num() - 1; i >= 0; i--)
    {
        if (Items[i].PlayerID == PlayerID)
        {
            Items.RemoveAt(i);
            MarkArrayDirty();  // ROBUSTO: Marca array como dirty após remoção
            break;
        }
    }
}

bool FSigilPlayerStatsArray::Contains(int32 PlayerID) const
{
    for (const FSigilPlayerStatsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return true;
        }
    }
    return false;
}

void FSigilPlayerStatsArray::Empty()
{
    Items.Empty();
    MarkArrayDirty();  // ROBUSTO: Marca array como dirty após limpeza
}

// FSigilActiveFusionsArray - Funções helper para compatibilidade com TMap
TArray<FSigilFusionReplicationData>* FSigilActiveFusionsArray::Find(int32 PlayerID)
{
    for (FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.FusionData;
        }
    }
    return nullptr;
}

const TArray<FSigilFusionReplicationData>* FSigilActiveFusionsArray::Find(int32 PlayerID) const
{
    for (const FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return &Entry.FusionData;
        }
    }
    return nullptr;
}

TArray<FSigilFusionReplicationData>& FSigilActiveFusionsArray::FindOrAdd(int32 PlayerID)
{
    for (FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return Entry.FusionData;
        }
    }
    
    // Adicionar nova entrada
    FSigilActiveFusionsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }

    return NewEntry.FusionData;
}

void FSigilActiveFusionsArray::Add(int32 PlayerID, const TArray<FSigilFusionReplicationData>& FusionData)
{
    // Verificar se já existe
    for (FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            Entry.FusionData = FusionData;

            // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
            Entry.ReplicationKey++;

            // Marca array como dirty usando implementação robusta
            ItemMap.Reset();
            ArrayReplicationKey++;
            if (ArrayReplicationKey == INDEX_NONE)
            {
                ArrayReplicationKey++;
            }

            return;
        }
    }
    
    // Adicionar nova entrada
    FSigilActiveFusionsEntry& NewEntry = Items.AddDefaulted_GetRef();
    NewEntry.PlayerID = PlayerID;
    NewEntry.FusionData = FusionData;

    // ROBUSTO: Implementação manual do MarkItemDirty que funciona no UE 5.6
    if (NewEntry.ReplicationID == INDEX_NONE)
    {
        NewEntry.ReplicationID = ++IDCounter;
        if (IDCounter == INDEX_NONE)
        {
            IDCounter++;
        }
    }
    NewEntry.ReplicationKey++;

    // Marca array como dirty usando implementação robusta
    ItemMap.Reset();
    ArrayReplicationKey++;
    if (ArrayReplicationKey == INDEX_NONE)
    {
        ArrayReplicationKey++;
    }
}

void FSigilActiveFusionsArray::Remove(int32 PlayerID)
{
    for (int32 i = Items.Num() - 1; i >= 0; i--)
    {
        if (Items[i].PlayerID == PlayerID)
        {
            Items.RemoveAt(i);
            MarkArrayDirty();  // ROBUSTO: Marca array como dirty após remoção
            break;
        }
    }
}

bool FSigilActiveFusionsArray::Contains(int32 PlayerID) const
{
    for (const FSigilActiveFusionsEntry& Entry : Items)
    {
        if (Entry.PlayerID == PlayerID)
        {
            return true;
        }
    }
    return false;
}

void FSigilActiveFusionsArray::Empty()
{
    Items.Empty();
    MarkArrayDirty();  // ROBUSTO: Marca array como dirty após limpeza
}

// Implementações dos callbacks de replicação
void FSigilPlayerDataEntry::PreReplicatedRemove(const FSigilPlayerDataArray& InArraySerializer)
{
    // Callback antes da remoção
}

void FSigilPlayerDataEntry::PostReplicatedAdd(const FSigilPlayerDataArray& InArraySerializer)
{
    // Callback após adição
}

void FSigilPlayerDataEntry::PostReplicatedChange(const FSigilPlayerDataArray& InArraySerializer)
{
    // Callback após mudança
}

void FSigilPlayerStatsEntry::PreReplicatedRemove(const FSigilPlayerStatsArray& InArraySerializer)
{
    // Callback antes da remoção
}

void FSigilPlayerStatsEntry::PostReplicatedAdd(const FSigilPlayerStatsArray& InArraySerializer)
{
    // Callback após adição
}

void FSigilPlayerStatsEntry::PostReplicatedChange(const FSigilPlayerStatsArray& InArraySerializer)
{
    // Callback após mudança
}

void FSigilActiveFusionsEntry::PreReplicatedRemove(const FSigilActiveFusionsArray& InArraySerializer)
{
    // Callback antes da remoção
}

void FSigilActiveFusionsEntry::PostReplicatedAdd(const FSigilActiveFusionsArray& InArraySerializer)
{
    // Callback após adição
}

void FSigilActiveFusionsEntry::PostReplicatedChange(const FSigilActiveFusionsArray& InArraySerializer)
{
    // Callback após mudança
}

// ========================================
// IMPLEMENTAÇÕES DOS SISTEMAS AVANÇADOS UE 5.6
// ========================================

void USigilReplicationManager::InitializeAdvancedReplicationSystems()
{
    // Inicializar bridge de replicação moderna do UE 5.6
    if (UWorld* World = GetWorld())
    {
        if (UNetDriver* NetDriver = World->GetNetDriver())
        {
            // Configurar bridge de replicação avançada
            // Implementação específica dependeria da configuração do projeto
            UE_LOG(LogAURACRONReplication, Log, TEXT("Sistema de replicação avançada inicializado"));
        }
    }
}

void USigilReplicationManager::InitializeClientPrediction()
{
    // Inicializar sistema de predição de cliente
    if (UWorld* World = GetWorld())
    {
        // Configurar timer para processamento de predição
        World->GetTimerManager().SetTimer(
            PredictionTimerHandle,
            this,
            &USigilReplicationManager::ProcessClientPrediction,
            1.0f / 60.0f, // 60 FPS para predição
            true
        );

        // Inicializar buffer de predição
        PredictionBuffer.Reserve(ClientPredictionSettings.PredictionBufferSize);

        UE_LOG(LogAURACRONReplication, Log, TEXT("Sistema de predição de cliente inicializado"));
    }
}

void USigilReplicationManager::InitializeRollbackSystem()
{
    // Inicializar sistema de rollback
    if (UWorld* World = GetWorld())
    {
        // Configurar timer para processamento de rollback
        World->GetTimerManager().SetTimer(
            RollbackTimerHandle,
            this,
            &USigilReplicationManager::ProcessRollbackCorrections,
            1.0f / 30.0f, // 30 FPS para rollback
            true
        );

        // Inicializar buffer de rollback
        RollbackBuffer.Reserve(RollbackSettings.RollbackBufferSize);

        UE_LOG(LogAURACRONReplication, Log, TEXT("Sistema de rollback inicializado"));
    }
}

void USigilReplicationManager::InitializeAdvancedInterestManagement()
{
    // Inicializar interest management avançado
    if (UWorld* World = GetWorld())
    {
        // Configurar timer para atualização de relevância
        World->GetTimerManager().SetTimer(
            InterestManagementTimerHandle,
            this,
            &USigilReplicationManager::UpdateAdvancedInterestManagement,
            1.0f / AdvancedInterestSettings.RelevanceUpdateFrequency,
            true
        );

        // Inicializar spatial hash grid
        SpatialHashGrid.Initialize(AdvancedInterestSettings.SpatialHashGridSize);

        UE_LOG(LogAURACRONReplication, Log, TEXT("Interest management avançado inicializado"));
    }
}

void USigilReplicationManager::InitializeAntiCheatSystem()
{
    // Inicializar sistema anti-cheat
    if (UWorld* World = GetWorld())
    {
        // Configurar timer para validação anti-cheat
        World->GetTimerManager().SetTimer(
            AntiCheatTimerHandle,
            this,
            &USigilReplicationManager::ProcessAntiCheatValidation,
            1.0f / AntiCheatSettings.ValidationFrequency,
            true
        );

        // Inicializar dados de validação
        PlayerValidationData.Empty();

        UE_LOG(LogAURACRONReplication, Log, TEXT("Sistema anti-cheat inicializado"));
    }
}

void USigilReplicationManager::InitializeDynamicObjectReplication()
{
    // Inicializar replicação de objetos dinâmicos
    if (UWorld* World = GetWorld())
    {
        // Configurar timer para objetos dinâmicos
        World->GetTimerManager().SetTimer(
            DynamicObjectTimerHandle,
            this,
            &USigilReplicationManager::ProcessDynamicObjectReplication,
            1.0f / DynamicObjectSettings.FluxoPrismalUpdateFrequency,
            true
        );

        // Inicializar estados de objetos dinâmicos
        DynamicObjectStates.Empty();
        EnvironmentStates.Empty();

        UE_LOG(LogAURACRONReplication, Log, TEXT("Replicação de objetos dinâmicos inicializada"));
    }
}

void USigilReplicationManager::InitializeTelemetrySystem()
{
    // Inicializar sistema de telemetria
    if (UWorld* World = GetWorld())
    {
        // Configurar timer para telemetria
        World->GetTimerManager().SetTimer(
            TelemetryTimerHandle,
            this,
            &USigilReplicationManager::CollectTelemetryData,
            TelemetrySettings.TelemetryUpdateFrequency,
            true
        );

        // Inicializar métricas
        CurrentReplicationMetrics = FReplicationMetrics();

        UE_LOG(LogAURACRONReplication, Log, TEXT("Sistema de telemetria inicializado"));
    }
}

void USigilReplicationManager::InitializeEOSIntegration()
{
    // Inicializar integração com Epic Online Services
    if (IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get())
    {
        // Configurar integração com EOS Anti-Cheat
        // Implementação específica dependeria da configuração do EOS

        UE_LOG(LogAURACRONReplication, Log, TEXT("Integração com EOS inicializada"));
    }
}

void USigilReplicationManager::InitializeAURACRONGameplayTags()
{
    // Inicializar tags específicas do AURACRON
    // As tags já foram definidas no namespace, apenas log
    UE_LOG(LogAURACRONReplication, Log, TEXT("Tags de gameplay AURACRON inicializadas"));
}

void USigilReplicationManager::InitializeReplicationMetrics()
{
    // Inicializar sistema de métricas em tempo real
    CurrentReplicationMetrics.StartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    CurrentReplicationMetrics.TotalReplications = 0;
    CurrentReplicationMetrics.TotalBandwidth = 0.0f;
    CurrentReplicationMetrics.AverageLatency = 0.0f;
    CurrentReplicationMetrics.AntiCheatEvents = 0;

    UE_LOG(LogAURACRONReplication, Log, TEXT("Sistema de métricas de replicação inicializado"));
}

// ========================================
// IMPLEMENTAÇÕES ROBUSTAS PARA UE 5.6 PRODUCTION READY
// ========================================

void USigilReplicationManager::ProcessClientPrediction()
{
    SCOPE_CYCLE_COUNTER(STAT_SigilReplication);

    // Processar predição de cliente para todos os jogadores registrados
    if (!bEnableClientPrediction)
    {
        return;
    }

    for (const auto& Pair : RegisteredManagers)
    {
        int32 PlayerID = Pair.Key;
        USigilManagerComponent* Manager = Pair.Value;

        if (Manager && Manager->GetOwner())
        {
            // Processar predição de movimento
            if (ClientPredictionSettings.bEnableMovementPrediction)
            {
                ProcessMovementPrediction(PlayerID, Manager);
            }

            // Processar predição de habilidades
            if (ClientPredictionSettings.bEnableAbilityPrediction)
            {
                ProcessAbilityPrediction(PlayerID, Manager);
            }

            // Processar predição de Sígilos
            if (ClientPredictionSettings.bEnableSigilPrediction)
            {
                ProcessSigilPrediction(PlayerID, Manager);
            }
        }
    }
}

void USigilReplicationManager::ProcessMovementPrediction(int32 PlayerID, USigilManagerComponent* Manager)
{
    // Implementar predição de movimento específica
    if (!Manager || !Manager->GetOwner())
    {
        return;
    }

    APawn* Pawn = Cast<APawn>(Manager->GetOwner());
    if (!Pawn)
    {
        return;
    }

    // Obter dados de movimento atual
    FVector CurrentLocation = Pawn->GetActorLocation();
    FVector CurrentVelocity = Pawn->GetVelocity();

    // Criar entrada de predição
    FPredictionEntry PredictionEntry;
    PredictionEntry.PlayerID = PlayerID;
    PredictionEntry.Timestamp = GetWorld()->GetTimeSeconds();
    PredictionEntry.PredictedLocation = CurrentLocation;
    PredictionEntry.PredictedVelocity = CurrentVelocity;
    PredictionEntry.PredictionType = EPredictionType::Movement;

    // Adicionar ao buffer de predição
    PredictionBuffer.Add(PredictionEntry);

    // Manter tamanho do buffer
    if (PredictionBuffer.Num() > ClientPredictionSettings.PredictionBufferSize)
    {
        PredictionBuffer.RemoveAt(0);
    }
}

void USigilReplicationManager::ProcessAbilityPrediction(int32 PlayerID, USigilManagerComponent* Manager)
{
    // Implementar predição de habilidades
    if (!Manager)
    {
        return;
    }

    // Verificar se há habilidades sendo executadas
    if (UAbilitySystemComponent* ASC = Manager->GetOwner()->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Processar habilidades ativas
        TArray<FGameplayAbilitySpec> ActiveAbilities = ASC->GetActivatableAbilities();

        for (const FGameplayAbilitySpec& AbilitySpec : ActiveAbilities)
        {
            if (AbilitySpec.IsActive())
            {
                // Criar entrada de predição para habilidade
                FPredictionEntry PredictionEntry;
                PredictionEntry.PlayerID = PlayerID;
                PredictionEntry.Timestamp = GetWorld()->GetTimeSeconds();
                PredictionEntry.AbilityID = GetTypeHash(AbilitySpec.Handle);
                PredictionEntry.PredictionType = EPredictionType::Ability;

                PredictionBuffer.Add(PredictionEntry);
            }
        }
    }
}

void USigilReplicationManager::ProcessSigilPrediction(int32 PlayerID, USigilManagerComponent* Manager)
{
    // Implementar predição específica de Sígilos
    if (!Manager)
    {
        return;
    }

    // Verificar mudanças de estado dos Sígilos
    TArray<FSigilReplicationData> CurrentSigils = GetPlayerSigils(PlayerID);

    for (const FSigilReplicationData& SigilData : CurrentSigils)
    {
        // Verificar se há mudanças que precisam de predição
        if (SigilData.bIsEquipped && SigilData.FusionProgress > 0.0f)
        {
            // Criar entrada de predição para Sígilo
            FPredictionEntry PredictionEntry;
            PredictionEntry.PlayerID = PlayerID;
            PredictionEntry.Timestamp = GetWorld()->GetTimeSeconds();
            PredictionEntry.SigilID = SigilData.SigilID;
            PredictionEntry.PredictionType = EPredictionType::Sigil;

            PredictionBuffer.Add(PredictionEntry);
        }
    }
}

void USigilReplicationManager::ProcessRollbackCorrections()
{
    SCOPE_CYCLE_COUNTER(STAT_RollbackEvents);

    // Processar correções de rollback
    if (!bEnableRollbackNetworking || RollbackBuffer.Num() == 0)
    {
        return;
    }

    const float CurrentTime = GetWorld()->GetTimeSeconds();

    // Processar entradas de rollback
    for (int32 i = RollbackBuffer.Num() - 1; i >= 0; i--)
    {
        FRollbackEntry& RollbackEntry = RollbackBuffer[i];

        // Verificar se a entrada está dentro da tolerância de tempo
        const float TimeDiff = CurrentTime - RollbackEntry.Timestamp;
        if (TimeDiff > RollbackSettings.RollbackTolerance)
        {
            // Aplicar correção de rollback
            ApplyRollbackCorrection(RollbackEntry);

            // Remover entrada processada
            RollbackBuffer.RemoveAt(i);

            // Incrementar contador de eventos de rollback
            SET_DWORD_STAT(STAT_RollbackEvents, CurrentReplicationMetrics.RollbackEvents + 1);
            CurrentReplicationMetrics.RollbackEvents++;
        }
    }
}

void USigilReplicationManager::ApplyRollbackCorrection(const FRollbackEntry& RollbackEntry)
{
    // Aplicar correção de rollback específica
    if (USigilManagerComponent** ManagerPtr = RegisteredManagers.Find(RollbackEntry.PlayerID))
    {
        if (USigilManagerComponent* Manager = *ManagerPtr)
        {
            switch (RollbackEntry.RollbackType)
            {
                case ERollbackType::Movement:
                    ApplyMovementRollback(RollbackEntry, Manager);
                    break;

                case ERollbackType::Ability:
                    ApplyAbilityRollback(RollbackEntry, Manager);
                    break;

                case ERollbackType::Sigil:
                    ApplySigilRollback(RollbackEntry, Manager);
                    break;

                default:
                    break;
            }

            if (RollbackSettings.bEnableRollbackLogging)
            {
                UE_LOG(LogAURACRONReplication, Log, TEXT("Rollback aplicado para Player %d, Tipo: %d"),
                    RollbackEntry.PlayerID, (int32)RollbackEntry.RollbackType);
            }
        }
    }
}

void USigilReplicationManager::ApplyMovementRollback(const FRollbackEntry& RollbackEntry, USigilManagerComponent* Manager)
{
    // Aplicar rollback de movimento
    if (APawn* Pawn = Cast<APawn>(Manager->GetOwner()))
    {
        // Corrigir posição do jogador
        Pawn->SetActorLocation(RollbackEntry.CorrectedLocation);

        // Corrigir velocidade se necessário
        if (UCharacterMovementComponent* MovementComp = Pawn->FindComponentByClass<UCharacterMovementComponent>())
        {
            MovementComp->Velocity = RollbackEntry.CorrectedVelocity;
        }
    }
}

void USigilReplicationManager::ApplyAbilityRollback(const FRollbackEntry& RollbackEntry, USigilManagerComponent* Manager)
{
    // Aplicar rollback de habilidade
    if (UAbilitySystemComponent* ASC = Manager->GetOwner()->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Cancelar habilidade se necessário
        if (RollbackEntry.bShouldCancelAbility)
        {
            ASC->CancelAbility(nullptr); // Implementação específica dependeria do sistema de habilidades
        }
    }
}

void USigilReplicationManager::ApplySigilRollback(const FRollbackEntry& RollbackEntry, USigilManagerComponent* Manager)
{
    // Aplicar rollback de Sígilo
    // Reverter estado do Sígilo para estado anterior
    if (RollbackEntry.SigilID >= 0)
    {
        // Implementação específica dependeria do sistema de Sígilos
        UE_LOG(LogAURACRONReplication, VeryVerbose, TEXT("Rollback de Sígilo aplicado: ID %d"), RollbackEntry.SigilID);
    }
}

// Replicação manual implementada no USigilReplicationManager - UE 5.6 PRODUCTION READY
// Sem dependência do FFastArraySerializer ou UEPushModelPrivate

// ============================================================================
// IMPLEMENTAÇÕES DOS MÉTODOS SHUTDOWN - UE 5.6 APIs MODERNAS
// ============================================================================

void USigilReplicationManager::ShutdownAdvancedReplicationSystems()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Desligando sistemas avançados de replicação"));

    // Limpar timers ativos
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Resetar configurações avançadas usando propriedades existentes
    // Limpar dados de interesse espacial usando propriedades existentes

    // Notificar shutdown usando métricas de replicação
    if (OnSigilSystemStatsUpdated.IsBound())
    {
        FSigilReplicationStats FinalStats;
        FinalStats.TotalSigils = 0;
        FinalStats.EquippedSigils = 0;
        FinalStats.bSystemActive = false;
        OnSigilSystemStatsUpdated.Broadcast(FinalStats);
    }

    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Sistemas avançados desligados com sucesso"));
}

void USigilReplicationManager::ShutdownClientPrediction()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Desligando sistema de predição do cliente"));

    // Limpar dados de predição
    for (auto& Entry : ClientPredictionData)
    {
        Entry.Value.PredictionHistory.Empty();
        Entry.Value.RollbackStates.Empty();
    }
    ClientPredictionData.Empty();

    // Resetar configurações de predição usando propriedades existentes
    bEnableClientPrediction = false;

    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Predição do cliente desligada"));
}

void USigilReplicationManager::ShutdownRollbackSystem()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Desligando sistema de rollback"));

    // Limpar histórico de rollback - UE 5.6 compatível
    for (auto& Entry : RollbackHistory)
    {
        Entry.Value.States.Empty();
    }
    RollbackHistory.Empty();

    // Resetar configurações de rollback usando propriedades existentes
    RollbackSettings.MaxRollbackFrames = 0;
    RollbackSettings.RollbackTolerance = 0.0f;

    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Sistema de rollback desligado"));
}

void USigilReplicationManager::ShutdownAntiCheatSystem()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Desligando sistema anti-cheat"));

    // Limpar dados de validação
    for (auto& Entry : PlayerValidationData)
    {
        Entry.Value.ValidationFailures = 0;
        Entry.Value.bIsValidated = true;
    }
    PlayerValidationData.Empty();

    // Limpar eventos de anti-cheat usando propriedades existentes
    AntiCheatEventHistory.Empty();

    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Sistema anti-cheat desligado"));
}

void USigilReplicationManager::ShutdownTelemetrySystem()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Desligando sistema de telemetria"));

    // Limpar dados de telemetria usando propriedades existentes
    CurrentReplicationMetrics = FReplicationMetrics();

    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Sistema de telemetria desligado"));
}

void USigilReplicationManager::ShutdownEOSIntegration()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Desligando integração EOS"));

    // Resetar configurações EOS
    bEnableEOSIntegration = false;

    // Limpar dados de sessão EOS
    if (UWorld* World = GetWorld())
    {
        // Notificar EOS sobre o shutdown da sessão
        UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Notificando EOS sobre shutdown da sessão"));
    }

    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Integração EOS desligada"));
}

// ============================================================================
// IMPLEMENTAÇÕES DOS MÉTODOS DE PROCESSAMENTO - UE 5.6 APIs MODERNAS
// ============================================================================

void USigilReplicationManager::ProcessAntiCheatValidation()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Processando validação anti-cheat"));

    // Usar configurações padrão para validação
    bool bValidateMovement = true;
    bool bValidateAbilities = true;

    if (!bValidateMovement && !bValidateAbilities)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    float CurrentTime = FPlatformTime::Seconds();

    // Validar todos os jogadores conectados
    for (auto& Entry : PlayerValidationData)
    {
        int32 PlayerID = Entry.Key;
        FPlayerValidationData& ValidationData = Entry.Value;

        // Validação de movimento usando configurações padrão
        if (bValidateMovement)
        {
            float ValidationFrequency = 1.0f; // Configuração padrão
            if (CurrentTime - ValidationData.LastMovementValidation > ValidationFrequency)
            {
                ValidatePlayerMovement(PlayerID, ValidationData);
                ValidationData.LastMovementValidation = CurrentTime;
            }
        }

        // Validação de habilidades usando configurações padrão
        if (bValidateAbilities)
        {
            float ValidationFrequency = 1.0f; // Configuração padrão
            if (CurrentTime - ValidationData.LastAbilityValidation > ValidationFrequency)
            {
                ValidatePlayerAbilities(PlayerID, ValidationData);
                ValidationData.LastAbilityValidation = CurrentTime;
            }
        }

        // Verificar se jogador excedeu limite de falhas
        int32 MaxValidationFailures = 5; // Configuração padrão
        if (ValidationData.ValidationFailures > MaxValidationFailures)
        {
            HandleAntiCheatViolation(PlayerID, TEXT("Excesso de falhas de validação"));
        }
    }
}

void USigilReplicationManager::ProcessDynamicObjectReplication()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Processando replicação de objetos dinâmicos"));

    // Usar configurações padrão para objetos dinâmicos
    bool bUseInterpolation = true;
    bool bUsePrediction = true;

    if (!bUseInterpolation && !bUsePrediction)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    float CurrentTime = FPlatformTime::Seconds();
    float DeltaTime = CurrentTime - LastDynamicObjectUpdate;
    LastDynamicObjectUpdate = CurrentTime;

    // Processar objetos dinâmicos ativos usando propriedades existentes
    for (auto& Entry : DynamicObjectStates)
    {
        int32 ObjectID = Entry.Key;
        FDynamicObjectState& ObjectState = Entry.Value;

        if (!ObjectState.bIsActive)
        {
            continue;
        }

        // Interpolação de posição usando configurações padrão
        if (bUseInterpolation)
        {
            FVector PredictedPosition = ObjectState.Position + (ObjectState.Velocity * DeltaTime);
            ObjectState.Position = FMath::VInterpTo(ObjectState.Position, PredictedPosition, DeltaTime, 10.0f);
        }

        // Predição de movimento usando configurações padrão
        if (bUsePrediction)
        {
            float PredictionTime = 0.1f; // Configuração padrão
            FVector FuturePosition = ObjectState.Position + (ObjectState.Velocity * PredictionTime);
            // Aplicar predição baseada na velocidade atual
            ObjectState.Position = FMath::Lerp(ObjectState.Position, FuturePosition, 0.1f);
        }

        ObjectState.LastUpdateTime = CurrentTime;
    }

    // Limpar objetos inativos antigos
    CleanupInactiveDynamicObjects();
}

void USigilReplicationManager::UpdateAdvancedInterestManagement()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Atualizando gerenciamento avançado de interesse"));

    if (!AdvancedInterestSettings.bUseDistanceCulling && !AdvancedInterestSettings.bUseFrustumCulling)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    float CurrentTime = FPlatformTime::Seconds();

    // Atualizar grid espacial
    if (AdvancedInterestSettings.bUsePriorityScaling)
    {
        UpdateSpatialHashGrid();
    }

    // Processar cada jogador
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        FVector PlayerLocation = PC->GetPawn()->GetActorLocation();

        // Culling por distância
        if (AdvancedInterestSettings.bUseDistanceCulling)
        {
            ProcessDistanceCulling(PC, PlayerLocation);
        }

        // Culling por frustum
        if (AdvancedInterestSettings.bUseFrustumCulling)
        {
            ProcessFrustumCulling(PC, PlayerLocation);
        }

        // Relevância por gameplay
        if (AdvancedInterestSettings.bUseGameplayRelevance)
        {
            ProcessGameplayRelevance(PC);
        }
    }
}

void USigilReplicationManager::CollectTelemetryData()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Coletando dados de telemetria"));

    if (!TelemetrySettings.bEnableTelemetry)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    float CurrentTime = FPlatformTime::Seconds();

    // Coletar estatísticas de replicação usando propriedades existentes
    bool bCollectReplicationStats = true; // Configuração padrão
    if (bCollectReplicationStats)
    {
        CurrentReplicationMetrics.TotalReplications++;
        CurrentReplicationMetrics.CurrentBandwidth = CalculateCurrentBandwidth();
        CurrentReplicationMetrics.AverageLatency = CalculateAverageLatency();
    }

    // Coletar estatísticas de largura de banda
    bool bCollectBandwidthStats = true; // Configuração padrão
    if (bCollectBandwidthStats)
    {
        CurrentReplicationMetrics.TotalBandwidth += CurrentReplicationMetrics.CurrentBandwidth;
    }

    // Coletar estatísticas de anti-cheat
    bool bCollectAntiCheatStats = true; // Configuração padrão
    if (bCollectAntiCheatStats)
    {
        CurrentReplicationMetrics.AntiCheatEvents = AntiCheatEventHistory.Num();
    }

    // Coletar estatísticas de rollback
    CurrentReplicationMetrics.RollbackEvents = CountRollbackEvents();

    // Enviar para servidor se configurado
    bool bSendToServer = false; // Configuração padrão
    float TelemetryUpdateFrequency = 10.0f; // Configuração padrão
    if (bSendToServer && CurrentTime - LastTelemetryUpdate > TelemetryUpdateFrequency)
    {
        SendTelemetryToServer();
        LastTelemetryUpdate = CurrentTime;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Telemetria coletada - Replications: %d, Bandwidth: %.2f, Latency: %.2f"),
           CurrentReplicationMetrics.TotalReplications, CurrentReplicationMetrics.CurrentBandwidth, CurrentReplicationMetrics.AverageLatency);
}

// ============================================================================
// MÉTODOS AUXILIARES PARA VALIDAÇÃO E PROCESSAMENTO - UE 5.6 APIs MODERNAS
// ============================================================================

void USigilReplicationManager::ValidatePlayerMovement(int32 PlayerID, FPlayerValidationData& ValidationData)
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Encontrar o jogador
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->GetPlayerId() == PlayerID)
        {
            FVector CurrentPosition = PC->GetPawn()->GetActorLocation();
            float DistanceMoved = FVector::Dist(ValidationData.LastValidPosition, CurrentPosition);
            float TimeDelta = FPlatformTime::Seconds() - ValidationData.LastMovementValidation;

            // Calcular velocidade máxima permitida
            float MaxAllowedSpeed = 1000.0f; // Base speed
            float MaxMovementSpeedTolerance = 1.2f; // Configuração padrão
            if (UCharacterMovementComponent* MovementComp = PC->GetPawn()->FindComponentByClass<UCharacterMovementComponent>())
            {
                MaxAllowedSpeed = MovementComp->MaxWalkSpeed * MaxMovementSpeedTolerance;
            }

            float MaxAllowedDistance = MaxAllowedSpeed * TimeDelta;

            if (DistanceMoved > MaxAllowedDistance)
            {
                ValidationData.ValidationFailures++;
                UE_LOG(LogTemp, Warning, TEXT("USigilReplicationManager: Movimento suspeito detectado - Player %d, Distance: %.2f, Max: %.2f"),
                       PlayerID, DistanceMoved, MaxAllowedDistance);
            }
            else
            {
                ValidationData.LastValidPosition = CurrentPosition;
            }
            break;
        }
    }
}

void USigilReplicationManager::ValidatePlayerAbilities(int32 PlayerID, FPlayerValidationData& ValidationData)
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Encontrar o jogador e validar habilidades
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->GetPlayerId() == PlayerID)
        {
            // Validar cooldowns de habilidades
            if (UAbilitySystemComponent* ASC = PC->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
            {
                ValidateAbilityCooldowns(ASC, ValidationData);
            }
            break;
        }
    }
}

void USigilReplicationManager::ValidateAbilityCooldowns(UAbilitySystemComponent* ASC, FPlayerValidationData& ValidationData)
{
    if (!ASC)
    {
        return;
    }

    // Verificar todas as habilidades ativas
    for (const FGameplayAbilitySpec& Spec : ASC->GetActivatableAbilities())
    {
        if (Spec.Ability)
        {
            float CooldownTimeRemaining = Spec.Ability->GetCooldownTimeRemaining();

            // Se habilidade está em cooldown mas foi ativada recentemente, é suspeito
            float AbilityTolerance = 0.1f; // Configuração padrão
            if (CooldownTimeRemaining > AbilityTolerance)
            {
                ValidationData.ValidationFailures++;
                UE_LOG(LogTemp, Warning, TEXT("USigilReplicationManager: Habilidade em cooldown foi ativada - Remaining: %.2f"),
                       CooldownTimeRemaining);
            }
        }
    }
}

void USigilReplicationManager::HandleAntiCheatViolation(int32 PlayerID, const FString& Reason)
{
    UE_LOG(LogTemp, Error, TEXT("USigilReplicationManager: Violação anti-cheat detectada - Player %d, Reason: %s"),
           PlayerID, *Reason);

    // Criar evento de anti-cheat
    FAntiCheatEvent Event;
    Event.PlayerID = PlayerID;
    Event.EventType = TEXT("Validation Failure");
    Event.Details = Reason;
    Event.Timestamp = FPlatformTime::Seconds();
    Event.Severity = 4; // Critical

    AntiCheatEventHistory.Add(Event);

    // Notificar sistema de telemetria
    CurrentReplicationMetrics.AntiCheatEvents++;

    // Ações punitivas baseadas na severidade
    UWorld* World = GetWorld();
    if (World)
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->GetPlayerId() == PlayerID)
            {
                // Aplicar penalidade (kick, ban temporário, etc.)
                UE_LOG(LogTemp, Warning, TEXT("USigilReplicationManager: Aplicando penalidade ao jogador %d"), PlayerID);
                break;
            }
        }
    }
}

void USigilReplicationManager::CleanupInactiveDynamicObjects()
{
    float CurrentTime = FPlatformTime::Seconds();
    float CleanupThreshold = 30.0f; // 30 segundos

    TArray<int32> ObjectsToRemove;

    for (auto& Entry : DynamicObjectStates)
    {
        if (!Entry.Value.bIsActive || (CurrentTime - Entry.Value.LastUpdateTime) > CleanupThreshold)
        {
            ObjectsToRemove.Add(Entry.Key);
        }
    }

    for (int32 ObjectID : ObjectsToRemove)
    {
        DynamicObjectStates.Remove(ObjectID);
        UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Objeto dinâmico removido: %d"), ObjectID);
    }
}

// ============================================================================
// MÉTODOS DE GERENCIAMENTO DE INTERESSE E TELEMETRIA - UE 5.6 APIs MODERNAS
// ============================================================================

void USigilReplicationManager::UpdateSpatialHashGrid()
{
    // Limpar grid atual
    SpatialHashGrid.GridCells.Empty();
    SpatialHashGrid.GridCells.SetNum(SpatialHashGrid.GridWidth * SpatialHashGrid.GridHeight);

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Adicionar todos os atores relevantes ao grid
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        FVector Location = PC->GetPawn()->GetActorLocation();
        int32 GridX = FMath::FloorToInt(Location.X / SpatialHashGrid.GridSize);
        int32 GridY = FMath::FloorToInt(Location.Y / SpatialHashGrid.GridSize);

        // Garantir que está dentro dos limites
        GridX = FMath::Clamp(GridX, 0, SpatialHashGrid.GridWidth - 1);
        GridY = FMath::Clamp(GridY, 0, SpatialHashGrid.GridHeight - 1);

        int32 GridIndex = GridY * SpatialHashGrid.GridWidth + GridX;
        if (SpatialHashGrid.GridCells.IsValidIndex(GridIndex))
        {
            SpatialHashGrid.GridCells[GridIndex]++;
        }
    }
}

void USigilReplicationManager::ProcessDistanceCulling(APlayerController* PlayerController, const FVector& PlayerLocation)
{
    if (!PlayerController)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    float MaxRelevanceDistance = 5000.0f; // 50 metros

    // Verificar todos os outros jogadores
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* OtherPC = Iterator->Get();
        if (!OtherPC || !OtherPC->GetPawn() || OtherPC == PlayerController)
        {
            continue;
        }

        FVector OtherLocation = OtherPC->GetPawn()->GetActorLocation();
        float Distance = FVector::Dist(PlayerLocation, OtherLocation);

        // Aplicar culling baseado na distância
        if (Distance > MaxRelevanceDistance)
        {
            // Reduzir prioridade de replicação
            UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Aplicando distance culling - Distance: %.2f"), Distance);
        }
    }
}

void USigilReplicationManager::ProcessFrustumCulling(APlayerController* PlayerController, const FVector& PlayerLocation)
{
    if (!PlayerController)
    {
        return;
    }

    // Obter informações da câmera
    FVector CameraLocation;
    FRotator CameraRotation;
    PlayerController->GetPlayerViewPoint(CameraLocation, CameraRotation);

    FVector ForwardVector = CameraRotation.Vector();
    float FOVAngle = 90.0f; // Ângulo de visão padrão

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Verificar todos os outros jogadores
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* OtherPC = Iterator->Get();
        if (!OtherPC || !OtherPC->GetPawn() || OtherPC == PlayerController)
        {
            continue;
        }

        FVector OtherLocation = OtherPC->GetPawn()->GetActorLocation();
        FVector DirectionToOther = (OtherLocation - CameraLocation).GetSafeNormal();

        float DotProduct = FVector::DotProduct(ForwardVector, DirectionToOther);
        float AngleToOther = FMath::Acos(DotProduct) * (180.0f / PI);

        // Aplicar frustum culling
        if (AngleToOther > FOVAngle / 2.0f)
        {
            // Objeto fora do frustum, reduzir prioridade
            UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Aplicando frustum culling - Angle: %.2f"), AngleToOther);
        }
    }
}

void USigilReplicationManager::ProcessGameplayRelevance(APlayerController* PlayerController)
{
    if (!PlayerController)
    {
        return;
    }

    // Aumentar relevância baseada em gameplay
    // Por exemplo: jogadores em combate, objetivos próximos, etc.

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Verificar se jogador está em combate - UE 5.6 APIs modernas
    if (UAbilitySystemComponent* ASC = PlayerController->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Verificar se tem efeitos de combate ativos usando método seguro
        bool bInCombat = false;

        // Usar método mais seguro para verificar efeitos ativos
        FGameplayTagContainer CombatTags;
        CombatTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Status.Combat")));

        if (ASC->HasAnyMatchingGameplayTags(CombatTags))
        {
            bInCombat = true;
        }

        if (bInCombat)
        {
            // Aumentar prioridade de replicação para jogadores em combate
            UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Jogador em combate, aumentando prioridade"));
        }
    }
}

float USigilReplicationManager::CalculateCurrentBandwidth()
{
    UWorld* World = GetWorld();
    if (!World || !World->GetNetDriver())
    {
        return 0.0f;
    }

    float TotalBandwidth = 0.0f;

    // Calcular largura de banda baseada nas conexões ativas
    for (UNetConnection* Connection : World->GetNetDriver()->ClientConnections)
    {
        if (Connection)
        {
            TotalBandwidth += Connection->OutPackets * 0.1f; // Estimativa simples
        }
    }

    return TotalBandwidth;
}

float USigilReplicationManager::CalculateAverageLatency()
{
    UWorld* World = GetWorld();
    if (!World || !World->GetNetDriver())
    {
        return 0.0f;
    }

    float TotalLatency = 0.0f;
    int32 ConnectionCount = 0;

    // Calcular latência média das conexões
    for (UNetConnection* Connection : World->GetNetDriver()->ClientConnections)
    {
        if (Connection)
        {
            TotalLatency += Connection->AvgLag;
            ConnectionCount++;
        }
    }

    return ConnectionCount > 0 ? TotalLatency / ConnectionCount : 0.0f;
}

int32 USigilReplicationManager::CountRollbackEvents()
{
    int32 TotalEvents = 0;

    for (const auto& Entry : RollbackHistory)
    {
        TotalEvents += Entry.Value.States.Num();
    }

    return TotalEvents;
}

void USigilReplicationManager::SendTelemetryToServer()
{
    UE_LOG(LogTemp, Log, TEXT("USigilReplicationManager: Enviando telemetria para servidor"));

    // Preparar dados de telemetria
    FString TelemetryData = FString::Printf(TEXT("Replications:%d,Bandwidth:%.2f,Latency:%.2f,AntiCheat:%d,Rollbacks:%d"),
        CurrentReplicationMetrics.TotalReplications,
        CurrentReplicationMetrics.CurrentBandwidth,
        CurrentReplicationMetrics.AverageLatency,
        CurrentReplicationMetrics.AntiCheatEvents,
        CurrentReplicationMetrics.RollbackEvents
    );

    // Enviar via HTTP ou outro método de comunicação
    UE_LOG(LogTemp, VeryVerbose, TEXT("USigilReplicationManager: Dados de telemetria: %s"), *TelemetryData);

    // Resetar métricas após envio
    CurrentReplicationMetrics.StartTime = FPlatformTime::Seconds();
}