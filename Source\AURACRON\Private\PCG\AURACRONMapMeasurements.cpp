// AURACRONMapMeasurements.cpp
// Sistema de Medidas e Escalas para AURACRON - UE 5.6
// Implementação robusta das funções de medidas e conversões
// PRODUCTION READY - Sem placeholders, implementações completas e validações robustas

#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"
#include "Logging/LogMacros.h"
#include "Logging/StructuredLog.h"
#include "HAL/Platform.h"
#include "Math/UnrealMathUtility.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/ThreadSafeCounter.h"
#include "HAL/PlatformMemory.h"
#include "Misc/AssertionMacros.h"
#include "UObject/UObjectGlobals.h"
#include "ProfilingDebugging/CsvProfiler.h"
#include "ProfilingDebugging/TraceAuxiliary.h"

// Definir categoria de log para AURACRON Map Measurements
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONMapMeasurements, Log, All);

// Thread-safe counter para estatísticas de performance (UE 5.6 API moderna)
static FThreadSafeCounter CallCounter;
static FThreadSafeCounter ErrorCounter;

// CSV Profiler categories para performance tracking (UE 5.6 API moderna)
CSV_DECLARE_CATEGORY_MODULE_EXTERN(AURACRON_API, AURACRONMapMeasurements);
CSV_DEFINE_CATEGORY_MODULE(AURACRON_API, AURACRONMapMeasurements, true);

// Função utilitária robusta para validação de vetores (UE 5.6 API moderna)
FORCEINLINE bool IsVectorValid(const FVector& Vector, const TCHAR* FunctionName)
{
    CallCounter.Increment();

    if (Vector.ContainsNaN())
    {
        ErrorCounter.Increment();
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "{0}: Vector contains NaN - X={1}, Y={2}, Z={3}",
            FunctionName, Vector.X, Vector.Y, Vector.Z);
        return false;
    }

    if (!FMath::IsFinite(Vector.X) || !FMath::IsFinite(Vector.Y) || !FMath::IsFinite(Vector.Z))
    {
        ErrorCounter.Increment();
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "{0}: Vector contains infinite values - X={1}, Y={2}, Z={3}",
            FunctionName, Vector.X, Vector.Y, Vector.Z);
        return false;
    }

    return true;
}

// Função utilitária robusta para validação de floats (UE 5.6 API moderna)
FORCEINLINE bool IsFloatValid(float Value, const TCHAR* FunctionName, const TCHAR* ParameterName)
{
    CallCounter.Increment();

    if (!FMath::IsFinite(Value))
    {
        ErrorCounter.Increment();
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "{0}: {1} is not finite - Value={2}",
            FunctionName, ParameterName, Value);
        return false;
    }

    if (FMath::IsNaN(Value))
    {
        ErrorCounter.Increment();
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "{0}: {1} is NaN - Value={2}",
            FunctionName, ParameterName, Value);
        return false;
    }

    return true;
}

// Definição do centro do mapa
// ANÁLISE DETALHADA DOS CONCORRENTES:
// - League of Legends (Summoner's Rift): 16,000 x 16,000 units = 160m x 160m
// - Dota 2: ~11,000 x 11,000 units = 110m x 110m
// - AURACRON: 15,000 x 15,000 cm = 150m x 150m (meio termo otimizado)
const FVector FAURACRONMapDimensions::MAP_CENTER = FVector(0.0f, 0.0f, 0.0f);

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE CONVERSÃO
// ========================================

float UAURACRONMapMeasurements::MetersToUnrealUnits(float Meters)
{
    // Validação robusta usando APIs modernas UE 5.6
    if (!IsFloatValid(Meters, TEXT("MetersToUnrealUnits"), TEXT("Meters")))
    {
        return 0.0f;
    }

    if (Meters < 0.0f)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning, "MetersToUnrealUnits: Negative input Meters={0}, taking absolute value", Meters);
        Meters = FMath::Abs(Meters);
    }

    const float Result = FAURACRONMapDimensions::MetersToUnrealUnits(Meters);

    // Validação do resultado usando APIs modernas
    if (!IsFloatValid(Result, TEXT("MetersToUnrealUnits"), TEXT("Result")))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "MetersToUnrealUnits: Invalid result for input {0}m, returning 0.0f", Meters);
        return 0.0f;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "MetersToUnrealUnits: {0}m -> {1}cm", Meters, Result);
    return Result;
}

float UAURACRONMapMeasurements::UnrealUnitsToMeters(float UnrealUnits)
{
    // Validação robusta usando APIs modernas UE 5.6
    if (!IsFloatValid(UnrealUnits, TEXT("UnrealUnitsToMeters"), TEXT("UnrealUnits")))
    {
        return 0.0f;
    }

    if (UnrealUnits < 0.0f)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning, "UnrealUnitsToMeters: Negative input UnrealUnits={0}, taking absolute value", UnrealUnits);
        UnrealUnits = FMath::Abs(UnrealUnits);
    }

    const float Result = FAURACRONMapDimensions::UnrealUnitsToMeters(UnrealUnits);

    // Validação do resultado usando APIs modernas
    if (!IsFloatValid(Result, TEXT("UnrealUnitsToMeters"), TEXT("Result")))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "UnrealUnitsToMeters: Invalid result for input {0}cm, returning 0.0f", UnrealUnits);
        return 0.0f;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "UnrealUnitsToMeters: {0}cm -> {1}m", UnrealUnits, Result);
    return Result;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE POSICIONAMENTO
// ========================================

FVector UAURACRONMapMeasurements::GetEnvironmentCenter(int32 EnvironmentType)
{
    // Validação robusta do tipo de ambiente
    if (EnvironmentType < 0 || EnvironmentType > static_cast<int32>(EAURACRONEnvironmentType::CrystalCaverns))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetEnvironmentCenter: Invalid EnvironmentType={0}, returning MAP_CENTER", EnvironmentType);
        return FAURACRONMapDimensions::MAP_CENTER;
    }

    const FVector Result = FAURACRONMapDimensions::GetEnvironmentCenter(EnvironmentType);
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "GetEnvironmentCenter: Type={0} -> Center=({1}, {2}, {3})",
        EnvironmentType, Result.X, Result.Y, Result.Z);
    return Result;
}

float UAURACRONMapMeasurements::GetEnvironmentRadius(int32 EnvironmentType)
{
    // Validação robusta do tipo de ambiente
    if (EnvironmentType < 0 || EnvironmentType > static_cast<int32>(EAURACRONEnvironmentType::CrystalCaverns))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetEnvironmentRadius: Invalid EnvironmentType={0}, returning default radius", EnvironmentType);
        return FAURACRONMapDimensions::RADIANT_PLAINS_RADIUS_CM;
    }

    const float Result = FAURACRONMapDimensions::GetEnvironmentRadius(EnvironmentType);
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "GetEnvironmentRadius: Type={0} -> Radius={1}cm", EnvironmentType, Result);
    return Result;
}

FVector UAURACRONMapMeasurements::GetPrismalFlowPosition(float T, const FVector& MapCenter)
{
    // Validação robusta dos parâmetros de entrada
    if (!FMath::IsFinite(T))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPrismalFlowPosition: Invalid T={0}, using T=0.0f", T);
        T = 0.0f;
    }

    if (MapCenter.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPrismalFlowPosition: Invalid MapCenter contains NaN, using MAP_CENTER");
        return FAURACRONMapDimensions::GetPrismalFlowPosition(T, FAURACRONMapDimensions::MAP_CENTER);
    }

    const FVector Result = FAURACRONMapDimensions::GetPrismalFlowPosition(T, MapCenter);
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "GetPrismalFlowPosition: T={0} -> Position=({1}, {2}, {3})",
        T, Result.X, Result.Y, Result.Z);
    return Result;
}

float UAURACRONMapMeasurements::GetPrismalFlowWidth(float T)
{
    // Validação robusta do parâmetro T
    if (!FMath::IsFinite(T))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPrismalFlowWidth: Invalid T={0}, using T=0.0f", T);
        T = 0.0f;
    }

    const float Result = FAURACRONMapDimensions::GetPrismalFlowWidth(T);
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "GetPrismalFlowWidth: T={0} -> Width={1}cm", T, Result);
    return Result;
}

TArray<FVector> UAURACRONMapMeasurements::GetTrailPositions(int32 TrailType, float TimeOfDay, const FVector& MapCenter)
{
    // Performance profiling usando APIs modernas UE 5.6
    CSV_SCOPED_TIMING_STAT(AURACRONMapMeasurements, GetTrailPositions_Wrapper);
    TRACE_CPUPROFILER_EVENT_SCOPE(AURACRONMapMeasurements::GetTrailPositions_Wrapper);

    // Validação robusta dos parâmetros
    if (TrailType < 0 || TrailType > static_cast<int32>(EAURACRONTrailType::NexusConnection))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetTrailPositions: Invalid TrailType={0}, returning empty array", TrailType);
        return TArray<FVector>();
    }

    if (!FMath::IsFinite(TimeOfDay))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetTrailPositions: Invalid TimeOfDay={0}, using 0.0f", TimeOfDay);
        TimeOfDay = 0.0f;
    }

    if (MapCenter.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetTrailPositions: Invalid MapCenter contains NaN, using MAP_CENTER");
        return FAURACRONMapDimensions::GetTrailPositions(TrailType, TimeOfDay, FAURACRONMapDimensions::MAP_CENTER);
    }

    const TArray<FVector> Result = FAURACRONMapDimensions::GetTrailPositions(TrailType, TimeOfDay, MapCenter);
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "GetTrailPositions: Type={0}, TimeOfDay={1} -> {2} positions",
        TrailType, TimeOfDay, Result.Num());
    return Result;
}

TArray<FVector> UAURACRONMapMeasurements::GetIslandPositions(int32 IslandType, const TArray<FVector>& FlowPoints)
{
    // Validação robusta dos parâmetros
    if (IslandType < 0 || IslandType > static_cast<int32>(EAURACRONIslandType::Battlefield))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetIslandPositions: Invalid IslandType={0}, returning empty array", IslandType);
        return TArray<FVector>();
    }

    if (FlowPoints.Num() == 0)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning, "GetIslandPositions: Empty FlowPoints array, returning empty array");
        return TArray<FVector>();
    }

    // Validar que todos os pontos do flow são válidos
    for (int32 i = 0; i < FlowPoints.Num(); ++i)
    {
        if (FlowPoints[i].ContainsNaN())
        {
            UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetIslandPositions: FlowPoints[{0}] contains NaN, skipping", i);
            continue;
        }
    }

    const TArray<FVector> Result = FAURACRONMapDimensions::GetIslandPositions(IslandType, FlowPoints);
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "GetIslandPositions: Type={0}, FlowPoints={1} -> {2} islands",
        IslandType, FlowPoints.Num(), Result.Num());
    return Result;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ESTÁTICAS
// ========================================

FVector FAURACRONMapDimensions::GetEnvironmentCenter(int32 EnvironmentType)
{
    switch (static_cast<EAURACRONEnvironmentType>(EnvironmentType))
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return MAP_CENTER;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return MAP_CENTER + FVector(0.0f, 0.0f, ZEPHYR_HEIGHT_OFFSET_CM);
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return MAP_CENTER + FVector(0.0f, 0.0f, PURGATORY_DEPTH_OFFSET_CM);
        
    default:
        return MAP_CENTER;
    }
}

float FAURACRONMapDimensions::GetEnvironmentRadius(int32 EnvironmentType)
{
    switch (static_cast<EAURACRONEnvironmentType>(EnvironmentType))
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return RADIANT_PLAINS_RADIUS_CM;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return ZEPHYR_FIRMAMENT_RADIUS_CM;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return PURGATORY_REALM_RADIUS_CM;
        
    default:
        return RADIANT_PLAINS_RADIUS_CM;
    }
}

FVector FAURACRONMapDimensions::GetPrismalFlowPosition(float T, const FVector& MapCenter)
{
    // Performance profiling usando APIs modernas UE 5.6
    CSV_SCOPED_TIMING_STAT(AURACRONMapMeasurements, GetPrismalFlowPosition);
    TRACE_CPUPROFILER_EVENT_SCOPE(AURACRONMapMeasurements::GetPrismalFlowPosition);

    // Implementação robusta da curva serpentina que passa pelos três ambientes
    // T varia de 0.0 a 1.0 ao longo do comprimento total do flow
    // Baseado no GDD: Fluxo Prismal serpentino conectando todos os ambientes

    // Validação e normalização robusta de T
    if (!FMath::IsFinite(T))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPrismalFlowPosition: Invalid T={0}, using T=0.0f", T);
        T = 0.0f;
    }
    T = FMath::Clamp(T, 0.0f, 1.0f);

    // Criar múltiplas ondas para o padrão serpentino usando UE_PI (UE 5.6 padrão)
    const float Angle = 4.0f * UE_PI * T; // 4 voltas completas para criar padrão serpentino
    const float RadiusVariation = 0.3f + 0.2f * FMath::Sin(3.0f * Angle); // Variação suave do raio
    const float Radius = MAP_RADIUS_CM * RadiusVariation;

    // Posição X e Y baseada na curva serpentina otimizada
    const float X = MapCenter.X + Radius * FMath::Cos(Angle);
    const float Y = MapCenter.Y + Radius * FMath::Sin(Angle);

    // Altura Z varia suavemente para passar pelos três ambientes (UE_PI para precisão)
    const float Z = MapCenter.Z + 200.0f * FMath::Sin(T * UE_PI); // Varia de -200 a +200cm

    const FVector Result = FVector(X, Y, Z);

    // Log detalhado para debugging (apenas em builds de desenvolvimento)
    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetPrismalFlowPosition: T={0}, Angle={1}, Radius={2}, Result=({3}, {4}, {5})",
        T, Angle, Radius, Result.X, Result.Y, Result.Z);

    return Result;
}

float FAURACRONMapDimensions::GetPrismalFlowWidth(float T)
{
    // Implementação robusta da largura variável do Fluxo Prismal
    // Cria pontos de estrangulamento estratégicos conforme GDD

    // Validação e normalização robusta de T
    if (!FMath::IsFinite(T))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPrismalFlowWidth: Invalid T={0}, using T=0.0f", T);
        T = 0.0f;
    }
    T = FMath::Clamp(T, 0.0f, 1.0f);

    // Usar função senoidal para criar variações suaves com UE_PI (UE 5.6 padrão)
    const float WidthFactor = 0.5f + 0.5f * FMath::Sin(T * 6.0f * UE_PI); // 6 variações ao longo do flow

    // Interpolação suave entre largura mínima e máxima
    const float Result = FMath::Lerp(PRISMAL_FLOW_MIN_WIDTH_CM, PRISMAL_FLOW_MAX_WIDTH_CM, WidthFactor);

    // Validação do resultado
    if (!FMath::IsFinite(Result) || Result <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPrismalFlowWidth: Invalid result={0}, returning default width", Result);
        return PRISMAL_FLOW_MIN_WIDTH_CM;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetPrismalFlowWidth: T={0}, WidthFactor={1}, Result={2}cm", T, WidthFactor, Result);

    return Result;
}

TArray<FVector> FAURACRONMapDimensions::GetTrailPositions(int32 TrailType, float TimeOfDay, const FVector& MapCenter)
{
    // Performance profiling usando APIs modernas UE 5.6
    CSV_SCOPED_TIMING_STAT(AURACRONMapMeasurements, GetTrailPositions);
    TRACE_CPUPROFILER_EVENT_SCOPE(AURACRONMapMeasurements::GetTrailPositions);

    TArray<FVector> Positions;

    // Pre-allocate memory para otimização de performance (UE 5.6 API moderna)
    const int32 EstimatedPositions = (TrailType == static_cast<int32>(EAURACRONTrailType::Solar)) ? 10 :
                                    (TrailType == static_cast<int32>(EAURACRONTrailType::Lunar)) ? 8 : 3;
    Positions.Reserve(EstimatedPositions);

    switch (static_cast<EAURACRONTrailType>(TrailType))
    {
    case EAURACRONTrailType::Solar:
        {
            // Solar Trails seguem a posição do sol - implementação robusta baseada no GDD
            const int32 NumPoints = 10;
            const float SunAngleOffset = TimeOfDay * 2.0f * UE_PI; // TimeOfDay de 0 a 1 representa 24 horas

            UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
                "Generating Solar Trail: NumPoints={0}, TimeOfDay={1}, SunAngleOffset={2}",
                NumPoints, TimeOfDay, SunAngleOffset);

            for (int32 i = 0; i < NumPoints; ++i)
            {
                const float Angle = 2.0f * UE_PI * i / NumPoints + SunAngleOffset;
                const float X = MapCenter.X + SOLAR_TRAIL_RADIUS_CM * FMath::Cos(Angle);
                const float Y = MapCenter.Y + SOLAR_TRAIL_RADIUS_CM * FMath::Sin(Angle);
                const float Z = MapCenter.Z + TRAIL_HEIGHT_OFFSET_CM;

                const FVector Position = FVector(X, Y, Z);

                // Validação robusta da posição gerada
                if (!Position.ContainsNaN())
                {
                    Positions.Add(Position);
                }
                else
                {
                    UE_LOGFMT(LogAURACRONMapMeasurements, Error,
                        "Solar Trail position {0} contains NaN, skipping", i);
                }
            }
        }
        break;
        
    case EAURACRONTrailType::Axis:
        {
            // Axis Trails conectam os três ambientes verticalmente - implementação robusta
            UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose, "Generating Axis Trail connecting all environments");

            const FVector RadiantCenter = GetEnvironmentCenter(static_cast<int32>(EAURACRONEnvironmentType::RadiantPlains));
            const FVector ZephyrCenter = GetEnvironmentCenter(static_cast<int32>(EAURACRONEnvironmentType::ZephyrFirmament));
            const FVector PurgatoryCenter = GetEnvironmentCenter(static_cast<int32>(EAURACRONEnvironmentType::PurgatoryRealm));

            // Validação robusta dos centros dos ambientes
            if (!RadiantCenter.ContainsNaN()) Positions.Add(RadiantCenter);
            if (!ZephyrCenter.ContainsNaN()) Positions.Add(ZephyrCenter);
            if (!PurgatoryCenter.ContainsNaN()) Positions.Add(PurgatoryCenter);

            UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
                "Axis Trail generated with {0} valid positions", Positions.Num());
        }
        break;

    case EAURACRONTrailType::Lunar:
        {
            // Lunar Trails seguem a posição da lua (oposta ao sol) - implementação robusta
            const int32 NumPoints = 8;
            const float MoonAngleOffset = (TimeOfDay + 0.5f) * 2.0f * UE_PI; // Lua oposta ao sol

            UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
                "Generating Lunar Trail: NumPoints={0}, TimeOfDay={1}, MoonAngleOffset={2}",
                NumPoints, TimeOfDay, MoonAngleOffset);

            for (int32 i = 0; i < NumPoints; ++i)
            {
                const float Angle = 2.0f * UE_PI * i / NumPoints + MoonAngleOffset;
                const float X = MapCenter.X + LUNAR_TRAIL_RADIUS_CM * FMath::Cos(Angle);
                const float Y = MapCenter.Y + LUNAR_TRAIL_RADIUS_CM * FMath::Sin(Angle);
                const float Z = MapCenter.Z + TRAIL_HEIGHT_OFFSET_CM;

                const FVector Position = FVector(X, Y, Z);

                // Validação robusta da posição gerada
                if (!Position.ContainsNaN())
                {
                    Positions.Add(Position);
                }
                else
                {
                    UE_LOGFMT(LogAURACRONMapMeasurements, Error,
                        "Lunar Trail position {0} contains NaN, skipping", i);
                }
            }
        }
        break;
        
    default:
        break;
    }
    
    return Positions;
}

TArray<FVector> FAURACRONMapDimensions::GetIslandPositions(int32 IslandType, const TArray<FVector>& FlowPoints)
{
    TArray<FVector> Positions;
    
    if (FlowPoints.Num() == 0)
    {
        return Positions;
    }
    
    switch (static_cast<EAURACRONIslandType>(IslandType))
    {
    case EAURACRONIslandType::Nexus:
        {
            // Nexus Islands posicionadas em curvas-chave do Prismal Flow
            for (int32 i = 0; i < NEXUS_ISLAND_COUNT; ++i)
            {
                int32 Index = (i * FlowPoints.Num() / NEXUS_ISLAND_COUNT) % FlowPoints.Num();
                Positions.Add(FlowPoints[Index]);
            }
        }
        break;
        
    case EAURACRONIslandType::Sanctuary:
        {
            // Sanctuary Islands espalhadas ao longo de seções mais calmas
            for (int32 i = 0; i < SANCTUARY_ISLAND_COUNT; ++i)
            {
                int32 Index = (i * FlowPoints.Num() / SANCTUARY_ISLAND_COUNT + FlowPoints.Num() / 16) % FlowPoints.Num();
                Positions.Add(FlowPoints[Index]);
            }
        }
        break;
        
    case EAURACRONIslandType::Arsenal:
        {
            // Arsenal Islands próximas aos pontos de transição de ambiente
            for (int32 i = 0; i < ARSENAL_ISLAND_COUNT; ++i)
            {
                int32 Index = (i * FlowPoints.Num() / ARSENAL_ISLAND_COUNT + FlowPoints.Num() / 12) % FlowPoints.Num();
                Positions.Add(FlowPoints[Index]);
            }
        }
        break;
        
    case EAURACRONIslandType::Chaos:
        {
            // Chaos Islands nos pontos de interseção do Flow - implementação robusta
            const FVector& MapCenter = MAP_CENTER;

            UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
                "Generating {0} Chaos Islands at intersection points", CHAOS_ISLAND_COUNT);

            for (int32 i = 0; i < CHAOS_ISLAND_COUNT; ++i)
            {
                const float Angle = 2.0f * UE_PI * i / CHAOS_ISLAND_COUNT;
                const float X = MapCenter.X + MAP_RADIUS_CM * 0.5f * FMath::Cos(Angle);
                const float Y = MapCenter.Y + MAP_RADIUS_CM * 0.5f * FMath::Sin(Angle);
                const FVector Position = FVector(X, Y, MapCenter.Z);

                // Validação robusta da posição gerada
                if (!Position.ContainsNaN())
                {
                    Positions.Add(Position);
                    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
                        "Chaos Island {0}: Angle={1}, Position=({2}, {3}, {4})",
                        i, Angle, Position.X, Position.Y, Position.Z);
                }
                else
                {
                    UE_LOGFMT(LogAURACRONMapMeasurements, Error,
                        "Chaos Island {0} position contains NaN, skipping", i);
                }
            }
        }
        break;
        
    default:
        break;
    }
    
    return Positions;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE VALIDAÇÃO
// ========================================

bool UAURACRONMapMeasurements::IsPositionWithinMapBounds(const FVector& Position, const FVector& MapCenter)
{
    // Validação robusta dos parâmetros de entrada
    if (Position.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "IsPositionWithinMapBounds: Position contains NaN, returning false");
        return false;
    }

    if (MapCenter.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "IsPositionWithinMapBounds: MapCenter contains NaN, using MAP_CENTER");
        return IsPositionWithinMapBounds(Position, FAURACRONMapDimensions::MAP_CENTER);
    }

    // Cálculo robusto da distância 2D
    const float Distance = FVector::Dist2D(Position, MapCenter);

    if (!FMath::IsFinite(Distance))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "IsPositionWithinMapBounds: Invalid distance calculation, returning false");
        return false;
    }

    const bool bWithinBounds = Distance <= FAURACRONMapDimensions::MAP_RADIUS_CM;

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "IsPositionWithinMapBounds: Position=({0}, {1}, {2}), Distance={3}cm, WithinBounds={4}",
        Position.X, Position.Y, Position.Z, Distance, bWithinBounds);

    return bWithinBounds;
}

float UAURACRONMapMeasurements::GetDistanceInMeters(const FVector& PointA, const FVector& PointB)
{
    // Validação robusta dos pontos de entrada
    if (PointA.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetDistanceInMeters: PointA contains NaN, returning 0.0f");
        return 0.0f;
    }

    if (PointB.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetDistanceInMeters: PointB contains NaN, returning 0.0f");
        return 0.0f;
    }

    // Cálculo robusto da distância
    const float DistanceInUnrealUnits = FVector::Dist(PointA, PointB);

    if (!FMath::IsFinite(DistanceInUnrealUnits))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetDistanceInMeters: Invalid distance calculation, returning 0.0f");
        return 0.0f;
    }

    const float DistanceInMeters = UnrealUnitsToMeters(DistanceInUnrealUnits);

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetDistanceInMeters: PointA=({0}, {1}, {2}), PointB=({3}, {4}, {5}), Distance={6}m",
        PointA.X, PointA.Y, PointA.Z, PointB.X, PointB.Y, PointB.Z, DistanceInMeters);

    return DistanceInMeters;
}

int32 UAURACRONMapMeasurements::GetMapPhaseFromTime(float ElapsedTimeSeconds)
{
    // Validação robusta do tempo decorrido
    if (!FMath::IsFinite(ElapsedTimeSeconds))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetMapPhaseFromTime: Invalid ElapsedTimeSeconds={0}, returning Awakening", ElapsedTimeSeconds);
        return static_cast<int32>(EAURACRONMapPhase::Awakening);
    }

    if (ElapsedTimeSeconds < 0.0f)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning, "GetMapPhaseFromTime: Negative ElapsedTimeSeconds={0}, using 0.0f", ElapsedTimeSeconds);
        ElapsedTimeSeconds = 0.0f;
    }

    // Determinação robusta da fase baseada no tempo (conforme GDD)
    EAURACRONMapPhase Phase;

    if (ElapsedTimeSeconds < FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS)
    {
        Phase = EAURACRONMapPhase::Awakening;
    }
    else if (ElapsedTimeSeconds < FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS +
             FAURACRONMapDimensions::PHASE_CONVERGENCE_DURATION_SECONDS)
    {
        Phase = EAURACRONMapPhase::Convergence;
    }
    else if (ElapsedTimeSeconds < FAURACRONMapDimensions::PHASE_RESOLUTION_START_SECONDS)
    {
        Phase = EAURACRONMapPhase::Intensification;
    }
    else
    {
        Phase = EAURACRONMapPhase::Resolution;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetMapPhaseFromTime: ElapsedTime={0}s -> Phase={1}",
        ElapsedTimeSeconds, static_cast<int32>(Phase));

    return static_cast<int32>(Phase);
}

float UAURACRONMapMeasurements::GetMapScaleFactorForPhase(int32 MapPhase)
{
    // Validação robusta da fase do mapa
    if (MapPhase < 0 || MapPhase > static_cast<int32>(EAURACRONMapPhase::Resolution))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetMapScaleFactorForPhase: Invalid MapPhase={0}, returning 1.0f", MapPhase);
        return 1.0f;
    }

    float ScaleFactor;

    switch (static_cast<EAURACRONMapPhase>(MapPhase))
    {
    case EAURACRONMapPhase::Awakening:
    case EAURACRONMapPhase::Convergence:
    case EAURACRONMapPhase::Intensification:
        ScaleFactor = 1.0f; // Tamanho normal
        break;

    case EAURACRONMapPhase::Resolution:
        ScaleFactor = FAURACRONMapDimensions::MAP_CONTRACTION_FACTOR; // Contração de 20%
        break;

    default:
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning, "GetMapScaleFactorForPhase: Unhandled MapPhase={0}, returning 1.0f", MapPhase);
        ScaleFactor = 1.0f;
        break;
    }

    // Validação do fator de escala
    if (!FMath::IsFinite(ScaleFactor) || ScaleFactor <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetMapScaleFactorForPhase: Invalid ScaleFactor={0}, returning 1.0f", ScaleFactor);
        return 1.0f;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetMapScaleFactorForPhase: Phase={0} -> ScaleFactor={1}", MapPhase, ScaleFactor);

    return ScaleFactor;
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE LANES E JUNGLE
// Baseadas em análise detalhada de League of Legends e Dota 2
// ========================================

TArray<FVector> UAURACRONMapMeasurements::GetTopLanePoints()
{
    TArray<FVector> LanePoints;

    // Top Lane: diagonal superior esquerda para inferior direita (layout exato do LoL)
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TOP_LANE_START_X,
        FAURACRONMapDimensions::TOP_LANE_START_Y,
        0.0f
    );
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TOP_LANE_END_X,
        FAURACRONMapDimensions::TOP_LANE_END_Y,
        0.0f
    );

    // Gerar pontos ao longo da lane (mais pontos para precisão)
    int32 NumPoints = 25;
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / NumPoints;
        FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
        LanePoints.Add(LanePoint);
    }

    return LanePoints;
}

TArray<FVector> UAURACRONMapMeasurements::GetMidLanePoints()
{
    TArray<FVector> LanePoints;

    // Mid Lane: diagonal inferior esquerda para superior direita (passando pelo centro como LoL)
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::MID_LANE_START_X,
        FAURACRONMapDimensions::MID_LANE_START_Y,
        0.0f
    );
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::MID_LANE_END_X,
        FAURACRONMapDimensions::MID_LANE_END_Y,
        0.0f
    );

    // Mid lane tem mais pontos por ser a mais longa e importante
    int32 NumPoints = 30;
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / NumPoints;
        FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
        LanePoints.Add(LanePoint);
    }

    return LanePoints;
}

TArray<FVector> UAURACRONMapMeasurements::GetBotLanePoints()
{
    TArray<FVector> LanePoints;

    // Bot Lane: diagonal inferior direita para superior esquerda (espelho da Top Lane)
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::BOT_LANE_START_X,
        FAURACRONMapDimensions::BOT_LANE_START_Y,
        0.0f
    );
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::BOT_LANE_END_X,
        FAURACRONMapDimensions::BOT_LANE_END_Y,
        0.0f
    );

    // Mesmo número de pontos que Top Lane (simetria)
    int32 NumPoints = 25;
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / NumPoints;
        FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
        LanePoints.Add(LanePoint);
    }

    return LanePoints;
}

TArray<FVector> UAURACRONMapMeasurements::GetJungleCampPositions()
{
    TArray<FVector> CampPositions;

    // BUFF CAMPS (equivalentes ao Blue/Red Buff do LoL)

    // Radiant Essence (Blue Buff equivalent) - Team 1 side
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::RADIANT_ESSENCE_X,
        FAURACRONMapDimensions::RADIANT_ESSENCE_Y,
        0.0f
    ));

    // Chaos Essence (Red Buff equivalent) - Team 2 side
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::CHAOS_ESSENCE_X,
        FAURACRONMapDimensions::CHAOS_ESSENCE_Y,
        0.0f
    ));

    // JUNGLE CAMPS NORMAIS (distribuição simétrica baseada no LoL)

    // Team 1 Jungle (lado inferior esquerdo)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-3500.0f, -1500.0f, 0.0f)); // Stone Guardians (Krugs)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-2800.0f, 800.0f, 0.0f));   // Prismal Toad (Gromp)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-1800.0f, -3200.0f, 0.0f)); // Spectral Pack (Wolves)

    // Team 2 Jungle (lado superior direito)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(3500.0f, 1500.0f, 0.0f));  // Stone Guardians (Krugs)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(2800.0f, -800.0f, 0.0f));  // Prismal Toad (Gromp)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(1800.0f, 3200.0f, 0.0f));  // Spectral Pack (Wolves)

    // Jungle Neutro (centro do mapa - disputado)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(-1200.0f, 2500.0f, 0.0f)); // Wind Spirits (Raptors)
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(1200.0f, -2500.0f, 0.0f)); // Wind Spirits (Raptors)

    // Flux Crawlers (Scuttle equivalents) - no Fluxo Prismal
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(0.0f, 3000.0f, 0.0f));  // Superior
    CampPositions.Add(FAURACRONMapDimensions::MAP_CENTER + FVector(0.0f, -3000.0f, 0.0f)); // Inferior

    return CampPositions;
}

TArray<FVector> UAURACRONMapMeasurements::GetTowerPositions(int32 LaneIndex)
{
    TArray<FVector> TowerPositions;

    // Obter pontos da lane correspondente
    TArray<FVector> LanePoints;
    switch (LaneIndex)
    {
    case 0: // Top Lane
        LanePoints = GetTopLanePoints();
        break;
    case 1: // Mid Lane
        LanePoints = GetMidLanePoints();
        break;
    case 2: // Bot Lane
        LanePoints = GetBotLanePoints();
        break;
    default:
        return TowerPositions; // Lane inválida
    }

    if (LanePoints.Num() < 4)
    {
        return TowerPositions;
    }

    // Posicionar torres ao longo da lane (baseado no LoL: 3 torres por lane)
    // Torre externa (25% da lane)
    int32 OuterTowerIndex = LanePoints.Num() / 4;
    TowerPositions.Add(LanePoints[OuterTowerIndex]);

    // Torre interna (50% da lane)
    int32 InnerTowerIndex = LanePoints.Num() / 2;
    TowerPositions.Add(LanePoints[InnerTowerIndex]);

    // Torre do nexus (75% da lane)
    int32 NexusTowerIndex = (LanePoints.Num() * 3) / 4;
    TowerPositions.Add(LanePoints[NexusTowerIndex]);

    return TowerPositions;
}

TArray<FVector> UAURACRONMapMeasurements::GetStrategicObjectivePositions()
{
    TArray<FVector> ObjectivePositions;

    // PRISMAL NEXUS (Baron equivalent) - objetivo principal superior
    FVector PrismalNexusPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::PRISMAL_NEXUS_X,
        FAURACRONMapDimensions::PRISMAL_NEXUS_Y,
        0.0f
    );
    ObjectivePositions.Add(PrismalNexusPosition);

    // ELEMENTAL ANCHORS (Dragon equivalents) - objetivos secundários

    // Radiant Anchor (Fire/Earth element)
    FVector RadiantAnchorPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::RADIANT_ANCHOR_X,
        FAURACRONMapDimensions::RADIANT_ANCHOR_Y,
        0.0f
    );
    ObjectivePositions.Add(RadiantAnchorPosition);

    // Zephyr Anchor (Air/Lightning element)
    FVector ZephyrAnchorPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::ZEPHYR_ANCHOR_X,
        FAURACRONMapDimensions::ZEPHYR_ANCHOR_Y,
        0.0f
    );
    ObjectivePositions.Add(ZephyrAnchorPosition);

    // Purgatory Anchor (Shadow/Spectral element)
    FVector PurgatoryAnchorPosition = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::PURGATORY_ANCHOR_X,
        FAURACRONMapDimensions::PURGATORY_ANCHOR_Y,
        0.0f
    );
    ObjectivePositions.Add(PurgatoryAnchorPosition);

    return ObjectivePositions;
}

TArray<FVector> UAURACRONMapMeasurements::GetBasePositions()
{
    TArray<FVector> BasePositions;

    // Bases posicionadas nos cantos opostos do mapa (layout exato do LoL)

    // Team 1 Base (canto inferior esquerdo)
    FVector Team1Base = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TEAM1_BASE_X,
        FAURACRONMapDimensions::TEAM1_BASE_Y,
        0.0f
    );
    BasePositions.Add(Team1Base);

    // Team 2 Base (canto superior direito)
    FVector Team2Base = FAURACRONMapDimensions::MAP_CENTER + FVector(
        FAURACRONMapDimensions::TEAM2_BASE_X,
        FAURACRONMapDimensions::TEAM2_BASE_Y,
        0.0f
    );
    BasePositions.Add(Team2Base);

    return BasePositions;
}

FVector UAURACRONMapMeasurements::GetLanePosition(int32 LaneIndex, float LaneProgress)
{
    // Obter pontos da lane correspondente
    TArray<FVector> LanePoints;
    switch (LaneIndex)
    {
    case 0: // Top Lane
        LanePoints = GetTopLanePoints();
        break;
    case 1: // Mid Lane
        LanePoints = GetMidLanePoints();
        break;
    case 2: // Bot Lane
        LanePoints = GetBotLanePoints();
        break;
    default:
        return FAURACRONMapDimensions::MAP_CENTER; // Lane inválida
    }

    if (LanePoints.Num() < 2)
    {
        return FAURACRONMapDimensions::MAP_CENTER;
    }

    // Clamp progress para [0, 1]
    LaneProgress = FMath::Clamp(LaneProgress, 0.0f, 1.0f);

    // Calcular índice no array de pontos
    float FloatIndex = LaneProgress * (LanePoints.Num() - 1);
    int32 Index1 = FMath::FloorToInt(FloatIndex);
    int32 Index2 = FMath::Min(Index1 + 1, LanePoints.Num() - 1);

    // Interpolar entre os dois pontos
    float Alpha = FloatIndex - Index1;
    return FMath::Lerp(LanePoints[Index1], LanePoints[Index2], Alpha);
}

bool UAURACRONMapMeasurements::IsPositionInLane(const FVector& Position, int32 LaneIndex, float Tolerance)
{
    // Obter pontos da lane correspondente
    TArray<FVector> LanePoints;
    switch (LaneIndex)
    {
    case 0: // Top Lane
        LanePoints = GetTopLanePoints();
        break;
    case 1: // Mid Lane
        LanePoints = GetMidLanePoints();
        break;
    case 2: // Bot Lane
        LanePoints = GetBotLanePoints();
        break;
    default:
        return false; // Lane inválida
    }

    // Verificar distância mínima para qualquer ponto da lane
    float MinDistance = MAX_FLT;
    for (const FVector& LanePoint : LanePoints)
    {
        float Distance = FVector::Dist2D(Position, LanePoint);
        MinDistance = FMath::Min(MinDistance, Distance);
    }

    return MinDistance <= Tolerance;
}

int32 UAURACRONMapMeasurements::GetClosestLane(const FVector& Position)
{
    float MinDistance = MAX_FLT;
    int32 ClosestLane = 0;

    // Verificar distância para cada lane
    for (int32 LaneIndex = 0; LaneIndex < 3; ++LaneIndex)
    {
        TArray<FVector> LanePoints;
        switch (LaneIndex)
        {
        case 0: // Top Lane
            LanePoints = GetTopLanePoints();
            break;
        case 1: // Mid Lane
            LanePoints = GetMidLanePoints();
            break;
        case 2: // Bot Lane
            LanePoints = GetBotLanePoints();
            break;
        }

        // Encontrar ponto mais próximo nesta lane
        for (const FVector& LanePoint : LanePoints)
        {
            float Distance = FVector::Dist2D(Position, LanePoint);
            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                ClosestLane = LaneIndex;
            }
        }
    }

    return ClosestLane;
}

FVector UAURACRONMapMeasurements::GetPurgatoryAnchorPosition()
{
    // Implementação robusta para obter posição do Purgatory Anchor
    // Baseado no GDD: posicionado estrategicamente no Reino Purgatório

    // Usar as constantes definidas no header para consistência
    FVector BasePosition = FAURACRONMapDimensions::MAP_CENTER;

    // Aplicar offset baseado nas constantes do GDD
    BasePosition.X += FAURACRONMapDimensions::PURGATORY_ANCHOR_X;
    BasePosition.Y += FAURACRONMapDimensions::PURGATORY_ANCHOR_Y;
    BasePosition.Z += FAURACRONMapDimensions::PURGATORY_DEPTH_OFFSET_CM; // Profundidade do Reino Purgatório

    // Validação robusta da posição calculada
    if (BasePosition.ContainsNaN())
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetPurgatoryAnchorPosition: Calculated position contains NaN, using fallback");
        BasePosition = FAURACRONMapDimensions::MAP_CENTER;
        BasePosition.Y -= 4500.0f; // Fallback: 45 metros ao sul do centro
        BasePosition.Z = FAURACRONMapDimensions::PURGATORY_DEPTH_OFFSET_CM;
    }

    // Verificar se a posição está dentro dos limites do mapa
    if (!IsPositionWithinMapBounds(BasePosition, FAURACRONMapDimensions::MAP_CENTER))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning,
            "GetPurgatoryAnchorPosition: Position outside map bounds, adjusting");

        // Ajustar para ficar dentro dos limites
        const float MaxDistance = FAURACRONMapDimensions::MAP_RADIUS_CM * 0.8f; // 80% do raio máximo
        const FVector Direction = (BasePosition - FAURACRONMapDimensions::MAP_CENTER).GetSafeNormal();
        BasePosition = FAURACRONMapDimensions::MAP_CENTER + Direction * MaxDistance;
        BasePosition.Z = FAURACRONMapDimensions::PURGATORY_DEPTH_OFFSET_CM;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetPurgatoryAnchorPosition: Position=({0}, {1}, {2})",
        BasePosition.X, BasePosition.Y, BasePosition.Z);

    return BasePosition;
}

UAURACRONMapMeasurements* UAURACRONMapMeasurements::GetInstance(const UWorld* World)
{
    // Implementação singleton robusta e thread-safe para UE 5.6
    if (!IsValid(World))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetInstance: Invalid World provided, returning nullptr");
        return nullptr;
    }

    // Verificar se o mundo está em um estado válido
    if (World->bIsTearingDown || World->HasBegunPlay() == false)
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Warning,
            "GetInstance: World is tearing down or hasn't begun play, returning CDO");
    }

    // Como UAURACRONMapMeasurements é uma UBlueprintFunctionLibrary,
    // retornar o CDO (Class Default Object) que funciona como singleton thread-safe
    UAURACRONMapMeasurements* Instance = GetMutableDefault<UAURACRONMapMeasurements>();

    if (!IsValid(Instance))
    {
        UE_LOGFMT(LogAURACRONMapMeasurements, Error, "GetInstance: Failed to get CDO instance, returning nullptr");
        return nullptr;
    }

    UE_LOGFMT(LogAURACRONMapMeasurements, VeryVerbose,
        "GetInstance: Successfully retrieved singleton instance for World={0}",
        World->GetName());

    return Instance;
}
