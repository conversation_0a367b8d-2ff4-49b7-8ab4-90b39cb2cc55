// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGChaosIslandManager.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Components/StaticMeshComponent.h"
#include "UObject/ConstructorHelpers.h"
#include "EngineUtils.h"
// Includes adicionais para APIs modernas UE 5.6 - NUNCA REMOVER
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "NiagaraComponent.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Net/UnrealNetwork.h"
#include "Engine/AssetManager.h"
#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"

AAURACRONPCGChaosIslandManager::AAURACRONPCGChaosIslandManager()
{
    PrimaryActorTick.bCanEverTick = true;
    // Otimização: Usar tick interval para performance
    PrimaryActorTick.TickInterval = 0.1f; // Tick a cada 0.1s ao invés de todo frame

    // Configurações padrão conforme GDD
    MaxChaosIslands = 4; // Número balanceado para o mapa
    MinDistanceBetweenIslands = 2000.0f; // Distância mínima em unidades UE
    bAutoSpawnPortals = true; // Auto-spawnar portais por padrão

    // Definir classes padrão - CORRIGIDO: usar AAURACRONPCGChaosIsland ao invés de AAURACRONPCGChaosIsland
    ChaosIslandClass = AAURACRONPCGChaosIsland::StaticClass();
    ChaosPortalClass = AAURACRONPCGChaosPortal::StaticClass();

    PrismalFlow = nullptr;

    // Inicializar propriedades modernas UE 5.6
    CurrentMapPhase = EAURACRONMapPhase::Awakening;

    // Configurar replicação para networking robusto
    bReplicates = true;
    bAlwaysRelevant = true;
    SetNetUpdateFrequency(10.0f); // 10 updates por segundo para performance

    // Inicializar StreamableManager para carregamento assíncrono
    StreamableManager = &UAssetManager::GetStreamableManager();

    // Inicializar componentes de áudio e VFX
    AudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("ChaosManagerAudio"));
    if (AudioComponent)
    {
        AudioComponent->bAutoActivate = false;
        AudioComponent->SetupAttachment(RootComponent);
    }

    // Componente de efeitos visuais globais
    GlobalVFXComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("GlobalChaosVFX"));
    if (GlobalVFXComponent)
    {
        GlobalVFXComponent->bAutoActivate = false;
        GlobalVFXComponent->SetupAttachment(RootComponent);
    }

    // Componente de iluminação ambiente
    AmbientLightComponent = CreateDefaultSubobject<UPointLightComponent>(TEXT("ChaosAmbientLight"));
    if (AmbientLightComponent)
    {
        AmbientLightComponent->SetLightColor(FLinearColor::Red);
        AmbientLightComponent->SetIntensity(0.5f);
        AmbientLightComponent->SetAttenuationRadius(5000.0f);
        AmbientLightComponent->SetupAttachment(RootComponent);
    }

    // Inicializar cache para performance
    CachedIntersectionPoints.Empty();
    LastIntersectionCalculationTime = 0.0f;
    IntersectionCacheValidDuration = 30.0f; // Cache válido por 30 segundos
}

void AAURACRONPCGChaosIslandManager::BeginPlay()
{
    Super::BeginPlay();

    // Se não foi inicializado manualmente, tentar encontrar o PrismalFlow na cena
    if (!PrismalFlow)
    {
        PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGPrismalFlow::StaticClass()));
        if (PrismalFlow)
        {
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: PrismalFlow encontrado automaticamente"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: PrismalFlow não encontrado na cena"));
        }
    }

    // Inicializar sistema de timers para substituir Tick onde possível
    if (UWorld* World = GetWorld())
    {
        // Timer para atualização periódica de efeitos (substitui parte do Tick)
        World->GetTimerManager().SetTimer(
            EffectsUpdateTimerHandle,
            this,
            &AAURACRONPCGChaosIslandManager::UpdateEffectsIntensity,
            5.0f, // A cada 5 segundos
            true  // Repetir
        );

        // Timer para limpeza de cache
        World->GetTimerManager().SetTimer(
            CacheCleanupTimerHandle,
            this,
            &AAURACRONPCGChaosIslandManager::CleanupCache,
            60.0f, // A cada 1 minuto
            true   // Repetir
        );

        // Timer para validação de networking
        if (HasAuthority())
        {
            World->GetTimerManager().SetTimer(
                NetworkValidationTimerHandle,
                this,
                &AAURACRONPCGChaosIslandManager::ValidateNetworkState,
                10.0f, // A cada 10 segundos
                true   // Repetir
            );
        }
    }

    // Inicializar componentes de áudio e VFX
    InitializeAudioSystem();
    InitializeVFXSystem();

    // Carregar assets assincronamente
    LoadRequiredAssetsAsync();
}

void AAURACRONPCGChaosIslandManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // IMPLEMENTAÇÃO ROBUSTA: Substituir placeholder por lógica real
    // Atualizar comportamento das ilhas caos com validações robustas
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Verificar se a ilha precisa de atualização baseada no estado atual
            if (Island->bIsActive)
            {
                // Atualizar intensidade baseada na fase do mapa
                float PhaseMultiplier = GetPhaseIntensityMultiplier(CurrentMapPhase);

                // Aplicar variação temporal para efeitos dinâmicos
                float TimeVariation = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.5f) * 0.2f + 1.0f;
                float FinalIntensity = PhaseMultiplier * TimeVariation;

                // Atualizar propriedades da ilha se necessário
                if (FMath::Abs(Island->EnvironmentalHazardIntensity - FinalIntensity) > 0.1f)
                {
                    Island->EnvironmentalHazardIntensity = FinalIntensity;

                    // Forçar atualização visual apenas quando necessário
                    Island->UpdateIslandVisuals();
                }

                // Verificar proximidade de jogadores para otimização
                bool bPlayersNearby = CheckPlayersNearIsland(Island, 3000.0f);
                if (bPlayersNearby != Island->IsActorTickEnabled())
                {
                    // Otimização: Desabilitar tick quando não há jogadores próximos
                    Island->SetActorTickEnabled(bPlayersNearby);
                }
            }
        }
    }

    // Atualizar sistema de áudio ambiente
    UpdateAmbientAudio(DeltaTime);

    // Atualizar efeitos visuais globais
    UpdateGlobalVFX(DeltaTime);

    // Validar integridade do sistema periodicamente
    static float ValidationTimer = 0.0f;
    ValidationTimer += DeltaTime;
    if (ValidationTimer >= 30.0f) // A cada 30 segundos
    {
        ValidateSystemIntegrity();
        ValidationTimer = 0.0f;
    }
}

void AAURACRONPCGChaosIslandManager::Initialize(AAURACRONPCGPrismalFlow* InPrismalFlow)
{
    if (!InPrismalFlow)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager::Initialize - PrismalFlow inválido"));
        return;
    }
    
    PrismalFlow = InPrismalFlow;
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Inicializado com PrismalFlow"));
}

void AAURACRONPCGChaosIslandManager::GenerateChaosIslands()
{
    if (!PrismalFlow)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - PrismalFlow não definido"));
        return;
    }

    // Validação de autoridade para networking
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Tentativa de gerar ilhas sem autoridade"));
        return;
    }

    // Limpar ilhas existentes com cleanup robusto
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Cleanup robusto antes de destruir
            Island->SetActorTickEnabled(false);
            Island->bIsActive = false;

            // Remover da replicação
            Island->SetReplicates(false);

            // Destruir com delay para evitar problemas de networking
            Island->SetLifeSpan(0.1f);
        }
    }
    ChaosIslands.Empty();

    // Limpar portais existentes com cleanup robusto
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            // Cleanup robusto antes de destruir
            Portal->SetActorTickEnabled(false);
            Portal->DeactivatePortal();

            // Remover da replicação
            Portal->SetReplicates(false);

            // Destruir com delay para evitar problemas de networking
            Portal->SetLifeSpan(0.1f);
        }
    }
    ChaosPortals.Empty();

    // Encontrar pontos de interseção do fluxo com cache para performance
    TArray<FVector> IntersectionPoints = GetCachedIntersectionPoints();

    if (IntersectionPoints.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Nenhum ponto de interseção encontrado"));
        return;
    }

    // Limitar ao número máximo de ilhas
    int32 IslandsToCreate = FMath::Min(IntersectionPoints.Num(), MaxChaosIslands);

    // Gerar ilhas nos pontos de interseção com spawn assíncrono
    for (int32 i = 0; i < IslandsToCreate; i++)
    {
        FVector SpawnLocation = IntersectionPoints[i];

        // Verificar se a posição está válida e não muito próxima de outras ilhas
        bool bValidPosition = true;
        for (const AAURACRONPCGChaosIsland* ExistingIsland : ChaosIslands)
        {
            if (ExistingIsland && FVector::Dist(SpawnLocation, ExistingIsland->GetActorLocation()) < MinDistanceBetweenIslands)
            {
                bValidPosition = false;
                break;
            }
        }

        if (!bValidPosition)
        {
            continue;
        }

        // Spawnar ilha caos com parâmetros robustos
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        SpawnParams.bNoFail = true;
        SpawnParams.Owner = this;
        SpawnParams.Instigator = GetInstigator();

        // CORRIGIDO: Usar AAURACRONPCGChaosIsland ao invés de AAURACRONPCGChaosIsland
        AAURACRONPCGChaosIsland* NewIsland = GetWorld()->SpawnActor<AAURACRONPCGChaosIsland>(ChaosIslandClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams);
        if (NewIsland)
        {
            ChaosIslands.Add(NewIsland);

            // Configurar a ilha conforme especificações do GDD
            NewIsland->SetIslandType(EPrismalFlowIslandType::Chaos);

            // Configurar propriedades baseadas na fase atual
            ConfigureIslandForCurrentPhase(NewIsland);

            // Configurar networking
            if (HasAuthority())
            {
                NewIsland->SetReplicates(true);
                NewIsland->SetReplicateMovement(false); // Ilhas não se movem
            }

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Ilha Caos criada em %s"), *SpawnLocation.ToString());

            // Criar portal caos associado se configurado
            if (bAutoSpawnPortals)
            {
                FVector PortalLocation = SpawnLocation + FVector(0, 0, 200);
                AAURACRONPCGChaosPortal* NewPortal = GetWorld()->SpawnActor<AAURACRONPCGChaosPortal>(ChaosPortalClass, PortalLocation, FRotator::ZeroRotator, SpawnParams);
                if (NewPortal)
                {
                    ChaosPortals.Add(NewPortal);

                    // Configurar portal
                    NewPortal->UpdateForMapPhase(CurrentMapPhase);

                    // Configurar networking
                    if (HasAuthority())
                    {
                        NewPortal->SetReplicates(true);
                        NewPortal->SetReplicateMovement(false);
                    }

                    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Portal Caos criado para ilha em %s"), *SpawnLocation.ToString());
                }
            }
        }
    }

    // Replicar mudanças para clientes
    if (HasAuthority())
    {
        OnRep_ChaosIslandsUpdated();
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Geradas %d ilhas caos"), ChaosIslands.Num());
}

void AAURACRONPCGChaosIslandManager::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Atualizar fase atual
    CurrentMapPhase = MapPhase;

    // Atualizar comportamento das ilhas baseado na fase do mapa
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Implementar lógica específica por fase com valores robustos
            switch (MapPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    // Fase inicial - ilhas menos ativas
                    Island->SetActivityLevel(0.3f);
                    Island->EnvironmentalHazardIntensity = 0.8f;
                    Island->TerrainInstabilityIntensity = 0.6f;
                    Island->HighRiskRewardMultiplier = 1.2f;
                    Island->VortexRotationSpeed = 30.0f;
                    Island->RunePulseIntensity = 0.5f;
                    break;

                case EAURACRONMapPhase::Convergence:
                    // Fase de convergência - atividade moderada
                    Island->SetActivityLevel(0.6f);
                    Island->EnvironmentalHazardIntensity = 1.2f;
                    Island->TerrainInstabilityIntensity = 1.0f;
                    Island->HighRiskRewardMultiplier = 1.5f;
                    Island->VortexRotationSpeed = 50.0f;
                    Island->RunePulseIntensity = 0.8f;
                    break;

                case EAURACRONMapPhase::Intensification:
                    // Fase de intensificação - alta atividade
                    Island->SetActivityLevel(0.9f);
                    Island->EnvironmentalHazardIntensity = 1.6f;
                    Island->TerrainInstabilityIntensity = 1.4f;
                    Island->HighRiskRewardMultiplier = 1.8f;
                    Island->VortexRotationSpeed = 75.0f;
                    Island->RunePulseIntensity = 1.2f;
                    break;

                case EAURACRONMapPhase::Resolution:
                    // Fase de resolução - máxima atividade
                    Island->SetActivityLevel(1.0f);
                    Island->EnvironmentalHazardIntensity = 2.0f;
                    Island->TerrainInstabilityIntensity = 1.8f;
                    Island->HighRiskRewardMultiplier = 2.2f;
                    Island->VortexRotationSpeed = 100.0f;
                    Island->RunePulseIntensity = 1.5f;
                    break;

                case EAURACRONMapPhase::Expansion:
                    // Fase de expansão - atividade crescente
                    Island->SetActivityLevel(0.45f);
                    Island->EnvironmentalHazardIntensity = 1.0f;
                    Island->TerrainInstabilityIntensity = 0.8f;
                    Island->HighRiskRewardMultiplier = 1.35f;
                    Island->VortexRotationSpeed = 40.0f;
                    Island->RunePulseIntensity = 0.65f;
                    break;
            }

            // Forçar atualização visual
            Island->UpdateIslandVisuals();
        }
    }

    // Atualizar portais também
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->UpdateForMapPhase(MapPhase);
        }
    }

    // Atualizar efeitos globais baseado na fase
    UpdateGlobalEffectsForPhase(MapPhase);

    // Replicar mudança de fase para clientes
    if (HasAuthority())
    {
        OnRep_MapPhaseChanged();
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Atualizado para fase %d com %d ilhas"),
        (int32)MapPhase, ChaosIslands.Num());
}

bool AAURACRONPCGChaosIslandManager::IsPointAtFlowIntersection(const FVector& Point, float Tolerance) const
{
    if (!PrismalFlow)
    {
        return false;
    }
    
    // Verificar se o ponto está próximo de uma interseção do fluxo
    TArray<FVector> IntersectionPoints = FindAllFlowIntersections();
    
    for (const FVector& IntersectionPoint : IntersectionPoints)
    {
        if (FVector::Dist(Point, IntersectionPoint) <= Tolerance)
        {
            return true;
        }
    }
    
    return false;
}

TArray<FVector> AAURACRONPCGChaosIslandManager::FindAllFlowIntersections() const
{
    TArray<FVector> IntersectionPoints;
    
    if (!PrismalFlow)
    {
        return IntersectionPoints;
    }
    
    // Obter pontos de controle do fluxo prismal
    TArray<FVector> FlowControlPoints = PrismalFlow->GetFlowControlPoints();
    
    if (FlowControlPoints.Num() < 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::FindAllFlowIntersections - Pontos de controle insuficientes"));
        return IntersectionPoints;
    }
    
    // Algoritmo para encontrar interseções entre segmentos do fluxo
    for (int32 i = 0; i < FlowControlPoints.Num() - 1; i++)
    {
        for (int32 j = i + 2; j < FlowControlPoints.Num() - 1; j++)
        {
            // Evitar segmentos adjacentes
            if (FMath::Abs(i - j) <= 1)
            {
                continue;
            }
            
            FVector P1 = FlowControlPoints[i];
            FVector P2 = FlowControlPoints[i + 1];
            FVector P3 = FlowControlPoints[j];
            FVector P4 = FlowControlPoints[j + 1];
            
            // Calcular interseção entre os segmentos P1-P2 e P3-P4
            FVector IntersectionPoint;
            if (CalculateLineIntersection(P1, P2, P3, P4, IntersectionPoint))
            {
                IntersectionPoints.Add(IntersectionPoint);
            }
        }
    }
    
    // Adicionar pontos de convergência principais (centro do mapa, etc.)
    if (FlowControlPoints.Num() > 0)
    {
        FVector CenterPoint = FVector::ZeroVector;
        for (const FVector& Point : FlowControlPoints)
        {
            CenterPoint += Point;
        }
        CenterPoint /= FlowControlPoints.Num();
        IntersectionPoints.Add(CenterPoint);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::FindAllFlowIntersections - Encontrados %d pontos de interseção"), IntersectionPoints.Num());
    
    return IntersectionPoints;
}

void AAURACRONPCGChaosIslandManager::SetAllChaosIslandsActive(bool bActive)
{
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->SetActorHiddenInGame(!bActive);
            Island->SetActorEnableCollision(bActive);
            Island->SetActorTickEnabled(bActive);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::SetAllChaosIslandsActive - Ilhas %s"), bActive ? TEXT("ativadas") : TEXT("desativadas"));
}

void AAURACRONPCGChaosIslandManager::SetAllChaosPortalsActive(bool bActive, float Duration, float Intensity)
{
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            if (bActive)
            {
                Portal->ActivatePortal(Duration, Intensity);
            }
            else
            {
                Portal->DeactivatePortal();
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::SetAllChaosPortalsActive - Portais %s"), bActive ? TEXT("ativados") : TEXT("desativados"));
}

bool AAURACRONPCGChaosIslandManager::CalculateLineIntersection(const FVector& P1, const FVector& P2, const FVector& P3, const FVector& P4, FVector& OutIntersection) const
{
    // Calcular interseção entre duas linhas em 2D (ignorando Z)
    float X1 = P1.X, Y1 = P1.Y;
    float X2 = P2.X, Y2 = P2.Y;
    float X3 = P3.X, Y3 = P3.Y;
    float X4 = P4.X, Y4 = P4.Y;
    
    float Denominator = (X1 - X2) * (Y3 - Y4) - (Y1 - Y2) * (X3 - X4);
    
    if (FMath::IsNearlyZero(Denominator))
    {
        // Linhas paralelas
        return false;
    }
    
    float T = ((X1 - X3) * (Y3 - Y4) - (Y1 - Y3) * (X3 - X4)) / Denominator;
    float U = -((X1 - X2) * (Y1 - Y3) - (Y1 - Y2) * (X1 - X3)) / Denominator;
    
    // Verificar se a interseção está dentro dos segmentos
    if (T >= 0.0f && T <= 1.0f && U >= 0.0f && U <= 1.0f)
    {
        OutIntersection.X = X1 + T * (X2 - X1);
        OutIntersection.Y = Y1 + T * (Y2 - Y1);
        OutIntersection.Z = (P1.Z + P2.Z + P3.Z + P4.Z) / 4.0f; // Média das alturas
        
        return true;
    }
    
    return false;
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

AAURACRONPCGChaosIsland* AAURACRONPCGChaosIslandManager::SpawnChaosIsland(const FVector& Location)
{
    if (!ChaosIslandClass)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: ChaosIslandClass is not set!"));
        return nullptr;
    }

    // Verificar se o ponto não está muito próximo de ilhas existentes
    if (IsPointTooCloseToExistingIslands(Location))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: Cannot spawn Chaos Island at %s - too close to existing islands"),
            *Location.ToString());
        return nullptr;
    }

    // Ajustar posição baseado no terreno usando line trace
    FVector AdjustedLocation = Location;
    FVector TraceStart = Location + FVector(0, 0, 2000);
    FVector TraceEnd = Location - FVector(0, 0, 2000);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.AddIgnoredActor(this);

    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
    {
        AdjustedLocation = HitResult.Location + FVector(0, 0, 100); // Elevar 1 metro acima do terreno
    }

    // Configurar parâmetros de spawn usando APIs modernas
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();

    // Spawnar a ilha caos
    AAURACRONPCGChaosIsland* NewChaosIsland = GetWorld()->SpawnActor<AAURACRONPCGChaosIsland>(
        ChaosIslandClass,
        AdjustedLocation,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (NewChaosIsland)
    {
        // Configurar propriedades da ilha baseado no estado atual do manager
        NewChaosIsland->SetActivityLevel(0.5f + (ChaosIslands.Num() * 0.1f)); // Aumentar atividade com mais ilhas

        // Configurar intensidades baseado na fase atual do mapa
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                NewChaosIsland->EnvironmentalHazardIntensity = 0.8f;
                NewChaosIsland->TerrainInstabilityIntensity = 0.6f;
                NewChaosIsland->HighRiskRewardMultiplier = 1.2f;
                break;

            case EAURACRONMapPhase::Convergence:
                NewChaosIsland->EnvironmentalHazardIntensity = 1.2f;
                NewChaosIsland->TerrainInstabilityIntensity = 1.0f;
                NewChaosIsland->HighRiskRewardMultiplier = 1.5f;
                break;

            case EAURACRONMapPhase::Intensification:
                NewChaosIsland->EnvironmentalHazardIntensity = 1.6f;
                NewChaosIsland->TerrainInstabilityIntensity = 1.4f;
                NewChaosIsland->HighRiskRewardMultiplier = 1.8f;
                break;

            case EAURACRONMapPhase::Resolution:
                NewChaosIsland->EnvironmentalHazardIntensity = 2.0f;
                NewChaosIsland->TerrainInstabilityIntensity = 1.8f;
                NewChaosIsland->HighRiskRewardMultiplier = 2.2f;
                break;
        }

        // Configurar ambientes de transição baseado na localização
        if (AdjustedLocation.X > 0 && AdjustedLocation.Y > 0)
        {
            NewChaosIsland->TransitionEnvironments.Add(EAURACRONEnvironmentType::RadiantPlains);
        }
        else if (AdjustedLocation.X < 0 && AdjustedLocation.Y > 0)
        {
            NewChaosIsland->TransitionEnvironments.Add(EAURACRONEnvironmentType::ZephyrFirmament);
        }
        else
        {
            NewChaosIsland->TransitionEnvironments.Add(EAURACRONEnvironmentType::PurgatoryRealm);
        }

        // Adicionar à lista de ilhas gerenciadas
        ChaosIslands.Add(NewChaosIsland);

        // Configurar networking se necessário
        if (HasAuthority())
        {
            NewChaosIsland->SetReplicates(true);
            NewChaosIsland->SetReplicateMovement(false); // Ilhas não se movem
        }

        // Ativar a ilha
        NewChaosIsland->bIsActive = true;

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Successfully spawned Chaos Island at %s (Total islands: %d)"),
            *AdjustedLocation.ToString(), ChaosIslands.Num());

        // Spawnar portal associado se configurado
        if (bAutoSpawnPortals)
        {
            FVector PortalLocation = AdjustedLocation + FVector(
                FMath::RandRange(-500.0f, 500.0f),
                FMath::RandRange(-500.0f, 500.0f),
                50.0f
            );

            SpawnChaosPortal(PortalLocation);
        }

        return NewChaosIsland;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: Failed to spawn Chaos Island at %s"),
            *AdjustedLocation.ToString());
        return nullptr;
    }
}

AAURACRONPCGChaosPortal* AAURACRONPCGChaosIslandManager::SpawnChaosPortal(const FVector& Location)
{
    if (!ChaosPortalClass)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: ChaosPortalClass is not set!"));
        return nullptr;
    }

    // Ajustar posição baseado no terreno
    FVector AdjustedLocation = Location;
    FVector TraceStart = Location + FVector(0, 0, 1000);
    FVector TraceEnd = Location - FVector(0, 0, 1000);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.AddIgnoredActor(this);

    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
    {
        AdjustedLocation = HitResult.Location + FVector(0, 0, 50); // Elevar meio metro acima do terreno
    }

    // Configurar parâmetros de spawn
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();

    // Spawnar o portal caos
    AAURACRONPCGChaosPortal* NewChaosPortal = GetWorld()->SpawnActor<AAURACRONPCGChaosPortal>(
        ChaosPortalClass,
        AdjustedLocation,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (NewChaosPortal)
    {
        // Determinar tipo do portal baseado no número de portais existentes e fase do mapa
        EChaosPortalType PortalType = EChaosPortalType::Standard;

        int32 ExistingPortals = ChaosPortals.Num();
        float RandomValue = FMath::RandRange(0.0f, 1.0f);

        // Probabilidades baseadas na fase do mapa
        float EliteChance = 0.2f;
        float LegendaryChance = 0.05f;

        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                EliteChance = 0.15f;
                LegendaryChance = 0.02f;
                break;

            case EAURACRONMapPhase::Convergence:
                EliteChance = 0.25f;
                LegendaryChance = 0.08f;
                break;

            case EAURACRONMapPhase::Intensification:
                EliteChance = 0.35f;
                LegendaryChance = 0.15f;
                break;

            case EAURACRONMapPhase::Resolution:
                EliteChance = 0.45f;
                LegendaryChance = 0.25f;
                break;
        }

        // Aumentar chances com mais portais existentes
        EliteChance += ExistingPortals * 0.05f;
        LegendaryChance += ExistingPortals * 0.02f;

        // Determinar tipo
        if (RandomValue < LegendaryChance)
        {
            PortalType = EChaosPortalType::Legendary;
        }
        else if (RandomValue < LegendaryChance + EliteChance)
        {
            PortalType = EChaosPortalType::Elite;
        }

        // Configurar o portal
        NewChaosPortal->SetPortalType(PortalType);
        NewChaosPortal->UpdateForMapPhase(CurrentMapPhase);

        // Configurar duração baseada no tipo e fase
        float PortalDuration = 0.0f; // 0 = permanente por padrão

        if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
        {
            // Na fase de resolução, portais têm duração limitada para criar urgência
            switch (PortalType)
            {
                case EChaosPortalType::Standard:
                    PortalDuration = 300.0f; // 5 minutos
                    break;
                case EChaosPortalType::Elite:
                    PortalDuration = 240.0f; // 4 minutos
                    break;
                case EChaosPortalType::Legendary:
                    PortalDuration = 180.0f; // 3 minutos
                    break;
            }
        }

        // Ativar o portal
        NewChaosPortal->ActivatePortal(PortalDuration, 1.0f);

        // Adicionar à lista de portais gerenciados
        ChaosPortals.Add(NewChaosPortal);

        // Configurar networking
        if (HasAuthority())
        {
            NewChaosPortal->SetReplicates(true);
            NewChaosPortal->SetReplicateMovement(false);
        }

        // Log do spawn
        FString TypeName;
        switch (PortalType)
        {
            case EChaosPortalType::Standard: TypeName = TEXT("Standard"); break;
            case EChaosPortalType::Elite: TypeName = TEXT("Elite"); break;
            case EChaosPortalType::Legendary: TypeName = TEXT("Legendary"); break;
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Successfully spawned %s Chaos Portal at %s (Total portals: %d)"),
            *TypeName, *AdjustedLocation.ToString(), ChaosPortals.Num());

        return NewChaosPortal;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: Failed to spawn Chaos Portal at %s"),
            *AdjustedLocation.ToString());
        return nullptr;
    }
}

bool AAURACRONPCGChaosIslandManager::IsPointTooCloseToExistingIslands(const FVector& Point) const
{
    // Distância mínima entre ilhas (em unidades do UE)
    float MinDistance = 2000.0f; // 20 metros

    // Verificar distância para todas as ilhas caos existentes
    for (const AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            float Distance = FVector::Dist(Point, Island->GetActorLocation());
            if (Distance < MinDistance)
            {
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Point %s is too close to existing Chaos Island (distance: %.2f, min: %.2f)"),
                    *Point.ToString(), Distance, MinDistance);
                return true;
            }
        }
    }

    // Verificar distância para portais também
    float MinPortalDistance = 1000.0f; // 10 metros
    for (const AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            float Distance = FVector::Dist(Point, Portal->GetActorLocation());
            if (Distance < MinPortalDistance)
            {
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Point %s is too close to existing Chaos Portal (distance: %.2f, min: %.2f)"),
                    *Point.ToString(), Distance, MinPortalDistance);
                return true;
            }
        }
    }

    // Verificar proximidade com outras ilhas importantes no mundo
    if (UWorld* World = GetWorld())
    {
        // Buscar outras ilhas no mundo (Sanctuary, Arsenal, etc.)
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor != this)
            {
                // Verificar se é uma ilha (baseado no nome da classe)
                FString ClassName = Actor->GetClass()->GetName();
                if (ClassName.Contains(TEXT("Island")) || ClassName.Contains(TEXT("Sanctuary")) || ClassName.Contains(TEXT("Arsenal")))
                {
                    float Distance = FVector::Dist(Point, Actor->GetActorLocation());
                    if (Distance < MinDistance)
                    {
                        UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Point %s is too close to existing island %s (distance: %.2f)"),
                            *Point.ToString(), *ClassName, Distance);
                        return true;
                    }
                }
            }
        }
    }

    return false;
}

void AAURACRONPCGChaosIslandManager::UpdateEffectsIntensity()
{
    // Calcular intensidade base baseada na fase do mapa
    float BaseIntensity = 1.0f;
    float IntensityMultiplier = 1.0f;

    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            BaseIntensity = 0.6f;
            IntensityMultiplier = 1.0f;
            break;

        case EAURACRONMapPhase::Convergence:
            BaseIntensity = 0.8f;
            IntensityMultiplier = 1.2f;
            break;

        case EAURACRONMapPhase::Intensification:
            BaseIntensity = 1.2f;
            IntensityMultiplier = 1.5f;
            break;

        case EAURACRONMapPhase::Resolution:
            BaseIntensity = 1.5f;
            IntensityMultiplier = 2.0f;
            break;
    }

    // Modificar intensidade baseado no número de ilhas ativas
    int32 ActiveIslands = 0;
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island) && Island->bIsActive)
        {
            ActiveIslands++;
        }
    }

    // Mais ilhas = mais caos = mais intensidade
    float IslandMultiplier = 1.0f + (ActiveIslands * 0.15f);
    float FinalIntensity = BaseIntensity * IntensityMultiplier * IslandMultiplier;

    // Atualizar todas as ilhas caos
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Aplicar intensidade com variação aleatória para cada ilha
            float IslandVariation = FMath::RandRange(0.8f, 1.2f);
            float IslandIntensity = FinalIntensity * IslandVariation;

            // Atualizar propriedades da ilha
            Island->EnvironmentalHazardIntensity = FMath::Clamp(IslandIntensity * 1.0f, 0.1f, 3.0f);
            Island->TerrainInstabilityIntensity = FMath::Clamp(IslandIntensity * 0.8f, 0.1f, 2.5f);
            Island->HighRiskRewardMultiplier = FMath::Clamp(1.0f + (IslandIntensity * 0.5f), 1.0f, 3.0f);

            // Atualizar velocidades de efeitos
            Island->VortexRotationSpeed = FMath::Clamp(30.0f + (IslandIntensity * 20.0f), 10.0f, 120.0f);
            Island->RunePulseIntensity = FMath::Clamp(IslandIntensity * 0.7f, 0.1f, 2.0f);

            // Forçar atualização visual
            Island->UpdateIslandVisuals();

            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Updated island %s with intensity %.2f"),
                *Island->GetName(), IslandIntensity);
        }
    }

    // Atualizar todos os portais caos
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            // Aplicar intensidade aos portais
            float PortalVariation = FMath::RandRange(0.9f, 1.1f);
            float PortalIntensity = FinalIntensity * PortalVariation;

            Portal->SetPortalIntensity(FMath::Clamp(PortalIntensity, 0.5f, 3.0f));

            // Atualizar probabilidades baseado na intensidade
            float ProbabilityMultiplier = FMath::Clamp(PortalIntensity, 0.5f, 2.0f);

            // Acessar propriedades através de reflexão ou métodos públicos se disponíveis
            // Como as propriedades são protected, vamos usar uma abordagem indireta
            Portal->UpdateForMapPhase(CurrentMapPhase);

            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Updated portal %s with intensity %.2f"),
                *Portal->GetName(), PortalIntensity);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Updated effects intensity - Base: %.2f, Multiplier: %.2f, Islands: %d, Final: %.2f"),
        BaseIntensity, IntensityMultiplier, ActiveIslands, FinalIntensity);
}

// ========================================
// IMPLEMENTAÇÕES DAS NOVAS FUNÇÕES ROBUSTAS - UE 5.6 APIs MODERNAS
// ========================================

void AAURACRONPCGChaosIslandManager::InitializeAudioSystem()
{
    if (!AudioComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: AudioComponent não inicializado"));
        return;
    }

    // Carregar som ambiente assincronamente
    if (StreamableManager)
    {
        FSoftObjectPath AmbientSoundPath(TEXT("/Game/Audio/Chaos/ChaosAmbient_Cue"));
        StreamableManager->RequestAsyncLoad(
            AmbientSoundPath,
            FStreamableDelegate::CreateUObject(this, &AAURACRONPCGChaosIslandManager::OnAmbientSoundLoaded, AmbientSoundPath)
        );
    }

    // Configurar propriedades de áudio
    AudioComponent->SetVolumeMultiplier(0.7f);
    AudioComponent->SetPitchMultiplier(1.0f);
    AudioComponent->bAutoActivate = false;

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Sistema de áudio inicializado"));
}

void AAURACRONPCGChaosIslandManager::InitializeVFXSystem()
{
    if (!GlobalVFXComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: GlobalVFXComponent não inicializado"));
        return;
    }

    // Carregar sistema Niagara assincronamente
    if (StreamableManager)
    {
        FSoftObjectPath VFXSystemPath(TEXT("/Game/VFX/Chaos/ChaosGlobalEffect_NS"));
        UAssetManager::GetStreamableManager().RequestAsyncLoad(
            VFXSystemPath,
            FStreamableDelegate::CreateUObject(this, &AAURACRONPCGChaosIslandManager::OnGlobalVFXLoaded, VFXSystemPath)
        );
    }

    // Configurar propriedades de VFX
    GlobalVFXComponent->bAutoActivate = false;
    GlobalVFXComponent->SetComponentTickEnabled(true);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Sistema de VFX inicializado"));
}

void AAURACRONPCGChaosIslandManager::LoadRequiredAssetsAsync()
{
    if (!StreamableManager)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: StreamableManager não disponível"));
        return;
    }

    // Lista de assets para carregar assincronamente
    TArray<FSoftObjectPath> AssetsToLoad;

    // Assets de áudio
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Audio/Chaos/ChaosPortalOpen_Cue")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Audio/Chaos/ChaosPortalClose_Cue")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Audio/Chaos/ChaosIslandActivate_Cue")));

    // Assets de VFX
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Chaos/ChaosPortalSpawn_NS")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Chaos/ChaosIslandSpawn_NS")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Chaos/ChaosEnergyPulse_NS")));

    // Assets de materiais
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Chaos/ChaosMaterial_MI")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Chaos/ChaosPortalMaterial_MI")));

    // Carregar todos os assets assincronamente
    StreamableManager->RequestAsyncLoad(
        AssetsToLoad,
        FStreamableDelegate::CreateUObject(this, &AAURACRONPCGChaosIslandManager::OnRequiredAssetsLoaded)
    );

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Iniciado carregamento assíncrono de %d assets"), AssetsToLoad.Num());
}

float AAURACRONPCGChaosIslandManager::GetPhaseIntensityMultiplier(EAURACRONMapPhase Phase) const
{
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return 0.6f;
        case EAURACRONMapPhase::Expansion:
            return 0.8f;
        case EAURACRONMapPhase::Convergence:
            return 1.0f;
        case EAURACRONMapPhase::Intensification:
            return 1.4f;
        case EAURACRONMapPhase::Resolution:
            return 1.8f;
        default:
            return 1.0f;
    }
}

bool AAURACRONPCGChaosIslandManager::CheckPlayersNearIsland(AAURACRONPCGChaosIsland* Island, float Distance) const
{
    if (!Island || !GetWorld())
    {
        return false;
    }

    // Buscar jogadores próximos usando APIs modernas
    FVector IslandLocation = Island->GetActorLocation();

    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            float DistanceToPlayer = FVector::Dist(IslandLocation, PC->GetPawn()->GetActorLocation());
            if (DistanceToPlayer <= Distance)
            {
                return true;
            }
        }
    }

    return false;
}

void AAURACRONPCGChaosIslandManager::UpdateAmbientAudio(float DeltaTime)
{
    if (!AudioComponent || !AudioComponent->IsValidLowLevel())
    {
        return;
    }

    // Calcular intensidade baseada no número de ilhas ativas
    int32 ActiveIslands = 0;
    for (const AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island) && Island->bIsActive)
        {
            ActiveIslands++;
        }
    }

    // Ajustar volume baseado na atividade
    float TargetVolume = FMath::Clamp(0.3f + (ActiveIslands * 0.2f), 0.0f, 1.0f);
    float CurrentVolume = AudioComponent->VolumeMultiplier;
    float NewVolume = FMath::FInterpTo(CurrentVolume, TargetVolume, DeltaTime, 2.0f);

    AudioComponent->SetVolumeMultiplier(NewVolume);

    // Ajustar pitch baseado na fase do mapa
    float TargetPitch = GetPhaseIntensityMultiplier(CurrentMapPhase);
    float CurrentPitch = AudioComponent->PitchMultiplier;
    float NewPitch = FMath::FInterpTo(CurrentPitch, TargetPitch, DeltaTime, 1.0f);

    AudioComponent->SetPitchMultiplier(NewPitch);
}

void AAURACRONPCGChaosIslandManager::UpdateGlobalVFX(float DeltaTime)
{
    if (!GlobalVFXComponent || !GlobalVFXComponent->IsValidLowLevel())
    {
        return;
    }

    // Atualizar parâmetros do sistema Niagara baseado no estado atual
    float PhaseIntensity = GetPhaseIntensityMultiplier(CurrentMapPhase);
    float IslandCount = static_cast<float>(ChaosIslands.Num());

    // Configurar parâmetros usando APIs modernas do UE 5.6
    GlobalVFXComponent->SetFloatParameter(FName("PhaseIntensity"), PhaseIntensity);
    GlobalVFXComponent->SetFloatParameter(FName("IslandCount"), IslandCount);
    GlobalVFXComponent->SetFloatParameter(FName("TimeMultiplier"), GetWorld()->GetTimeSeconds() * 0.1f);

    // Atualizar cor baseada na fase
    FLinearColor PhaseColor = GetPhaseColor(CurrentMapPhase);
    GlobalVFXComponent->SetColorParameter(FName("PhaseColor"), PhaseColor);
}

void AAURACRONPCGChaosIslandManager::ValidateSystemIntegrity()
{
    // Validar integridade das ilhas
    for (int32 i = ChaosIslands.Num() - 1; i >= 0; i--)
    {
        AAURACRONPCGChaosIsland* Island = ChaosIslands[i];
        if (!Island || !IsValid(Island))
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: Removendo ilha inválida do índice %d"), i);
            ChaosIslands.RemoveAt(i);
        }
    }

    // Validar integridade dos portais
    for (int32 i = ChaosPortals.Num() - 1; i >= 0; i--)
    {
        AAURACRONPCGChaosPortal* Portal = ChaosPortals[i];
        if (!Portal || !IsValid(Portal))
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: Removendo portal inválido do índice %d"), i);
            ChaosPortals.RemoveAt(i);
        }
    }

    // Validar componentes
    if (AudioComponent && !IsValid(AudioComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: AudioComponent inválido detectado"));
        InitializeAudioSystem();
    }

    if (GlobalVFXComponent && !IsValid(GlobalVFXComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager: GlobalVFXComponent inválido detectado"));
        InitializeVFXSystem();
    }
}

TArray<FVector> AAURACRONPCGChaosIslandManager::GetCachedIntersectionPoints()
{
    // Verificar se o cache ainda é válido
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastIntersectionCalculationTime < IntersectionCacheValidDuration && CachedIntersectionPoints.Num() > 0)
    {
        return CachedIntersectionPoints;
    }

    // Recalcular pontos de interseção
    CachedIntersectionPoints = FindAllFlowIntersections();
    LastIntersectionCalculationTime = CurrentTime;

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Cache de interseções atualizado com %d pontos"), CachedIntersectionPoints.Num());

    return CachedIntersectionPoints;
}

void AAURACRONPCGChaosIslandManager::ConfigureIslandForCurrentPhase(AAURACRONPCGChaosIsland* Island)
{
    if (!Island)
    {
        return;
    }

    // Configurar propriedades baseadas na fase atual
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            Island->EnvironmentalHazardIntensity = 0.8f;
            Island->TerrainInstabilityIntensity = 0.6f;
            Island->HighRiskRewardMultiplier = 1.2f;
            Island->VortexRotationSpeed = 30.0f;
            Island->RunePulseIntensity = 0.5f;
            break;

        case EAURACRONMapPhase::Convergence:
            Island->EnvironmentalHazardIntensity = 1.2f;
            Island->TerrainInstabilityIntensity = 1.0f;
            Island->HighRiskRewardMultiplier = 1.5f;
            Island->VortexRotationSpeed = 50.0f;
            Island->RunePulseIntensity = 0.8f;
            break;

        case EAURACRONMapPhase::Intensification:
            Island->EnvironmentalHazardIntensity = 1.6f;
            Island->TerrainInstabilityIntensity = 1.4f;
            Island->HighRiskRewardMultiplier = 1.8f;
            Island->VortexRotationSpeed = 75.0f;
            Island->RunePulseIntensity = 1.2f;
            break;

        case EAURACRONMapPhase::Resolution:
            Island->EnvironmentalHazardIntensity = 2.0f;
            Island->TerrainInstabilityIntensity = 1.8f;
            Island->HighRiskRewardMultiplier = 2.2f;
            Island->VortexRotationSpeed = 100.0f;
            Island->RunePulseIntensity = 1.5f;
            break;

        case EAURACRONMapPhase::Expansion:
            Island->EnvironmentalHazardIntensity = 1.0f;
            Island->TerrainInstabilityIntensity = 0.8f;
            Island->HighRiskRewardMultiplier = 1.35f;
            Island->VortexRotationSpeed = 40.0f;
            Island->RunePulseIntensity = 0.65f;
            break;
    }

    // Configurar ambientes de transição baseado na localização
    FVector IslandLocation = Island->GetActorLocation();
    Island->TransitionEnvironments.Empty();

    if (IslandLocation.X > 0 && IslandLocation.Y > 0)
    {
        Island->TransitionEnvironments.Add(EAURACRONEnvironmentType::RadiantPlains);
    }
    else if (IslandLocation.X < 0 && IslandLocation.Y > 0)
    {
        Island->TransitionEnvironments.Add(EAURACRONEnvironmentType::ZephyrFirmament);
    }
    else if (IslandLocation.X < 0 && IslandLocation.Y < 0)
    {
        Island->TransitionEnvironments.Add(EAURACRONEnvironmentType::PurgatoryRealm);
    }
    else
    {
        Island->TransitionEnvironments.Add(EAURACRONEnvironmentType::CrystalCaverns);
    }

    // Ativar a ilha
    Island->bIsActive = true;
}

void AAURACRONPCGChaosIslandManager::UpdateGlobalEffectsForPhase(EAURACRONMapPhase Phase)
{
    // Atualizar iluminação ambiente baseada na fase
    if (AmbientLightComponent)
    {
        FLinearColor PhaseColor = GetPhaseColor(Phase);
        float PhaseIntensity = GetPhaseIntensityMultiplier(Phase);

        AmbientLightComponent->SetLightColor(PhaseColor);
        AmbientLightComponent->SetIntensity(0.5f * PhaseIntensity);
        AmbientLightComponent->SetAttenuationRadius(5000.0f * PhaseIntensity);
    }

    // Atualizar efeitos de áudio globais
    if (AudioComponent && AudioComponent->IsPlaying())
    {
        float PhaseIntensity = GetPhaseIntensityMultiplier(Phase);
        AudioComponent->SetVolumeMultiplier(0.7f * PhaseIntensity);
        AudioComponent->SetPitchMultiplier(0.8f + (PhaseIntensity * 0.4f));
    }

    // Atualizar efeitos visuais globais
    if (GlobalVFXComponent && GlobalVFXComponent->IsActive())
    {
        float PhaseIntensity = GetPhaseIntensityMultiplier(Phase);
        FLinearColor PhaseColor = GetPhaseColor(Phase);

        GlobalVFXComponent->SetFloatParameter(FName("GlobalIntensity"), PhaseIntensity);
        GlobalVFXComponent->SetColorParameter(FName("GlobalColor"), PhaseColor);
        GlobalVFXComponent->SetFloatParameter(FName("PhaseIndex"), static_cast<float>((int32)Phase));
    }
}

FLinearColor AAURACRONPCGChaosIslandManager::GetPhaseColor(EAURACRONMapPhase Phase) const
{
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return FLinearColor(0.8f, 0.2f, 0.2f, 1.0f); // Vermelho suave
        case EAURACRONMapPhase::Expansion:
            return FLinearColor(1.0f, 0.4f, 0.2f, 1.0f); // Laranja
        case EAURACRONMapPhase::Convergence:
            return FLinearColor(1.0f, 0.6f, 0.0f, 1.0f); // Amarelo-laranja
        case EAURACRONMapPhase::Intensification:
            return FLinearColor(1.0f, 0.2f, 0.8f, 1.0f); // Magenta
        case EAURACRONMapPhase::Resolution:
            return FLinearColor(0.6f, 0.0f, 1.0f, 1.0f); // Roxo intenso
        default:
            return FLinearColor::Red;
    }
}

void AAURACRONPCGChaosIslandManager::CleanupCache()
{
    // Limpar cache de interseções se muito antigo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastIntersectionCalculationTime > IntersectionCacheValidDuration * 2.0f)
    {
        CachedIntersectionPoints.Empty();
        UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosIslandManager: Cache de interseções limpo"));
    }

    // Limpar referências inválidas
    ValidateSystemIntegrity();
}

void AAURACRONPCGChaosIslandManager::ValidateNetworkState()
{
    if (!HasAuthority())
    {
        return;
    }

    // Validar estado de replicação das ilhas
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            if (!Island->GetIsReplicated())
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: Corrigindo replicação da ilha %s"), *Island->GetName());
                Island->SetReplicates(true);
            }
        }
    }

    // Validar estado de replicação dos portais
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            if (!Portal->GetIsReplicated())
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: Corrigindo replicação do portal %s"), *Portal->GetName());
                Portal->SetReplicates(true);
            }
        }
    }
}

// ========================================
// CALLBACKS DE CARREGAMENTO ASSÍNCRONO - UE 5.6
// ========================================

void AAURACRONPCGChaosIslandManager::OnAmbientSoundLoaded(FSoftObjectPath AssetPath)
{
    if (!AudioComponent)
    {
        return;
    }

    // Carregar o asset usando StreamableManager
    if (UObject* LoadedAsset = UAssetManager::GetStreamableManager().LoadSynchronous(AssetPath))
    {
        if (USoundBase* AmbientSound = Cast<USoundBase>(LoadedAsset))
        {
            AudioComponent->SetSound(AmbientSound);
            AudioComponent->Play();

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Som ambiente carregado e reproduzindo"));
        }
    }
}

void AAURACRONPCGChaosIslandManager::OnGlobalVFXLoaded(FSoftObjectPath AssetPath)
{
    if (!GlobalVFXComponent)
    {
        return;
    }

    // Carregar o asset usando StreamableManager
    if (UObject* LoadedAsset = UAssetManager::GetStreamableManager().LoadSynchronous(AssetPath))
    {
        if (UNiagaraSystem* VFXSystem = Cast<UNiagaraSystem>(LoadedAsset))
        {
            GlobalVFXComponent->SetAsset(VFXSystem);
            GlobalVFXComponent->Activate();

            // Configurar parâmetros iniciais
            UpdateGlobalVFX(0.0f);

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Sistema VFX global carregado e ativado"));
        }
    }
}

void AAURACRONPCGChaosIslandManager::OnRequiredAssetsLoaded()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Todos os assets necessários foram carregados"));

    // Marcar sistema como totalmente inicializado
    bSystemFullyInitialized = true;

    // Ativar sistemas que dependem dos assets
    if (AudioComponent && !AudioComponent->IsPlaying())
    {
        AudioComponent->Play();
    }

    if (GlobalVFXComponent && !GlobalVFXComponent->IsActive())
    {
        GlobalVFXComponent->Activate();
    }

    // Notificar outras partes do sistema que o manager está pronto
    OnChaosManagerFullyInitialized.Broadcast();
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - UE 5.6 NETWORKING
// ========================================

void AAURACRONPCGChaosIslandManager::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGChaosIslandManager, ChaosIslands);
    DOREPLIFETIME(AAURACRONPCGChaosIslandManager, ChaosPortals);
    DOREPLIFETIME(AAURACRONPCGChaosIslandManager, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGChaosIslandManager, MaxChaosIslands);
    DOREPLIFETIME(AAURACRONPCGChaosIslandManager, bAutoSpawnPortals);
    DOREPLIFETIME_CONDITION(AAURACRONPCGChaosIslandManager, PrismalFlow, COND_InitialOnly);
}

void AAURACRONPCGChaosIslandManager::OnRep_ChaosIslandsUpdated()
{
    // Callback quando a lista de ilhas é replicada
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Lista de ilhas caos atualizada via replicação (%d ilhas)"), ChaosIslands.Num());

    // Atualizar efeitos visuais baseado na nova lista
    UpdateGlobalVFX(0.0f);

    // Notificar sistemas dependentes
    OnChaosIslandsListChanged.Broadcast(ChaosIslands);
}

void AAURACRONPCGChaosIslandManager::OnRep_MapPhaseChanged()
{
    // Callback quando a fase do mapa é replicada
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Fase do mapa atualizada via replicação para %d"), (int32)CurrentMapPhase);

    // Atualizar efeitos globais para a nova fase
    UpdateGlobalEffectsForPhase(CurrentMapPhase);

    // Notificar sistemas dependentes
    OnMapPhaseUpdated.Broadcast(CurrentMapPhase);
}

// ========================================
// RPCs PARA NETWORKING ROBUSTO - UE 5.6
// ========================================

void AAURACRONPCGChaosIslandManager::ServerSpawnChaosIsland_Implementation(const FVector& Location)
{
    // Validar autoridade
    if (!HasAuthority())
    {
        return;
    }

    // Spawnar ilha usando função existente
    AAURACRONPCGChaosIsland* NewIsland = SpawnChaosIsland(Location);
    if (NewIsland)
    {
        // Notificar clientes
        ClientNotifyIslandSpawned(NewIsland, Location);
    }
}

bool AAURACRONPCGChaosIslandManager::ServerSpawnChaosIsland_Validate(const FVector& Location)
{
    // Validar se a localização é razoável
    return Location.Size() < 100000.0f; // Máximo 1km do centro
}

void AAURACRONPCGChaosIslandManager::ClientNotifyIslandSpawned_Implementation(AAURACRONPCGChaosIsland* Island, const FVector& Location)
{
    if (Island)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Cliente notificado sobre nova ilha em %s"), *Location.ToString());

        // Reproduzir efeito de spawn no cliente
        PlayIslandSpawnEffect(Location);
    }
}

void AAURACRONPCGChaosIslandManager::ServerDestroyAllChaosIslands_Implementation()
{
    // Validar autoridade
    if (!HasAuthority())
    {
        return;
    }

    // Destruir todas as ilhas
    for (AAURACRONPCGChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->Destroy();
        }
    }
    ChaosIslands.Empty();

    // Destruir todos os portais
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->Destroy();
        }
    }
    ChaosPortals.Empty();

    // Notificar clientes
    ClientNotifyAllIslandsDestroyed();
}

bool AAURACRONPCGChaosIslandManager::ServerDestroyAllChaosIslands_Validate()
{
    return true; // Sempre válido
}

void AAURACRONPCGChaosIslandManager::ClientNotifyAllIslandsDestroyed_Implementation()
{
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Cliente notificado sobre destruição de todas as ilhas"));

    // Parar efeitos globais
    if (GlobalVFXComponent && GlobalVFXComponent->IsActive())
    {
        GlobalVFXComponent->Deactivate();
    }

    if (AudioComponent && AudioComponent->IsPlaying())
    {
        AudioComponent->Stop();
    }
}

// ========================================
// FUNÇÕES AUXILIARES DE EFEITOS - UE 5.6
// ========================================

void AAURACRONPCGChaosIslandManager::PlayIslandSpawnEffect(const FVector& Location)
{
    if (!GetWorld())
    {
        return;
    }

    // Reproduzir efeito visual de spawn usando Niagara
    if (StreamableManager)
    {
        FSoftObjectPath SpawnVFXPath(TEXT("/Game/VFX/Chaos/ChaosIslandSpawn_NS"));
        if (UObject* LoadedAsset = UAssetManager::GetStreamableManager().LoadSynchronous(SpawnVFXPath))
        {
            if (UNiagaraSystem* SpawnVFX = Cast<UNiagaraSystem>(LoadedAsset))
            {
                UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    SpawnVFX,
                    Location,
                    FRotator::ZeroRotator,
                    FVector::OneVector,
                    true,
                    true,
                    ENCPoolMethod::None
                );
            }
        }
    }

    // Reproduzir som de spawn
    if (StreamableManager)
    {
        FSoftObjectPath SpawnSoundPath(TEXT("/Game/Audio/Chaos/ChaosIslandSpawn_Cue"));
        if (UObject* LoadedAsset = UAssetManager::GetStreamableManager().LoadSynchronous(SpawnSoundPath))
        {
            if (USoundBase* SpawnSound = Cast<USoundBase>(LoadedAsset))
            {
                UGameplayStatics::PlaySoundAtLocation(
                    GetWorld(),
                    SpawnSound,
                    Location,
                    1.0f,
                    1.0f,
                    0.0f,
                    nullptr,
                    nullptr
                );
            }
        }
    }
}

void AAURACRONPCGChaosIslandManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Cleanup robusto ao finalizar
    if (UWorld* World = GetWorld())
    {
        // Limpar timers
        World->GetTimerManager().ClearTimer(EffectsUpdateTimerHandle);
        World->GetTimerManager().ClearTimer(CacheCleanupTimerHandle);
        World->GetTimerManager().ClearTimer(NetworkValidationTimerHandle);
    }

    // Parar componentes de áudio e VFX
    if (AudioComponent && AudioComponent->IsPlaying())
    {
        AudioComponent->Stop();
    }

    if (GlobalVFXComponent && GlobalVFXComponent->IsActive())
    {
        GlobalVFXComponent->Deactivate();
    }

    // Limpar cache
    CachedIntersectionPoints.Empty();

    // Limpar referências
    StreamableManager = nullptr;
    PrismalFlow = nullptr;

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Sistema finalizado corretamente"));
}
