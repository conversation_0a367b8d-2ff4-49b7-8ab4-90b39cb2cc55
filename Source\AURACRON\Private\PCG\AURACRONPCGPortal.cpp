// AURACRONPCGPortal.cpp
// Implementação do sistema de Portais de Transição entre Ambientes

#include "PCG/AURACRONPCGPortal.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "Kismet/GameplayStatics.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraComponent.h"
#include "GameFramework/Character.h"

AAURACRONPCGPortal::AAURACRONPCGPortal()
    : TeleportEffect(nullptr)
    , TeleportSound(nullptr)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , PortalDynamicMaterial(nullptr)
    , PortalLifetime(0.0f)
    , RotationSpeed(15.0f)
    , PulsateIntensity(0.2f)
    , PulsateFrequency(1.0f)
    , FadeTransitionDuration(1.5f)
    , TeleportingCharacter(nullptr)
    , CurrentFadeValue(0.0f)
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);
    
    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // Criar componente de malha estática
    PortalMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("PortalMesh"));
    PortalMesh->SetupAttachment(RootComponent);
    PortalMesh->SetCollisionProfileName(TEXT("OverlapAll"));
    PortalMesh->SetGenerateOverlapEvents(true);
    
    // Criar componente de colisão
    ActivationSphere = CreateDefaultSubobject<USphereComponent>(TEXT("ActivationSphere"));
    ActivationSphere->SetupAttachment(RootComponent);
    ActivationSphere->SetSphereRadius(300.0f);
    ActivationSphere->SetCollisionProfileName(TEXT("OverlapAll"));
    ActivationSphere->SetGenerateOverlapEvents(true);
    
    // Configurar evento de sobreposição
    ActivationSphere->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGPortal::OnOverlapBegin);
    
    // Criar componente de efeito de partículas
    PortalEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PortalEffect"));
    PortalEffect->SetupAttachment(RootComponent);
    
    // Carregar recursos padrão
    static ConstructorHelpers::FObjectFinder<UParticleSystem> DefaultTeleportEffect(TEXT("/Game/AURACRON/FX/P_TeleportEffect"));
    if (DefaultTeleportEffect.Succeeded())
    {
        TeleportEffect = DefaultTeleportEffect.Object;
    }
    
    static ConstructorHelpers::FObjectFinder<USoundBase> DefaultTeleportSound(TEXT("/Game/AURACRON/Audio/S_TeleportSound"));
    if (DefaultTeleportSound.Succeeded())
    {
        TeleportSound = DefaultTeleportSound.Object;
    }
}

void AAURACRONPCGPortal::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar material dinâmico
    if (PortalMesh && PortalMesh->GetMaterial(0))
    {
        PortalDynamicMaterial = PortalMesh->CreateAndSetMaterialInstanceDynamic(0);
    }
    
    // Atualizar parâmetros iniciais
    UpdateMaterialParameters();
    UpdateParticleParameters();
}

void AAURACRONPCGPortal::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (PortalSettings.bIsActive)
    {
        // Atualizar tempo de vida
        PortalLifetime += DeltaTime;
        
        // Atualizar efeitos visuais
        UpdateVisualEffects(DeltaTime);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGPortal::InitializePortal(const FAURACRONPortalSettings& Settings)
{
    PortalSettings = Settings;
    
    // Configurar escala
    SetActorScale3D(FVector(PortalSettings.PortalScale));
    
    // Configurar raio de ativação
    if (ActivationSphere)
    {
        ActivationSphere->SetSphereRadius(PortalSettings.ActivationRadius);
    }
    
    // Atualizar visibilidade
    SetPortalVisibility(PortalSettings.bIsVisible);
    
    // Atualizar parâmetros
    UpdateMaterialParameters();
    UpdateParticleParameters();
    
    // Ativar ou desativar o portal baseado nas configurações
    if (PortalSettings.bIsActive)
    {
        ActivatePortal();
    }
    else
    {
        DeactivatePortal();
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::InitializePortal - Portal initialized for environment %d, type %d"),
           (int32)PortalSettings.CurrentEnvironment, (int32)PortalSettings.PortalType);
}

void AAURACRONPCGPortal::InitializePortalWithType(EAURACRONPortalType PortalType, FVector DestLocation, FRotator DestRotation)
{
    // Criar configurações baseadas no tipo de portal
    FAURACRONPortalSettings Settings(PortalType);
    
    // Configurar localização e rotação de destino
    Settings.DestinationLocation = DestLocation;
    Settings.DestinationRotation = DestRotation;
    
    // Inicializar o portal com as configurações
    InitializePortal(Settings);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::InitializePortalWithType - Portal type %d initialized"), (int32)PortalType);
}

void AAURACRONPCGPortal::CreateRadiantPortal(FVector DestLocation, FRotator DestRotation)
{
    // Inicializar portal radiante (energia dourada) para Planície Radiante
    InitializePortalWithType(EAURACRONPortalType::RadiantPlains, DestLocation, DestRotation);
    
    // Configurações específicas para portal radiante
    PortalSettings.EffectIntensity = 1.2f; // Energia dourada mais intensa
    PortalSettings.PortalScale = 1.1f;     // Ligeiramente maior
    
    // Atualizar escala
    SetActorScale3D(FVector(PortalSettings.PortalScale));
    
    // Atualizar parâmetros
    UpdateMaterialParameters();
    UpdateParticleParameters();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::CreateRadiantPortal - Radiant portal created (golden energy)"));
}

void AAURACRONPCGPortal::CreateZephyrPortal(FVector DestLocation, FRotator DestRotation)
{
    // Inicializar portal zephyr (energia prateada) para Firmamento Zephyr
    InitializePortalWithType(EAURACRONPortalType::ZephyrFirmament, DestLocation, DestRotation);
    
    // Configurações específicas para portal zephyr
    PortalSettings.EffectIntensity = 1.0f; // Energia prateada padrão
    PortalSettings.PortalScale = 1.0f;     // Tamanho padrão
    
    // Atualizar escala
    SetActorScale3D(FVector(PortalSettings.PortalScale));
    
    // Atualizar parâmetros
    UpdateMaterialParameters();
    UpdateParticleParameters();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::CreateZephyrPortal - Zephyr portal created (silver energy)"));
}

void AAURACRONPCGPortal::CreateUmbralPortal(FVector DestLocation, FRotator DestRotation)
{
    // Inicializar portal umbral (energia violeta) para Reino Purgatório
    InitializePortalWithType(EAURACRONPortalType::PurgatoryRealm, DestLocation, DestRotation);
    
    // Configurações específicas para portal umbral
    PortalSettings.EffectIntensity = 1.5f; // Energia violeta mais intensa
    PortalSettings.PortalScale = 0.9f;     // Ligeiramente menor
    
    // Atualizar escala
    SetActorScale3D(FVector(PortalSettings.PortalScale));
    
    // Atualizar parâmetros
    UpdateMaterialParameters();
    UpdateParticleParameters();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::CreateUmbralPortal - Umbral portal created (violet energy)"));
}

void AAURACRONPCGPortal::ActivatePortal()
{
    PortalSettings.bIsActive = true;
    
    // Ativar componentes
    if (PortalMesh)
    {
        PortalMesh->SetComponentTickEnabled(true);
    }
    
    if (PortalEffect)
    {
        PortalEffect->Activate(true);
    }
    
    if (ActivationSphere)
    {
        ActivationSphere->SetGenerateOverlapEvents(true);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::ActivatePortal - Portal activated"));
}

void AAURACRONPCGPortal::DeactivatePortal()
{
    PortalSettings.bIsActive = false;
    
    // Desativar componentes
    if (PortalMesh)
    {
        PortalMesh->SetComponentTickEnabled(false);
    }
    
    if (PortalEffect)
    {
        PortalEffect->Deactivate();
    }
    
    if (ActivationSphere)
    {
        ActivationSphere->SetGenerateOverlapEvents(false);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::DeactivatePortal - Portal deactivated"));
}

void AAURACRONPCGPortal::SetPortalVisibility(bool bVisible)
{
    PortalSettings.bIsVisible = bVisible;
    
    // Atualizar visibilidade dos componentes
    if (PortalMesh)
    {
        PortalMesh->SetVisibility(bVisible);
    }
    
    if (PortalEffect)
    {
        PortalEffect->SetVisibility(bVisible);
    }
}

void AAURACRONPCGPortal::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    CurrentMapPhase = MapPhase;
    
    // Ajustar parâmetros baseados na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            PulsateIntensity = 0.2f;
            PulsateFrequency = 0.5f;
            RotationSpeed = 10.0f;
            break;
            
        case EAURACRONMapPhase::Convergence:
            PulsateIntensity = 0.3f;
            PulsateFrequency = 0.8f;
            RotationSpeed = 15.0f;
            break;
            
        case EAURACRONMapPhase::Intensification:
            PulsateIntensity = 0.4f;
            PulsateFrequency = 1.2f;
            RotationSpeed = 20.0f;
            break;
            
        case EAURACRONMapPhase::Resolution:
            PulsateIntensity = 0.5f;
            PulsateFrequency = 1.5f;
            RotationSpeed = 25.0f;
            break;
            
        default:
            break;
    }
    
    // Atualizar parâmetros
    UpdateMaterialParameters();
    UpdateParticleParameters();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::UpdateForMapPhase - Updated for phase %d"), (int32)CurrentMapPhase);
}

void AAURACRONPCGPortal::ApplyTeleportEffect(float EffectAlpha, bool bFadeIn)
{
    // Calcular alpha efetivo baseado na direção da transição
    float EffectiveAlpha = bFadeIn ? EffectAlpha : (1.0f - EffectAlpha);
    
    // Aplicar efeito de teletransporte ao material
    if (PortalDynamicMaterial)
    {
        PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("TransitionAlpha")), EffectiveAlpha);
        PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("Opacity")), EffectiveAlpha);
    }
    
    // Aplicar efeito de teletransporte às partículas
    if (PortalEffect)
    {
        PortalEffect->SetFloatParameter(FName(TEXT("TransitionAlpha")), EffectiveAlpha);
        PortalEffect->SetFloatParameter(FName(TEXT("Opacity")), EffectiveAlpha);
    }
    
    // Ajustar escala baseada no efeito
    float ScaleFactor = 1.0f + (EffectiveAlpha * 0.5f);
    SetActorScale3D(FVector(PortalSettings.PortalScale * ScaleFactor));
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPortal::ApplyTeleportEffect - Applied effect alpha %.2f (FadeIn: %s)"),
           EffectiveAlpha, bFadeIn ? TEXT("true") : TEXT("false"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGPortal::UpdateVisualEffects(float DeltaTime)
{
    // Aplicar rotação
    if (PortalMesh)
    {
        FRotator CurrentRotation = PortalMesh->GetRelativeRotation();
        FRotator NewRotation = CurrentRotation + FRotator(0.0f, RotationSpeed * DeltaTime, 0.0f);
        PortalMesh->SetRelativeRotation(NewRotation);
    }
    
    // Aplicar pulsação
    if (PortalDynamicMaterial)
    {
        float PulsateValue = FMath::Sin(PortalLifetime * PulsateFrequency * PI * 2.0f) * PulsateIntensity + 1.0f;
        PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("PulsateValue")), PulsateValue);
    }
}

void AAURACRONPCGPortal::UpdateMaterialParameters()
{
    if (PortalDynamicMaterial)
    {
        // Configurar cor baseada no tipo de portal
        PortalDynamicMaterial->SetVectorParameterValue(FName(TEXT("PortalColor")), PortalSettings.PortalColor);
        
        // Configurar intensidade do efeito
        PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("EffectIntensity")), PortalSettings.EffectIntensity);
        
        // Configurar parâmetros baseados na fase do mapa
        PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("MapPhaseIntensity")), (float)CurrentMapPhase / 3.0f);
        
        // Configurar parâmetros específicos baseados no tipo de portal
        switch (PortalSettings.PortalType)
        {
        case EAURACRONPortalType::RadiantPlains:
            // Parâmetros específicos para portal radiante (energia dourada)
            PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 2.0f);
            PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("PulsateSpeed")), 1.2f);
            break;
            
        case EAURACRONPortalType::ZephyrFirmament:
            // Parâmetros específicos para portal zephyr (energia prateada)
            PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 1.5f);
            PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("PulsateSpeed")), 1.0f);
            break;
            
        case EAURACRONPortalType::PurgatoryRealm:
            // Parâmetros específicos para portal umbral (energia violeta)
            PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 2.5f);
            PortalDynamicMaterial->SetScalarParameterValue(FName(TEXT("PulsateSpeed")), 1.5f);
            break;
        }
    }
}

void AAURACRONPCGPortal::UpdateParticleParameters()
{
    if (PortalEffect)
    {
        // Configurar cor baseada no tipo de portal
        PortalEffect->SetVectorParameter(FName(TEXT("PortalColor")), FVector(PortalSettings.PortalColor.R, PortalSettings.PortalColor.G, PortalSettings.PortalColor.B));
        
        // Configurar intensidade do efeito
        PortalEffect->SetFloatParameter(FName(TEXT("EffectIntensity")), PortalSettings.EffectIntensity);
        
        // Configurar parâmetros baseados na fase do mapa
        PortalEffect->SetFloatParameter(FName(TEXT("MapPhaseIntensity")), (float)CurrentMapPhase / 3.0f);
        
        // Configurar parâmetros específicos baseados no tipo de portal
        switch (PortalSettings.PortalType)
        {
        case EAURACRONPortalType::RadiantPlains:
            // Parâmetros específicos para portal radiante (energia dourada)
            PortalEffect->SetFloatParameter(FName(TEXT("ParticleSize")), 1.2f);
            PortalEffect->SetFloatParameter(FName(TEXT("ParticleSpeed")), 1.5f);
            PortalEffect->SetFloatParameter(FName(TEXT("EmissiveIntensity")), 2.0f);
            break;
            
        case EAURACRONPortalType::ZephyrFirmament:
            // Parâmetros específicos para portal zephyr (energia prateada)
            PortalEffect->SetFloatParameter(FName(TEXT("ParticleSize")), 1.0f);
            PortalEffect->SetFloatParameter(FName(TEXT("ParticleSpeed")), 2.0f);
            PortalEffect->SetFloatParameter(FName(TEXT("EmissiveIntensity")), 1.5f);
            break;
            
        case EAURACRONPortalType::PurgatoryRealm:
            // Parâmetros específicos para portal umbral (energia violeta)
            PortalEffect->SetFloatParameter(FName(TEXT("ParticleSize")), 0.8f);
            PortalEffect->SetFloatParameter(FName(TEXT("ParticleSpeed")), 2.5f);
            PortalEffect->SetFloatParameter(FName(TEXT("EmissiveIntensity")), 2.5f);
            break;
        }
    }
}

void AAURACRONPCGPortal::StartFadeOutEffect(ACharacter* PlayerCharacter)
{
    if (!PlayerCharacter)
    {
        return;
    }
    
    // Inicializar valores para o efeito de fade
    TeleportingCharacter = PlayerCharacter;
    CurrentFadeValue = 0.0f;
    
    // Iniciar o timer para atualizar o efeito de fade
    GetWorldTimerManager().SetTimer(FadeTimerHandle, this, &AAURACRONPCGPortal::UpdateFadeEffect, 0.01f, true);
    
    // Desativar controles do jogador durante o teletransporte
    APlayerController* PC = Cast<APlayerController>(PlayerCharacter->GetController());
    if (PC)
    {
        PC->SetIgnoreMoveInput(true);
    }
    
    // Aplicar efeito específico baseado no tipo de portal
    if (TeleportEffect)
    {
        FVector PlayerLocation = PlayerCharacter->GetActorLocation();
        FRotator PlayerRotation = PlayerCharacter->GetActorRotation();
        
        switch (PortalSettings.PortalType)
        {
        case EAURACRONPortalType::RadiantPlains:
            // Fade out com partículas douradas
            UGameplayStatics::SpawnEmitterAttached(
                TeleportEffect,
                PlayerCharacter->GetRootComponent(),
                NAME_None,
                FVector(0, 0, 0),
                FRotator::ZeroRotator,
                FVector(1.0f),
                EAttachLocation::SnapToTarget,
                true,
                EPSCPoolMethod::AutoRelease,
                true
            );
            // Configurar cor dourada para o efeito
            if (PortalDynamicMaterial)
            {
                PortalDynamicMaterial->SetVectorParameterValue(FName(TEXT("FadeColor")), FLinearColor(1.0f, 0.84f, 0.0f));
            }
            break;
            
        case EAURACRONPortalType::ZephyrFirmament:
            // Dissolução com efeitos de vento
            UGameplayStatics::SpawnEmitterAttached(
                TeleportEffect,
                PlayerCharacter->GetRootComponent(),
                NAME_None,
                FVector(0, 0, 0),
                FRotator::ZeroRotator,
                FVector(1.0f),
                EAttachLocation::SnapToTarget,
                true,
                EPSCPoolMethod::AutoRelease,
                true
            );
            // Configurar cor prateada para o efeito
            if (PortalDynamicMaterial)
            {
                PortalDynamicMaterial->SetVectorParameterValue(FName(TEXT("FadeColor")), FLinearColor(0.75f, 0.75f, 0.75f));
            }
            break;
            
        case EAURACRONPortalType::PurgatoryRealm:
            // Distorção espectral com energia violeta
            UGameplayStatics::SpawnEmitterAttached(
                TeleportEffect,
                PlayerCharacter->GetRootComponent(),
                NAME_None,
                FVector(0, 0, 0),
                FRotator::ZeroRotator,
                FVector(1.0f),
                EAttachLocation::SnapToTarget,
                true,
                EPSCPoolMethod::AutoRelease,
                true
            );
            // Configurar cor violeta para o efeito
            if (PortalDynamicMaterial)
            {
                PortalDynamicMaterial->SetVectorParameterValue(FName(TEXT("FadeColor")), FLinearColor(0.5f, 0.0f, 0.5f));
            }
            break;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::StartFadeOutEffect - Iniciando efeito de Fade Out"));
}

void AAURACRONPCGPortal::StartFadeInEffect(ACharacter* PlayerCharacter)
{
    // Esta função é chamada automaticamente após o teletransporte
    // O efeito de Fade In é controlado pela função UpdateFadeEffect
    
    // Reativar controles do jogador após o teletransporte
    if (PlayerCharacter)
    {
        APlayerController* PC = Cast<APlayerController>(PlayerCharacter->GetController());
        if (PC)
        {
            PC->SetIgnoreMoveInput(false);
        }
        
        // Aplicar efeito de materialização específico baseado no tipo de portal
        if (TeleportEffect)
        {
            FVector PlayerLocation = PlayerCharacter->GetActorLocation();
            FRotator PlayerRotation = PlayerCharacter->GetActorRotation();
            
            switch (PortalSettings.PortalType)
            {
            case EAURACRONPortalType::RadiantPlains:
                // Materialização do jogador no ponto de destino com brilho dourado
                UGameplayStatics::SpawnEmitterAtLocation(
                    GetWorld(),
                    TeleportEffect,
                    PlayerLocation,
                    PlayerRotation,
                    FVector(1.5f),
                    true,
                    EPSCPoolMethod::AutoRelease,
                    true
                );
                break;
                
            case EAURACRONPortalType::ZephyrFirmament:
                // Materialização do jogador no ponto de destino com redemoinhos prateados
                UGameplayStatics::SpawnEmitterAtLocation(
                    GetWorld(),
                    TeleportEffect,
                    PlayerLocation,
                    PlayerRotation,
                    FVector(1.5f),
                    true,
                    EPSCPoolMethod::AutoRelease,
                    true
                );
                break;
                
            case EAURACRONPortalType::PurgatoryRealm:
                // Materialização do jogador no ponto de destino com aura violeta
                UGameplayStatics::SpawnEmitterAtLocation(
                    GetWorld(),
                    TeleportEffect,
                    PlayerLocation,
                    PlayerRotation,
                    FVector(1.5f),
                    true,
                    EPSCPoolMethod::AutoRelease,
                    true
                );
                break;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::StartFadeInEffect - Iniciando efeito de Fade In"));
}

/** Executar o teletransporte após o Fade Out */
void AAURACRONPCGPortal::ExecuteTeleport()
{
    if (!TeleportingCharacter)
    {
        return;
    }
    
    // Obter controlador do jogador
    AController* PlayerController = TeleportingCharacter->GetController();
    if (!PlayerController)
    {
        TeleportingCharacter = nullptr;
        return;
    }
    
    // Teletransportar o jogador para o destino
    FVector DestLocation = PortalSettings.DestinationLocation;
    FRotator DestRotation = PortalSettings.DestinationRotation;
    
    // Verificar se a localização de destino é válida
    if (DestLocation.IsZero())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPortal::ExecuteTeleport - Invalid destination location"));
        TeleportingCharacter = nullptr;
        return;
    }
    
    // Aplicar efeito de teletransporte específico baseado no tipo de portal
    switch (PortalSettings.PortalType)
    {
    case EAURACRONPortalType::RadiantPlains:
        // Efeito de teletransporte com rastro de luz
        if (TeleportEffect)
        {
            UGameplayStatics::SpawnEmitterAttached(
                TeleportEffect,
                TeleportingCharacter->GetRootComponent(),
                NAME_None,
                FVector(0, 0, 0),
                FRotator::ZeroRotator,
                FVector(1.0f),
                EAttachLocation::SnapToTarget,
                true,
                EPSCPoolMethod::AutoRelease,
                true
            );
        }
        break;
        
    case EAURACRONPortalType::ZephyrFirmament:
        // Efeito de teletransporte com correntes de ar
        if (TeleportEffect)
        {
            UGameplayStatics::SpawnEmitterAttached(
                TeleportEffect,
                TeleportingCharacter->GetRootComponent(),
                NAME_None,
                FVector(0, 0, 0),
                FRotator::ZeroRotator,
                FVector(1.0f),
                EAttachLocation::SnapToTarget,
                true,
                EPSCPoolMethod::AutoRelease,
                true
            );
        }
        break;
        
    case EAURACRONPortalType::PurgatoryRealm:
        // Efeito de teletransporte com sombras fluidas
        if (TeleportEffect)
        {
            UGameplayStatics::SpawnEmitterAttached(
                TeleportEffect,
                TeleportingCharacter->GetRootComponent(),
                NAME_None,
                FVector(0, 0, 0),
                FRotator::ZeroRotator,
                FVector(1.0f),
                EAttachLocation::SnapToTarget,
                true,
                EPSCPoolMethod::AutoRelease,
                true
            );
        }
        break;
    }
    
    // Executar o teletransporte
    bool bSuccess = TeleportingCharacter->TeleportTo(DestLocation, DestRotation);
    
    if (bSuccess)
    {
        // Tocar som de teletransporte
        if (TeleportSound)
        {
            UGameplayStatics::PlaySoundAtLocation(this, TeleportSound, DestLocation);
        }
        
        // Iniciar o efeito de Fade In após o teletransporte
        StartFadeInEffect(TeleportingCharacter);
        
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::ExecuteTeleport - Player teleported to destination"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPortal::ExecuteTeleport - Teleport failed"));
        TeleportingCharacter = nullptr;
    }
}

/** Atualizar o efeito de Fade */
void AAURACRONPCGPortal::UpdateFadeEffect()
{
    if (!TeleportingCharacter)
    {
        return;
    }
    
    // Atualizar o valor de fade
    CurrentFadeValue += GetWorld()->GetDeltaSeconds() / FadeTransitionDuration;
    
    // Aplicar o efeito de fade
    ApplyTeleportEffect(CurrentFadeValue, CurrentFadeValue < 0.5f);
    
    // Verificar se o fade out está completo
    if (CurrentFadeValue >= 0.5f && CurrentFadeValue < 0.51f)
    {
        // Executar o teletransporte no meio do efeito de fade
        ExecuteTeleport();
    }
    
    // Verificar se o efeito de fade está completo
    if (CurrentFadeValue >= 1.0f)
    {
        // Resetar o efeito de fade
        CurrentFadeValue = 0.0f;
        TeleportingCharacter = nullptr;
        
        // Parar o timer
        GetWorldTimerManager().ClearTimer(FadeTimerHandle);
    }
}

void AAURACRONPCGPortal::TeleportPlayerToDestination(ACharacter* PlayerCharacter)
{
    // Verificar se o portal está ativo
    if (!PortalSettings.bIsActive || !PlayerCharacter)
    {
        return;
    }
    
    // Armazenar referência ao personagem para uso durante o processo de fade
    TeleportingCharacter = PlayerCharacter;
    
    // Iniciar o efeito de Fade Out antes do teletransporte
    StartFadeOutEffect(PlayerCharacter);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::TeleportPlayerToDestination - Iniciando sequência de teletransporte com Fade Out/In"));
}

void AAURACRONPCGPortal::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                        UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                        bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se o portal está ativo
    if (!PortalSettings.bIsActive)
    {
        return;
    }
    
    // Verificar se é um personagem jogável
    ACharacter* PlayerCharacter = Cast<ACharacter>(OtherActor);
    if (!PlayerCharacter || !PlayerCharacter->IsPlayerControlled())
    {
        return;
    }
    
    // Verificar se já existe um personagem em processo de teletransporte
    if (TeleportingCharacter != nullptr)
    {
        return;
    }
    
    // Teletransportar o jogador para o destino com efeito de Fade Out/In
    TeleportPlayerToDestination(PlayerCharacter);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPortal::OnOverlapBegin - Player entered portal. Starting teleport sequence"));
}