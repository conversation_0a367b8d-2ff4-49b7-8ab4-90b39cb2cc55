// AURACRONPCGSanctuaryIsland.cpp
// Implementação da classe ASanctuaryIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGSanctuaryIsland.h"
#include "GAS/AURACRONAttributeSet.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"

ASanctuaryIsland::ASanctuaryIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    HealingPower = 2.0f;
    ProtectionDuration = 30.0f;
    EnvironmentType = EAURACRONEnvironmentType::RadiantPlains; // Padrão para santuários
    StrategicValue = 75.0f; // Valor estratégico alto para santuários
    
    // Configurar componentes específicos da Sanctuary Island
    
    // Fonte de cura central
    HealingFountain = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("HealingFountain"));
    HealingFountain->SetupAttachment(RootComponent);
    HealingFountain->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
    HealingFountain->SetRelativeScale3D(FVector(1.5f, 1.5f, 2.0f));
    HealingFountain->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito de cura da fonte
    HealingEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("HealingEffect"));
    HealingEffect->SetupAttachment(HealingFountain);
    HealingEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
    
    // Árvore antiga
    AncientTree = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("AncientTree"));
    AncientTree->SetupAttachment(RootComponent);
    AncientTree->SetRelativeLocation(FVector(150.0f, 0.0f, 50.0f));
    AncientTree->SetRelativeScale3D(FVector(2.0f, 2.0f, 4.0f));
    AncientTree->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Barreira protetora
    ProtectiveBarrier = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ProtectiveBarrier"));
    ProtectiveBarrier->SetupAttachment(RootComponent);
    ProtectiveBarrier->SetRelativeLocation(FVector(0.0f, 0.0f, 0.0f));
    ProtectiveBarrier->SetRelativeScale3D(FVector(10.0f, 10.0f, 5.0f));
    ProtectiveBarrier->SetCollisionProfileName(TEXT("OverlapAll"));
    
    // Efeito da barreira protetora
    BarrierEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("BarrierEffect"));
    BarrierEffect->SetupAttachment(ProtectiveBarrier);

    // Zona segura (componente de colisão esférica)
    SecureZone = CreateDefaultSubobject<USphereComponent>(TEXT("SecureZone"));
    SecureZone->SetupAttachment(RootComponent);
    SecureZone->SetSphereRadius(1500.0f); // 15 metros de raio
    SecureZone->SetCollisionProfileName(TEXT("OverlapAll"));
    SecureZone->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    SecureZone->SetCollisionResponseToAllChannels(ECR_Ignore);
    SecureZone->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Definir o tipo de ilha como Sanctuary
    IslandType = EPrismalFlowIslandType::Sanctuary;
}

void ASanctuaryIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar efeitos visuais da ilha santuário
    if (bIsActive)
    {
        // Pulsar efeito de cura
        float Time = GetGameTimeSinceCreation();
        float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * 1.0f);
        
        if (HealingEffect)
        {
            HealingEffect->SetFloatParameter(FName("Intensity"), PulseValue * 2.0f);
        }
        
        // Rotação suave da barreira protetora
        if (BarrierEffect)
        {
            BarrierEffect->SetFloatParameter(FName("RotationSpeed"), 0.2f);
            BarrierEffect->SetFloatParameter(FName("Opacity"), 0.3f + 0.2f * PulseValue);
        }
        
        // Verificar personagens dentro da área de cura
        if (GetWorld()->GetTimeSeconds() - LastHealTime > 1.0f) // Curar a cada segundo
        {
            LastHealTime = GetWorld()->GetTimeSeconds();
            HealCharactersInRange();
        }
    }
}

void ASanctuaryIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OtherActor)
    {
        return;
    }

    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    if (HealingEffect)
    {
        HealingEffect->SetFloatParameter(FName("EffectIntensity"), 3.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (HealingEffect)
            {
                HealingEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
    
    // Aplicar cura imediata
    ApplyHealing(OtherActor);

    // Conceder proteção temporária
    GrantProtection(OtherActor);
}

void ASanctuaryIsland::ApplyHealing(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de cura
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para cura
    UGameplayEffect* LocalHealingGameplayEffect = NewObject<UGameplayEffect>(this, FName("GE_SanctuaryIslandHealing"));
    LocalHealingGameplayEffect->DurationPolicy = EGameplayEffectDurationType::Instant;
    
    // Modificador para cura instantânea (+75 HP)
    FGameplayModifierInfo HealModifier;
    HealModifier.ModifierMagnitude = FScalableFloat(75.0f);
    HealModifier.ModifierOp = EGameplayModOp::Additive;
    // Usar atributo de vida para cura
HealModifier.Attribute = UAURACRONAttributeSet::GetHealthAttribute();
    LocalHealingGameplayEffect->Modifiers.Add(HealModifier);

    // Modificador para regeneração contínua (+5 HP/s por 10s)
    FGameplayModifierInfo RegenModifier;
    RegenModifier.ModifierMagnitude = FScalableFloat(5.0f);
    RegenModifier.ModifierOp = EGameplayModOp::Additive;
    // Usar atributo de regeneração de vida
RegenModifier.Attribute = UAURACRONAttributeSet::GetHealthRegenerationAttribute();
    LocalHealingGameplayEffect->Modifiers.Add(RegenModifier);

    // Aplicar o efeito de cura
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(LocalHealingGameplayEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Sanctuary Island: Cura aplicada com sucesso em %s (+75 HP + 5 HP/s)"), *TargetActor->GetName());
        }
    }
    
    // Criar feedback visual de cura
    UNiagaraSystem* HealingVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_SanctuaryIslandHealing"));
    if (HealingVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            HealingVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void ASanctuaryIsland::GrantProtection(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de proteção
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para proteção
    UGameplayEffect* ProtectionEffect = NewObject<UGameplayEffect>(this, FName("GE_SanctuaryIslandProtection"));
    ProtectionEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
    ProtectionEffect->DurationMagnitude = FScalableFloat(ProtectionDuration);
    
    // Modificador para resistência a dano (+40%)
    FGameplayModifierInfo DamageResistanceModifier;
    DamageResistanceModifier.ModifierMagnitude = FScalableFloat(0.6f); // 40% de redução = 60% do dano
    DamageResistanceModifier.ModifierOp = EGameplayModOp::Additive;
    // Usar atributo de armadura para resistência a dano
DamageResistanceModifier.Attribute = UAURACRONAttributeSet::GetArmorAttribute();
    ProtectionEffect->Modifiers.Add(DamageResistanceModifier);
    
    // Modificador para imunidade a efeitos negativos
    FGameplayModifierInfo StatusImmunityModifier;
    StatusImmunityModifier.ModifierMagnitude = FScalableFloat(1.0f);
    StatusImmunityModifier.ModifierOp = EGameplayModOp::Override;
    // Usar atributo de resistência mágica para imunidade a status
StatusImmunityModifier.Attribute = UAURACRONAttributeSet::GetMagicResistanceAttribute();
    ProtectionEffect->Modifiers.Add(StatusImmunityModifier);
    
    // Aplicar o efeito de proteção
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(ProtectionEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Sanctuary Island: Proteção concedida para %s por %.1f segundos (-40%% dano, imunidade a status)"), *TargetActor->GetName(), ProtectionDuration);
        }
    }
    
    // Criar feedback visual de proteção
    UNiagaraSystem* ProtectionVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_SanctuaryIslandProtection"));
    if (ProtectionVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ProtectionVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.2f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void ASanctuaryIsland::HealCharactersInRange()
{
    // Obter todos os personagens na área de cura
    TArray<AActor*> OverlappingActors;
    if (InteractionArea)
    {
        InteractionArea->GetOverlappingActors(OverlappingActors, ACharacter::StaticClass());
    }
    
    // Aplicar cura a cada personagem na área
    for (AActor* Actor : OverlappingActors)
    {
        ApplyHealing(Actor);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ADICIONAIS - UE 5.6 MODERN APIS
// ========================================

void ASanctuaryIsland::SetEnvironmentType(EAURACRONEnvironmentType NewType)
{
    // Implementação robusta para definir tipo de ambiente
    if (EnvironmentType != NewType)
    {
        EAURACRONEnvironmentType OldType = EnvironmentType;
        EnvironmentType = NewType;

        // Ajustar propriedades baseadas no novo tipo
        switch (NewType)
        {
            case EAURACRONEnvironmentType::RadiantPlains:
                HealingPower = 2.5f;
                StrategicValue = 80.0f;
                break;

            case EAURACRONEnvironmentType::ZephyrFirmament:
                HealingPower = 2.0f;
                StrategicValue = 75.0f;
                break;

            case EAURACRONEnvironmentType::PurgatoryRealm:
                HealingPower = 1.5f;
                StrategicValue = 70.0f;
                break;
        }

        UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::SetEnvironmentType - Environment type changed from %d to %d"),
               (int32)OldType, (int32)NewType);
    }
}

bool ASanctuaryIsland::HasValidHealingConfiguration() const
{
    // Implementação robusta para verificar configuração de cura

    // Verificar se tem fonte de cura
    if (!HealingFountain || !IsValid(HealingFountain))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidHealingConfiguration - No healing fountain"));
        return false;
    }

    // Verificar se tem efeito de cura
    if (!HealingEffect || !IsValid(HealingEffect))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidHealingConfiguration - No healing effect"));
        return false;
    }

    // Verificar se o poder de cura é válido
    if (HealingPower <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidHealingConfiguration - Invalid healing power: %.2f"), HealingPower);
        return false;
    }

    // Verificar se tem área de interação
    if (!InteractionArea || !IsValid(InteractionArea))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidHealingConfiguration - No interaction area"));
        return false;
    }

    return true;
}

bool ASanctuaryIsland::HasValidProtectionConfiguration() const
{
    // Implementação robusta para verificar configuração de proteção

    // Verificar se tem barreira protetora
    if (!ProtectiveBarrier || !IsValid(ProtectiveBarrier))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidProtectionConfiguration - No protective barrier"));
        return false;
    }

    // Verificar se a duração de proteção é válida
    if (ProtectionDuration <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidProtectionConfiguration - Invalid protection duration: %.2f"), ProtectionDuration);
        return false;
    }

    // Verificar se tem área de interação
    if (!InteractionArea || !IsValid(InteractionArea))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::HasValidProtectionConfiguration - No interaction area"));
        return false;
    }

    return true;
}

void ASanctuaryIsland::InitializeSanctuaryIsland()
{
    // Implementação robusta para inicializar ilha santuário

    // Configurar componentes baseados no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Configuração dourada para Radiant Plains
            if (HealingFountain && IsValid(HealingFountain))
            {
                // Criar material dinâmico dourado
                if (UMaterialInterface* BaseMaterial = HealingFountain->GetMaterial(0))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = HealingFountain->CreateAndSetMaterialInstanceDynamic(0);
                    if (DynamicMaterial)
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor(1.0f, 0.8f, 0.3f, 1.0f));
                        DynamicMaterial->SetScalarParameterValue(TEXT("Intensity"), 2.0f);
                    }
                }
            }
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Configuração azul etérea para Zephyr Firmament
            if (HealingFountain && IsValid(HealingFountain))
            {
                if (UMaterialInterface* BaseMaterial = HealingFountain->GetMaterial(0))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = HealingFountain->CreateAndSetMaterialInstanceDynamic(0);
                    if (DynamicMaterial)
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor(0.3f, 0.7f, 1.0f, 1.0f));
                        DynamicMaterial->SetScalarParameterValue(TEXT("Intensity"), 1.5f);
                    }
                }
            }
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Configuração sombria para Purgatory Realm
            if (HealingFountain && IsValid(HealingFountain))
            {
                if (UMaterialInterface* BaseMaterial = HealingFountain->GetMaterial(0))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = HealingFountain->CreateAndSetMaterialInstanceDynamic(0);
                    if (DynamicMaterial)
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor(0.5f, 0.2f, 0.8f, 1.0f));
                        DynamicMaterial->SetScalarParameterValue(TEXT("Intensity"), 1.0f);
                    }
                }
            }
            break;
    }

    // Ativar efeitos de partículas
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->Activate();
        HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        HealingEffect->SetFloatParameter(TEXT("EnvironmentType"), (float)(int32)EnvironmentType);
    }

    // Configurar zona segura como ativa por padrão
    ActivateSecureZone();

    UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::InitializeSanctuaryIsland - Sanctuary island initialized for environment type %d"), (int32)EnvironmentType);
}

void ASanctuaryIsland::ActivateSecureZone()
{
    // Implementação robusta para ativar zona segura
    bSecureZoneActive = true;

    // Ativar barreira protetora
    if (ProtectiveBarrier && IsValid(ProtectiveBarrier))
    {
        ProtectiveBarrier->SetVisibility(true);
        ProtectiveBarrier->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

        // Criar material dinâmico para barreira ativa
        if (UMaterialInterface* BaseMaterial = ProtectiveBarrier->GetMaterial(0))
        {
            UMaterialInstanceDynamic* DynamicMaterial = ProtectiveBarrier->CreateAndSetMaterialInstanceDynamic(0);
            if (DynamicMaterial)
            {
                DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), 0.7f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Active"), 1.0f);
                DynamicMaterial->SetVectorParameterValue(TEXT("BarrierColor"), FLinearColor(0.0f, 1.0f, 0.0f, 0.7f));
            }
        }
    }

    // Intensificar efeitos de cura
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("SecureZoneActive"), 1.0f);
        HealingEffect->SetFloatParameter(TEXT("Intensity"), HealingPower * 1.5f);
    }

    UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::ActivateSecureZone - Secure zone activated"));
}

void ASanctuaryIsland::DeactivateSecureZone()
{
    // Implementação robusta para desativar zona segura
    bSecureZoneActive = false;

    // Desativar barreira protetora
    if (ProtectiveBarrier && IsValid(ProtectiveBarrier))
    {
        ProtectiveBarrier->SetVisibility(false);
        ProtectiveBarrier->SetCollisionEnabled(ECollisionEnabled::NoCollision);

        // Atualizar material para barreira inativa
        if (UMaterialInterface* BaseMaterial = ProtectiveBarrier->GetMaterial(0))
        {
            UMaterialInstanceDynamic* DynamicMaterial = ProtectiveBarrier->CreateAndSetMaterialInstanceDynamic(0);
            if (DynamicMaterial)
            {
                DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), 0.0f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Active"), 0.0f);
            }
        }
    }

    // Reduzir efeitos de cura
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("SecureZoneActive"), 0.0f);
        HealingEffect->SetFloatParameter(TEXT("Intensity"), HealingPower);
    }

    UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::DeactivateSecureZone - Secure zone deactivated"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

bool ASanctuaryIsland::IsSecureZoneActive() const
{
    // Implementação robusta para verificar se zona segura está ativa
    return bSecureZoneActive;
}

bool ASanctuaryIsland::IsInCalmFlowSection() const
{
    // Implementação robusta para verificar se está em seção de flow calmo
    return bInCalmFlowSection;
}

void ASanctuaryIsland::SetInCalmFlowSection(bool bInCalmFlow)
{
    // Implementação robusta para definir se está em seção de flow calmo
    bInCalmFlowSection = bInCalmFlow;

    if (bInCalmFlow)
    {
        // Aumentar efeitos de cura quando em flow calmo
        HealingPower *= 1.5f;

        // Atualizar efeitos visuais
        if (HealingEffect && IsValid(HealingEffect))
        {
            HealingEffect->SetFloatParameter(TEXT("CalmFlowSection"), 1.0f);
            HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        }

        UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::SetInCalmFlowSection - Entered calm flow section"));
    }
    else
    {
        // Restaurar efeitos normais
        HealingPower /= 1.5f;

        if (HealingEffect && IsValid(HealingEffect))
        {
            HealingEffect->SetFloatParameter(TEXT("CalmFlowSection"), 0.0f);
            HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        }

        UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::SetInCalmFlowSection - Left calm flow section"));
    }
}

void ASanctuaryIsland::GrantHealingEffect(AActor* Actor)
{
    // Implementação robusta para conceder efeito de cura
    if (!Actor || !IsValid(Actor))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::GrantHealingEffect - Actor is invalid"));
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Aplicar efeito de cura usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::GrantHealingEffect - Applied healing effect to %s"), *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        if (ACharacter* Character = Cast<ACharacter>(Actor))
        {
            // Implementar cura direta se necessário
            UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::GrantHealingEffect - Applied direct healing to %s"), *Actor->GetName());
        }
    }
}

void ASanctuaryIsland::GrantProtectionEffect(AActor* Actor)
{
    // Implementação robusta para conceder efeito de proteção
    if (!Actor || !IsValid(Actor))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::GrantProtectionEffect - Actor is invalid"));
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Aplicar efeito de proteção usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::GrantProtectionEffect - Applied protection effect to %s"), *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::GrantProtectionEffect - Applied direct protection to %s"), *Actor->GetName());
    }
}

void ASanctuaryIsland::GrantVisionAmplificationEffect(AActor* Actor)
{
    // Implementação robusta para conceder efeito de amplificação de visão
    if (!Actor || !IsValid(Actor))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::GrantVisionAmplificationEffect - Actor is invalid"));
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Aplicar efeito de amplificação de visão usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::GrantVisionAmplificationEffect - Applied vision amplification to %s"), *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::GrantVisionAmplificationEffect - Applied direct vision amplification to %s"), *Actor->GetName());
    }
}

void ASanctuaryIsland::RemoveIslandEffects(AActor* Actor)
{
    // Implementação robusta para remover efeitos da ilha
    if (!Actor || !IsValid(Actor))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::RemoveIslandEffects - Actor is invalid"));
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Remover todos os efeitos da ilha usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::RemoveIslandEffects - Removed island effects from %s"), *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::RemoveIslandEffects - Removed direct effects from %s"), *Actor->GetName());
    }
}

void ASanctuaryIsland::UpdateIslandVisuals()
{
    // Implementação robusta para atualizar visuais da ilha

    // Atualizar efeitos de cura
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        HealingEffect->SetFloatParameter(TEXT("ProtectionDuration"), ProtectionDuration);
        HealingEffect->SetFloatParameter(TEXT("SecureZoneActive"), bSecureZoneActive ? 1.0f : 0.0f);
        HealingEffect->SetFloatParameter(TEXT("CalmFlowSection"), bInCalmFlowSection ? 1.0f : 0.0f);
    }

    // Atualizar material dinâmico se existir
    if (IslandMesh && IsValid(IslandMesh))
    {
        UMaterialInstanceDynamic* DynamicMaterial = IslandMesh->CreateAndSetMaterialInstanceDynamic(0);
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("HealingIntensity"), HealingPower);
            DynamicMaterial->SetScalarParameterValue(TEXT("SecureZoneActive"), bSecureZoneActive ? 1.0f : 0.0f);
        }
    }

    // Atualizar escala dos componentes baseado no poder de cura
    float ScaleMultiplier = 1.0f + (HealingPower - 2.0f) * 0.2f; // Base healing power é 2.0f

    if (HealingFountain && IsValid(HealingFountain))
    {
        HealingFountain->SetRelativeScale3D(FVector(1.5f * ScaleMultiplier, 1.5f * ScaleMultiplier, 2.0f * ScaleMultiplier));
    }

    if (AncientTree && IsValid(AncientTree))
    {
        AncientTree->SetRelativeScale3D(FVector(2.0f * ScaleMultiplier, 2.0f * ScaleMultiplier, 4.0f * ScaleMultiplier));
    }

    UE_LOG(LogTemp, Verbose, TEXT("ASanctuaryIsland::UpdateIslandVisuals - Island visuals updated"));
}

void ASanctuaryIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    // Implementação robusta para replicação de propriedades
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades específicas da Sanctuary Island
    DOREPLIFETIME(ASanctuaryIsland, bSecureZoneActive);
    DOREPLIFETIME(ASanctuaryIsland, bInCalmFlowSection);
    DOREPLIFETIME(ASanctuaryIsland, HealingPower);
    DOREPLIFETIME(ASanctuaryIsland, ProtectionDuration);
}

void ASanctuaryIsland::SetSecureZoneActive(bool bActive)
{
    // Implementação robusta para definir zona segura ativa
    bSecureZoneActive = bActive;

    if (bActive)
    {
        ActivateSecureZone();
    }
    else
    {
        DeactivateSecureZone();
    }

    UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::SetSecureZoneActive - Secure zone set to %s"), bActive ? TEXT("active") : TEXT("inactive"));
}

bool ASanctuaryIsland::IsActorInSecureZone(AActor* Actor) const
{
    // Implementação robusta para verificar se ator está na zona segura
    if (!Actor || !IsValid(Actor) || !bSecureZoneActive)
    {
        return false;
    }

    // Verificar se o ator está dentro do raio da zona segura
    if (SecureZone && IsValid(SecureZone))
    {
        float Distance = FVector::Dist(GetActorLocation(), Actor->GetActorLocation());
        float ZoneRadius = SecureZone->GetScaledSphereRadius();

        bool bInZone = Distance <= ZoneRadius;

        UE_LOG(LogTemp, VeryVerbose, TEXT("ASanctuaryIsland::IsActorInSecureZone - Actor %s is %s secure zone (Distance: %.2f, Radius: %.2f)"),
               *Actor->GetName(), bInZone ? TEXT("in") : TEXT("outside"), Distance, ZoneRadius);

        return bInZone;
    }

    return false;
}

void ASanctuaryIsland::ApplyAllSanctuaryEffects(AActor* Actor)
{
    // Implementação robusta para aplicar todos os efeitos do santuário
    if (!Actor || !IsValid(Actor))
    {
        UE_LOG(LogTemp, Warning, TEXT("ASanctuaryIsland::ApplyAllSanctuaryEffects - Actor is invalid"));
        return;
    }

    // Aplicar efeito de cura
    GrantHealingEffect(Actor);

    // Aplicar efeito de proteção se zona segura estiver ativa
    if (bSecureZoneActive)
    {
        GrantProtectionEffect(Actor);
    }

    // Aplicar amplificação de visão se em seção de flow calmo
    if (bInCalmFlowSection)
    {
        GrantVisionAmplificationEffect(Actor);
    }

    UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::ApplyAllSanctuaryEffects - Applied all sanctuary effects to %s"), *Actor->GetName());
}

void ASanctuaryIsland::UpdateBasedOnMapPhase(EAURACRONMapPhase Phase)
{
    // Implementação robusta para atualizar baseado na fase do mapa
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - efeitos suaves
            HealingPower = 2.0f;
            ProtectionDuration = 30.0f;
            break;

        case EAURACRONMapPhase::Convergence:
            // Fase de convergência - efeitos moderados
            HealingPower = 3.0f;
            ProtectionDuration = 45.0f;
            break;

        case EAURACRONMapPhase::Intensification:
            // Fase de intensificação - efeitos fortes
            HealingPower = 4.0f;
            ProtectionDuration = 60.0f;
            break;

        case EAURACRONMapPhase::Resolution:
            // Fase de resolução - efeitos máximos
            HealingPower = 5.0f;
            ProtectionDuration = 90.0f;
            break;
    }

    // Atualizar visuais baseado na nova configuração
    UpdateIslandVisuals();

    UE_LOG(LogTemp, Log, TEXT("ASanctuaryIsland::UpdateBasedOnMapPhase - Updated for phase %d (Healing: %.1f, Protection: %.1f)"),
           (int32)Phase, HealingPower, ProtectionDuration);
}