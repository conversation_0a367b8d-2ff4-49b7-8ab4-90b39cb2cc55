// AURACRONPCGSubsystem.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação do subsistema principal para gerenciar a geração procedural do mapa

#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGVolume.h"
#include "PCGSettings.h"
#include "PCGPoint.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "Components/AURACRONMovementComponent.h"

UAURACRONPCGSubsystem::UAURACRONPCGSubsystem()
    : PrismalFlowComponent(nullptr)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , ElapsedTime(0.0f)
{
}

void UAURACRONPCGSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    // Inicializar os componentes PCG
    SetupPCGComponents();
}

void UAURACRONPCGSubsystem::Deinitialize()
{
    // Limpar referências
    EnvironmentComponents.Empty();
    TrailComponents.Empty();
    PrismalFlowComponent = nullptr;
    EnvironmentVolumes.Empty();
    
    Super::Deinitialize();
}

bool UAURACRONPCGSubsystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // Criar apenas no servidor ou em jogos standalone
    UWorld* World = Cast<UWorld>(Outer);
    return World && (World->GetNetMode() == NM_DedicatedServer || 
                    World->GetNetMode() == NM_ListenServer || 
                    World->GetNetMode() == NM_Standalone);
}

void UAURACRONPCGSubsystem::GenerateMap(FVector MapCenter, float MapRadius)
{
    // Resetar o tempo decorrido
    ElapsedTime = 0.0f;
    
    // Definir a fase inicial
    CurrentMapPhase = EAURACRONMapPhase::Awakening;
    
    // Gerar os ambientes
    GenerateEnvironment(EAURACRONEnvironmentType::RadiantPlains, MapCenter, MapRadius);
    
    // No início, apenas o Radiant Plains está totalmente ativo
    // Os outros ambientes são gerados como "preview zones"
    FVector ZephyrCenter = MapCenter + FVector(0.0f, 0.0f, 1000.0f); // Acima do Radiant Plains
    GenerateEnvironment(EAURACRONEnvironmentType::ZephyrFirmament, ZephyrCenter, MapRadius * 0.8f);
    
    FVector PurgatoryCenter = MapCenter + FVector(0.0f, 0.0f, -500.0f); // Abaixo do Radiant Plains
    GenerateEnvironment(EAURACRONEnvironmentType::PurgatoryRealm, PurgatoryCenter, MapRadius * 0.7f);
    
    // Gerar as trilhas iniciais
    // Solar Trails - Seguem o sol, mais fortes ao meio-dia
    TArray<FVector> SolarControlPoints;
    // Calcular pontos de controle baseados na posição do sol (simplificado para este exemplo)
    const int32 NumSolarPoints = 10;
    for (int32 i = 0; i < NumSolarPoints; ++i)
    {
        float Angle = 2.0f * PI * i / NumSolarPoints;
        float X = MapCenter.X + MapRadius * 0.6f * FMath::Cos(Angle);
        float Y = MapCenter.Y + MapRadius * 0.6f * FMath::Sin(Angle);
        SolarControlPoints.Add(FVector(X, Y, MapCenter.Z + 50.0f));
    }
    GenerateTrail(EAURACRONTrailType::Solar, SolarControlPoints);
    
    // Axis Trails - Conectam pontos de transição entre ambientes
    TArray<FVector> AxisControlPoints;
    // Criar pontos que conectam os três ambientes
    AxisControlPoints.Add(MapCenter);
    AxisControlPoints.Add(ZephyrCenter);
    AxisControlPoints.Add(PurgatoryCenter);
    GenerateTrail(EAURACRONTrailType::Axis, AxisControlPoints);
    
    // Lunar Trails - Visíveis apenas à noite, permitem furtividade
    TArray<FVector> LunarControlPoints;
    // Calcular pontos de controle baseados na posição da lua (simplificado)
    const int32 NumLunarPoints = 8;
    for (int32 i = 0; i < NumLunarPoints; ++i)
    {
        float Angle = 2.0f * PI * i / NumLunarPoints + PI / NumLunarPoints; // Deslocado em relação ao Solar
        float X = MapCenter.X + MapRadius * 0.4f * FMath::Cos(Angle);
        float Y = MapCenter.Y + MapRadius * 0.4f * FMath::Sin(Angle);
        LunarControlPoints.Add(FVector(X, Y, MapCenter.Z + 30.0f));
    }
    GenerateTrail(EAURACRONTrailType::Lunar, LunarControlPoints);
    
    // Gerar o Prismal Flow - O rio de energia serpenteante que atravessa os três ambientes
    TArray<FVector> FlowControlPoints;
    // Criar um caminho serpenteante que passa pelos três ambientes
    const int32 NumFlowPoints = 20;
    for (int32 i = 0; i < NumFlowPoints; ++i)
    {
        float T = static_cast<float>(i) / (NumFlowPoints - 1);
        float Angle = 4.0f * PI * T;
        float Radius = MapRadius * (0.3f + 0.2f * FMath::Sin(3.0f * Angle));
        float X = MapCenter.X + Radius * FMath::Cos(Angle);
        float Y = MapCenter.Y + Radius * FMath::Sin(Angle);
        float Z = MapCenter.Z + 200.0f * FMath::Sin(T * PI); // Varia a altura para passar pelos três ambientes
        FlowControlPoints.Add(FVector(X, Y, Z));
    }
    GeneratePrismalFlow(FlowControlPoints, 30.0f); // Largura inicial de 30 unidades
    
    // Gerar as ilhas estratégicas
    // Nexus Islands (5 total) - Posicionadas em curvas-chave do Prismal Flow
    for (int32 i = 0; i < 5; ++i)
    {
        int32 Index = (i * NumFlowPoints / 5) % NumFlowPoints;
        GenerateIsland(EAURACRONIslandType::Nexus, FlowControlPoints[Index], 150.0f);
    }
    
    // Sanctuary Islands (8 total) - Espalhadas ao longo de seções mais calmas do Flow
    for (int32 i = 0; i < 8; ++i)
    {
        int32 Index = (i * NumFlowPoints / 8 + NumFlowPoints / 16) % NumFlowPoints;
        GenerateIsland(EAURACRONIslandType::Sanctuary, FlowControlPoints[Index], 100.0f);
    }
    
    // Arsenal Islands (6 total) - Próximas aos pontos de transição de ambiente
    for (int32 i = 0; i < 6; ++i)
    {
        int32 Index = (i * NumFlowPoints / 6 + NumFlowPoints / 12) % NumFlowPoints;
        GenerateIsland(EAURACRONIslandType::Arsenal, FlowControlPoints[Index], 120.0f);
    }
    
    // Chaos Islands (4 total) - Nos pontos de interseção do Flow
    for (int32 i = 0; i < 4; ++i)
    {
        float Angle = 2.0f * PI * i / 4;
        float X = MapCenter.X + MapRadius * 0.5f * FMath::Cos(Angle);
        float Y = MapCenter.Y + MapRadius * 0.5f * FMath::Sin(Angle);
        GenerateIsland(EAURACRONIslandType::Chaos, FVector(X, Y, MapCenter.Z), 130.0f);
    }
}

void UAURACRONPCGSubsystem::AdvanceToNextPhase()
{
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        SetMapPhase(EAURACRONMapPhase::Convergence);
        break;
    case EAURACRONMapPhase::Convergence:
        SetMapPhase(EAURACRONMapPhase::Intensification);
        break;
    case EAURACRONMapPhase::Intensification:
        SetMapPhase(EAURACRONMapPhase::Resolution);
        break;
    case EAURACRONMapPhase::Resolution:
        // Já estamos na fase final
        break;
    }
}

void UAURACRONPCGSubsystem::SetMapPhase(EAURACRONMapPhase NewPhase)
{
    if (CurrentMapPhase != NewPhase)
    {
        CurrentMapPhase = NewPhase;
        
        // Atualizar os ambientes, trilhas e o Prismal Flow com base na nova fase
        UpdateEnvironmentBasedOnPhase();
        UpdateTrailsBasedOnPhase();
        UpdatePrismalFlowBasedOnPhase();
    }
}

void UAURACRONPCGSubsystem::GenerateEnvironment(EAURACRONEnvironmentType EnvironmentType, FVector Center, float Radius)
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }
    
    // Criar um volume PCG para o ambiente se ainda não existir
    if (!EnvironmentVolumes.Contains(EnvironmentType))
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
        
        APCGVolume* NewVolume = World->SpawnActor<APCGVolume>(Center, FRotator::ZeroRotator, SpawnParams);
        if (NewVolume)
        {
            // Configurar o volume
            NewVolume->SetActorScale3D(FVector(Radius / 100.0f)); // Ajustar escala com base no raio
            
            // Adicionar um componente PCG se ainda não existir
            if (!EnvironmentComponents.Contains(EnvironmentType))
            {
                UPCGComponent* PCGComp = NewObject<UPCGComponent>(NewVolume);
                if (PCGComp)
                {
                    PCGComp->RegisterComponent();
                    
                    // Configurar o componente PCG com base no tipo de ambiente
                    // Isso seria feito carregando um PCGSettings específico para cada ambiente
                    // Por simplicidade, estamos apenas criando o componente aqui
                    
                    EnvironmentComponents.Add(EnvironmentType, PCGComp);
                }
            }
            
            EnvironmentVolumes.Add(EnvironmentType, NewVolume);
        }
    }
}

void UAURACRONPCGSubsystem::GenerateTrail(EAURACRONTrailType TrailType, TArray<FVector> ControlPoints)
{
    UWorld* World = GetWorld();
    if (!World || ControlPoints.Num() < 2)
    {
        return;
    }
    
    // Criar um ator para a trilha
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    // Usar o primeiro ponto de controle como localização inicial
    APCGVolume* TrailVolume = World->SpawnActor<APCGVolume>(ControlPoints[0], FRotator::ZeroRotator, SpawnParams);
    if (TrailVolume)
    {
        // Adicionar um componente PCG
        UPCGComponent* PCGComp = NewObject<UPCGComponent>(TrailVolume);
        if (PCGComp)
        {
            PCGComp->RegisterComponent();
            
            // Configurar o componente PCG com base no tipo de trilha
            // Isso seria feito carregando um PCGSettings específico para cada tipo de trilha
            // Por simplicidade, estamos apenas criando o componente aqui
            
            TrailComponents.Add(TrailType, PCGComp);
        }
    }
}

void UAURACRONPCGSubsystem::UpdateTrailPositions(float DeltaTime)
{
    // Atualizar a posição das trilhas com base no tempo
    ElapsedTime += DeltaTime;
    
    // Obter tempo atual do mundo para cálculos solares
    float WorldTime = GetWorld()->GetTimeSeconds();
    float TimeOfDay = FMath::Fmod(WorldTime / 3600.0f, 24.0f); // Ciclo de 24 horas
    
    // Calcular posição do sol (0.0 = meia-noite, 12.0 = meio-dia)
    float SunAngle = (TimeOfDay / 24.0f) * 2.0f * PI;
    float SunElevation = FMath::Sin(SunAngle) * 90.0f; // -90 a +90 graus
    float SunAzimuth = (TimeOfDay / 24.0f) * 360.0f; // 0 a 360 graus
    
    // Atualizar Solar Trails com base na posição do sol
    if (TrailComponents.Contains(EAURACRONTrailType::Solar))
    {
        UPCGComponent* SolarTrailComp = TrailComponents[EAURACRONTrailType::Solar];
        if (SolarTrailComp)
        {
            // Calcular direção solar
            FVector SunDirection = FVector(
                FMath::Cos(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
                FMath::Sin(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
                FMath::Sin(FMath::DegreesToRadians(SunElevation))
            );
            
            // Aplicar transformação baseada na posição solar
            float SolarIntensity = FMath::Max(0.0f, SunElevation / 90.0f);
            // UPCGComponent não tem GetComponentTransform/SetWorldTransform
            // Usar o owner actor para transformações
            if (AActor* OwnerActor = SolarTrailComp->GetOwner())
            {
                FTransform SolarTransform = OwnerActor->GetActorTransform();

                // Ajustar posição com movimento sutil seguindo o sol
                FVector SolarOffset = SunDirection * 100.0f * SolarIntensity * FMath::Sin(ElapsedTime * 0.1f);
                SolarTransform.AddToTranslation(SolarOffset * DeltaTime * 0.05f);

                OwnerActor->SetActorTransform(SolarTransform);
            }
            SolarTrailComp->Generate();
        }
    }
    
    // Atualizar Axis Trails - manter conexões estáveis
    if (TrailComponents.Contains(EAURACRONTrailType::Axis))
    {
        UPCGComponent* AxisTrailComp = TrailComponents[EAURACRONTrailType::Axis];
        if (AxisTrailComp)
        {
            // Axis Trails mantêm posições fixas mas variam intensidade
            float ConnectionPulse = 0.7f + 0.3f * FMath::Sin(ElapsedTime * 0.3f);
            // Aplicar variação de intensidade através de parâmetros PCG
        }
    }
    
    // Atualizar Lunar Trails com base na posição da lua
    if (TrailComponents.Contains(EAURACRONTrailType::Lunar))
    {
        UPCGComponent* LunarTrailComp = TrailComponents[EAURACRONTrailType::Lunar];
        if (LunarTrailComp)
        {
            // Lunar Trails são mais ativos durante a noite
            float NightIntensity = FMath::Max(0.0f, -SunElevation / 90.0f);
            float LunarPhase = FMath::Sin(ElapsedTime * 0.05f) * 0.5f + 0.5f; // Simulação de fases lunares
            
            float LunarActivity = NightIntensity * LunarPhase;
            
            // Calcular posição lunar (oposta ao sol com variação)
            float MoonAzimuth = SunAzimuth + 180.0f + 30.0f * FMath::Sin(ElapsedTime * 0.02f);
            FVector MoonDirection = FVector(
                FMath::Cos(FMath::DegreesToRadians(MoonAzimuth)),
                FMath::Sin(FMath::DegreesToRadians(MoonAzimuth)),
                FMath::Sin(FMath::DegreesToRadians(-SunElevation * 0.8f))
            );
            
            // UPCGComponent não tem GetComponentTransform/SetWorldTransform
            // Usar o owner actor para transformações
            if (AActor* OwnerActor = LunarTrailComp->GetOwner())
            {
                FTransform LunarTransform = OwnerActor->GetActorTransform();
                FVector LunarOffset = MoonDirection * 80.0f * LunarActivity * FMath::Sin(ElapsedTime * 0.15f);
                LunarTransform.AddToTranslation(LunarOffset * DeltaTime * 0.03f);

                OwnerActor->SetActorTransform(LunarTransform);
            }
            if (LunarActivity > 0.1f) // Só regenerar se suficientemente ativo
            {
                LunarTrailComp->Generate();
            }
        }
    }
}

void UAURACRONPCGSubsystem::GeneratePrismalFlow(TArray<FVector> FlowControlPoints, float Width)
{
    UWorld* World = GetWorld();
    if (!World || FlowControlPoints.Num() < 2)
    {
        return;
    }
    
    // Criar um ator para o Prismal Flow
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    // Usar o primeiro ponto de controle como localização inicial
    APCGVolume* FlowVolume = World->SpawnActor<APCGVolume>(FlowControlPoints[0], FRotator::ZeroRotator, SpawnParams);
    if (FlowVolume)
    {
        // Adicionar um componente PCG
        UPCGComponent* PCGComp = NewObject<UPCGComponent>(FlowVolume);
        if (PCGComp)
        {
            PCGComp->RegisterComponent();
            
            // Configurar o componente PCG para o Prismal Flow
            // Isso seria feito carregando um PCGSettings específico
            // Por simplicidade, estamos apenas criando o componente aqui
            
            PrismalFlowComponent = PCGComp;
        }
    }
}

void UAURACRONPCGSubsystem::UpdatePrismalFlow(float DeltaTime)
{
    // Atualizar o Prismal Flow com base no tempo
    ElapsedTime += DeltaTime;
    
    // A cada 10 minutos, o padrão de fluxo muda
    float FlowChangeInterval = 600.0f; // 10 minutos em segundos
    float NormalizedTime = FMath::Fmod(ElapsedTime, FlowChangeInterval) / FlowChangeInterval;
    
    // Lógica para atualizar o Prismal Flow
    // Por simplicidade, não implementamos a lógica completa aqui
}

void UAURACRONPCGSubsystem::GenerateIsland(EAURACRONIslandType IslandType, FVector Location, float Radius)
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }
    
    // Criar um volume PCG para a ilha
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    
    APCGVolume* IslandVolume = World->SpawnActor<APCGVolume>(Location, FRotator::ZeroRotator, SpawnParams);
    if (IslandVolume)
    {
        // Configurar o volume
        IslandVolume->SetActorScale3D(FVector(Radius / 100.0f)); // Ajustar escala com base no raio
        
        // Adicionar um componente PCG
        UPCGComponent* PCGComp = NewObject<UPCGComponent>(IslandVolume);
        if (PCGComp)
        {
            PCGComp->RegisterComponent();
            
            // Configurar o componente PCG com base no tipo de ilha
            // Isso seria feito carregando um PCGSettings específico para cada tipo de ilha
            // Por simplicidade, estamos apenas criando o componente aqui
        }
    }
}

void UAURACRONPCGSubsystem::SetupPCGComponents()
{
    // Inicializar os mapas
    EnvironmentComponents.Empty();
    TrailComponents.Empty();
    EnvironmentVolumes.Empty();
    PrismalFlowComponent = nullptr;
}

void UAURACRONPCGSubsystem::UpdateEnvironmentBasedOnPhase()
{
    // Atualizar os ambientes com base na fase atual do mapa
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Apenas Radiant Plains ativo, outros como "preview zones"
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::RadiantPlains))
        {
            // Configurar Radiant Plains como totalmente ativo
        }
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::ZephyrFirmament))
        {
            // Configurar ZephyrFirmament como preview zone
        }
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::PurgatoryRealm))
        {
            // Configurar PurgatoryRealm como preview zone
        }
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Transição suave para Zephyr Firmament, Abyss como preview area
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::RadiantPlains))
        {
            // Manter Radiant Plains ativo
        }
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::ZephyrFirmament))
        {
            // Ativar ZephyrFirmament gradualmente
        }
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::PurgatoryRealm))
        {
            // Manter PurgatoryRealm como preview zone
        }
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Limites entre camadas começam a se misturar
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::RadiantPlains))
        {
            // Manter Radiant Plains ativo com mudanças de terreno
        }
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::ZephyrFirmament))
        {
            // Manter ZephyrFirmament ativo com mudanças de terreno
        }
        if (EnvironmentVolumes.Contains(EAURACRONEnvironmentType::PurgatoryRealm))
        {
            // Ativar PurgatoryRealm gradualmente
        }
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Convergência final
        // Todos os ambientes ativos e interconectados
        break;
    }
}

void UAURACRONPCGSubsystem::UpdateTrailsBasedOnPhase()
{
    // Atualizar as trilhas com base na fase atual do mapa
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Trilhas a 50% de potência
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Trilhas atingem potência com base na capacidade do dispositivo
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Trilhas se cruzam com base na capacidade de renderização
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Trilhas convergem com efeitos escaláveis
        break;
    }
}

void UAURACRONPCGSubsystem::UpdatePrismalFlowBasedOnPhase()
{
    // Atualizar o Prismal Flow com base na fase atual do mapa
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Prismal Flow flui em padrão predeterminado
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Corrente do Prismal Flow gradualmente se fortalece
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Prismal Flow com volatilidade adaptada ao hardware
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Surto final do Prismal Flow com intensidade adaptativa
        break;
    }
}

int32 UAURACRONPCGSubsystem::GetCurrentQualityLevel() const
{
    // Obter nível de qualidade atual baseado nas configurações do sistema
    // Usar APIs modernas do UE 5.6 para detectar qualidade automaticamente

    if (UWorld* World = GetWorld())
    {
        // Verificar configurações de qualidade do engine
        if (GEngine)
        {
            // Usar configurações de qualidade baseadas na performance
            // 0 = Baixa, 1 = Média, 2 = Alta, 3 = Épica

            // Detectar baseado em configurações de renderização
            static const auto* CVarQualityLevel = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ViewDistanceQuality"));
            if (CVarQualityLevel)
            {
                int32 ViewDistanceQuality = CVarQualityLevel->GetInt();
                return FMath::Clamp(ViewDistanceQuality, 0, 3);
            }

            // Fallback: detectar baseado na performance
            float DeltaTime = World->GetDeltaSeconds();
            if (DeltaTime < 0.016f) // 60+ FPS
            {
                return 3; // Qualidade épica
            }
            else if (DeltaTime < 0.033f) // 30+ FPS
            {
                return 2; // Alta qualidade
            }
            else if (DeltaTime < 0.050f) // 20+ FPS
            {
                return 1; // Qualidade média
            }
            else
            {
                return 0; // Baixa qualidade
            }
        }
    }

    // Valor padrão
    return 2; // Alta qualidade por padrão
}

// ========================================
// MÉTODOS DE INTEGRAÇÃO COM MOVIMENTO
// ========================================

void UAURACRONPCGSubsystem::OnCharacterEnteredPrismalFlow(ACharacter* Character, FVector FlowDirection, float FlowSpeed)
{
    if (!Character)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("Personagem %s entrou no fluxo prismal - Direção: %s, Velocidade: %f"),
           *Character->GetName(), *FlowDirection.ToString(), FlowSpeed);

    // Aplicar efeitos do fluxo prismal ao personagem
    if (UAURACRONMovementComponent* MovementComp = Character->FindComponentByClass<UAURACRONMovementComponent>())
    {
        // Notificar o componente de movimento sobre entrada no fluxo
        // Implementar lógica específica conforme necessário
    }
}

void UAURACRONPCGSubsystem::OnCharacterExitedPrismalFlow(ACharacter* Character)
{
    if (!Character)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("Personagem %s saiu do fluxo prismal"), *Character->GetName());

    // Remover efeitos do fluxo prismal do personagem
    if (UAURACRONMovementComponent* MovementComp = Character->FindComponentByClass<UAURACRONMovementComponent>())
    {
        // Notificar o componente de movimento sobre saída do fluxo
        // Implementar lógica específica conforme necessário
    }
}

void UAURACRONPCGSubsystem::OnCharacterMovementStateChanged(ACharacter* Character, EAURACRONMovementState MovementState)
{
    if (!Character)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("Estado de movimento do personagem %s mudou para: %d"),
           *Character->GetName(), (int32)MovementState);

    // Reagir à mudança de estado de movimento
    // Implementar lógica específica baseada no estado
    switch (MovementState)
    {
        case EAURACRONMovementState::Normal:
            // Lógica para movimento normal
            break;
        case EAURACRONMovementState::PrismalFlow:
            // Lógica para movimento no fluxo prismal
            break;
        case EAURACRONMovementState::SigilDash:
            // Lógica para dash de sígilo
            break;
        case EAURACRONMovementState::EnvironmentBoost:
            // Lógica para boost de ambiente
            break;
        case EAURACRONMovementState::Stunned:
            // Lógica para estado atordoado
            break;
        case EAURACRONMovementState::Rooted:
            // Lógica para estado enraizado
            break;
        default:
            break;
    }
}