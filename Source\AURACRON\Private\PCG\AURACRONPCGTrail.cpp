// AURACRONPCGTrail.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação da classe para gerenciar as trilhas dinâmicas

#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGSettings.h"
#include "PCGGraph.h"
#include "Helpers/PCGGraphParametersHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "NiagaraComponent.h"
#include "Math/UnrealMathUtility.h"
#include "NiagaraSystem.h"
#include "Components/PointLightComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Engine/OverlapResult.h"

// Implementação da classe base ATrailBase
ATrailBase::ATrailBase()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar o componente Spline
    SplineComponent = CreateDefaultSubobject<USplineComponent>(TEXT("SplineComponent"));
    SplineComponent->SetupAttachment(RootComponent);
    SplineComponent->SetClosedLoop(false);
    
    // Criar o componente de colisão para detectar jogadores
    CollisionComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetupAttachment(RootComponent);
    CollisionComponent->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
    CollisionComponent->SetGenerateOverlapEvents(true);
    CollisionComponent->SetBoxExtent(FVector(250.0f, 250.0f, 100.0f)); // 5m x 5m x 2m por padrão

    // Criar o componente de efeito Niagara
    TrailEffectComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TrailEffectComponent"));
    TrailEffectComponent->SetupAttachment(RootComponent);
    TrailEffectComponent->SetAutoActivate(false);

    // Criar o componente Niagara principal para trilhas
    TrailNiagaraComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TrailNiagaraComponent"));
    TrailNiagaraComponent->SetupAttachment(RootComponent);
    TrailNiagaraComponent->SetAutoActivate(false);

    // Criar o componente de luz
    TrailLightComponent = CreateDefaultSubobject<UPointLightComponent>(TEXT("TrailLightComponent"));
    TrailLightComponent->SetupAttachment(RootComponent);
    TrailLightComponent->SetIntensity(5000.0f);
    TrailLightComponent->SetLightColor(FLinearColor(0.5f, 0.8f, 1.0f));
    TrailLightComponent->SetAttenuationRadius(500.0f);
    TrailLightComponent->SetCastShadows(false);
    TrailLightComponent->SetVisibility(false);

    // Valores padrão
    bIsActive = false;
    bIsVisible = true;
    TrailType = EAURACRONTrailType::None;
}

void ATrailBase::BeginPlay()
{
    Super::BeginPlay();
    
    // Registrar eventos de overlap
    CollisionComponent->OnComponentBeginOverlap.AddDynamic(this, &ATrailBase::OnOverlapBegin);
    CollisionComponent->OnComponentEndOverlap.AddDynamic(this, &ATrailBase::OnOverlapEnd);
    
    // Iniciar com trail desativado
    SetTrailActive(false);
}

void ATrailBase::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Implementação básica que pode ser sobrescrita pelas classes derivadas
    if (bIsActive)
    {
        // Atualizar efeitos visuais se necessário
        UpdateTrailEffects(DeltaTime);
    }
}

void ATrailBase::SetTrailActive(bool bActive)
{
    bIsActive = bActive;
    
    // Ativar/desativar componentes visuais
    TrailEffectComponent->SetVisibility(bActive);
    TrailLightComponent->SetVisibility(bActive);
    
    // Ativar/desativar o sistema de partículas
    if (bActive)
    {
        TrailEffectComponent->Activate(true);
    }
    else
    {
        TrailEffectComponent->Deactivate();
    }
}

void ATrailBase::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação base que pode ser sobrescrita pelas classes derivadas
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (Character)
    {
        // Aplicar efeito básico (pode ser sobrescrito por classes derivadas)
        UE_LOG(LogTemp, Log, TEXT("Trail Base: Aplicando efeito básico ao personagem %s"), *Character->GetName());
    }
}

// Construtor da classe ASolarTrail
ASolarTrail::ASolarTrail()
{
    // Configurações padrão para Solar Trail
    TrailType = EAURACRONTrailType::Solar;
    PowerPercentage = 1.0f;
    
    // Configurar luz com tom dourado para o efeito solar
    if (TrailLightComponent)
    {
        TrailLightComponent->SetLightColor(FLinearColor(1.0f, 0.8f, 0.3f, 1.0f)); // Dourado solar
        TrailLightComponent->SetIntensity(3.0f);
        TrailLightComponent->SetAttenuationRadius(800.0f);
    }
}

void ASolarTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar efeitos baseados no tempo do dia
    float TimeOfDay = GetWorld()->GetTimeSeconds();
    float DayNightCycle = FMath::Sin(TimeOfDay * 0.1f) * 0.5f + 0.5f; // Ciclo de 0 a 1
    
    // Ajustar intensidade da luz baseado no ciclo solar
    if (TrailLightComponent)
    {
        float SolarIntensity = FMath::Lerp(1.0f, 4.0f, DayNightCycle);
        TrailLightComponent->SetIntensity(SolarIntensity);
        
        // Cor mais intensa durante o "dia"
        FLinearColor SolarColor = FLinearColor::LerpUsingHSV(
            FLinearColor(1.0f, 0.6f, 0.2f, 1.0f), // Laranja suave
            FLinearColor(1.0f, 0.9f, 0.4f, 1.0f), // Dourado brilhante
            DayNightCycle
        );
        TrailLightComponent->SetLightColor(SolarColor);
    }
    
    // Atualizar parâmetros do Niagara para efeitos solares
    if (TrailNiagaraComponent)
    {
        TrailNiagaraComponent->SetFloatParameter(TEXT("SolarIntensity"), DayNightCycle);
        TrailNiagaraComponent->SetFloatParameter(TEXT("ParticleCount"), FMath::Lerp(50.0f, 200.0f, DayNightCycle));
    }
}

void ASolarTrail::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação específica para o Solar Trail
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar boost de velocidade
    UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement();
    if (MovementComp)
    {
        // Armazenar velocidade original
        float OriginalSpeed = MovementComp->MaxWalkSpeed;
        
        // Aumentar velocidade em 30%
        MovementComp->MaxWalkSpeed = OriginalSpeed * 1.3f;
        
        UE_LOG(LogTemp, Display, TEXT("Solar Trail: Aplicando boost de velocidade para %s"), *Character->GetName());
    }
    
    // Aplicar efeito de regeneração (seria implementado via GameplayEffect)
    UE_LOG(LogTemp, Display, TEXT("Solar Trail: Aplicando regeneração para %s"), *Character->GetName());
    
    // Atualizar propriedades visuais do trail
    UpdateTrailProperties();
}

// Construtor da classe AAxisTrail
AAxisTrail::AAxisTrail()
{
    // Configurações padrão para Axis Trail
    TrailType = EAURACRONTrailType::Axis;
    PowerPercentage = 1.0f;
    ForceStrength = 1000.0f;
    EffectRadius = 500.0f;
    bAttractMode = true;
    
    // Configurar luz com tom violeta para o efeito axis
    if (TrailLightComponent)
    {
        TrailLightComponent->SetLightColor(FLinearColor(0.8f, 0.3f, 1.0f, 1.0f)); // Violeta axis
        TrailLightComponent->SetIntensity(2.5f);
        TrailLightComponent->SetAttenuationRadius(600.0f);
    }
}

void AAxisTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Aplicar força aos atores próximos continuamente
    ApplyForceToNearbyActors();
    
    // Atualizar efeitos visuais baseados na força
    if (TrailLightComponent)
    {
        // Pulsar a luz baseado na força
        float PulseIntensity = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.0f) * 0.3f + 0.7f;
        TrailLightComponent->SetIntensity(2.5f * PulseIntensity);
        
        // Cor mais intensa quando em modo atração
        FLinearColor AxisColor = bAttractMode ? 
            FLinearColor(0.8f, 0.3f, 1.0f, 1.0f) : // Violeta para atração
            FLinearColor(1.0f, 0.3f, 0.3f, 1.0f);  // Vermelho para repulsão
        TrailLightComponent->SetLightColor(AxisColor);
    }
    
    // Atualizar parâmetros do Niagara
    if (TrailNiagaraComponent)
    {
        TrailNiagaraComponent->SetFloatParameter(TEXT("ForceStrength"), ForceStrength);
        TrailNiagaraComponent->SetFloatParameter(TEXT("EffectRadius"), EffectRadius);
        TrailNiagaraComponent->SetBoolParameter(TEXT("AttractMode"), bAttractMode);
    }
}

void AAxisTrail::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação específica para o Axis Trail
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Marcar o personagem para transição instantânea
    UE_LOG(LogTemp, Display, TEXT("Axis Trail: Permitindo transição instantânea para %s"), *Character->GetName());
    
    // Aplicar força aos atores próximos
    ApplyForceToNearbyActors();
}

void AAxisTrail::ApplyForceToNearbyActors()
{
    if (!GetWorld())
    {
        return;
    }
    
    // Definir raio de busca para atores próximos
    float SearchRadius = 1000.0f; // 10 metros
    FVector TrailCenter = GetActorLocation();
    
    // Buscar todos os atores próximos
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    
    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        TrailCenter,
        FQuat::Identity,
        ECC_Pawn,
        FCollisionShape::MakeSphere(SearchRadius),
        QueryParams
    );
    
    if (bHasOverlaps)
    {
        for (const FOverlapResult& Result : OverlapResults)
        {
            if (AActor* OverlappedActor = Result.GetActor())
            {
                // Aplicar força de repulsão ou atração baseada no tipo de ator
                if (UPrimitiveComponent* PrimComp = Result.GetComponent())
                {
                    FVector DirectionToActor = (OverlappedActor->GetActorLocation() - TrailCenter).GetSafeNormal();
                    float Distance = FVector::Distance(OverlappedActor->GetActorLocation(), TrailCenter);
                    
                    // Calcular força baseada na distância (mais forte quando mais próximo)
                    float LocalForceStrength = FMath::Clamp(1.0f - (Distance / SearchRadius), 0.1f, 1.0f) * 500000.0f;
                    
                    // Aplicar força de repulsão para objetos físicos
                    if (PrimComp->IsSimulatingPhysics())
                    {
                        FVector ForceVector = DirectionToActor * LocalForceStrength;
                        PrimComp->AddImpulseAtLocation(ForceVector, OverlappedActor->GetActorLocation());

                        UE_LOG(LogTemp, Verbose, TEXT("Axis Trail: Aplicando força de %f ao ator %s"),
                               LocalForceStrength, *OverlappedActor->GetName());
                    }
                    
                    // Para personagens, aplicar efeito de movimento especial
                    if (ACharacter* Character = Cast<ACharacter>(OverlappedActor))
                    {
                        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
                        {
                            // Aplicar impulso de movimento
                            FVector MovementImpulse = DirectionToActor * (ForceStrength * 0.001f); // Reduzir para personagens
                            MovementComp->AddImpulse(MovementImpulse, true);
                        }
                    }
                }
            }
        }
    }
}

void ALunarTrail::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação específica para o Lunar Trail
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito de invisibilidade
    Character->SetActorHiddenInGame(true);
    
    // Aplicar visão aprimorada (seria implementado via GameplayEffect)
    UE_LOG(LogTemp, Display, TEXT("Lunar Trail: Aplicando furtividade e visão aprimorada para %s"), *Character->GetName());
    
    // Aplicar efeito de invisibilidade
    ApplyInvisibilityEffect(Character);
}

void ALunarTrail::ApplyInvisibilityEffect(ACharacter* Character)
{
    if (!Character)
    {
        return;
    }
    
    // Calcular intensidade baseada na fase lunar atual
    float LunarPhase = UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(GetWorld());
    float EffectiveInvisibility = InvisibilityStrength * LunarPhase;
    
    // Aplicar modificador de invisibilidade ao componente de movimento
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        // Reduzir ruído de movimento baseado na invisibilidade
        float NoiseReduction = 1.0f - EffectiveInvisibility;
        MovementComp->MaxWalkSpeed *= (1.0f + MovementSpeedBonus * LunarPhase);
        
        // Aplicar efeito de furtividade através de tags de gameplay
        if (UAbilitySystemComponent* ASC = Character->GetComponentByClass<UAbilitySystemComponent>())
        {
            // Criar GameplayEffect temporário para invisibilidade
            FGameplayEffectSpecHandle EffectSpec = ASC->MakeOutgoingSpec(
                UGameplayEffect::StaticClass(), 1.0f, ASC->MakeEffectContext());
            
            if (EffectSpec.IsValid())
            {
                // Configurar duração do efeito
                EffectSpec.Data->SetDuration(EffectDuration, false);
                
                // Aplicar o efeito
                ASC->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
            }
        }
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("Lunar Trail: Aplicando invisibilidade de %f%% (fase lunar: %f) ao jogador %s por %f segundos"), 
           EffectiveInvisibility * 100.0f, LunarPhase, *Character->GetName(), EffectDuration);
}

// Construtor da classe ALunarTrail
ALunarTrail::ALunarTrail()
{
    // Configurações padrão para Lunar Trail
    TrailType = EAURACRONTrailType::Lunar;
    InvisibilityStrength = 0.7f; // 70% de invisibilidade máxima
    EffectDuration = 5.0f; // Efeito dura 5 segundos após sair do trail
    MovementSpeedBonus = 0.2f; // 20% de velocidade adicional
    
    // Configurar luz com tom azulado para o efeito lunar
    if (TrailLightComponent)
    {
        TrailLightComponent->SetLightColor(FLinearColor(0.3f, 0.5f, 1.0f, 1.0f)); // Azul lunar
        TrailLightComponent->SetIntensity(2.0f);
        TrailLightComponent->SetAttenuationRadius(500.0f);
    }
}

void ALunarTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsActive)
    {
        return;
    }
    
    // Atualizar intensidade baseada na fase lunar
    float LunarPhase = UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(GetWorld());
    
    // Ajustar intensidade da luz baseada na fase lunar
    if (TrailLightComponent)
    {
        float LightIntensity = 1.0f + (LunarPhase * 2.0f); // Varia de 1.0 a 3.0
        TrailLightComponent->SetIntensity(LightIntensity);
        
        // Ajustar cor baseada na fase lunar (mais azul durante lua cheia)
        FLinearColor LunarColor = FLinearColor::LerpUsingHSV(
            FLinearColor(0.5f, 0.5f, 0.8f, 1.0f), // Azul claro
            FLinearColor(0.2f, 0.3f, 1.0f, 1.0f), // Azul intenso
            LunarPhase
        );
        TrailLightComponent->SetLightColor(LunarColor);
    }
    
    // Atualizar efeitos visuais específicos dos Lunar Trilhos
    if (TrailEffectComponent)
    {
        // Parâmetros básicos de fase lunar
        TrailEffectComponent->SetFloatParameter(TEXT("LunarPhase"), LunarPhase);
        
        // Mecânica visual: Névoa azul suave
        float BlueMistIntensity = FMath::Lerp(0.2f, 0.8f, LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("BlueMistIntensity"), BlueMistIntensity);
        TrailEffectComponent->SetVectorParameter(TEXT("MistColor"), FVector(0.3f, 0.6f, 1.0f)); // Azul etéreo
        TrailEffectComponent->SetFloatParameter(TEXT("MistSpread"), 120.0f + (LunarPhase * 80.0f));
        TrailEffectComponent->SetFloatParameter(TEXT("MistOpacity"), 0.4f + (LunarPhase * 0.3f));
        
        // Mecânica visual: Partículas de poeira estelar
        float StardustCount = FMath::Lerp(15.0f, 85.0f, LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("StardustParticleCount"), StardustCount);
        TrailEffectComponent->SetVectorParameter(TEXT("StardustColor"), FVector(0.9f, 0.95f, 1.0f)); // Branco estelar
        
        // Efeito de cintilação das partículas estelares
        float StardustSparkle = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.5f) * 0.4f + 0.6f;
        TrailEffectComponent->SetFloatParameter(TEXT("StardustSparkle"), StardustSparkle * LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("StardustSize"), 0.8f + (LunarPhase * 0.5f));
        
        // Efeito de brilho etéreo geral
        float EtherealGlow = LunarPhase * 0.7f;
        TrailEffectComponent->SetFloatParameter(TEXT("EtherealGlow"), EtherealGlow);
        
        // Pulso suave do brilho etéreo
        float GlowPulse = FMath::Sin(GetWorld()->GetTimeSeconds() * 1.2f) * 0.25f + 0.75f;
        TrailEffectComponent->SetFloatParameter(TEXT("GlowPulse"), GlowPulse);
        
        // Efeito de ondulação da névoa
        float MistWave = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.8f) * 0.3f + 0.7f;
        TrailEffectComponent->SetFloatParameter(TEXT("MistWave"), MistWave * LunarPhase);
    }
}

void ATrailBase::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                               UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                               bool bFromSweep, const FHitResult& SweepResult)
{
    if (bIsActive && OtherActor)
    {
        // Aplicar efeito quando um ator entra no trail
        ApplyTrailEffect(OtherActor);
    }
}

void ATrailBase::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                             UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    // Implementação base para quando um ator sai do trail
    // Pode ser sobrescrita pelas classes derivadas
}

void ATrailBase::UpdateTrailEffects(float DeltaTime)
{
    // Implementação base para atualizar efeitos visuais
    // Pode ser sobrescrita pelas classes derivadas
}

// Implementação da classe AAURACRONPCGTrail
AAURACRONPCGTrail::AAURACRONPCGTrail()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar o componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));

    // Valores padrão específicos para AAURACRONPCGTrail
    TrailType = EAURACRONTrailType::PrismalFlow;
    ActivityScale = 0.0f;
    TimeSinceLastUpdate = 0.0f;
    UpdateInterval = 0.5f; // Atualizar a cada meio segundo
    NumOverlappingPlayers = 0;
    TrailDistancePublic = 0.0f;

    // Inicializar características específicas do Prismal Flow
    FlowIntensity = 1.0f;
    FlowWidth = 500.0f; // 5 metros
    FlowSpeed = 1.0f;
    bHasFlowObstacles = true;
    FlowColor = FLinearColor::Blue;
    DefaultFlowIntensity = 1.0f;
    DefaultFlowWidth = 500.0f;
    DefaultFlowSpeed = 1.0f;

    // Inicializar características específicas do Ethereal Path
    PathVisibility = 0.7f;
    PathWidth = 300.0f; // 3 metros
    PathFluctuation = 0.5f;
    PathAlpha = 1.0f;
    PathColor = FLinearColor::Green;
    bHasPathGuides = true;
    DefaultPathVisibility = 0.7f;
    DefaultPathWidth = 300.0f;
    DefaultPathFluctuation = 0.5f;

    // Inicializar características específicas do Nexus Connection
    ConnectionStrength = 0.8f;
    ConnectionWidth = 200.0f; // 2 metros
    ConnectionPulseRate = 1.0f;
    ConnectionColor = FLinearColor::Red;
    bHasConnectionNodes = true;
    DefaultConnectionStrength = 0.8f;
    DefaultConnectionWidth = 200.0f;
    DefaultConnectionPulseRate = 1.0f;

    // Renomear o componente de colisão para manter compatibilidade
    InteractionVolume = CollisionComponent;
}

void AAURACRONPCGTrail::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar o componente PCG com as configurações apropriadas
    if (TrailSettings)
    {
        // Configurar PCG Graph Instance (UE 5.6 API moderna)
        if (PCGComponent->GetGraphInstance())
        {
            // O GraphInstance já existe, podemos configurar parâmetros se necessário
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: PCG GraphInstance configurado"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGTrail: PCG GraphInstance não encontrado - será configurado via Blueprint"));
        }
    }
    
    // Registrar eventos de overlap (UE 5.6 API moderna)
    InteractionVolume->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGTrail::HandlePlayerOverlap);
    InteractionVolume->OnComponentEndOverlap.AddDynamic(this, &AAURACRONPCGTrail::HandlePlayerEndOverlap);
    
    // Iniciar com visibilidade desativada até que seja explicitamente ativado
    SetTrailVisibility(false);

    // Definir escala de atividade inicial
    SetActivityScale(0.0f);

    // Inicializar propriedades de streaming e data layer
    bStreamingEnabled = true;
    StreamingDistance = 5000.0f;
    AssociatedDataLayer = NAME_None;

    // Inicializar configuração de streaming com valores padrão
    StreamingConfiguration.LoadingDistance = 3000.0f;
    StreamingConfiguration.UnloadingDistance = 4000.0f;
    StreamingConfiguration.StreamingPriority = 50;
    StreamingConfiguration.bUseAsyncStreaming = true;
    StreamingConfiguration.GridSize = 2000.0f;
    StreamingConfiguration.StreamingDistance = 5000.0f;
}

void AAURACRONPCGTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar o tempo decorrido
    TimeSinceLastUpdate += DeltaTime;

    // Verificar se é hora de atualizar a trilha
    if (TimeSinceLastUpdate >= UpdateInterval)
    {
        // Atualizar parâmetros da trilha
        UpdateTrailParameters();

        // Regenerar a trilha se estiver ativa
        if (ActivityScale > 0.01f)
        {
            GenerateTrail();
        }

        // Resetar o temporizador
        TimeSinceLastUpdate = 0.0f;
    }
    
    // Aplicar efeitos a todos os jogadores na trilha
    for (ACharacter* Player : OverlappingPlayers)
    {
        if (IsValid(Player))
        {
            ApplyTrailEffectsToPlayer(Player, DeltaTime);
        }
    }
    
    // Atualizar o volume de interação para seguir a spline
    UpdateInteractionVolume();
}

void AAURACRONPCGTrail::SetTrailType(EAURACRONTrailType NewType)
{
    TrailType = NewType;
    
    // Reconfigurar a trilha com base no novo tipo
    // Isso pode envolver a alteração das configurações do PCG
    GenerateTrail();
}

void AAURACRONPCGTrail::GenerateTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Limpar trilha anterior
    ClearTrail();

    // Gerar pontos da trilha baseados no tipo usando as APIs modernas
    TArray<FVector> TrailPoints = GenerateTrailPointsModern();

    if (TrailPoints.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGTrail: Não foi possível gerar pontos suficientes para a trilha"));
        return;
    }

    // Configurar spline com os pontos gerados
    SplineComponent->ClearSplinePoints();
    for (int32 i = 0; i < TrailPoints.Num(); ++i)
    {
        SplineComponent->AddSplinePoint(TrailPoints[i], ESplineCoordinateSpace::World);
        SplineComponent->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }

    // Atualizar tangentes da spline para curvas suaves
    SplineComponent->UpdateSpline();

    // Gerar características específicas com base no tipo de trilha
    switch (TrailType)
    {
    case EAURACRONTrailType::Solar:
        GenerateSolarTrail();
        break;

    case EAURACRONTrailType::Axis:
        GenerateAxisTrail();
        break;

    case EAURACRONTrailType::Lunar:
        GenerateLunarTrail();
        break;

    default:
        break;
    }

    // Aplicar escala de atividade
    ApplyActivityScale();
}

void AAURACRONPCGTrail::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Atualizar a trilha com base na fase do mapa
    // Diferentes fases do mapa podem afetar a aparência e comportamento da trilha
    
    // Ajustar a escala de atividade com base na fase do mapa
    switch (MapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - trilha com atividade moderada
            SetActivityScale(0.7f);
            
            // Ajustes específicos para cada tipo de trilha na fase Awakening
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal mais calmo e azulado na fase inicial
                FlowWidth = DefaultFlowWidth * 0.8f;
                FlowIntensity = DefaultFlowIntensity * 0.7f;
                FlowSpeed = DefaultFlowSpeed * 0.6f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    // Log para debug - parâmetros serão configurados via Blueprint/Graph
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando FlowColor para PrismalFlow via GraphInstance"));
                }
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo mais sutil e esverdeado na fase inicial
                PathWidth = DefaultPathWidth * 0.7f;
                PathVisibility = DefaultPathVisibility * 0.6f;
                PathFluctuation = DefaultPathFluctuation * 0.5f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    // Log para debug - parâmetros serão configurados via Blueprint/Graph
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando PathColor para EtherealPath via GraphInstance"));
                }
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus mais fraca e arroxeada na fase inicial
                ConnectionWidth = DefaultConnectionWidth * 0.6f;
                ConnectionStrength = DefaultConnectionStrength * 0.5f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 0.4f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    // Log para debug - parâmetros serão configurados via Blueprint/Graph
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando ConnectionColor para NexusConnection via GraphInstance"));
                }
            }
            break;
            
        case EAURACRONMapPhase::Convergence:
            // Segunda fase - trilha com atividade aumentada
            SetActivityScale(0.9f);
            
            // Ajustes específicos para cada tipo de trilha na fase Convergence
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal mais intenso e arroxeado na segunda fase
                FlowWidth = DefaultFlowWidth * 0.9f;
                FlowIntensity = DefaultFlowIntensity * 0.9f;
                FlowSpeed = DefaultFlowSpeed * 0.8f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando FlowColor Roxo para Convergence via GraphInstance"));
                }
                
                // Adicionar obstáculos na segunda fase
                bHasFlowObstacles = true;
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo mais visível e amarelado na segunda fase
                PathWidth = DefaultPathWidth * 0.9f;
                PathVisibility = DefaultPathVisibility * 0.8f;
                PathFluctuation = DefaultPathFluctuation * 0.7f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando PathColor Verde Amarelado para Convergence via GraphInstance"));
                }
                
                // Adicionar guias na segunda fase
                bHasPathGuides = true;
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus mais forte e rosada na segunda fase
                ConnectionWidth = DefaultConnectionWidth * 0.8f;
                ConnectionStrength = DefaultConnectionStrength * 0.7f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 0.6f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando ConnectionColor Rosa para Convergence via GraphInstance"));
                }
                
                // Adicionar nós na segunda fase
                bHasConnectionNodes = true;
            }
            break;
            
        case EAURACRONMapPhase::Intensification:
            // Terceira fase - trilha com alta atividade
            SetActivityScale(1.1f);
            
            // Ajustes específicos para cada tipo de trilha na fase Intensification
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal mais largo e alaranjado na terceira fase
                FlowWidth = DefaultFlowWidth * 1.1f;
                FlowIntensity = DefaultFlowIntensity * 1.2f;
                FlowSpeed = DefaultFlowSpeed * 1.3f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGTrail: Configurando FlowColor Laranja para Intensification via GraphInstance"));
                }
                
                // Aumentar número de obstáculos na terceira fase
                bHasFlowObstacles = true;
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo mais brilhante e amarelado na terceira fase
                PathWidth = DefaultPathWidth * 1.1f;
                PathVisibility = DefaultPathVisibility * 1.2f;
                PathFluctuation = DefaultPathFluctuation * 1.3f;
                SetPCGParameterModern(TEXT("PathColor"), FVector(1.0f, 1.0f, 0.0f), TEXT("Amarelo brilhante"));
                
                // Aumentar número de guias na terceira fase
                bHasPathGuides = true;
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus mais intensa e avermelhada na terceira fase
                ConnectionWidth = DefaultConnectionWidth * 1.1f;
                ConnectionStrength = DefaultConnectionStrength * 1.2f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 1.3f;
                SetPCGParameterModern(TEXT("ConnectionColor"), FVector(1.0f, 0.0f, 0.0f), TEXT("Vermelho"));
                
                // Aumentar número de nós na terceira fase
                bHasConnectionNodes = true;
            }
            break;
            
        case EAURACRONMapPhase::Resolution:
            // Fase final - trilha com atividade máxima
            SetActivityScale(1.3f);
            
            // Ajustes específicos para cada tipo de trilha na fase Resolution
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal no máximo e avermelhado na fase final
                FlowWidth = DefaultFlowWidth * 1.3f;
                FlowIntensity = DefaultFlowIntensity * 1.5f;
                FlowSpeed = DefaultFlowSpeed * 1.7f;
                SetPCGParameterModern(TEXT("FlowColor"), FVector(1.0f, 0.0f, 0.0f), TEXT("Vermelho"));
                
                // Máximo de obstáculos na fase final
                bHasFlowObstacles = true;
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo no máximo e branco puro na fase final
                PathWidth = DefaultPathWidth * 1.3f;
                PathVisibility = DefaultPathVisibility * 1.5f;
                PathFluctuation = DefaultPathFluctuation * 1.7f;
                SetPCGParameterModern(TEXT("PathColor"), FVector(1.0f, 1.0f, 1.0f), TEXT("Branco puro"));
                
                // Máximo de guias na fase final
                bHasPathGuides = true;
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus no máximo e alaranjada na fase final
                ConnectionWidth = DefaultConnectionWidth * 1.3f;
                ConnectionStrength = DefaultConnectionStrength * 1.5f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 1.7f;
                SetPCGParameterModern(TEXT("ConnectionColor"), FVector(1.0f, 0.5f, 0.0f), TEXT("Laranja"));
                
                // Máximo de nós na fase final
                bHasConnectionNodes = true;
            }
            break;
    }
    
    // Regenerar a trilha para aplicar as mudanças
    GenerateTrail();
}

void AAURACRONPCGTrail::SetTrailVisibility(bool bVisible)
{
    // Definir a visibilidade de todos os componentes gerados
    SetActorHiddenInGame(!bVisible);
    
    // Se estiver visível, garantir que a geração PCG esteja atualizada
    if (bVisible)
    {
        GenerateTrail();
    }
}

void AAURACRONPCGTrail::SetActivityScale(float Scale)
{
    // Limitar a escala entre 0.0 e 1.0
    ActivityScale = FMath::Clamp(Scale, 0.0f, 1.0f);
    
    // Aplicar a escala de atividade aos parâmetros de geração
    UpdateTrailParameters();
    
    // Regenerar a trilha se a escala for significativa
    if (ActivityScale > 0.01f)
    {
        GenerateTrail();
    }
}

void AAURACRONPCGTrail::SetTrailEndpoints(const FVector& StartPoint, const FVector& EndPoint)
{
    // Limpar os pontos existentes
    SplineComponent->ClearSplinePoints(false);
    
    // Adicionar os pontos de início e fim
    SplineComponent->AddSplinePoint(StartPoint, ESplineCoordinateSpace::World, false);
    SplineComponent->AddSplinePoint(EndPoint, ESplineCoordinateSpace::World, false);
    
    // Atualizar a spline
    SplineComponent->UpdateSpline();
    
    // Armazenar os pontos de início e fim para referência
    StartLocation = StartPoint;
    EndLocation = EndPoint;
    
    // Calcular a distância entre os pontos (em centímetros)
    float Distance = FVector::Dist(StartPoint, EndPoint);
    
    // Converter para metros para facilitar a leitura
    float DistanceInMeters = Distance / 100.0f;
    
    // Ajustar parâmetros baseados na distância
    if (TrailType == EAURACRONTrailType::PrismalFlow)
    {
        // Para o Fluxo Prismal, ajustar a largura baseada na distância
        // Trilhas mais longas são um pouco mais largas para manter a proporção visual
        if (DistanceInMeters > 100.0f) // Mais de 100 metros
        {
            FlowWidth = DefaultFlowWidth * 1.5f;
        }
        else if (DistanceInMeters > 50.0f) // Entre 50 e 100 metros
        {
            FlowWidth = DefaultFlowWidth * 1.2f;
        }
        else // Menos de 50 metros
        {
            FlowWidth = DefaultFlowWidth;
        }
        
        // Ajustar a velocidade do fluxo baseada na distância
        // Trilhas mais longas têm fluxo mais rápido
        FlowSpeed = DefaultFlowSpeed * (0.8f + (DistanceInMeters / 200.0f)); // Máximo de 1.3x para 100 metros
        
        // Passar os valores atualizados para o PCGComponent
        SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
        SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));
        SetPCGParameterModern(TEXT("TrailDistancePublic"), FVector(TrailDistancePublic, 0.0f, 0.0f), TEXT("TrailDistancePublic"));
    }
    else if (TrailType == EAURACRONTrailType::EtherealPath)
    {
        // Para o Caminho Etéreo, ajustar a visibilidade baseada na distância
        // Trilhas mais longas são um pouco menos visíveis para criar efeito de profundidade
        if (DistanceInMeters > 100.0f) // Mais de 100 metros
        {
            PathVisibility = DefaultPathVisibility * 0.7f;
        }
        else if (DistanceInMeters > 50.0f) // Entre 50 e 100 metros
        {
            PathVisibility = DefaultPathVisibility * 0.85f;
        }
        else // Menos de 50 metros
        {
            PathVisibility = DefaultPathVisibility;
        }
        
        // Ajustar a flutuação baseada na distância
        // Trilhas mais longas têm mais flutuação
        PathFluctuation = DefaultPathFluctuation * (1.0f + (DistanceInMeters / 100.0f)); // Máximo de 2x para 100 metros
        
        // Passar os valores atualizados para o PCGComponent
        SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
        SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));
        SetPCGParameterModern(TEXT("TrailDistancePublic"), FVector(TrailDistancePublic, 0.0f, 0.0f), TEXT("TrailDistancePublic"));
    }
    else if (TrailType == EAURACRONTrailType::NexusConnection)
    {
        // Para a Conexão de Nexus, ajustar a força baseada na distância
        // Trilhas mais longas são um pouco mais fracas para criar efeito de atenuação
        if (DistanceInMeters > 100.0f) // Mais de 100 metros
        {
            ConnectionStrength = DefaultConnectionStrength * 0.8f;
        }
        else if (DistanceInMeters > 50.0f) // Entre 50 e 100 metros
        {
            ConnectionStrength = DefaultConnectionStrength * 0.9f;
        }
        else // Menos de 50 metros
        {
            ConnectionStrength = DefaultConnectionStrength;
        }
        
        // Ajustar a taxa de pulso baseada na distância
        // Trilhas mais longas têm pulso mais lento
        ConnectionPulseRate = DefaultConnectionPulseRate * (1.0f - (DistanceInMeters / 200.0f)); // Mínimo de 0.5x para 100 metros
        
        // Passar os valores atualizados para o PCGComponent
        SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
        SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));
        SetPCGParameterModern(TEXT("TrailDistancePublic"), FVector(TrailDistancePublic, 0.0f, 0.0f), TEXT("TrailDistancePublic"));
    }
    
    // Regenerar a trilha
    GenerateTrail();
}

void AAURACRONPCGTrail::AddControlPoint(const FVector& ControlPoint)
{
    // Adicionar um ponto de controle à spline
    SplineComponent->AddSplinePoint(ControlPoint, ESplineCoordinateSpace::World, false);
    
    // Atualizar a spline
    SplineComponent->UpdateSpline();
    
    // Regenerar a trilha
    GenerateTrail();
}

void AAURACRONPCGTrail::ClearControlPoints()
{
    // Limpar todos os pontos da spline
    SplineComponent->ClearSplinePoints(false);
    
    // Atualizar a spline
    SplineComponent->UpdateSpline();
}

// Implementações de geração de características específicas

void AAURACRONPCGTrail::GeneratePrismalFlow()
{
    // Implementação da geração de Prismal Flow
    // Usar PCG para criar um fluxo de energia prismática ao longo da spline
    
    // Calcular valores baseados na escala de atividade
    float EffectiveWidth = FlowWidth * ActivityScale;
    float EffectiveIntensity = FlowIntensity * ActivityScale;
    float EffectiveSpeed = FlowSpeed * ActivityScale;
    
    // Calcular o comprimento total da spline em unidades do Unreal (1 unidade = 1cm)
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Calcular o número de segmentos baseado no comprimento (1 segmento a cada 100 unidades)
    int32 NumSegments = FMath::Max(FMath::FloorToInt(SplineLength / 100.0f), 1);
    
    // Calcular o número de obstáculos se habilitados
    int32 NumObstacles = 0;
    if (bHasFlowObstacles)
    {
        // 1 obstáculo a cada 1000 unidades (10 metros)
        NumObstacles = FMath::FloorToInt(SplineLength / 1000.0f);
        
        // Posicionar obstáculos ao longo da spline
        for (int32 i = 0; i < NumObstacles; ++i)
        {
            // Calcular a posição do obstáculo (distribuir uniformemente)
            float Distance = (i + 1) * (SplineLength / (NumObstacles + 1));
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            
            // Adicionar metadados para o obstáculo nesta posição
            SetPCGParameterModern(FString::Printf(TEXT("ObstacleLocation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            SetPCGParameterModern(FString::Printf(TEXT("ObstacleRotation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
        }
    }
    
    // Passar valores para o sistema PCG
    SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
    SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity, 0.0f, 0.0f), TEXT("FlowIntensity"));
    SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));
    SetPCGParameterModern(TEXT("NumSegments"), FVector(NumSegments, 0.0f, 0.0f), TEXT("NumSegments"));
    SetPCGParameterModern(TEXT("NumObstacles"), FVector(NumObstacles, 0.0f, 0.0f), TEXT("NumObstacles"));
    
    // Adicionar parâmetros específicos para o Fluxo Prismal
    SetPCGParameterModern(TEXT("SplinePoints"), FVector(SplinePoints.Num(), 0.0f, 0.0f), TEXT("SplinePoints"));
    SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    
    // Definir a cor do fluxo baseado na fase do mapa
    FlowColor = FLinearColor(0.0f, 0.5f, 1.0f, 1.0f); // Azul por padrão
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        switch (CurrentPhase)
        {
        case EAURACRONMapPhase::Awakening:
            FlowColor = FLinearColor(0.0f, 0.5f, 1.0f, 1.0f); // Azul
            break;
        case EAURACRONMapPhase::Convergence:
            FlowColor = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Roxo
            break;
        case EAURACRONMapPhase::Intensification:
            FlowColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            break;
        case EAURACRONMapPhase::Resolution:
            FlowColor = FLinearColor(1.0f, 0.0f, 0.0f, 1.0f); // Vermelho
            break;
        }
    }
    
    SetPCGParameterModern(TEXT("FlowColor"), FVector(FlowColor.R, FlowColor.G, FlowColor.B), TEXT("FlowColor"));
}

void AAURACRONPCGTrail::GenerateEtherealPath()
{
    // Implementação da geração de Ethereal Path
    // Usar PCG para criar um caminho etéreo ao longo da spline
    
    // Calcular valores baseados na escala de atividade
    float EffectiveWidth = PathWidth * ActivityScale;
    float EffectiveVisibility = PathVisibility * ActivityScale;
    float EffectiveFluctuation = PathFluctuation * ActivityScale;
    
    // Calcular o comprimento total da spline em unidades do Unreal (1 unidade = 1cm)
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Calcular o número de segmentos baseado no comprimento (1 segmento a cada 100 unidades)
    int32 NumSegments = FMath::Max(FMath::FloorToInt(SplineLength / 100.0f), 1);
    
    // Calcular o número de guias se habilitados
    int32 NumGuides = 0;
    if (bHasPathGuides)
    {
        // 1 guia a cada 2000 unidades (20 metros)
        NumGuides = FMath::FloorToInt(SplineLength / 2000.0f);
        
        // Posicionar guias ao longo da spline
        for (int32 i = 0; i < NumGuides; ++i)
        {
            // Calcular a posição da guia (distribuir uniformemente)
            float Distance = (i + 1) * (SplineLength / (NumGuides + 1));
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            
            // Adicionar metadados para a guia nesta posição
            SetPCGParameterModern(FString::Printf(TEXT("GuideLocation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            SetPCGParameterModern(FString::Printf(TEXT("GuideRotation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
        }
    }
    
    // Passar valores para o sistema PCG
    SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth, 0.0f, 0.0f), TEXT("PathWidth"));
    SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
    SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));
    SetPCGParameterModern(TEXT("NumSegments"), FVector(NumSegments, 0.0f, 0.0f), TEXT("NumSegments"));
    SetPCGParameterModern(TEXT("NumGuides"), FVector(NumGuides, 0.0f, 0.0f), TEXT("NumGuides"));
    
    // Adicionar parâmetros específicos para o Caminho Etéreo
    SetPCGParameterModern(TEXT("SplinePoints"), FVector(SplinePoints.Num(), 0.0f, 0.0f), TEXT("SplinePoints"));
    SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    
    // Definir a cor do caminho baseado na fase do mapa
    PathColor = FLinearColor(0.5f, 1.0f, 0.5f, EffectiveVisibility); // Verde etéreo por padrão
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        switch (CurrentPhase)
        {
        case EAURACRONMapPhase::Awakening:
            PathColor = FLinearColor(0.5f, 1.0f, 0.5f, EffectiveVisibility); // Verde etéreo
            break;
        case EAURACRONMapPhase::Convergence:
            PathColor = FLinearColor(0.7f, 1.0f, 0.3f, EffectiveVisibility); // Verde amarelado
            break;
        case EAURACRONMapPhase::Intensification:
            PathColor = FLinearColor(1.0f, 1.0f, 0.3f, EffectiveVisibility); // Amarelo brilhante
            break;
        case EAURACRONMapPhase::Resolution:
            PathColor = FLinearColor(1.0f, 1.0f, 1.0f, EffectiveVisibility); // Branco puro
            break;
        }
    }
    
    SetPCGParameterModern(TEXT("PathColor"), FVector(PathColor.R, PathColor.G, PathColor.B), TEXT("PathColor"));
    SetPCGParameterModern(TEXT("PathAlpha"), FVector(PathAlpha, 0.0f, 0.0f), TEXT("PathAlpha"));
    
    // Adicionar efeito de flutuação baseado no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float FluctuationOffset = FMath::Sin(CurrentTime * 0.5f) * EffectiveFluctuation;
    SetPCGParameterModern(TEXT("FluctuationOffset"), FVector(FluctuationOffset, 0.0f, 0.0f), TEXT("FluctuationOffset"));
}

void AAURACRONPCGTrail::GenerateNexusConnection()
{
    // Implementação da geração de Nexus Connection
    // Usar PCG para criar uma conexão de nexus ao longo da spline
    
    // Calcular valores baseados na escala de atividade
    float EffectiveWidth = ConnectionWidth * ActivityScale;
    float EffectiveStrength = ConnectionStrength * ActivityScale;
    float EffectivePulseRate = ConnectionPulseRate * ActivityScale;
    
    // Calcular o comprimento total da spline em unidades do Unreal (1 unidade = 1cm)
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Calcular o número de segmentos baseado no comprimento (1 segmento a cada 100 unidades)
    int32 NumSegments = FMath::Max(FMath::FloorToInt(SplineLength / 100.0f), 1);
    
    // Calcular o número de nós se habilitados
    int32 NumNodes = 0;
    if (bHasConnectionNodes)
    {
        // 1 nó a cada 1500 unidades (15 metros)
        NumNodes = FMath::FloorToInt(SplineLength / 1500.0f);
        
        // Posicionar nós ao longo da spline
        for (int32 i = 0; i < NumNodes; ++i)
        {
            // Calcular a posição do nó (distribuir uniformemente)
            float Distance = (i + 1) * (SplineLength / (NumNodes + 1));
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            
            // Adicionar metadados para o nó nesta posição
            SetPCGParameterModern(FString::Printf(TEXT("NodeLocation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            SetPCGParameterModern(FString::Printf(TEXT("NodeRotation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            
            // Adicionar variação de energia para cada nó
            float NodeEnergy = FMath::RandRange(0.8f, 1.2f) * EffectiveStrength;
            SetPCGParameterModern(FString::Printf(TEXT("NodeEnergy_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
        }
    }
    
    // Passar valores para o sistema PCG
    SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth, 0.0f, 0.0f), TEXT("ConnectionWidth"));
    SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
    SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));
    SetPCGParameterModern(TEXT("NumSegments"), FVector(NumSegments, 0.0f, 0.0f), TEXT("NumSegments"));
    SetPCGParameterModern(TEXT("NumNodes"), FVector(NumNodes, 0.0f, 0.0f), TEXT("NumNodes"));
    
    // Adicionar parâmetros específicos para a Conexão de Nexus
    SetPCGParameterModern(TEXT("SplinePoints"), FVector(SplinePoints.Num(), 0.0f, 0.0f), TEXT("SplinePoints"));
    SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    
    // Definir a cor da conexão baseado na fase do mapa
    ConnectionColor = FLinearColor(0.8f, 0.0f, 0.8f, 1.0f); // Roxo por padrão
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        switch (CurrentPhase)
        {
        case EAURACRONMapPhase::Awakening:
            ConnectionColor = FLinearColor(0.8f, 0.0f, 0.8f, 1.0f); // Roxo
            break;
        case EAURACRONMapPhase::Convergence:
            ConnectionColor = FLinearColor(1.0f, 0.0f, 0.5f, 1.0f); // Rosa
            break;
        case EAURACRONMapPhase::Intensification:
            ConnectionColor = FLinearColor(1.0f, 0.0f, 0.0f, 1.0f); // Vermelho
            break;
        case EAURACRONMapPhase::Resolution:
            ConnectionColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            break;
        }
    }
    
    SetPCGParameterModern(TEXT("ConnectionColor"), FVector(ConnectionColor.R, ConnectionColor.G, ConnectionColor.B), TEXT("ConnectionColor"));
    
    // Adicionar efeito de pulso baseado no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float PulseValue = (FMath::Sin(CurrentTime * EffectivePulseRate) + 1.0f) * 0.5f; // Normalizado entre 0 e 1
    SetPCGParameterModern(TEXT("PulseValue"), FVector(PulseValue, 0.0f, 0.0f), TEXT("PulseValue"));
    
    // Adicionar parâmetros para interação com jogadores
    TArray<AActor*> OverlappingActors;
    GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    NumOverlappingPlayers = OverlappingActors.Num();
    SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
}

void AAURACRONPCGTrail::CalculateSplinePoints()
{
    // Verificar se temos pelo menos dois pontos na spline
    int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
    if (NumPoints < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGTrail: Spline não tem pontos suficientes para gerar a trilha %s"), 
               *GetNameSafe(this));
        return;
    }

    // Calcular pontos intermediários se necessário
    if (NumPoints == 2)
    {
        // Obter os pontos de início e fim
        FVector StartPoint = SplineComponent->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::World);
        FVector EndPoint = SplineComponent->GetLocationAtSplinePoint(1, ESplineCoordinateSpace::World);
        
        // Calcular a distância entre os pontos
        float Distance = FVector::Distance(StartPoint, EndPoint);
        
        // Se a distância for grande, adicionar pontos intermediários
        if (Distance > 2000.0f) // 20 metros
        {
            // Número de pontos intermediários (1 a cada 1000 unidades)
            int32 NumIntermediatePoints = FMath::FloorToInt(Distance / 1000.0f);
            
            // Limpar os pontos existentes
            SplineComponent->ClearSplinePoints(false);
            
            // Adicionar o ponto de início
            SplineComponent->AddSplinePoint(StartPoint, ESplineCoordinateSpace::World, false);
            
            // Adicionar pontos intermediários
            for (int32 i = 1; i <= NumIntermediatePoints; ++i)
            {
                float Alpha = (float)i / (NumIntermediatePoints + 1);
                FVector IntermediatePoint = FMath::Lerp(StartPoint, EndPoint, Alpha);
                
                // Adicionar alguma variação aleatória aos pontos intermediários
                float RandomOffset = 200.0f; // 2 metros
                IntermediatePoint.X += FMath::RandRange(-RandomOffset, RandomOffset);
                IntermediatePoint.Y += FMath::RandRange(-RandomOffset, RandomOffset);
                
                // Garantir que o ponto não esteja abaixo do solo
                // Isso seria implementado com um rastreio para o solo
                
                SplineComponent->AddSplinePoint(IntermediatePoint, ESplineCoordinateSpace::World, false);
            }
            
            // Adicionar o ponto de fim
            SplineComponent->AddSplinePoint(EndPoint, ESplineCoordinateSpace::World, false);
            
            // Atualizar a spline
            SplineComponent->UpdateSpline();
        }
    }
}

// Implementação das funções de interação com jogadores
void AAURACRONPCGTrail::HandlePlayerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se o ator que entrou na trilha é um personagem jogável
    ACharacter* PlayerCharacter = Cast<ACharacter>(OtherActor);
    if (PlayerCharacter)
    {
        // Adicionar o jogador à lista de jogadores na trilha
        OverlappingPlayers.AddUnique(PlayerCharacter);
        NumOverlappingPlayers = OverlappingPlayers.Num();
        
        // Atualizar parâmetros do PCG com base no número de jogadores
        SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
        
        // Notificar via Blueprint para todos os tipos de trilha
        OnPlayerEnterTrailEvent.Broadcast(PlayerCharacter);
        
        // Aplicar efeitos iniciais baseados no tipo de trilha
        switch (TrailType)
        {
            case EAURACRONTrailType::PrismalFlow:
                // Efeito de velocidade para o Fluxo Prismal (similar ao rio no LoL)
                // Implementação completa seria feita no Character com um GameplayEffect
                UE_LOG(LogTemp, Display, TEXT("%s entrou no Fluxo Prismal - Aplicando efeito de velocidade"), *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::EtherealPath:
                // Efeito de invisibilidade parcial para o Caminho Etéreo (similar à névoa de guerra no Dota 2)
                UE_LOG(LogTemp, Display, TEXT("%s entrou no Caminho Etéreo - Aplicando efeito de invisibilidade"), *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::NexusConnection:
                // Efeito de aumento de poder para a Conexão de Nexus (similar ao buff de Barão no LoL)
                UE_LOG(LogTemp, Display, TEXT("%s entrou na Conexão de Nexus - Aplicando efeito de poder"), *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Solar:
                // Solar Trail: boost de velocidade e regeneração de vida
                if (PlayerCharacter->GetCharacterMovement())
                {
                    // Armazenar velocidade original
                    float OriginalSpeed = PlayerCharacter->GetCharacterMovement()->MaxWalkSpeed;
                    OriginalPlayerSpeeds.Add(PlayerCharacter, OriginalSpeed);
                    
                    // Aumentar velocidade de movimento em 30%
                    PlayerCharacter->GetCharacterMovement()->MaxWalkSpeed = OriginalSpeed * 1.3f;
                    
                    // Iniciar regeneração de vida (via Blueprint)
                    OnApplySolarEffectEvent.Broadcast(PlayerCharacter);
                    
                    UE_LOG(LogTemp, Display, TEXT("%s entrou no Solar Trail - Aplicando boost de velocidade e regeneração"), 
                           *PlayerCharacter->GetName());
                }
                break;
                
            case EAURACRONTrailType::Axis:
                // Axis Trail: permitir transição instantânea entre ambientes
                // Marcar o personagem para transição (implementado via Blueprint)
                OnApplyAxisEffectEvent.Broadcast(PlayerCharacter);
                UE_LOG(LogTemp, Display, TEXT("%s entrou no Axis Trail - Permitindo transição instantânea"), 
                       *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Lunar:
                // Lunar Trail: conceder furtividade e visão aprimorada
                // Aplicar efeito de furtividade
                PlayerCharacter->SetActorHiddenInGame(true);
                InvisiblePlayers.Add(PlayerCharacter);
                
                // Aplicar visão aprimorada (via Blueprint)
                OnApplyLunarEffectEvent.Broadcast(PlayerCharacter);
                UE_LOG(LogTemp, Display, TEXT("%s entrou no Lunar Trail - Aplicando furtividade e visão aprimorada"), 
                       *PlayerCharacter->GetName());
                break;
        }
    }
}

void AAURACRONPCGTrail::HandlePlayerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    // Verificar se o ator que saiu da trilha é um personagem jogável
    ACharacter* PlayerCharacter = Cast<ACharacter>(OtherActor);
    if (PlayerCharacter)
    {
        // Remover o jogador da lista de jogadores na trilha
        OverlappingPlayers.Remove(PlayerCharacter);
        NumOverlappingPlayers = OverlappingPlayers.Num();
        
        // Atualizar parâmetros do PCG com base no número de jogadores
        SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
        
        // Remover efeitos baseados no tipo de trilha
        switch (TrailType)
        {
            case EAURACRONTrailType::PrismalFlow:
                UE_LOG(LogTemp, Display, TEXT("%s saiu do Fluxo Prismal - Removendo efeito de velocidade"), *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::EtherealPath:
                UE_LOG(LogTemp, Display, TEXT("%s saiu do Caminho Etéreo - Removendo efeito de invisibilidade"), *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::NexusConnection:
                UE_LOG(LogTemp, Display, TEXT("%s saiu da Conexão de Nexus - Removendo efeito de poder"), *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Solar:
                // Restaurar velocidade normal do jogador
                if (PlayerCharacter->GetCharacterMovement() && OriginalPlayerSpeeds.Contains(PlayerCharacter))
                {
                    // Restaurar velocidade original
                    PlayerCharacter->GetCharacterMovement()->MaxWalkSpeed = OriginalPlayerSpeeds[PlayerCharacter];
                    OriginalPlayerSpeeds.Remove(PlayerCharacter);
                    
                    // Parar regeneração de vida via Blueprint
                    
                    UE_LOG(LogTemp, Display, TEXT("%s saiu do Solar Trail - Removendo boost de velocidade e regeneração"), 
                           *PlayerCharacter->GetName());
                }
                break;
                
            case EAURACRONTrailType::Axis:
                // Remover status de pronto para transição via Blueprint
                UE_LOG(LogTemp, Display, TEXT("%s saiu do Axis Trail - Removendo possibilidade de transição"), 
                       *PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Lunar:
                // Remover efeito de furtividade e visão noturna
                if (InvisiblePlayers.Contains(PlayerCharacter))
                {
                    // Remover invisibilidade
                    PlayerCharacter->SetActorHiddenInGame(false);
                    InvisiblePlayers.Remove(PlayerCharacter);
                    
                    // Remover visão noturna via Blueprint
                    
                    UE_LOG(LogTemp, Display, TEXT("%s saiu do Lunar Trail - Removendo furtividade e visão noturna"), 
                           *PlayerCharacter->GetName());
                }
                break;
        }
    }
}

void AAURACRONPCGTrail::ApplyTrailEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!Player)
        return;
    
    // Obter a posição do jogador ao longo da trilha (0.0 = início, 1.0 = fim)
    float PositionAlongTrail = GetPlayerPositionAlongTrail(Player);
    
    // Aplicar efeitos baseados no tipo de trilha e posição
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
            // Aplicar efeito de velocidade - aumenta com a direção do fluxo
            // Implementação completa seria feita com um GameplayEffect
            {
                // Calcular direção do movimento do jogador
                FVector PlayerVelocity = Player->GetVelocity();
                FVector SplineDirection = SplineComponent->GetDirectionAtDistanceAlongSpline(
                    PositionAlongTrail * SplineComponent->GetSplineLength(), 
                    ESplineCoordinateSpace::World);
                
                // Verificar se o jogador está se movendo na direção do fluxo
                float DotProduct = FVector::DotProduct(PlayerVelocity.GetSafeNormal(), SplineDirection.GetSafeNormal());
                
                // Aplicar boost de velocidade se estiver se movendo na direção do fluxo
                if (DotProduct > 0.0f)
                {
                    // Aqui seria aplicado um modificador de velocidade ao jogador
                    // Player->CustomSpeedModifier = 1.0f + (FlowSpeed * DotProduct * 0.5f);
                    UE_LOG(LogTemp, Verbose, TEXT("Aplicando boost de velocidade de %f ao jogador %s"), 
                           FlowSpeed * DotProduct * 0.5f, *Player->GetName());
                }
                // Aplicar redução de velocidade se estiver se movendo contra o fluxo
                else if (DotProduct < 0.0f)
                {
                    // Aqui seria aplicado um modificador de velocidade ao jogador
                    // Player->CustomSpeedModifier = 1.0f + (FlowSpeed * DotProduct * 0.3f);
                    UE_LOG(LogTemp, Verbose, TEXT("Aplicando redução de velocidade de %f ao jogador %s"), 
                           FlowSpeed * DotProduct * 0.3f, *Player->GetName());
                }
            }
            break;
            
        case EAURACRONTrailType::EtherealPath:
            // Aplicar efeito de invisibilidade parcial
            // Implementação completa seria feita com um GameplayEffect
            {
                // Calcular nível de invisibilidade baseado na visibilidade do caminho
                float InvisibilityFactor = 1.0f - PathVisibility;
                
                // Aqui seria aplicado um modificador de visibilidade ao jogador
                // Player->CustomVisibilityModifier = InvisibilityFactor;
                UE_LOG(LogTemp, Verbose, TEXT("Aplicando invisibilidade de %f ao jogador %s"), 
                       InvisibilityFactor, *Player->GetName());
            }
            break;
            
        case EAURACRONTrailType::NexusConnection:
            // Aplicar efeito de aumento de poder
            // Implementação completa seria feita com um GameplayEffect
            {
                // Calcular boost de poder baseado na força da conexão
                float PowerBoost = ConnectionStrength * 0.2f; // 20% do valor da força
                
                // Aqui seria aplicado um modificador de dano ao jogador
                // Player->CustomDamageModifier = 1.0f + PowerBoost;
                UE_LOG(LogTemp, Verbose, TEXT("Aplicando boost de poder de %f ao jogador %s"), 
                       PowerBoost, *Player->GetName());
            }
            break;
            
        case EAURACRONTrailType::Solar:
            // Aplicar efeito de boost de velocidade e regeneração de vida
            {
                // Calcular intensidade solar baseada na hora do dia
                float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f; // Simular ciclo de 24 horas
                float SolarIntensity = FMath::Sin(TimeOfDay * 2.0f * PI) * 0.5f + 0.5f; // 0.0 a 1.0
                
                // Aplicar boost de velocidade proporcional à intensidade solar
                if (Player->GetCharacterMovement())
                {
                    float SpeedBoost = 1.0f + (SolarIntensity * 0.3f); // Até 30% mais rápido
                    // Player->GetCharacterMovement()->MaxWalkSpeed *= SpeedBoost;
                    
                    // Regenerar vida do jogador através de sistema de atributos
                    float HealthRegen = SolarIntensity * 5.0f * DeltaTime; // 5 pontos de vida por segundo no máximo
                    
                    // Implementar regeneração via componente de saúde
                    if (UActorComponent* HealthComponent = Player->GetComponentByClass(UActorComponent::StaticClass()))
                    {
                        // Aplicar regeneração de vida
                        OnSolarHealthRegeneration.Broadcast(Player, HealthRegen, SpeedBoost);
                    }
                    
                    UE_LOG(LogTemp, Verbose, TEXT("Solar Trail: Aplicando boost de velocidade de %f e regeneração de %f ao jogador %s"), 
                           SpeedBoost, HealthRegen, *Player->GetName());
                }
                
                // Efeito visual de brilho solar
                if (SolarTrailEffect)
                {
                    SolarTrailEffect->SetFloatParameter(TEXT("Intensity"), SolarIntensity);
                }
            }
            break;
            
        case EAURACRONTrailType::Axis:
            // Aplicar efeito de transição entre ambientes
            {
                // Verificar se o jogador está próximo de um ponto de conexão
                float MinDistanceToConnection = 9999999.0f;
                int32 NearestConnectionPoint = -1;
                
                // Encontrar o ponto de conexão mais próximo
                for (int32 i = 0; i < SplineComponent->GetNumberOfSplinePoints(); i++)
                {
                    FVector PointLocation = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
                    float Distance = FVector::Distance(Player->GetActorLocation(), PointLocation);
                    
                    if (Distance < MinDistanceToConnection)
                    {
                        MinDistanceToConnection = Distance;
                        NearestConnectionPoint = i;
                    }
                }
                
                // Se estiver próximo o suficiente de um ponto de conexão, permitir transição
                if (NearestConnectionPoint >= 0 && MinDistanceToConnection < 200.0f) // 2 metros
                {
                    // Marcar o jogador como pronto para transição
                if (ACharacter* Character = Cast<ACharacter>(Player))
                {
                    // Implementar sistema de transição via componente customizado
                    if (UActorComponent* TransitionComponent = Character->GetComponentByClass(UActorComponent::StaticClass()))
                    {
                        // Configurar destino de transição
                        FVector TransitionDestination = SplineComponent->GetLocationAtSplinePoint(NearestConnectionPoint, ESplineCoordinateSpace::World);
                        
                        // Broadcast evento de transição disponível
                        OnAxisTransitionAvailable.Broadcast(Character, TransitionDestination, NearestConnectionPoint);
                    }
                }
                    
                    UE_LOG(LogTemp, Verbose, TEXT("Axis Trail: Jogador %s próximo do ponto de conexão %d - pronto para transição"), 
                           *Player->GetName(), NearestConnectionPoint);
                    
                    // Efeito visual de distorção espacial
                    if (AxisTrailEffect)
                    {
                        AxisTrailEffect->SetVectorParameter(TEXT("ConnectionPoint"), 
                            SplineComponent->GetLocationAtSplinePoint(NearestConnectionPoint, ESplineCoordinateSpace::World));
                        AxisTrailEffect->SetFloatParameter(TEXT("DistortionIntensity"), 1.0f - (MinDistanceToConnection / 200.0f));
                    }
                }
            }
            break;
            
        case EAURACRONTrailType::Lunar:
            // Aplicar efeito de furtividade e visão aprimorada
            {
                // Calcular intensidade lunar baseada na hora da noite
                float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f; // Simular ciclo de 24 horas
                float DayNightCycle = FMath::Sin(TimeOfDay * 2.0f * PI); // -1.0 a 1.0
                float IsNight = DayNightCycle < 0.0f ? 1.0f : 0.0f; // 1.0 se for noite, 0.0 se for dia
                float LunarIntensity = IsNight * FMath::Abs(DayNightCycle); // 0.0 a 1.0, só à noite
                
                // Aplicar efeito de furtividade proporcional à intensidade lunar
                float StealthFactor = LunarIntensity * 0.8f; // Até 80% de furtividade
                
                // Aplicar visão aprimorada através de modificadores de componente
                float NightVisionFactor = LunarIntensity * 0.7f; // Até 70% de visão noturna
                
                // Implementar efeito de visão noturna via componente de câmera
                if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
                {
                    if (APawn* Pawn = PC->GetPawn())
                    {
                        // Aplicar modificador de visão noturna
                        OnLunarVisionEffect.Broadcast(Pawn, NightVisionFactor, StealthFactor);
                    }
                }
                
                UE_LOG(LogTemp, Verbose, TEXT("Lunar Trail: Aplicando furtividade de %f e visão noturna de %f ao jogador %s"), 
                       StealthFactor, NightVisionFactor, *Player->GetName());
                
                // Efeito visual de brilho lunar
                if (LunarTrailEffect)
                {
                    LunarTrailEffect->SetFloatParameter(TEXT("Intensity"), LunarIntensity);
                    LunarTrailEffect->SetFloatParameter(TEXT("StealthFactor"), StealthFactor);
                }
            }
            break;
    }
}

bool AAURACRONPCGTrail::IsPlayerInTrail(ACharacter* Player) const
{
    return OverlappingPlayers.Contains(Player);
}

float AAURACRONPCGTrail::GetPlayerPositionAlongTrail(ACharacter* Player) const
{
    if (!Player)
        return 0.0f;
    
    // Obter a posição do jogador
    FVector PlayerLocation = Player->GetActorLocation();
    
    // Encontrar o ponto mais próximo na spline
    float ClosestDistanceAlongSpline = 0.0f;
    float ClosestDistanceSquared = MAX_FLT;
    
    // Calcular o comprimento total da spline
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Número de amostras para verificar ao longo da spline
    const int32 NumSamples = 20;
    
    for (int32 i = 0; i <= NumSamples; ++i)
    {
        float Distance = (float)i / (float)NumSamples * SplineLength;
        FVector PointOnSpline = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        
        float DistanceSquared = FVector::DistSquared(PlayerLocation, PointOnSpline);
        if (DistanceSquared < ClosestDistanceSquared)
        {
            ClosestDistanceSquared = DistanceSquared;
            ClosestDistanceAlongSpline = Distance;
        }
    }
    
    // Retornar a posição normalizada ao longo da spline (0.0 = início, 1.0 = fim)
    return ClosestDistanceAlongSpline / SplineLength;
}

void AAURACRONPCGTrail::UpdateInteractionVolume()
{
    // Atualizar o volume de interação para seguir a spline
    if (SplineComponent->GetNumberOfSplinePoints() < 2)
        return;
    
    // Calcular o comprimento total da spline
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Ajustar o tamanho do volume de interação baseado no tipo de trilha
    float VolumeWidth = 0.0f;
    float VolumeHeight = 200.0f; // 2 metros de altura padrão
    
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
            VolumeWidth = FlowWidth * 1.2f; // Um pouco maior que a largura visual
            break;
            
        case EAURACRONTrailType::EtherealPath:
            VolumeWidth = PathWidth * 1.5f; // Significativamente maior que a largura visual
            break;
            
        case EAURACRONTrailType::NexusConnection:
            VolumeWidth = ConnectionWidth * 1.3f; // Um pouco maior que a largura visual
            break;
    }
    
    // Ajustar o tamanho do volume de interação
    InteractionVolume->SetBoxExtent(FVector(SplineLength * 0.5f, VolumeWidth * 0.5f, VolumeHeight * 0.5f));
    
    // Posicionar o volume no centro da spline
    FVector StartPoint = SplineComponent->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::World);
    FVector EndPoint = SplineComponent->GetLocationAtSplinePoint(SplineComponent->GetNumberOfSplinePoints() - 1, ESplineCoordinateSpace::World);
    FVector CenterPoint = (StartPoint + EndPoint) * 0.5f;
    
    // Calcular a rotação para alinhar com a direção da spline
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FRotator Rotation = Direction.Rotation();
    
    // Atualizar a transformação do volume de interação
    InteractionVolume->SetWorldLocation(CenterPoint);
    InteractionVolume->SetWorldRotation(Rotation);
}

void AAURACRONPCGTrail::UpdateTrailParameters()
{
    // Atualizar parâmetros dinâmicos da trilha ao longo do tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Atualizar parâmetros baseados no tipo de trilha
    switch (TrailType)
    {
    case EAURACRONTrailType::PrismalFlow:
        {
            // Adicionar alguma variação ao longo do tempo
            FlowIntensity = 1.0f + 0.2f * FMath::Sin(CurrentTime * 0.5f);
            FlowSpeed = 1.0f + 0.3f * FMath::Cos(CurrentTime * 0.3f);

            // Atualizar parâmetros dinâmicos no PCGComponent
            SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity, 0.0f, 0.0f), TEXT("FlowIntensity"));
            SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));

            // Adicionar efeito de ondulação baseado no tempo
            float WaveEffect = FMath::Sin(CurrentTime * 0.8f) * 0.5f;
            SetPCGParameterModern(TEXT("WaveEffect"), FVector(WaveEffect, 0.0f, 0.0f), TEXT("WaveEffect"));
            break;
        }
        
    case EAURACRONTrailType::EtherealPath:
        {
            // Adicionar alguma variação ao longo do tempo
            PathVisibility = 0.7f + 0.3f * FMath::Sin(CurrentTime * 0.2f);
            PathFluctuation = 0.5f + 0.3f * FMath::Cos(CurrentTime * 0.4f);

            // Atualizar parâmetros dinâmicos no PCGComponent
            SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
            SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));

            // Atualizar efeito de flutuação baseado no tempo
            float FluctuationOffset = FMath::Sin(CurrentTime * 0.5f) * PathFluctuation;
            SetPCGParameterModern(TEXT("FluctuationOffset"), FVector(FluctuationOffset, 0.0f, 0.0f), TEXT("FluctuationOffset"));

            // Atualizar alpha baseado na visibilidade
            SetPCGParameterModern(TEXT("PathAlpha"), FVector(PathAlpha, 0.0f, 0.0f), TEXT("PathAlpha"));
            break;
        }
        
    case EAURACRONTrailType::NexusConnection:
        {
            // Adicionar alguma variação ao longo do tempo
            ConnectionStrength = 0.8f + 0.2f * FMath::Sin(CurrentTime * 0.3f);
            ConnectionPulseRate = 1.0f + 0.5f * FMath::Cos(CurrentTime * 0.6f);

            // Atualizar parâmetros dinâmicos no PCGComponent
            SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
            SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));

            // Atualizar efeito de pulso baseado no tempo
            float PulseValue = (FMath::Sin(CurrentTime * ConnectionPulseRate) + 1.0f) * 0.5f; // Normalizado entre 0 e 1
            SetPCGParameterModern(TEXT("PulseValue"), FVector(PulseValue, 0.0f, 0.0f), TEXT("PulseValue"));

            // Verificar jogadores próximos e atualizar parâmetros de interação
            TArray<AActor*> OverlappingActors;
            GetOverlappingActors(OverlappingActors, APawn::StaticClass());
            NumOverlappingPlayers = OverlappingActors.Num();
            SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
            break;
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS NOVAS FUNÇÕES MODERNAS
// ========================================

void AAURACRONPCGTrail::ClearTrail()
{
    // Limpar elementos gerados anteriormente
    GeneratedElements.Empty();

    // Limpar componentes dinâmicos
    TArray<UActorComponent*> ComponentsToRemove;
    TArray<UActorComponent*> AllComponents = GetComponents().Array();

    for (UActorComponent* Component : AllComponents)
    {
        if (Component != PCGComponent && Component != RootComponent &&
            Component != SplineComponent && Component != InteractionVolume)
        {
            ComponentsToRemove.Add(Component);
        }
    }

    for (UActorComponent* Component : ComponentsToRemove)
    {
        Component->DestroyComponent();
    }
}

TArray<FVector> AAURACRONPCGTrail::GenerateTrailPointsModern()
{
    TArray<FVector> Points;
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f; // Converter para ciclo de 24h

    // Gerar pontos de trilha usando as funções matemáticas modernas disponíveis
    FVector StartPoint = StartLocation.IsZero() ? MapCenter + FVector(-2000.0f, 0.0f, 0.0f) : StartLocation;
    FVector EndPoint = EndLocation.IsZero() ? MapCenter + FVector(2000.0f, 0.0f, 0.0f) : EndLocation;

    // Usar diferentes algoritmos baseados no tipo de trilha
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
        case EAURACRONTrailType::EtherealPath:
        case EAURACRONTrailType::NexusConnection:
        {
            // Criar uma curva serpentina suave
            FAURACRONSplineCurve Curve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint,
                EndPoint,
                10, // NumControlPoints
                500.0f, // Amplitude
                2.0f // Frequency
            );

            // Avaliar pontos ao longo da curva
            int32 NumPoints = 20;
            for (int32 i = 0; i <= NumPoints; ++i)
            {
                float T = static_cast<float>(i) / static_cast<float>(NumPoints);
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(Curve, T);
                Points.Add(Point);
            }
            break;
        }

        case EAURACRONTrailType::Solar:
        {
            // Trilhas solares seguem um padrão orbital
            Points = UAURACRONPCGMathLibrary::CreateOrbitalPath(
                MapCenter,
                3000.0f, // Radius
                500.0f, // Height
                16, // NumPoints
                TimeOfDay * 2.0f * PI // PhaseOffset baseado na hora do dia
            );
            break;
        }

        case EAURACRONTrailType::Axis:
        {
            // Trilhas axiais conectam verticalmente
            Points.Add(StartPoint);
            Points.Add(FVector(StartPoint.X, StartPoint.Y, StartPoint.Z + 1000.0f));
            Points.Add(FVector(EndPoint.X, EndPoint.Y, EndPoint.Z + 1000.0f));
            Points.Add(EndPoint);
            break;
        }

        case EAURACRONTrailType::Lunar:
        {
            // Trilhas lunares seguem um padrão mais sutil
            Points = UAURACRONPCGMathLibrary::CreateOrbitalPath(
                MapCenter,
                2000.0f, // Radius menor que solar
                200.0f, // Height menor
                12, // Menos pontos
                (TimeOfDay + 0.5f) * 2.0f * PI // Offset de fase lunar
            );
            break;
        }

        default:
        {
            // Fallback: linha reta simples
            Points.Add(StartPoint);
            Points.Add(EndPoint);
            break;
        }
    }

    return Points;
}

void AAURACRONPCGTrail::GenerateSolarTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Solar Trails seguem o movimento do sol
    float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f;
    float SolarIntensity = UAURACRONPCGMathLibrary::GetSolarIntensity(TimeOfDay);

    // Criar efeitos visuais solares ao longo da spline
    int32 NumSegments = SplineComponent->GetNumberOfSplinePoints() - 1;

    for (int32 i = 0; i < NumSegments; ++i)
    {
        float SplineDistance = static_cast<float>(i) / NumSegments;
        FVector SegmentLocation = SplineComponent->GetLocationAtDistanceAlongSpline(
            SplineDistance * SplineComponent->GetSplineLength(),
            ESplineCoordinateSpace::World
        );

        // Criar componente de efeito solar
        UNiagaraComponent* SolarEffect = NewObject<UNiagaraComponent>(this);
        if (SolarEffect && SolarTrailEffect)
        {
            SolarEffect->SetAsset(SolarTrailEffectAsset);
            SolarEffect->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            SolarEffect->SetWorldLocation(SegmentLocation);

            // Configurar intensidade baseada no horário (mais forte ao meio-dia)
            SolarEffect->SetFloatParameter(FName("Intensity"), SolarIntensity);
            SolarEffect->SetFloatParameter(FName("TimeOfDay"), TimeOfDay);
            
            // Adicionar efeitos de distorção de calor nas bordas
            SolarEffect->SetFloatParameter(FName("HeatDistortion"), SolarIntensity * 0.8f);
            
            // Configurar cor dourada para partículas
            SolarEffect->SetColorParameter(FName("TrailColor"), FLinearColor(1.0f, 0.8f, 0.2f, 1.0f));

            GeneratedElements.Add(SolarEffect);
        }
    }
    
    // Adicionar efeito de boost de velocidade e regeneração de vida ao volume de interação
    if (InteractionVolume)
    {
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }
}

void AAURACRONPCGTrail::GenerateAxisTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Axis Trails conectam os três ambientes verticalmente
    TArray<FVector> EnvironmentCenters;
    EnvironmentCenters.Add(UAURACRONMapMeasurements::GetEnvironmentCenter(
        static_cast<int32>(EAURACRONEnvironmentType::RadiantPlains)));
    EnvironmentCenters.Add(UAURACRONMapMeasurements::GetEnvironmentCenter(
        static_cast<int32>(EAURACRONEnvironmentType::ZephyrFirmament)));
    EnvironmentCenters.Add(UAURACRONMapMeasurements::GetEnvironmentCenter(
        static_cast<int32>(EAURACRONEnvironmentType::PurgatoryRealm)));

    // Criar conexões verticais entre ambientes
    for (int32 i = 0; i < EnvironmentCenters.Num(); ++i)
    {
        FVector ConnectionPoint = EnvironmentCenters[i];

        // Criar componente de efeito Niagara para o Axis Trail
        UNiagaraComponent* AxisEffect = NewObject<UNiagaraComponent>(this);
        if (AxisEffect && AxisTrailEffect)
        {
            AxisEffect->SetAsset(AxisTrailEffectAsset);
            AxisEffect->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            AxisEffect->SetWorldLocation(ConnectionPoint);

            // Configurar aparência cinza/prata neutra
            AxisEffect->SetColorParameter(FName("TrailColor"), FLinearColor(0.75f, 0.75f, 0.8f, 1.0f));
            
            // Adicionar efeitos de distorção gravitacional
            AxisEffect->SetFloatParameter(FName("GravityDistortion"), 0.8f);
            
            // Configurar padrões geométricos prateados
            AxisEffect->SetFloatParameter(FName("GeometricPatternIntensity"), 0.9f);

            GeneratedElements.Add(AxisEffect);
        }
        
        // Criar componente de conexão axial (ponto de transição)
        UStaticMeshComponent* AxisConnection = NewObject<UStaticMeshComponent>(this);
        if (AxisConnection && AxisConnectionMesh)
        {
            AxisConnection->SetStaticMesh(AxisConnectionMesh);
            AxisConnection->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            AxisConnection->SetWorldLocation(ConnectionPoint);

            // Configurar escala baseada na importância do ambiente
            float EnvironmentImportance = (i == 0) ? 1.0f : 0.8f; // Radiant Plains mais importante
            AxisConnection->SetWorldScale3D(FVector(EnvironmentImportance));

            GeneratedElements.Add(AxisConnection);
        }
    }
    
    // Configurar o volume de interação para permitir transição instantânea entre ambientes
    if (InteractionVolume)
    {
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }
}

void AAURACRONPCGTrail::GenerateLunarTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Lunar Trails seguem o movimento da lua
    float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f;
    float LunarIntensity = UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(TimeOfDay);
    
    // Verificar se é noite (os trilhos lunares são visíveis apenas à noite)
    bool bIsNight = TimeOfDay < 0.25f || TimeOfDay > 0.75f;
    float Visibility = bIsNight ? 1.0f : 0.1f; // Quase invisível durante o dia

    // Criar efeitos visuais lunares ao longo da spline
    int32 NumSegments = SplineComponent->GetNumberOfSplinePoints() - 1;

    for (int32 i = 0; i < NumSegments; ++i)
    {
        float SplineDistance = static_cast<float>(i) / NumSegments;
        FVector SegmentLocation = SplineComponent->GetLocationAtDistanceAlongSpline(
            SplineDistance * SplineComponent->GetSplineLength(),
            ESplineCoordinateSpace::World
        );

        // Criar componente de efeito lunar
        UNiagaraComponent* LunarEffect = NewObject<UNiagaraComponent>(this);
        if (LunarEffect && LunarTrailEffect)
        {
            LunarEffect->SetAsset(LunarTrailEffectAsset);
            LunarEffect->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            LunarEffect->SetWorldLocation(SegmentLocation);

            // Configurar intensidade baseada na fase lunar
            LunarEffect->SetFloatParameter(FName("Intensity"), LunarIntensity);
            LunarEffect->SetFloatParameter(FName("LunarPhase"), TimeOfDay);

            // Configurar aparência etérea azul-branco
            LunarEffect->SetColorParameter(FName("TrailColor"), FLinearColor(0.5f, 0.7f, 1.0f, 1.0f));
            
            // Adicionar névoa azul suave e partículas de poeira estelar
            LunarEffect->SetFloatParameter(FName("MistIntensity"), LunarIntensity * 0.8f);
            LunarEffect->SetFloatParameter(FName("StardustAmount"), LunarIntensity * 0.6f);
            
            // Visibilidade baseada no ciclo dia/noite
            LunarEffect->SetFloatParameter(FName("Opacity"), Visibility * 0.7f);

            GeneratedElements.Add(LunarEffect);
        }
    }
    
    // Configurar o volume de interação para conceder furtividade e visão aprimorada
    if (InteractionVolume)
    {
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }
}

void AAURACRONPCGTrail::ApplyActivityScale()
{
    // Aplicar escala de atividade a todos os elementos gerados
    for (UActorComponent* Element : GeneratedElements)
    {
        if (IsValid(Element))
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Element))
            {
                // Ajustar visibilidade baseada na escala de atividade
                bool bShouldBeVisible = ActivityScale > 0.1f;
                PrimComp->SetVisibility(bShouldBeVisible);

                // Ajustar escala visual
                if (bShouldBeVisible)
                {
                    FVector Scale = FVector(ActivityScale);
                    PrimComp->SetWorldScale3D(Scale);
                }
            }

            if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Element))
            {
                // Ajustar intensidade dos efeitos de partículas
                NiagaraComp->SetFloatParameter(FName("ActivityScale"), ActivityScale);
            }
        }
    }
}

void AAURACRONPCGTrail::SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Context)
{
    // Função auxiliar para configurar parâmetros PCG usando API moderna UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGTrail::SetPCGParameterModern - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de parâmetros do UE 5.6
    if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
    {
        // Usar API moderna para definir parâmetros via GraphInstance
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            // Criar um nome de parâmetro completo com contexto
            FString FullParameterName = Context.IsEmpty() ? ParameterName : FString::Printf(TEXT("%s_%s"), *Context, *ParameterName);

            // Usar API moderna do UE 5.6 para definir parâmetros
            // Determinar o tipo de parâmetro com base no nome
            if (ParameterName.Contains(TEXT("Position")) || ParameterName.Contains(TEXT("Location")))
            {
                // Parâmetros de transformação
                FTransform ParameterTransform = FTransform(FRotator::ZeroRotator, FVector(Value.X, Value.Y, Value.Z), FVector::OneVector);
                UPCGGraphParametersHelpers::SetTransformParameter(GraphInstance, FName(*FullParameterName), ParameterTransform);
            }
            else if (ParameterName.Contains(TEXT("Scale")) || ParameterName.Contains(TEXT("Intensity")) || ParameterName.Contains(TEXT("Alpha")))
            {
                // Parâmetros escalares
                float ScalarValue = Value.X; // Usar apenas o componente X para valores escalares
                UPCGGraphParametersHelpers::SetFloatParameter(GraphInstance, FName(*FullParameterName), ScalarValue);
            }
            else if (ParameterName.Contains(TEXT("Color")) || ParameterName.Contains(TEXT("Direction")) || ParameterName.Contains(TEXT("Offset")))
            {
                // Parâmetros vetoriais
                UPCGGraphParametersHelpers::SetVectorParameter(GraphInstance, FName(*FullParameterName), Value);
            }
            else if (ParameterName.Contains(TEXT("Enable")) || ParameterName.Contains(TEXT("Active")) || ParameterName.Contains(TEXT("Visible")))
            {
                // Parâmetros booleanos
                bool BoolValue = Value.X > 0.5f; // Converter para booleano
                UPCGGraphParametersHelpers::SetBoolParameter(GraphInstance, FName(*FullParameterName), BoolValue);
            }

            // Forçar regeneração do PCG se necessário
            if (bIsActive && PCGComponent->GetGraph())
            {
                // Usar API moderna para regeneração incremental
                PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
            }

            UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGTrail::SetPCGParameterModern - Set parameter %s = (%.2f, %.2f, %.2f) in context %s"),
                   *ParameterName, Value.X, Value.Y, Value.Z, *Context);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGTrail::SetPCGParameterModern - GraphInstance not found"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGTrail::SetPCGParameterModern - PCG Graph not found"));
    }
}

void AAURACRONPCGTrail::UpdateObjectiveConnections(const TArray<FVector>& ObjectivePositions, EAURACRONMapPhase MapPhase)
{
    if (!IsValid(PCGComponent) || ObjectivePositions.Num() == 0)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::UpdateObjectiveConnections - Atualizando conexões com %d objetivos na fase %s"), 
           ObjectivePositions.Num(), *UEnum::GetValueAsString(MapPhase));
    
    // Limpar pontos de controle existentes
    ClearControlPoints();
    
    // Adicionar pontos de início e fim da trilha como pontos de controle
    AddControlPoint(StartLocation);
    
    // Encontrar os objetivos mais próximos para conectar com a trilha
    // Limitamos a no máximo 3 objetivos para não sobrecarregar a trilha
    const int32 MaxObjectivesToConnect = FMath::Min(3, ObjectivePositions.Num());
    
    // Ordenar objetivos por distância ao ponto inicial da trilha
    TArray<TPair<float, FVector>> SortedObjectives;
    for (const FVector& ObjPos : ObjectivePositions)
    {
        float Distance = FVector::Distance(StartLocation, ObjPos);
        SortedObjectives.Add(TPair<float, FVector>(Distance, ObjPos));
    }
    
    // Ordenar do mais próximo ao mais distante
    SortedObjectives.Sort([](const TPair<float, FVector>& A, const TPair<float, FVector>& B) {
        return A.Key < B.Key;
    });
    
    // Adicionar os objetivos mais próximos como pontos de controle
    for (int32 i = 0; i < MaxObjectivesToConnect; i++)
    {
        if (i < SortedObjectives.Num())
        {
            // Adicionar o objetivo como ponto de controle
            AddControlPoint(SortedObjectives[i].Value);
            
            // Ajustar a intensidade da trilha com base na fase do mapa
            float IntensityMultiplier = 1.0f;
            switch (MapPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    IntensityMultiplier = 0.7f;
                    break;
                case EAURACRONMapPhase::Convergence:
                    IntensityMultiplier = 1.0f;
                    break;
                case EAURACRONMapPhase::Intensification:
                    IntensityMultiplier = 1.5f;
                    break;
                default:
                    break;
            }
            
            // Configurar parâmetros específicos do tipo de trilha
            switch (TrailType)
            {
                case EAURACRONTrailType::PrismalFlow:
                    SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity * IntensityMultiplier), TEXT("Objective"));
                    SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth * 1.2f), TEXT("Objective"));
                    break;
                case EAURACRONTrailType::EtherealPath:
                    SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility * IntensityMultiplier), TEXT("Objective"));
                    SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth * 1.2f), TEXT("Objective"));
                    break;
                case EAURACRONTrailType::NexusConnection:
                    SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength * IntensityMultiplier), TEXT("Objective"));
                    SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth * 1.2f), TEXT("Objective"));
                    break;
                default:
                    break;
            }
        }
    }
    
    // Adicionar o ponto final da trilha
    AddControlPoint(EndLocation);
    
    // Regenerar a trilha com os novos pontos de controle
    GenerateTrail();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::UpdateObjectiveConnections - Trilha regenerada com %d conexões de objetivos"), 
           MaxObjectivesToConnect);
}

void AAURACRONPCGTrail::OnMapContraction(float ContractionFactor)
{
    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Aplicando contração %.2f à trilha %d"), ContractionFactor, (int32)TrailType);

    // Aplicar contração aos pontos da spline
    FVector MapCenter = FVector::ZeroVector; // Centro do mapa

    if (IsValid(PCGSplineComponent))
    {
        int32 NumPoints = PCGSplineComponent->GetNumberOfSplinePoints();

        for (int32 i = 0; i < NumPoints; ++i)
        {
            FVector OriginalLocation = PCGSplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            PCGSplineComponent->SetLocationAtSplinePoint(i, NewLocation, ESplineCoordinateSpace::World);
        }

        // Atualizar spline após modificações
        PCGSplineComponent->UpdateSpline();
    }

    // Aplicar contração aos pontos de início e fim
    FVector DirectionToCenter = (MapCenter - StartLocation).GetSafeNormal();
    StartLocation = StartLocation + DirectionToCenter * (1.0f - ContractionFactor) * StartLocation.Size2D();

    DirectionToCenter = (MapCenter - EndLocation).GetSafeNormal();
    EndLocation = EndLocation + DirectionToCenter * (1.0f - ContractionFactor) * EndLocation.Size2D();

    // Atualizar pontos da spline interna
    for (FVector& SplinePoint : SplinePoints)
    {
        DirectionToCenter = (MapCenter - SplinePoint).GetSafeNormal();
        SplinePoint = SplinePoint + DirectionToCenter * (1.0f - ContractionFactor) * SplinePoint.Size2D();
    }

    // Atualizar volume de interação
    UpdateInteractionVolume();

    // Recalcular parâmetros da trilha
    UpdateTrailParameters();

    // Aplicar contração específica baseada no tipo de trilha
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
            // Ajustar largura do fluxo baseado na contração
            FlowWidth *= ContractionFactor;
            SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
            break;

        case EAURACRONTrailType::EtherealPath:
            // Ajustar largura do caminho etéreo
            PathWidth *= ContractionFactor;
            SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth, 0.0f, 0.0f), TEXT("PathWidth"));
            break;

        case EAURACRONTrailType::NexusConnection:
            // Ajustar largura da conexão
            ConnectionWidth *= ContractionFactor;
            SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth, 0.0f, 0.0f), TEXT("ConnectionWidth"));
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Contração aplicada com sucesso à trilha"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

// Implementações da classe base ATrailBase
void ATrailBase::ConfigureForAwakeningPhase(bool bEnable)
{
    // Implementação robusta para configurar para fase Awakening
    if (bEnable)
    {
        // Configurar trilha para fase inicial
        PowerPercentage = 0.6f;

        // Configurar efeitos visuais suaves
        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("AwakeningPhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 0.6f);
        }

        UE_LOG(LogTemp, Log, TEXT("ATrailBase::ConfigureForAwakeningPhase - Awakening phase enabled"));
    }
    else
    {
        // Desativar configurações da fase Awakening
        PowerPercentage = 1.0f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("AwakeningPhase"), 0.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("ATrailBase::ConfigureForAwakeningPhase - Awakening phase disabled"));
    }
}

void ATrailBase::ConfigureForConvergencePhase(bool bEnableConvergence, bool bEnableIntensification, bool bEnableResolution)
{
    // Implementação robusta para configurar para fases avançadas

    if (bEnableConvergence)
    {
        // Configurar para fase de convergência
        PowerPercentage = 1.2f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("ConvergencePhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 1.2f);
        }

        UE_LOG(LogTemp, Log, TEXT("ATrailBase::ConfigureForConvergencePhase - Convergence phase enabled"));
    }

    if (bEnableIntensification)
    {
        // Intensificar efeitos para fase de intensificação
        PowerPercentage = 1.6f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("IntensificationPhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 1.6f);
        }

        UE_LOG(LogTemp, Log, TEXT("ATrailBase::ConfigureForConvergencePhase - Intensification phase enabled"));
    }

    if (bEnableResolution)
    {
        // Configurar para fase de resolução
        PowerPercentage = 2.0f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("ResolutionPhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 2.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("ATrailBase::ConfigureForConvergencePhase - Resolution phase enabled"));
    }
}

void ATrailBase::SetPowerPercentage(float NewPowerPercentage)
{
    // Implementação robusta para definir porcentagem de poder
    PowerPercentage = FMath::Clamp(NewPowerPercentage, 0.0f, 3.0f);

    // Atualizar efeitos visuais
    if (TrailEffectComponent && IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
    }

    // Atualizar propriedades da trilha baseado no poder
    UpdateTrailProperties();

    UE_LOG(LogTemp, Log, TEXT("ATrailBase::SetPowerPercentage - Power percentage set to %.2f"), PowerPercentage);
}

// Implementações da classe AAURACRONPCGTrail
void AAURACRONPCGTrail::ConfigureWorldPartitionStreaming(const FAURACRONPCGStreamingConfig& StreamingConfig)
{
    // Implementação robusta para configurar streaming do World Partition
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::ConfigureWorldPartitionStreaming - Configuring streaming"));

    // Armazenar configuração de streaming
    StreamingConfiguration = StreamingConfig;

    // Configurar streaming baseado na configuração fornecida
    bStreamingEnabled = StreamingConfig.bUseAsyncStreaming;

    if (bStreamingEnabled)
    {
        // Configurar parâmetros de streaming
        StreamingDistance = StreamingConfig.StreamingDistance;

        // Configurar LOD baseado na distância
        if (PCGComponent && IsValid(PCGComponent))
        {
            // Implementar configuração de LOD quando disponível
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::ConfigureWorldPartitionStreaming - Streaming enabled with distance %.2f"),
               StreamingDistance);
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::ConfigureWorldPartitionStreaming - Streaming disabled"));
    }
}

void AAURACRONPCGTrail::AssociateWithDataLayer(const FName& DataLayerName)
{
    // Implementação robusta para associar com Data Layer
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::AssociateWithDataLayer - Associating with data layer: %s"),
           *DataLayerName.ToString());

    // Armazenar nome do data layer
    AssociatedDataLayer = DataLayerName;

    // Implementar associação real com Data Layer quando API estiver disponível
    // Usar UDataLayerSubsystem quando disponível no UE 5.6

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGTrail::AssociateWithDataLayer - Associated with data layer successfully"));
}

// Implementação da função UpdateTrailProperties para ASolarTrail
void ASolarTrail::UpdateTrailProperties()
{
    // Implementação robusta para atualizar propriedades da trilha solar

    // Atualizar intensidade dos efeitos baseado na porcentagem de poder
    if (TrailEffectComponent && IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("SolarIntensity"), PowerPercentage);
        TrailEffectComponent->SetFloatParameter(TEXT("RegenerationRate"), PowerPercentage * 0.5f);

        // Atualizar cor baseada no poder
        FLinearColor SolarColor = FLinearColor::Yellow;
        SolarColor.R = FMath::Clamp(PowerPercentage, 0.5f, 1.0f);
        SolarColor.G = FMath::Clamp(PowerPercentage * 0.8f, 0.3f, 0.9f);
        SolarColor.B = FMath::Clamp(PowerPercentage * 0.2f, 0.1f, 0.3f);

        TrailEffectComponent->SetVectorParameter(TEXT("SolarColor"), FVector(SolarColor.R, SolarColor.G, SolarColor.B));
    }

    // Atualizar componentes de luz se existirem
    TArray<UPointLightComponent*> LightComponents;
    GetComponents<UPointLightComponent>(LightComponents);

    for (UPointLightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(Cast<UObject>(LightComp)))
        {
            float BaseIntensity = 2000.0f;
            LightComp->SetIntensity(BaseIntensity * PowerPercentage);
            LightComp->SetLightColor(FLinearColor::Yellow * PowerPercentage);
        }
    }

    // Atualizar raio de efeito da trilha
    if (CollisionComponent && IsValid(CollisionComponent))
    {
        FVector BaseExtent = FVector(250.0f, 250.0f, 100.0f);
        CollisionComponent->SetBoxExtent(BaseExtent * PowerPercentage);
    }

    UE_LOG(LogTemp, Verbose, TEXT("ASolarTrail::UpdateTrailProperties - Trail properties updated with power %.2f"), PowerPercentage);
}
