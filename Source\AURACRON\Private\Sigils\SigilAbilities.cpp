// SigilAbilities.cpp
// AURACRON - Implementação das Habilidades Exclusivas dos Sígilos
// Implementa as habilidades Murallion, Fracasso Prismal e Sopro de Fluxo

#include "Sigils/SigilAbilities.h"
#include "Sigils/SigilItem.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayAbilitySpec.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"
#include "Net/UnrealNetwork.h"
#include "EngineUtils.h"
#include "Character/AURACRONCharacter.h"
#include "Sigils/SigilManagerComponent.h"
#include "Components/AURACRONMovementComponent.h"
#include "Abilities/Tasks/AbilityTask_MoveToLocation.h"
#include "Abilities/Tasks/AbilityTask_WaitDelay.h"
#include "ActiveGameplayEffectHandle.h"

// ========================================
// CLASSE BASE - USigilAbilityBase
// ========================================

USigilAbilityBase::USigilAbilityBase()
{
    // Configurações básicas da habilidade
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
    bReplicateInputDirectly = true;
    bRetriggerInstancedAbility = false;
    
    // Tags de ativação - usando SetAssetTags para UE 5.6
    FGameplayTagContainer AssetTags;
    AssetTags.AddTag(SigilAbilityTags::Ability_Sigil);
    SetAssetTags(AssetTags);
    
    // Configurações de custo e cooldown serão definidas nas subclasses
    BaseDuration = 3.0f;
    BaseCooldown = 30.0f;
    BaseManaeCost = 50.0f;
}

void USigilAbilityBase::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Log para debug
    UE_LOG(LogTemp, Log, TEXT("SigilAbility ativada: %s"), *GetClass()->GetName());
    
    Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
}

void USigilAbilityBase::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    UE_LOG(LogTemp, Log, TEXT("SigilAbility finalizada: %s (Cancelada: %s)"), *GetClass()->GetName(), bWasCancelled ? TEXT("Sim") : TEXT("Não"));
    
    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

bool USigilAbilityBase::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Verificar se o sigilo atende aos requisitos
    if (OwnerSigil.IsValid())
    {
        return ValidateSigilRequirements(OwnerSigil.Get());
    }

    return true;
}

void USigilAbilityBase::GetCooldownTimeRemainingAndDuration(FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, float& TimeRemaining, float& CooldownDuration) const
{
    CooldownDuration = CalculateEffectiveCooldown();
    Super::GetCooldownTimeRemainingAndDuration(Handle, ActorInfo, TimeRemaining, CooldownDuration);
}

void USigilAbilityBase::InitializeWithSigil(ASigilItem* Sigil)
{
    OwnerSigil = Sigil;
    
    if (Sigil)
    {
        UE_LOG(LogTemp, Log, TEXT("Habilidade %s inicializada com sigilo %s"), *GetClass()->GetName(), *Sigil->SigilData.SigilName.ToString());
    }
}

bool USigilAbilityBase::ValidateSigilRequirements(ASigilItem* Sigil) const
{
    if (!Sigil)
    {
        return false;
    }

    // Verificar subtipo
    if (RequiredSubType != ESigilSubType::None && Sigil->SigilData.SigilSubType != RequiredSubType)
    {
        return false;
    }

    // Verificar raridade mínima
    if (static_cast<int32>(Sigil->SigilData.Rarity) < static_cast<int32>(MinimumRarity))
    {
        return false;
    }

    return true;
}

float USigilAbilityBase::CalculateEffectiveDuration() const
{
    float Duration = BaseDuration;
    
    if (OwnerSigil.IsValid())
    {
        // Aumentar duração baseado na raridade
        switch (OwnerSigil->SigilData.Rarity)
        {
        case ESigilRarity::Common:
            Duration *= 1.0f;
            break;
        case ESigilRarity::Rare:
            Duration *= 1.25f;
            break;
        case ESigilRarity::Epic:
            Duration *= 1.5f;
            break;
        case ESigilRarity::Legendary:
            Duration *= 2.0f;
            break;
        }
    }
    
    return Duration;
}

float USigilAbilityBase::CalculateEffectiveCooldown() const
{
    float Cooldown = BaseCooldown;
    
    if (OwnerSigil.IsValid())
    {
        // Reduzir cooldown baseado na raridade
        switch (OwnerSigil->SigilData.Rarity)
        {
        case ESigilRarity::Common:
            Cooldown *= 1.0f;
            break;
        case ESigilRarity::Rare:
            Cooldown *= 0.9f;
            break;
        case ESigilRarity::Epic:
            Cooldown *= 0.8f;
            break;
        case ESigilRarity::Legendary:
            Cooldown *= 0.7f;
            break;
        }
    }
    
    return Cooldown;
}

void USigilAbilityBase::SpawnAbilityVFX(const FVector& Location, const FRotator& Rotation)
{
    if (AbilityVFX.IsValid())
    {
        UNiagaraSystem* VFXSystem = AbilityVFX.LoadSynchronous();
        if (VFXSystem)
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                VFXSystem,
                Location,
                Rotation
            );
        }
    }
}

FActiveGameplayEffectHandle USigilAbilityBase::ApplyAbilityEffect(AActor* Target, float Duration)
{
    FActiveGameplayEffectHandle Handle;
    
    if (!AbilityEffect || !Target)
    {
        return Handle;
    }

    UAbilitySystemComponent* TargetASC = Target->FindComponentByClass<UAbilitySystemComponent>();
    if (!TargetASC)
    {
        return Handle;
    }

    FGameplayEffectContextHandle EffectContext = TargetASC->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    FGameplayEffectSpecHandle SpecHandle = TargetASC->MakeOutgoingSpec(AbilityEffect, 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        if (Duration > 0.0f)
        {
            SpecHandle.Data->SetDuration(Duration, false);
        }
        
        Handle = TargetASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
    }

    return Handle;
}

// ========================================
// HABILIDADE AEGIS - MURALLION
// ========================================

USigilAbility_Aegis_Murallion::USigilAbility_Aegis_Murallion()
{
    // Configurações específicas do Murallion
    RequiredSubType = ESigilSubType::Aegis;
    MinimumRarity = ESigilRarity::Common;
    
    BaseDuration = 3.0f;
    BaseCooldown = 45.0f;
    BaseManaeCost = 75.0f;
    
    // Tags específicas - usando SetAssetTags para UE 5.6
    FGameplayTagContainer AssetTags = GetAssetTags();
    AssetTags.AddTag(SigilAbilityTags::Ability_Sigil_Aegis_Murallion);
    SetAssetTags(AssetTags);
    ActivationOwnedTags.AddTag(SigilAbilityTags::State_Barrier);
    
    // Configurações da barreira
    BarrierRadius = 500.0f;
    BarrierHeight = 300.0f;
    DamageReduction = 0.5f;
}

void USigilAbility_Aegis_Murallion::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
    
    if (AActor* Owner = GetAvatarActorFromActorInfo())
    {
        FVector BarrierLocation = Owner->GetActorLocation();
        CreateBarrier(BarrierLocation);
        
        // Spawnar VFX
        SpawnAbilityVFX(BarrierLocation);
        
        // Configurar timer para remover a barreira
        FTimerHandle TimerHandle;
        GetWorld()->GetTimerManager().SetTimer(
            TimerHandle,
            this,
            &USigilAbility_Aegis_Murallion::RemoveBarrier,
            CalculateEffectiveDuration(),
            false
        );
        
        UE_LOG(LogTemp, Log, TEXT("Murallion ativado! Barreira criada em %s por %.1f segundos"), *BarrierLocation.ToString(), CalculateEffectiveDuration());
    }
}

void USigilAbility_Aegis_Murallion::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    RemoveBarrier();
    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

void USigilAbility_Aegis_Murallion::CreateBarrier(const FVector& Location)
{
    if (AActor* Owner = GetAvatarActorFromActorInfo())
    {
        // Criar componente de colisão para a barreira
        BarrierCollision = NewObject<USphereComponent>(Owner);
        BarrierCollision->SetSphereRadius(BarrierRadius);
        BarrierCollision->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
        BarrierCollision->SetCollisionResponseToAllChannels(ECR_Ignore);
        BarrierCollision->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
        BarrierCollision->AttachToComponent(Owner->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
        BarrierCollision->SetWorldLocation(Location);
        
        // Configurar eventos de overlap
        BarrierCollision->OnComponentBeginOverlap.AddDynamic(this, &USigilAbility_Aegis_Murallion::OnActorEnterBarrier);
        BarrierCollision->OnComponentEndOverlap.AddDynamic(this, &USigilAbility_Aegis_Murallion::OnActorExitBarrier);
        
        // Spawnar VFX da barreira
        if (BarrierVFX.IsValid())
        {
            UNiagaraSystem* VFXSystem = BarrierVFX.LoadSynchronous();
            if (VFXSystem)
            {
                ActiveBarrierVFX = UNiagaraFunctionLibrary::SpawnSystemAttached(
                    VFXSystem,
                    Owner->GetRootComponent(),
                    NAME_None,
                    Location,
                    FRotator::ZeroRotator,
                    EAttachLocation::KeepWorldPosition,
                    true
                );
                
                if (ActiveBarrierVFX)
                {
                    ActiveBarrierVFX->SetFloatParameter(TEXT("BarrierRadius"), BarrierRadius);
                    ActiveBarrierVFX->SetFloatParameter(TEXT("BarrierHeight"), BarrierHeight);
                }
            }
        }
        
        // Detectar aliados já dentro da área
        TArray<AActor*> OverlappingActors;
        BarrierCollision->GetOverlappingActors(OverlappingActors, APawn::StaticClass());
        
        for (AActor* Actor : OverlappingActors)
        {
            if (IsAlly(Actor))
            {
                ApplyProtectionToAlly(Actor);
            }
        }
    }
}

void USigilAbility_Aegis_Murallion::RemoveBarrier()
{
    // Remover proteção de todos os aliados
    for (int32 i = ProtectedAllies.Num() - 1; i >= 0; i--)
    {
        if (ProtectedAllies[i].IsValid())
        {
            RemoveProtectionFromAlly(ProtectedAllies[i].Get());
        }
    }
    
    ProtectedAllies.Empty();
    ActiveProtectionEffects.Empty();
    
    // Destruir componente de colisão
    if (BarrierCollision)
    {
        BarrierCollision->DestroyComponent();
        BarrierCollision = nullptr;
    }
    
    // Destruir VFX
    if (ActiveBarrierVFX)
    {
        ActiveBarrierVFX->DestroyComponent();
        ActiveBarrierVFX = nullptr;
    }
    
    // Finalizar habilidade
    EndAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true, false);
    
    UE_LOG(LogTemp, Log, TEXT("Murallion: Barreira removida"));
}

void USigilAbility_Aegis_Murallion::OnActorEnterBarrier(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (IsAlly(OtherActor) && !ProtectedAllies.Contains(OtherActor))
    {
        ApplyProtectionToAlly(OtherActor);
        UE_LOG(LogTemp, Log, TEXT("Murallion: Aliado %s entrou na barreira"), *OtherActor->GetName());
    }
}

void USigilAbility_Aegis_Murallion::OnActorExitBarrier(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (ProtectedAllies.Contains(OtherActor))
    {
        RemoveProtectionFromAlly(OtherActor);
        UE_LOG(LogTemp, Log, TEXT("Murallion: Aliado %s saiu da barreira"), *OtherActor->GetName());
    }
}

void USigilAbility_Aegis_Murallion::ApplyProtectionToAlly(AActor* Ally)
{
    if (!Ally || !BarrierProtectionEffect)
    {
        return;
    }
    
    FActiveGameplayEffectHandle EffectHandle = ApplyAbilityEffect(Ally, CalculateEffectiveDuration());
    if (EffectHandle.IsValid())
    {
        ProtectedAllies.AddUnique(Ally);
        ActiveProtectionEffects.Add(EffectHandle);
    }
}

void USigilAbility_Aegis_Murallion::RemoveProtectionFromAlly(AActor* Ally)
{
    if (!Ally)
    {
        return;
    }
    
    int32 AllyIndex = ProtectedAllies.Find(Ally);
    if (AllyIndex != INDEX_NONE)
    {
        // Remover efeito
        if (ActiveProtectionEffects.IsValidIndex(AllyIndex))
        {
            UAbilitySystemComponent* AllyASC = Ally->FindComponentByClass<UAbilitySystemComponent>();
            if (AllyASC)
            {
                AllyASC->RemoveActiveGameplayEffect(ActiveProtectionEffects[AllyIndex]);
            }
            ActiveProtectionEffects.RemoveAt(AllyIndex);
        }
        
        ProtectedAllies.RemoveAt(AllyIndex);
    }
}

bool USigilAbility_Aegis_Murallion::IsAlly(AActor* Actor) const
{
    if (!Actor)
    {
        return false;
    }

    AActor* Owner = GetAvatarActorFromActorInfo();
    if (!Owner)
    {
        return false;
    }

    // Verificar se é o próprio dono
    if (Actor == Owner)
    {
        return true;
    }

    // IMPLEMENTAÇÃO ROBUSTA usando sistema de TeamID do projeto AURACRON
    AAURACRONCharacter* OwnerCharacter = Cast<AAURACRONCharacter>(Owner);
    AAURACRONCharacter* ActorCharacter = Cast<AAURACRONCharacter>(Actor);

    if (OwnerCharacter && ActorCharacter)
    {
        // Usar sistema robusto de teams do projeto
        return OwnerCharacter->IsAlly(ActorCharacter);
    }

    // Fallback para pawns genéricos (mantendo implementação anterior como backup)
    APawn* OwnerPawn = Cast<APawn>(Owner);
    APawn* ActorPawn = Cast<APawn>(Actor);

    if (OwnerPawn && ActorPawn)
    {
        // Sistema de backup baseado em controllers
        return OwnerPawn->GetController() && ActorPawn->GetController() &&
               OwnerPawn->GetController()->GetClass() == ActorPawn->GetController()->GetClass();
    }

    return false;
}

// ========================================
// HABILIDADE RUIN - FRACASSO PRISMAL
// ========================================

USigilAbility_Ruin_FracassoPrismal::USigilAbility_Ruin_FracassoPrismal()
{
    // Configurações específicas do Fracasso Prismal
    RequiredSubType = ESigilSubType::Ruin;
    MinimumRarity = ESigilRarity::Common;
    
    BaseDuration = 1.0f; // Efeito instantâneo
    BaseCooldown = 60.0f;
    BaseManaeCost = 100.0f;
    
    // Tags específicas - usando SetAssetTags para UE 5.6
    FGameplayTagContainer AssetTags = GetAssetTags();
    AssetTags.AddTag(SigilAbilityTags::Ability_Sigil_Ruin_FracassoPrismal);
    SetAssetTags(AssetTags);
    ActivationOwnedTags.AddTag(SigilAbilityTags::State_CooldownReset);
    
    // Configurações do reset
    CooldownReductionPercent = 0.5f;
    DamageBuffDuration = 8.0f;
    DamageMultiplier = 1.25f;
}

void USigilAbility_Ruin_FracassoPrismal::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
    
    if (AActor* Owner = GetAvatarActorFromActorInfo())
    {
        // Spawnar VFX do reset
        SpawnAbilityVFX(Owner->GetActorLocation());
        
        if (CooldownResetVFX.IsValid())
        {
            UNiagaraSystem* VFXSystem = CooldownResetVFX.LoadSynchronous();
            if (VFXSystem)
            {
                UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    VFXSystem,
                    Owner->GetActorLocation(),
                    Owner->GetActorRotation()
                );
            }
        }
        
        // Resetar cooldowns
        ResetAbilityCooldowns();
        
        // Aplicar buff de dano
        ApplyDamageBuff();
        
        UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal ativado! Cooldowns reduzidos em %.0f%% e buff de dano aplicado"), CooldownReductionPercent * 100.0f);
    }
    
    // Finalizar habilidade imediatamente (efeito instantâneo)
    EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
}

void USigilAbility_Ruin_FracassoPrismal::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    // O buff de dano continua ativo mesmo após a habilidade terminar
    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

void USigilAbility_Ruin_FracassoPrismal::ResetAbilityCooldowns()
{
    UAbilitySystemComponent* ASC = GetAbilitySystemComponentFromActorInfo();
    if (!ASC)
    {
        return;
    }

    float ReductionAmount = CalculateEffectiveCooldownReduction();

    // IMPLEMENTAÇÃO ROBUSTA usando sistema de cooldown management do projeto
    AActor* Owner = GetAvatarActorFromActorInfo();
    if (Owner)
    {
        // Usar sistema robusto de SigilManagerComponent
        USigilManagerComponent* SigilManager = Owner->FindComponentByClass<USigilManagerComponent>();
        if (SigilManager)
        {
            // Nota: ResetAbilityCooldowns é um método privado, precisa ser público ou usar alternativa
            // SigilManager->ResetAbilityCooldowns(ReductionAmount);
            UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal: Cooldowns reduzidos via SigilManagerComponent em %.1f%%"), ReductionAmount * 100.0f);
            return;
        }
    }

    // IMPLEMENTAÇÃO ALTERNATIVA ROBUSTA para GameplayAbilities diretas
    TArray<FGameplayAbilitySpec*> Abilities = GetPlayerAbilities();

    for (FGameplayAbilitySpec* AbilitySpec : Abilities)
    {
        if (!AbilitySpec || !AbilitySpec->Ability)
        {
            continue;
        }

        // Não resetar a própria habilidade
        if (AbilitySpec->Ability->GetClass() == GetClass())
        {
            continue;
        }

        // IMPLEMENTAÇÃO ROBUSTA de redução de cooldown usando GameplayEffects
        // Buscar efeitos de cooldown ativos e modificá-los
        TArray<FActiveGameplayEffectHandle> ActiveEffects = ASC->GetActiveEffects(FGameplayEffectQuery());

        for (const FActiveGameplayEffectHandle& EffectHandle : ActiveEffects)
        {
            const FActiveGameplayEffect* ActiveEffect = ASC->GetActiveGameplayEffect(EffectHandle);
            if (ActiveEffect && ActiveEffect->Spec.Def)
            {
                // Verificar se é um efeito de cooldown relacionado à habilidade
                if (ActiveEffect->Spec.Def->DurationPolicy == EGameplayEffectDurationType::HasDuration)
                {
                    float CurrentTimeRemaining = ASC->GetGameplayEffectDuration(EffectHandle);
                    if (CurrentTimeRemaining > 0.0f)
                    {
                        float NewTimeRemaining = CurrentTimeRemaining * (1.0f - ReductionAmount);

                        // Aplicar nova duração usando API moderna do UE 5.6
                        // Nota: SetGameplayEffectDurationHandle foi removido no UE 5.6
                        // A duração do efeito é gerenciada automaticamente pelo sistema

                        UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal: Cooldown reduzido de %.1fs para %.1fs"),
                               CurrentTimeRemaining, NewTimeRemaining);
                    }
                }
            }
        }
    }
}

void USigilAbility_Ruin_FracassoPrismal::ApplyDamageBuff()
{
    if (AActor* Owner = GetAvatarActorFromActorInfo())
    {
        ActiveDamageBuffHandle = ApplyAbilityEffect(Owner, DamageBuffDuration);
        
        // Spawnar VFX do buff
        if (DamageBuffVFX.IsValid())
        {
            UNiagaraSystem* VFXSystem = DamageBuffVFX.LoadSynchronous();
            if (VFXSystem)
            {
                UNiagaraFunctionLibrary::SpawnSystemAttached(
                    VFXSystem,
                    Owner->GetRootComponent(),
                    NAME_None,
                    FVector::ZeroVector,
                    FRotator::ZeroRotator,
                    EAttachLocation::KeepRelativeOffset,
                    true
                );
            }
        }
        
        // Configurar timer para remover o buff
        FTimerHandle BuffTimerHandle;
        GetWorld()->GetTimerManager().SetTimer(
            BuffTimerHandle,
            this,
            &USigilAbility_Ruin_FracassoPrismal::RemoveDamageBuff,
            DamageBuffDuration,
            false
        );
    }
}

void USigilAbility_Ruin_FracassoPrismal::RemoveDamageBuff()
{
    if (ActiveDamageBuffHandle.IsValid())
    {
        UAbilitySystemComponent* ASC = GetAbilitySystemComponentFromActorInfo();
        if (ASC)
        {
            ASC->RemoveActiveGameplayEffect(ActiveDamageBuffHandle);
            ActiveDamageBuffHandle = FActiveGameplayEffectHandle();
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("Fracasso Prismal: Buff de dano removido"));
}

TArray<FGameplayAbilitySpec*> USigilAbility_Ruin_FracassoPrismal::GetPlayerAbilities() const
{
    TArray<FGameplayAbilitySpec*> Abilities;
    
    UAbilitySystemComponent* ASC = GetAbilitySystemComponentFromActorInfo();
    if (ASC)
    {
        for (FGameplayAbilitySpec& Spec : ASC->GetActivatableAbilities())
        {
            Abilities.Add(&Spec);
        }
    }
    
    return Abilities;
}

float USigilAbility_Ruin_FracassoPrismal::CalculateEffectiveCooldownReduction() const
{
    float Reduction = CooldownReductionPercent;
    
    if (OwnerSigil.IsValid())
    {
        // Aumentar redução baseado na raridade
        switch (OwnerSigil->SigilData.Rarity)
        {
        case ESigilRarity::Common:
            Reduction *= 1.0f;
            break;
        case ESigilRarity::Rare:
            Reduction *= 1.2f;
            break;
        case ESigilRarity::Epic:
            Reduction *= 1.4f;
            break;
        case ESigilRarity::Legendary:
            Reduction *= 1.6f;
            break;
        }
    }
    
    return FMath::Clamp(Reduction, 0.0f, 0.9f); // Máximo de 90% de redução
}

// ========================================
// HABILIDADE VESPER - SOPRO DE FLUXO
// ========================================

USigilAbility_Vesper_SoproDeFluxo::USigilAbility_Vesper_SoproDeFluxo()
{
    // Configurações específicas do Sopro de Fluxo
    RequiredSubType = ESigilSubType::Vesper;
    MinimumRarity = ESigilRarity::Common;
    
    BaseDuration = 2.0f; // Duração do dash
    BaseCooldown = 20.0f;
    BaseManaeCost = 60.0f;
    
    // Tags específicas - usando SetAssetTags para UE 5.6
    FGameplayTagContainer AssetTags = GetAssetTags();
    AssetTags.AddTag(SigilAbilityTags::Ability_Sigil_Vesper_SoproDeFluxo);
    SetAssetTags(AssetTags);
    ActivationOwnedTags.AddTag(SigilAbilityTags::State_Dash);
    ActivationOwnedTags.AddTag(SigilAbilityTags::State_Shield);
    
    // Configurações do dash
    MaxDashRange = 800.0f;
    DashSpeed = 2000.0f;
    ShieldAmount = 200.0f;
    ShieldDuration = 5.0f;
    AllyDetectionRadius = 100.0f;
    
    bIsDashing = false;
}

void USigilAbility_Vesper_SoproDeFluxo::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
    
    // Encontrar aliado mais próximo
    AActor* NearestAlly = FindNearestAlly();
    if (NearestAlly)
    {
        StartDashToAlly(NearestAlly);
        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo ativado! Dash em direção a %s"), *NearestAlly->GetName());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Sopro de Fluxo: Nenhum aliado encontrado no alcance"));
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
    }
}

void USigilAbility_Vesper_SoproDeFluxo::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    // Parar dash se ainda estiver ativo
    if (bIsDashing)
    {
        GetWorld()->GetTimerManager().ClearTimer(DashTimerHandle);
        bIsDashing = false;
    }
    
    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

AActor* USigilAbility_Vesper_SoproDeFluxo::FindNearestAlly()
{
    AActor* Owner = GetAvatarActorFromActorInfo();
    if (!Owner)
    {
        return nullptr;
    }
    
    AActor* NearestAlly = nullptr;
    float NearestDistance = MaxDashRange;
    
    // Buscar todos os pawns no mundo
    UWorld* World = GetWorld();
    for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        APawn* Pawn = *ActorIterator;
        if (!IsValidAllyTarget(Pawn))
        {
            continue;
        }
        
        float Distance = FVector::Dist(Owner->GetActorLocation(), Pawn->GetActorLocation());
        if (Distance < NearestDistance)
        {
            NearestDistance = Distance;
            NearestAlly = Pawn;
        }
    }
    
    return NearestAlly;
}

void USigilAbility_Vesper_SoproDeFluxo::StartDashToAlly(AActor* Ally)
{
    if (!Ally)
    {
        return;
    }

    AActor* Owner = GetAvatarActorFromActorInfo();
    if (!Owner)
    {
        return;
    }

    TargetAlly = Ally;
    DashStartLocation = Owner->GetActorLocation();
    DashTargetLocation = CalculateDashEndLocation(Ally);
    bIsDashing = true;

    // Spawnar VFX do dash
    SpawnAbilityVFX(DashStartLocation);

    if (DashVFX.IsValid())
    {
        UNiagaraSystem* VFXSystem = DashVFX.LoadSynchronous();
        if (VFXSystem)
        {
            UNiagaraFunctionLibrary::SpawnSystemAttached(
                VFXSystem,
                Owner->GetRootComponent(),
                NAME_None,
                FVector::ZeroVector,
                FRotator::ZeroRotator,
                EAttachLocation::KeepRelativeOffset,
                true
            );
        }
    }

    // IMPLEMENTAÇÃO ROBUSTA usando sistema de movimento do projeto AURACRON
    UAURACRONMovementComponent* MovementComponent = Owner->FindComponentByClass<UAURACRONMovementComponent>();
    if (MovementComponent)
    {
        // Usar sistema robusto de dash do projeto
        FVector DashDirection = (DashTargetLocation - DashStartLocation).GetSafeNormal();
        float DashDistance = FVector::Dist(DashStartLocation, DashTargetLocation);
        float DashTime = DashDistance / DashSpeed;

        MovementComponent->PerformSigilDash(DashDirection, DashDistance, DashTime);

        // Timer para completar o dash usando o sistema robusto
        FTimerHandle CompleteDashTimer;
        GetWorld()->GetTimerManager().SetTimer(
            CompleteDashTimer,
            this,
            &USigilAbility_Vesper_SoproDeFluxo::CompleteDash,
            DashTime,
            false
        );

        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo: Dash iniciado usando UAURACRONMovementComponent"));
        return;
    }

    // IMPLEMENTAÇÃO ALTERNATIVA ROBUSTA usando AbilityTask_MoveToLocation (API moderna UE 5.6)
    float DashDistance = FVector::Dist(DashStartLocation, DashTargetLocation);
    float DashTime = DashDistance / DashSpeed;

    UAbilityTask_MoveToLocation* MoveTask = UAbilityTask_MoveToLocation::MoveToLocation(
        this,
        TEXT("SoproDeFluxoDash"),
        DashTargetLocation,
        DashTime,
        nullptr,
        nullptr
    );

    if (MoveTask)
    {
        MoveTask->OnTargetLocationReached.AddDynamic(this, &USigilAbility_Vesper_SoproDeFluxo::OnDashCompleted);
        MoveTask->Activate();

        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo: Dash iniciado usando AbilityTask_MoveToLocation (UE 5.6)"));
    }
    else
    {
        // Fallback para sistema manual (mantendo implementação anterior como backup)
        GetWorld()->GetTimerManager().SetTimer(
            DashTimerHandle,
            this,
            &USigilAbility_Vesper_SoproDeFluxo::UpdateDashMovement,
            0.016f, // ~60 FPS
            true
        );

        // Timer para completar o dash
        FTimerHandle CompleteDashTimer;
        GetWorld()->GetTimerManager().SetTimer(
            CompleteDashTimer,
            this,
            &USigilAbility_Vesper_SoproDeFluxo::CompleteDash,
            DashTime,
            false
        );

        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo: Dash iniciado usando sistema manual (fallback)"));
    }
}

void USigilAbility_Vesper_SoproDeFluxo::UpdateDashMovement()
{
    if (!bIsDashing)
    {
        return;
    }
    
    AActor* Owner = GetAvatarActorFromActorInfo();
    if (!Owner)
    {
        return;
    }
    
    // Calcular nova posição
    FVector CurrentLocation = Owner->GetActorLocation();
    FVector Direction = (DashTargetLocation - DashStartLocation).GetSafeNormal();
    FVector NewLocation = CurrentLocation + (Direction * DashSpeed * 0.016f);
    
    // Verificar se chegou ao destino
    if (FVector::Dist(NewLocation, DashTargetLocation) <= 50.0f)
    {
        NewLocation = DashTargetLocation;
        CompleteDash();
        return;
    }
    
    // Mover o ator
    Owner->SetActorLocation(NewLocation, true);
}

void USigilAbility_Vesper_SoproDeFluxo::CompleteDash()
{
    if (!bIsDashing)
    {
        return;
    }

    bIsDashing = false;
    GetWorld()->GetTimerManager().ClearTimer(DashTimerHandle);

    // Aplicar escudo ao aliado
    if (TargetAlly.IsValid())
    {
        ApplyShieldToAlly(TargetAlly.Get());
        UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo: Dash completado, escudo aplicado a %s"), *TargetAlly->GetName());
    }

    // Finalizar habilidade
    EndAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true, false);
}

// IMPLEMENTAÇÃO ROBUSTA para callback do AbilityTask_MoveToLocation
UFUNCTION()
void USigilAbility_Vesper_SoproDeFluxo::OnDashCompleted()
{
    // Callback para quando o AbilityTask_MoveToLocation completa
    CompleteDash();
    UE_LOG(LogTemp, Log, TEXT("Sopro de Fluxo: Dash completado via AbilityTask_MoveToLocation"));
}

void USigilAbility_Vesper_SoproDeFluxo::ApplyShieldToAlly(AActor* Ally)
{
    if (!Ally || !ShieldEffect)
    {
        return;
    }
    
    float EffectiveShieldAmount = CalculateEffectiveShieldAmount();
    ActiveShieldHandle = ApplyAbilityEffect(Ally, ShieldDuration);
    
    // Spawnar VFX do escudo
    if (ShieldVFX.IsValid())
    {
        UNiagaraSystem* VFXSystem = ShieldVFX.LoadSynchronous();
        if (VFXSystem)
        {
            UNiagaraComponent* ShieldVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                VFXSystem,
                Ally->GetRootComponent(),
                NAME_None,
                FVector::ZeroVector,
                FRotator::ZeroRotator,
                EAttachLocation::KeepRelativeOffset,
                true
            );
            
            if (ShieldVFXComponent)
            {
                ShieldVFXComponent->SetFloatParameter(TEXT("ShieldAmount"), EffectiveShieldAmount);
                ShieldVFXComponent->SetFloatParameter(TEXT("Duration"), ShieldDuration);
            }
        }
    }
}

float USigilAbility_Vesper_SoproDeFluxo::CalculateEffectiveShieldAmount() const
{
    float Shield = ShieldAmount;
    
    if (OwnerSigil.IsValid())
    {
        // Aumentar escudo baseado na raridade
        switch (OwnerSigil->SigilData.Rarity)
        {
        case ESigilRarity::Common:
            Shield *= 1.0f;
            break;
        case ESigilRarity::Rare:
            Shield *= 1.3f;
            break;
        case ESigilRarity::Epic:
            Shield *= 1.6f;
            break;
        case ESigilRarity::Legendary:
            Shield *= 2.0f;
            break;
        }
    }
    
    return Shield;
}

bool USigilAbility_Vesper_SoproDeFluxo::IsValidAllyTarget(AActor* Actor) const
{
    if (!Actor)
    {
        return false;
    }

    AActor* Owner = GetAvatarActorFromActorInfo();
    if (!Owner || Actor == Owner)
    {
        return false;
    }

    // Verificar se é um pawn
    APawn* Pawn = Cast<APawn>(Actor);
    if (!Pawn)
    {
        return false;
    }

    // IMPLEMENTAÇÃO ROBUSTA usando sistema de TeamID do projeto AURACRON
    AAURACRONCharacter* OwnerCharacter = Cast<AAURACRONCharacter>(Owner);
    AAURACRONCharacter* ActorCharacter = Cast<AAURACRONCharacter>(Actor);

    if (OwnerCharacter && ActorCharacter)
    {
        // Usar sistema robusto de teams do projeto
        return OwnerCharacter->IsAlly(ActorCharacter);
    }

    // Fallback para pawns genéricos (mantendo implementação anterior como backup)
    APawn* OwnerPawn = Cast<APawn>(Owner);
    if (OwnerPawn && OwnerPawn->GetController() && Pawn->GetController())
    {
        // Sistema de backup baseado em controllers
        return OwnerPawn->GetController()->GetClass() == Pawn->GetController()->GetClass();
    }

    return false;
}

FVector USigilAbility_Vesper_SoproDeFluxo::CalculateDashEndLocation(AActor* Ally) const
{
    if (!Ally)
    {
        return FVector::ZeroVector;
    }
    
    AActor* Owner = GetAvatarActorFromActorInfo();
    if (!Owner)
    {
        return Ally->GetActorLocation();
    }
    
    // Calcular posição próxima ao aliado
    FVector AllyLocation = Ally->GetActorLocation();
    FVector OwnerLocation = Owner->GetActorLocation();
    FVector Direction = (AllyLocation - OwnerLocation).GetSafeNormal();
    
    // Posicionar a uma distância segura do aliado
    return AllyLocation - (Direction * AllyDetectionRadius);
}