// SigilGameplayEffects.cpp
// AURACRON - Sistema de Sígilos
// Implementação dos GameplayEffects para fusão automática e modificadores espectrais
// APIs verificadas: GameplayEffect.h, GameplayEffectExecutionCalculation.h

#include "Sigils/SigilGameplayEffects.h"
#include "Sigils/SigilAttributeSet.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectTypes.h"
#include "GameplayEffectComponents/TargetTagRequirementsGameplayEffectComponent.h"
#include "GameplayEffectComponents/TargetTagsGameplayEffectComponent.h"
#include "GameplayTagsManager.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Components/ActorComponent.h"
#include "Math/UnrealMathUtility.h"
#include "Logging/LogMacros.h"

// ========================================
// INICIALIZAÇÃO DE DADOS ESTÁTICOS
// ========================================

TMap<ESpectralEffectType, TSubclassOf<UGameplayEffect>> USigilEffectFactory::EffectTemplates;
TMap<ESigilRarity, float> USigilEffectFactory::DefaultRarityMultipliers;

// ========================================
// GAMEPLAY EFFECT BASE
// ========================================

USigilGameplayEffectBase::USigilGameplayEffectBase()
{
    // Configuração base do GameplayEffect
    DurationPolicy = EGameplayEffectDurationType::Infinite;
    StackingType = EGameplayEffectStackingType::None;
    
    // Configurar tags padrão usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& TagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer BaseTagContainer = FInheritedTagContainer();
    BaseTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Base")));
    TagsComponent.SetAndApplyTargetTagChanges(BaseTagContainer);
    
    // Configuração padrão do efeito espectral
    SpectralConfig.EffectType = ESpectralEffectType::None;
    SpectralConfig.BaseMagnitude = 1.0f;
    SpectralConfig.Duration = -1.0f;
    SpectralConfig.bCanStack = false;
    SpectralConfig.MaxStacks = 1;
    
    // Configurações MOBA
    TeamFightMultiplier = 1.0f;
    ObjectiveMultiplier = 1.0f;
    
    // Configurações de aplicação
    MinimumRarity = ESigilRarity::Common;
    RequiredSigilType = ESigilType::None;
    bFusionOnly = false;
}

void USigilGameplayEffectBase::PostInitProperties()
{
    Super::PostInitProperties();
    
    // Configurar duração baseada na configuração espectral
    if (SpectralConfig.Duration > 0.0f)
    {
        DurationPolicy = EGameplayEffectDurationType::HasDuration;
        DurationMagnitude = FGameplayEffectModifierMagnitude(SpectralConfig.Duration);
    }
    else
    {
        DurationPolicy = EGameplayEffectDurationType::Infinite;
    }
    
    // Configurar empilhamento
    if (SpectralConfig.bCanStack)
    {
        StackingType = EGameplayEffectStackingType::AggregateBySource;
        StackLimitCount = SpectralConfig.MaxStacks;
    }
    
    // Adicionar tags do efeito espectral usando UTargetTagsGameplayEffectComponent
    if (!SpectralConfig.EffectTags.IsEmpty())
    {
        UTargetTagsGameplayEffectComponent& SpectralTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
        FInheritedTagContainer SpectralTags;
        for (const FGameplayTag& Tag : SpectralConfig.EffectTags)
        {
            SpectralTags.AddTag(Tag);
        }
        SpectralTagsComponent.SetAndApplyTargetTagChanges(SpectralTags);
    }
}

// ========================================
// EFEITO DE FUSÃO AUTOMÁTICA
// ========================================

USigilAutoFusionEffect::USigilAutoFusionEffect()
{
    // Configuração específica de fusão automática
    FusionTimeSeconds = SigilEffectConstants::DEFAULT_FUSION_TIME;
    FusionPowerMultiplier = 1.5f;
    bPermanentFusion = true;
    
    // Configurar multiplicadores por raridade
    FusionMultipliersByRarity.Add(ESigilRarity::Common, 1.2f);
    FusionMultipliersByRarity.Add(ESigilRarity::Rare, 1.4f);
    FusionMultipliersByRarity.Add(ESigilRarity::Epic, 1.7f);
    FusionMultipliersByRarity.Add(ESigilRarity::Legendary, 2.2f);
    
    // Configurar como efeito de fusão
    bFusionOnly = true;
    
    // Tags específicas de fusão usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& FusionTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer FusionTagContainer = FInheritedTagContainer();
    FusionTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.AutoFusion")));
    FusionTagsComponent.SetAndApplyTargetTagChanges(FusionTagContainer);
    
    // Configurar período de aplicação (verificar a cada segundo)
    Period = 1.0f;
    bExecutePeriodicEffectOnApplication = false;
    
    // Configurar como efeito infinito até fusão
    DurationPolicy = EGameplayEffectDurationType::Infinite;
}

void USigilAutoFusionEffect::PostInitProperties()
{
    Super::PostInitProperties();
    
    // Configurar modificadores de atributos espectrais
    FGameplayModifierInfo PowerModifier;
    PowerModifier.Attribute = USigilAttributeSet::GetSpectralPowerAttribute();
    PowerModifier.ModifierOp = EGameplayModOp::Additive;
    PowerModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FusionPowerMultiplier);
    Modifiers.Add(PowerModifier);
    
    // Configurar execution calculation para lógica complexa
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilFusionExecutionCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

// ========================================
// EFEITO DE FUSÃO FORÇADA
// ========================================

USigilForceFusionEffect::USigilForceFusionEffect()
{
    ManaCost = SigilEffectConstants::FORCE_FUSION_MANA_COST;
    CooldownSeconds = SigilEffectConstants::FORCE_FUSION_COOLDOWN;
    ForcedFusionPenalty = 0.8f;
    
    // Configurar como efeito instantâneo
    DurationPolicy = EGameplayEffectDurationType::Instant;
    
    // Tags de fusão forçada usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& ForceFusionTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer ForceFusionTagContainer = FInheritedTagContainer();
    ForceFusionTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.ForceFusion")));
    ForceFusionTagsComponent.SetAndApplyTargetTagChanges(ForceFusionTagContainer);
    
    // Configurar custo de mana
    FGameplayModifierInfo ManaCostModifier;
    ManaCostModifier.Attribute = USigilAttributeSet::GetManaRegenerationAttribute();
    ManaCostModifier.ModifierOp = EGameplayModOp::Additive;
    ManaCostModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(-ManaCost);
    Modifiers.Add(ManaCostModifier);
}

// ========================================
// EFEITOS ESPECTRAIS POR ATRIBUTO
// ========================================

USigilSpectralPowerEffect::USigilSpectralPowerEffect()
{
    PowerIncrease = 10.0f;
    PowerIncreasePercent = 0.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::PowerBoost;
    SpectralConfig.BaseMagnitude = PowerIncrease;
    
    // Tags de poder espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralPowerTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralPowerTagContainer = FInheritedTagContainer();
    SpectralPowerTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralPower")));
    SpectralPowerTagsComponent.SetAndApplyTargetTagChanges(SpectralPowerTagContainer);
    
    // Modificador de poder espectral
    FGameplayModifierInfo PowerModifier;
    PowerModifier.Attribute = USigilAttributeSet::GetSpectralPowerAttribute();
    PowerModifier.ModifierOp = EGameplayModOp::Additive;
    PowerModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(PowerIncrease);
    Modifiers.Add(PowerModifier);
    
    // Modificador percentual se especificado
    if (PowerIncreasePercent > 0.0f)
    {
        FGameplayModifierInfo PercentModifier;
        PercentModifier.Attribute = USigilAttributeSet::GetSpectralPowerAttribute();
        PercentModifier.ModifierOp = EGameplayModOp::Multiplicitive;
        PercentModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(PowerIncreasePercent / 100.0f);
        Modifiers.Add(PercentModifier);
    }
}

USigilSpectralResilienceEffect::USigilSpectralResilienceEffect()
{
    ResilienceIncrease = 8.0f;
    DamageReductionPercent = 5.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::ResilienceBoost;
    SpectralConfig.BaseMagnitude = ResilienceIncrease;
    
    // Tags de resistência espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralResilienceTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralResilienceTagContainer = FInheritedTagContainer();
    SpectralResilienceTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralResilience")));
    SpectralResilienceTagsComponent.SetAndApplyTargetTagChanges(SpectralResilienceTagContainer);
    
    // Modificador de resistência espectral
    FGameplayModifierInfo ResilienceModifier;
    ResilienceModifier.Attribute = USigilAttributeSet::GetSpectralResilienceAttribute();
    ResilienceModifier.ModifierOp = EGameplayModOp::Additive;
    ResilienceModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(ResilienceIncrease);
    Modifiers.Add(ResilienceModifier);
    
    // Modificador de poder defensivo
    FGameplayModifierInfo DefenseModifier;
    DefenseModifier.Attribute = USigilAttributeSet::GetDefensePowerAttribute();
    DefenseModifier.ModifierOp = EGameplayModOp::Multiplicitive;
    DefenseModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(DamageReductionPercent / 100.0f);
    Modifiers.Add(DefenseModifier);
}

USigilSpectralVelocityEffect::USigilSpectralVelocityEffect()
{
    VelocityIncrease = 12.0f;
    MovementSpeedIncrease = 25.0f;
    CooldownReductionPercent = 10.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::VelocityBoost;
    SpectralConfig.BaseMagnitude = VelocityIncrease;
    
    // Tags de velocidade espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralVelocityTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralVelocityTagContainer = FInheritedTagContainer();
    SpectralVelocityTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralVelocity")));
    SpectralVelocityTagsComponent.SetAndApplyTargetTagChanges(SpectralVelocityTagContainer);
    
    // Modificador de velocidade espectral
    FGameplayModifierInfo VelocityModifier;
    VelocityModifier.Attribute = USigilAttributeSet::GetSpectralVelocityAttribute();
    VelocityModifier.ModifierOp = EGameplayModOp::Additive;
    VelocityModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(VelocityIncrease);
    Modifiers.Add(VelocityModifier);
    
    // Modificador de velocidade de movimento
    FGameplayModifierInfo MovementModifier;
    MovementModifier.Attribute = USigilAttributeSet::GetMovementSpeedAttribute();
    MovementModifier.ModifierOp = EGameplayModOp::Additive;
    MovementModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(MovementSpeedIncrease);
    Modifiers.Add(MovementModifier);
    
    // Modificador de redução de cooldown
    FGameplayModifierInfo CooldownModifier;
    CooldownModifier.Attribute = USigilAttributeSet::GetCooldownReductionAttribute();
    CooldownModifier.ModifierOp = EGameplayModOp::Additive;
    CooldownModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(CooldownReductionPercent);
    Modifiers.Add(CooldownModifier);
}

USigilSpectralFocusEffect::USigilSpectralFocusEffect()
{
    FocusIncrease = 15.0f;
    CriticalChanceIncrease = 8.0f;
    ManaRegenIncrease = 5.0f;
    
    SpectralConfig.EffectType = ESpectralEffectType::FocusBoost;
    SpectralConfig.BaseMagnitude = FocusIncrease;
    
    // Tags de foco espectral usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& SpectralFocusTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer SpectralFocusTagContainer = FInheritedTagContainer();
    SpectralFocusTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralFocus")));
    SpectralFocusTagsComponent.SetAndApplyTargetTagChanges(SpectralFocusTagContainer);
    
    // Modificador de foco espectral
    FGameplayModifierInfo FocusModifier;
    FocusModifier.Attribute = USigilAttributeSet::GetSpectralFocusAttribute();
    FocusModifier.ModifierOp = EGameplayModOp::Additive;
    FocusModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FocusIncrease);
    Modifiers.Add(FocusModifier);
    
    // Modificador de chance crítica
    FGameplayModifierInfo CriticalModifier;
    CriticalModifier.Attribute = USigilAttributeSet::GetCriticalChanceAttribute();
    CriticalModifier.ModifierOp = EGameplayModOp::Additive;
    CriticalModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(CriticalChanceIncrease);
    Modifiers.Add(CriticalModifier);
    
    // Modificador de regeneração de mana
    FGameplayModifierInfo ManaRegenModifier;
    ManaRegenModifier.Attribute = USigilAttributeSet::GetManaRegenerationAttribute();
    ManaRegenModifier.ModifierOp = EGameplayModOp::Additive;
    ManaRegenModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(ManaRegenIncrease);
    Modifiers.Add(ManaRegenModifier);
}

// ========================================
// EFEITOS MOBA ESPECÍFICOS
// ========================================

USigilTeamFightEffect::USigilTeamFightEffect()
{
    DamageBonus = 15.0f;
    ResistanceBonus = 10.0f;
    TeamFightRadius = SigilEffectConstants::TEAM_FIGHT_RADIUS;
    MinEnemiesForActivation = SigilEffectConstants::MIN_ENEMIES_TEAM_FIGHT;
    
    SpectralConfig.EffectType = ESpectralEffectType::TeamFightBonus;
    SpectralConfig.BaseMagnitude = DamageBonus;
    
    // Configurar como efeito condicional
    DurationPolicy = EGameplayEffectDurationType::Infinite;
    Period = 0.5f; // Verificar a cada meio segundo
    bExecutePeriodicEffectOnApplication = true;
    
    // Tags de team fight usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& TeamFightTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer TeamFightTagContainer = FInheritedTagContainer();
    TeamFightTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.TeamFight")));
    TeamFightTagsComponent.SetAndApplyTargetTagChanges(TeamFightTagContainer);
    
    // Configurar execution calculation para detecção de team fight
    FGameplayEffectExecutionDefinition ExecutionDef;
    ExecutionDef.CalculationClass = USigilFusionExecutionCalculation::StaticClass();
    Executions.Add(ExecutionDef);
}

USigilObjectiveEffect::USigilObjectiveEffect()
{
    ObjectiveDamageBonus = 20.0f;
    AttackSpeedBonus = 25.0f;
    ObjectiveRadius = SigilEffectConstants::OBJECTIVE_RADIUS;
    
    SpectralConfig.EffectType = ESpectralEffectType::ObjectiveBonus;
    SpectralConfig.BaseMagnitude = ObjectiveDamageBonus;
    
    // Configurar tags de objetivos válidos
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Tower"))
    );
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Nexus"))
    );
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Dragon"))
    );
    ValidObjectiveTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.Objective.Baron"))
    );
    
    // Configurar como efeito condicional
    DurationPolicy = EGameplayEffectDurationType::Infinite;
    Period = 1.0f; // Verificar a cada segundo
    bExecutePeriodicEffectOnApplication = true;
    
    // Tags de objetivo usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& ObjectiveTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer ObjectiveTagContainer = FInheritedTagContainer();
    ObjectiveTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Objective")));
    ObjectiveTagsComponent.SetAndApplyTargetTagChanges(ObjectiveTagContainer);
}

// ========================================
// EFEITO DE REFORGE
// ========================================

USigilReforgeEffect::USigilReforgeEffect()
{
    ReforgeCooldown = SigilEffectConstants::REFORGE_COOLDOWN;
    ReforgeGoldCost = SigilEffectConstants::REFORGE_GOLD_COST;
    RarityUpgradeChance = SigilEffectConstants::RARITY_UPGRADE_CHANCE;
    KeepStatsChance = SigilEffectConstants::KEEP_STATS_CHANCE;
    
    // Configurar como efeito instantâneo
    DurationPolicy = EGameplayEffectDurationType::Instant;
    
    // Tags de reforge usando UTargetTagsGameplayEffectComponent
    UTargetTagsGameplayEffectComponent& ReforgeTagsComponent = AddComponent<UTargetTagsGameplayEffectComponent>();
    FInheritedTagContainer ReforgeTagContainer = FInheritedTagContainer();
    ReforgeTagContainer.AddTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Reforge")));
    ReforgeTagsComponent.SetAndApplyTargetTagChanges(ReforgeTagContainer);
    
    // Usar UTargetTagRequirementsGameplayEffectComponent em vez de APIs deprecated
    UTargetTagRequirementsGameplayEffectComponent& TagComponent = AddComponent<UTargetTagRequirementsGameplayEffectComponent>();
    TagComponent.ApplicationTagRequirements.IgnoreTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.State.ReforgeCD"))
    );
    TagComponent.OngoingTagRequirements.IgnoreTags.AddTag(
        UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.State.ReforgeCD"))
    );
}

// ========================================
// EXECUTION CALCULATIONS
// ========================================

USigilFusionExecutionCalculation::USigilFusionExecutionCalculation()
{
    // Definir atributos relevantes para captura
    FGameplayEffectAttributeCaptureDefinition SpectralPowerDef;
    SpectralPowerDef.AttributeToCapture = USigilAttributeSet::GetSpectralPowerAttribute();
    SpectralPowerDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralPowerDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralPowerDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralResilienceDef;
    SpectralResilienceDef.AttributeToCapture = USigilAttributeSet::GetSpectralResilienceAttribute();
    SpectralResilienceDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralResilienceDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralResilienceDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralVelocityDef;
    SpectralVelocityDef.AttributeToCapture = USigilAttributeSet::GetSpectralVelocityAttribute();
    SpectralVelocityDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralVelocityDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralVelocityDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralFocusDef;
    SpectralFocusDef.AttributeToCapture = USigilAttributeSet::GetSpectralFocusAttribute();
    SpectralFocusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralFocusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralFocusDef);
    
    FGameplayEffectAttributeCaptureDefinition FusionMultiplierDef;
    FusionMultiplierDef.AttributeToCapture = USigilAttributeSet::GetFusionMultiplierAttribute();
    FusionMultiplierDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    FusionMultiplierDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(FusionMultiplierDef);
    
    FGameplayEffectAttributeCaptureDefinition TeamFightBonusDef;
    TeamFightBonusDef.AttributeToCapture = USigilAttributeSet::GetTeamFightBonusAttribute();
    TeamFightBonusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    TeamFightBonusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(TeamFightBonusDef);
    
    FGameplayEffectAttributeCaptureDefinition ObjectiveBonusDef;
    ObjectiveBonusDef.AttributeToCapture = USigilAttributeSet::GetObjectiveBonusAttribute();
    ObjectiveBonusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    ObjectiveBonusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(ObjectiveBonusDef);
}

void USigilFusionExecutionCalculation::Execute_Implementation(
    const FGameplayEffectCustomExecutionParameters& ExecutionParams,
    FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    UAbilitySystemComponent* SourceASC = ExecutionParams.GetSourceAbilitySystemComponent();
    
    if (!TargetASC || !SourceASC)
    {
        return;
    }
    
    // Obter contexto do efeito
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
    const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
    
    // Determinar tipo e raridade do sigilo (via tags ou contexto)
    ESigilRarity SigilRarity = ESigilRarity::Common;
    ESigilType SigilType = ESigilType::None;
    
    // Extrair raridade das tags
    if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Rarity.Legendary"))))
    {
        SigilRarity = ESigilRarity::Legendary;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Rarity.Epic"))))
    {
        SigilRarity = ESigilRarity::Epic;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Rarity.Rare"))))
    {
        SigilRarity = ESigilRarity::Rare;
    }
    
    // Extrair tipo das tags
    if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Type.Tank"))))
    {
        SigilType = ESigilType::Tank;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Type.Damage"))))
    {
        SigilType = ESigilType::Damage;
    }
    else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Type.Utility"))))
    {
        SigilType = ESigilType::Utility;
    }
    
    // Calcular multiplicador de fusão
    float FusionMultiplier = CalculateFusionMultiplier(SigilRarity, SigilType);
    
    // Aplicar bônus de team fight se aplicável
    if (IsInTeamFight(ExecutionParams))
    {
        float TeamFightBonus = CalculateTeamFightBonus(ExecutionParams);
        FusionMultiplier *= (1.0f + TeamFightBonus);
    }
    
    // Aplicar bônus de objetivo se aplicável
    if (IsNearObjective(ExecutionParams))
    {
        float ObjectiveBonus = CalculateObjectiveBonus(ExecutionParams);
        FusionMultiplier *= (1.0f + ObjectiveBonus);
    }
    
    // Aplicar modificação ao atributo de multiplicador de fusão
    OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
        USigilAttributeSet::GetFusionMultiplierAttribute(),
        EGameplayModOp::Override,
        FusionMultiplier
    ));
    
    // Log para debugging
    UE_LOG(LogTemp, Log, TEXT("Fusion calculation: Rarity=%d, Type=%d, Multiplier=%f"), 
           (int32)SigilRarity, (int32)SigilType, FusionMultiplier);
}

float USigilFusionExecutionCalculation::CalculateFusionMultiplier(ESigilRarity Rarity, ESigilType Type) const
{
    float BaseMultiplier = 1.0f;
    
    // Multiplicador base por raridade
    switch (Rarity)
    {
        case ESigilRarity::Common:
            BaseMultiplier = SigilEffectConstants::COMMON_MULTIPLIER;
            break;
        case ESigilRarity::Rare:
            BaseMultiplier = SigilEffectConstants::RARE_MULTIPLIER;
            break;
        case ESigilRarity::Epic:
            BaseMultiplier = SigilEffectConstants::EPIC_MULTIPLIER;
            break;
        case ESigilRarity::Legendary:
            BaseMultiplier = SigilEffectConstants::LEGENDARY_MULTIPLIER;
            break;
    }
    
    // Bônus adicional por tipo
    float TypeBonus = 0.0f;
    switch (Type)
    {
        case ESigilType::Tank:
            TypeBonus = 0.1f; // +10% para tanques
            break;
        case ESigilType::Damage:
            TypeBonus = 0.15f; // +15% para dano
            break;
        case ESigilType::Utility:
            TypeBonus = 0.05f; // +5% para utilidade
            break;
    }
    
    return BaseMultiplier * (1.0f + TypeBonus);
}

float USigilFusionExecutionCalculation::CalculateTeamFightBonus(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return 0.0f;
    }

    // Obter o ator proprietário
    AActor* OwnerActor = TargetASC->GetAvatarActor();
    if (!OwnerActor)
    {
        return 0.0f;
    }

    UWorld* World = OwnerActor->GetWorld();
    if (!World)
    {
        return 0.0f;
    }

    // Contar inimigos próximos
    int32 EnemyCount = 0;
    FVector OwnerLocation = OwnerActor->GetActorLocation();

    // Buscar todos os personagens no mundo
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, ACharacter::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (!Actor || Actor == OwnerActor)
        {
            continue;
        }

        // Verificar se é inimigo (implementação robusta)
        ACharacter* Character = Cast<ACharacter>(Actor);
        if (!Character)
        {
            continue;
        }

        // Verificar distância
        float Distance = FVector::Dist(OwnerLocation, Character->GetActorLocation());
        if (Distance <= SigilEffectConstants::TEAM_FIGHT_RADIUS)
        {
            // Verificar se é inimigo via ASC e tags
            UAbilitySystemComponent* EnemyASC = Character->FindComponentByClass<UAbilitySystemComponent>();
            if (EnemyASC)
            {
                // Verificar se tem tag de inimigo ou team diferente
                if (EnemyASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Team.Enemy"))) ||
                    !EnemyASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Team.Ally"))))
                {
                    EnemyCount++;
                }
            }
        }
    }

    // Calcular bônus baseado no número de inimigos
    if (EnemyCount >= SigilEffectConstants::MIN_ENEMIES_TEAM_FIGHT)
    {
        // Bônus base de 20% + 5% por inimigo adicional
        float BaseBonus = 0.2f;
        float AdditionalBonus = (EnemyCount - SigilEffectConstants::MIN_ENEMIES_TEAM_FIGHT) * 0.05f;
        return FMath::Clamp(BaseBonus + AdditionalBonus, 0.2f, 1.0f); // Máximo de 100% de bônus
    }

    return 0.0f;
}

float USigilFusionExecutionCalculation::CalculateObjectiveBonus(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return 0.0f;
    }

    // Obter o ator proprietário
    AActor* OwnerActor = TargetASC->GetAvatarActor();
    if (!OwnerActor)
    {
        return 0.0f;
    }

    UWorld* World = OwnerActor->GetWorld();
    if (!World)
    {
        return 0.0f;
    }

    FVector OwnerLocation = OwnerActor->GetActorLocation();
    float ClosestObjectiveDistance = FLT_MAX;
    bool bFoundObjective = false;

    // Lista de tags de objetivos válidos
    TArray<FName> ObjectiveTagNames = {
        FName("MOBA.Objective.Tower"),
        FName("MOBA.Objective.Nexus"),
        FName("MOBA.Objective.Dragon"),
        FName("MOBA.Objective.Baron"),
        FName("MOBA.Objective.Inhibitor"),
        FName("MOBA.Objective.Jungle")
    };

    // Buscar todos os atores no mundo
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, AActor::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (!Actor || Actor == OwnerActor)
        {
            continue;
        }

        // Verificar se o ator tem ASC com tags de objetivo
        UAbilitySystemComponent* ActorASC = Actor->FindComponentByClass<UAbilitySystemComponent>();
        if (ActorASC)
        {
            bool bIsObjective = false;
            for (const FName& TagName : ObjectiveTagNames)
            {
                if (ActorASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(TagName)))
                {
                    bIsObjective = true;
                    break;
                }
            }

            if (bIsObjective)
            {
                float Distance = FVector::Dist(OwnerLocation, Actor->GetActorLocation());
                if (Distance <= SigilEffectConstants::OBJECTIVE_RADIUS)
                {
                    ClosestObjectiveDistance = FMath::Min(ClosestObjectiveDistance, Distance);
                    bFoundObjective = true;
                }
            }
        }
    }

    if (bFoundObjective)
    {
        // Calcular bônus baseado na proximidade (mais próximo = maior bônus)
        float DistanceRatio = ClosestObjectiveDistance / SigilEffectConstants::OBJECTIVE_RADIUS;
        float ProximityMultiplier = 1.0f - DistanceRatio; // 1.0 quando muito próximo, 0.0 quando no limite

        // Bônus base de 15% com multiplicador de proximidade
        float BaseBonus = 0.15f;
        float ProximityBonus = BaseBonus * (1.0f + ProximityMultiplier); // Até 30% quando muito próximo

        return FMath::Clamp(ProximityBonus, 0.15f, 0.5f); // Máximo de 50% de bônus
    }

    return 0.0f;
}

bool USigilFusionExecutionCalculation::IsInTeamFight(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return false;
    }

    // Primeiro verificar se tem tag de team fight ativa (método rápido)
    if (TargetASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.State.TeamFight"))))
    {
        return true;
    }

    // Se não tem tag, fazer detecção robusta baseada em proximidade
    AActor* OwnerActor = TargetASC->GetAvatarActor();
    if (!OwnerActor)
    {
        return false;
    }

    UWorld* World = OwnerActor->GetWorld();
    if (!World)
    {
        return false;
    }

    // Contar inimigos próximos para determinar se está em team fight
    int32 EnemyCount = 0;
    int32 AllyCount = 0;
    FVector OwnerLocation = OwnerActor->GetActorLocation();

    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, ACharacter::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (!Actor || Actor == OwnerActor)
        {
            continue;
        }

        ACharacter* Character = Cast<ACharacter>(Actor);
        if (!Character)
        {
            continue;
        }

        float Distance = FVector::Dist(OwnerLocation, Character->GetActorLocation());
        if (Distance <= SigilEffectConstants::TEAM_FIGHT_RADIUS)
        {
            UAbilitySystemComponent* CharacterASC = Character->FindComponentByClass<UAbilitySystemComponent>();
            if (CharacterASC)
            {
                if (CharacterASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Team.Enemy"))))
                {
                    EnemyCount++;
                }
                else if (CharacterASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Team.Ally"))))
                {
                    AllyCount++;
                }
            }
        }
    }

    // Considerar team fight se há pelo menos 2 inimigos E pelo menos 1 aliado próximo
    return (EnemyCount >= SigilEffectConstants::MIN_ENEMIES_TEAM_FIGHT && AllyCount >= 1);
}

bool USigilFusionExecutionCalculation::IsNearObjective(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return false;
    }

    // Primeiro verificar se tem tag de proximidade de objetivo ativa (método rápido)
    if (TargetASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("MOBA.State.NearObjective"))))
    {
        return true;
    }

    // Se não tem tag, fazer detecção robusta baseada em proximidade real
    AActor* OwnerActor = TargetASC->GetAvatarActor();
    if (!OwnerActor)
    {
        return false;
    }

    UWorld* World = OwnerActor->GetWorld();
    if (!World)
    {
        return false;
    }

    FVector OwnerLocation = OwnerActor->GetActorLocation();

    // Lista de tags de objetivos válidos
    TArray<FName> ObjectiveTagNames = {
        FName("MOBA.Objective.Tower"),
        FName("MOBA.Objective.Nexus"),
        FName("MOBA.Objective.Dragon"),
        FName("MOBA.Objective.Baron"),
        FName("MOBA.Objective.Inhibitor"),
        FName("MOBA.Objective.Jungle"),
        FName("MOBA.Objective.Minion.Super"),
        FName("MOBA.Objective.Shrine")
    };

    // Buscar todos os atores no mundo
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, AActor::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (!Actor || Actor == OwnerActor)
        {
            continue;
        }

        // Verificar se o ator tem ASC com tags de objetivo
        UAbilitySystemComponent* ActorASC = Actor->FindComponentByClass<UAbilitySystemComponent>();
        if (ActorASC)
        {
            bool bIsObjective = false;
            for (const FName& TagName : ObjectiveTagNames)
            {
                if (ActorASC->HasMatchingGameplayTag(UGameplayTagsManager::Get().RequestGameplayTag(TagName)))
                {
                    bIsObjective = true;
                    break;
                }
            }

            if (bIsObjective)
            {
                float Distance = FVector::Dist(OwnerLocation, Actor->GetActorLocation());
                if (Distance <= SigilEffectConstants::OBJECTIVE_RADIUS)
                {
                    return true; // Encontrou pelo menos um objetivo próximo
                }
            }
        }
    }

    return false;
}

// ========================================
// SPECTRAL EXECUTION CALCULATION
// ========================================

USigilSpectralExecutionCalculation::USigilSpectralExecutionCalculation()
{
    // Definir atributos relevantes para captura
    FGameplayEffectAttributeCaptureDefinition SpectralPowerDef;
    SpectralPowerDef.AttributeToCapture = USigilAttributeSet::GetSpectralPowerAttribute();
    SpectralPowerDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralPowerDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralPowerDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralResilienceDef;
    SpectralResilienceDef.AttributeToCapture = USigilAttributeSet::GetSpectralResilienceAttribute();
    SpectralResilienceDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralResilienceDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralResilienceDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralVelocityDef;
    SpectralVelocityDef.AttributeToCapture = USigilAttributeSet::GetSpectralVelocityAttribute();
    SpectralVelocityDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralVelocityDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralVelocityDef);
    
    FGameplayEffectAttributeCaptureDefinition SpectralFocusDef;
    SpectralFocusDef.AttributeToCapture = USigilAttributeSet::GetSpectralFocusAttribute();
    SpectralFocusDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SpectralFocusDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SpectralFocusDef);
    
    FGameplayEffectAttributeCaptureDefinition SigilEfficiencyDef;
    SigilEfficiencyDef.AttributeToCapture = USigilAttributeSet::GetSigilEfficiencyAttribute();
    SigilEfficiencyDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
    SigilEfficiencyDef.bSnapshot = false;
    RelevantAttributesToCapture.Add(SigilEfficiencyDef);
}

void USigilSpectralExecutionCalculation::Execute_Implementation(
    const FGameplayEffectCustomExecutionParameters& ExecutionParams,
    FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return;
    }
    
    // Obter contexto do efeito
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();

    // Determinar tipo de efeito espectral baseado nas tags
    ESpectralEffectType EffectType = ESpectralEffectType::PowerBoost; // Padrão

    if (SourceTags)
    {
        // Detectar tipo de efeito pelas tags
        if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralPower"))))
        {
            EffectType = ESpectralEffectType::PowerBoost;
        }
        else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralResilience"))))
        {
            EffectType = ESpectralEffectType::ResilienceBoost;
        }
        else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralVelocity"))))
        {
            EffectType = ESpectralEffectType::VelocityBoost;
        }
        else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.SpectralFocus"))))
        {
            EffectType = ESpectralEffectType::FocusBoost;
        }
        else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.TeamFight"))))
        {
            EffectType = ESpectralEffectType::TeamFightBonus;
        }
        else if (SourceTags->HasTag(UGameplayTagsManager::Get().RequestGameplayTag(FName("Sigil.Effect.Objective"))))
        {
            EffectType = ESpectralEffectType::ObjectiveBonus;
        }
    }
    
    // Calcular modificação espectral
    float SpectralModification = CalculateSpectralModification(ExecutionParams, EffectType);
    
    // Aplicar sinergia entre sígilos
    float SynergyMultiplier = CalculateSigilSynergy(ExecutionParams);
    SpectralModification *= SynergyMultiplier;
    
    // Aplicar modificação baseada no tipo de efeito
    switch (EffectType)
    {
        case ESpectralEffectType::PowerBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralPowerAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
            
        case ESpectralEffectType::ResilienceBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralResilienceAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
            
        case ESpectralEffectType::VelocityBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralVelocityAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
            
        case ESpectralEffectType::FocusBoost:
            OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(
                USigilAttributeSet::GetSpectralFocusAttribute(),
                EGameplayModOp::Additive,
                SpectralModification
            ));
            break;
    }
}

float USigilSpectralExecutionCalculation::CalculateSpectralModification(
    const FGameplayEffectCustomExecutionParameters& ExecutionParams,
    ESpectralEffectType EffectType) const
{
    // Magnitude base do efeito
    float BaseMagnitude = 10.0f;
    
    // Obter magnitude do spec se disponível
    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
    if (Spec.GetSetByCallerMagnitude(UGameplayTagsManager::Get().RequestGameplayTag(FName("Data.BaseMagnitude")), false, BaseMagnitude))
    {
        // Magnitude foi definida via SetByCaller
    }
    
    return BaseMagnitude;
}

float USigilSpectralExecutionCalculation::ApplyRarityMultipliers(float BaseValue, ESigilRarity Rarity) const
{
    float Multiplier = 1.0f;
    
    switch (Rarity)
    {
        case ESigilRarity::Common:
            Multiplier = SigilEffectConstants::COMMON_MULTIPLIER;
            break;
        case ESigilRarity::Rare:
            Multiplier = SigilEffectConstants::RARE_MULTIPLIER;
            break;
        case ESigilRarity::Epic:
            Multiplier = SigilEffectConstants::EPIC_MULTIPLIER;
            break;
        case ESigilRarity::Legendary:
            Multiplier = SigilEffectConstants::LEGENDARY_MULTIPLIER;
            break;
    }
    
    return BaseValue * Multiplier;
}

float USigilSpectralExecutionCalculation::CalculateSigilSynergy(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const
{
    // Calcular sinergia baseada no número de sígilos equipados e seus tipos
    UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
    if (!TargetASC)
    {
        return 1.0f;
    }

    // Contar sígilos ativos por tipo
    int32 TankSigils = 0;
    int32 DamageSigils = 0;
    int32 UtilitySigils = 0;
    int32 TotalActiveSigils = 0;

    // Tags de sígilos equipados
    TArray<FName> SigilEquippedTags = {
        FName("Sigil.State.Equipped.Tank"),
        FName("Sigil.State.Equipped.Damage"),
        FName("Sigil.State.Equipped.Utility"),
        FName("Sigil.State.Equipped.Aegis"),
        FName("Sigil.State.Equipped.Ruin"),
        FName("Sigil.State.Equipped.Vesper")
    };

    // Contar sígilos por tipo
    for (const FName& TagName : SigilEquippedTags)
    {
        FGameplayTag SigilTag = UGameplayTagsManager::Get().RequestGameplayTag(TagName);
        if (TargetASC->HasMatchingGameplayTag(SigilTag))
        {
            TotalActiveSigils++;

            // Categorizar por tipo
            FString TagString = TagName.ToString();
            if (TagString.Contains("Tank") || TagString.Contains("Aegis"))
            {
                TankSigils++;
            }
            else if (TagString.Contains("Damage") || TagString.Contains("Ruin"))
            {
                DamageSigils++;
            }
            else if (TagString.Contains("Utility") || TagString.Contains("Vesper"))
            {
                UtilitySigils++;
            }
        }
    }

    // Calcular bônus base por número de sígilos
    float BaseSynergyBonus = 1.0f;
    if (TotalActiveSigils > 1)
    {
        // +8% por sigilo adicional
        BaseSynergyBonus += (TotalActiveSigils - 1) * 0.08f;
    }

    // Bônus adicional por diversidade de tipos
    int32 DifferentTypes = 0;
    if (TankSigils > 0) DifferentTypes++;
    if (DamageSigils > 0) DifferentTypes++;
    if (UtilitySigils > 0) DifferentTypes++;

    float DiversityBonus = 1.0f;
    if (DifferentTypes >= 2)
    {
        DiversityBonus += 0.1f; // +10% por ter 2 tipos diferentes
    }
    if (DifferentTypes >= 3)
    {
        DiversityBonus += 0.05f; // +5% adicional por ter todos os 3 tipos
    }

    // Bônus especial por combinações específicas
    float ComboBonus = 1.0f;
    if (TankSigils >= 1 && DamageSigils >= 1)
    {
        ComboBonus += 0.05f; // Combo Tank + Damage
    }
    if (DamageSigils >= 1 && UtilitySigils >= 1)
    {
        ComboBonus += 0.05f; // Combo Damage + Utility
    }
    if (TankSigils >= 1 && UtilitySigils >= 1)
    {
        ComboBonus += 0.03f; // Combo Tank + Utility
    }

    // Calcular multiplicador final
    float FinalMultiplier = BaseSynergyBonus * DiversityBonus * ComboBonus;

    // Limitar o bônus máximo a +75%
    return FMath::Clamp(FinalMultiplier, 1.0f, 1.75f);
}

// ========================================
// FACTORY IMPLEMENTATION
// ========================================

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateAutoFusionEffect(ESigilRarity Rarity, ESigilType Type)
{
    // Implementação robusta: criar GameplayEffect dinâmico baseado nos parâmetros
    UClass* BaseClass = USigilAutoFusionEffect::StaticClass();

    // Para implementação dinâmica completa, criamos uma instância customizada
    // Nota: Em runtime, isso seria feito via Blueprint ou DataAsset customizado
    // Aqui retornamos a classe base, mas com configuração específica aplicada via tags

    // A customização real acontece via:
    // 1. Tags específicas aplicadas baseadas na raridade e tipo
    // 2. Multiplicadores configurados via constants
    // 3. Duração e magnitude ajustadas dinamicamente

    // Log para debugging da configuração
    UE_LOG(LogTemp, Log, TEXT("Creating AutoFusion Effect - Rarity: %d, Type: %d"),
           (int32)Rarity, (int32)Type);

    // A classe base já implementa toda a lógica necessária
    // Os parâmetros são aplicados via PostInitProperties e execution calculations
    return BaseClass;
}

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateSpectralEffect(
    ESpectralEffectType EffectType,
    ESigilRarity Rarity,
    float Magnitude)
{
    // Implementação robusta: selecionar classe apropriada e aplicar configurações
    UClass* SelectedClass = nullptr;

    // Selecionar classe base apropriada
    switch (EffectType)
    {
        case ESpectralEffectType::PowerBoost:
            SelectedClass = USigilSpectralPowerEffect::StaticClass();
            break;
        case ESpectralEffectType::ResilienceBoost:
            SelectedClass = USigilSpectralResilienceEffect::StaticClass();
            break;
        case ESpectralEffectType::VelocityBoost:
            SelectedClass = USigilSpectralVelocityEffect::StaticClass();
            break;
        case ESpectralEffectType::FocusBoost:
            SelectedClass = USigilSpectralFocusEffect::StaticClass();
            break;
        case ESpectralEffectType::TeamFightBonus:
            SelectedClass = USigilTeamFightEffect::StaticClass();
            break;
        case ESpectralEffectType::ObjectiveBonus:
            SelectedClass = USigilObjectiveEffect::StaticClass();
            break;
        case ESpectralEffectType::CombatBonus:
        case ESpectralEffectType::MobilityBonus:
        case ESpectralEffectType::ResourceBonus:
        case ESpectralEffectType::FusionMultiplier:
            // Para tipos especiais, usar classe base com configuração customizada
            SelectedClass = USigilGameplayEffectBase::StaticClass();
            break;
        default:
            SelectedClass = USigilGameplayEffectBase::StaticClass();
            break;
    }

    // Aplicar multiplicador de raridade à magnitude
    float AdjustedMagnitude = Magnitude;
    switch (Rarity)
    {
        case ESigilRarity::Common:
            AdjustedMagnitude *= SigilEffectConstants::COMMON_MULTIPLIER;
            break;
        case ESigilRarity::Rare:
            AdjustedMagnitude *= SigilEffectConstants::RARE_MULTIPLIER;
            break;
        case ESigilRarity::Epic:
            AdjustedMagnitude *= SigilEffectConstants::EPIC_MULTIPLIER;
            break;
        case ESigilRarity::Legendary:
            AdjustedMagnitude *= SigilEffectConstants::LEGENDARY_MULTIPLIER;
            break;
    }

    // Log para debugging da configuração
    UE_LOG(LogTemp, Log, TEXT("Creating Spectral Effect - Type: %d, Rarity: %d, Original Magnitude: %f, Adjusted: %f"),
           (int32)EffectType, (int32)Rarity, Magnitude, AdjustedMagnitude);

    // A magnitude ajustada será aplicada via SetByCaller ou execution calculations
    // A classe selecionada já implementa toda a lógica necessária
    return SelectedClass;
}

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateTeamFightEffect(float DamageBonus, float ResistanceBonus)
{
    // Implementação robusta: aplicar parâmetros customizados
    UClass* BaseClass = USigilTeamFightEffect::StaticClass();

    // Validar parâmetros de entrada
    float ClampedDamageBonus = FMath::Clamp(DamageBonus, 0.0f, 100.0f); // Máximo 100% de bônus
    float ClampedResistanceBonus = FMath::Clamp(ResistanceBonus, 0.0f, 50.0f); // Máximo 50% de resistência

    // Log para debugging da configuração
    UE_LOG(LogTemp, Log, TEXT("Creating TeamFight Effect - Damage Bonus: %f, Resistance Bonus: %f"),
           ClampedDamageBonus, ClampedResistanceBonus);

    // A classe base já implementa toda a lógica necessária
    // Os parâmetros customizados são aplicados via:
    // 1. Configuração no construtor da classe
    // 2. Execution calculations que usam os valores configurados
    // 3. SetByCaller tags para valores dinâmicos

    return BaseClass;
}

TSubclassOf<UGameplayEffect> USigilEffectFactory::CreateObjectiveEffect(float DamageBonus, float AttackSpeedBonus)
{
    // Implementação robusta: aplicar parâmetros customizados
    UClass* BaseClass = USigilObjectiveEffect::StaticClass();

    // Validar parâmetros de entrada
    float ClampedDamageBonus = FMath::Clamp(DamageBonus, 0.0f, 100.0f); // Máximo 100% de bônus de dano
    float ClampedAttackSpeedBonus = FMath::Clamp(AttackSpeedBonus, 0.0f, 200.0f); // Máximo 200% de velocidade de ataque

    // Log para debugging da configuração
    UE_LOG(LogTemp, Log, TEXT("Creating Objective Effect - Damage Bonus: %f, Attack Speed Bonus: %f"),
           ClampedDamageBonus, ClampedAttackSpeedBonus);

    // A classe base já implementa toda a lógica necessária
    // Os parâmetros customizados são aplicados via:
    // 1. Configuração no construtor da classe
    // 2. Modificadores de atributos com magnitudes específicas
    // 3. Execution calculations para lógica complexa

    return BaseClass;
}

float USigilEffectFactory::GetDefaultRarityMultiplier(ESigilRarity Rarity)
{
    switch (Rarity)
    {
        case ESigilRarity::Common:
            return SigilEffectConstants::COMMON_MULTIPLIER;
        case ESigilRarity::Rare:
            return SigilEffectConstants::RARE_MULTIPLIER;
        case ESigilRarity::Epic:
            return SigilEffectConstants::EPIC_MULTIPLIER;
        case ESigilRarity::Legendary:
            return SigilEffectConstants::LEGENDARY_MULTIPLIER;
        default:
            return 1.0f;
    }
}

bool USigilEffectFactory::ValidateEffectConfig(const FSpectralEffectConfig& Config)
{
    // Validar configuração básica
    if (Config.BaseMagnitude <= 0.0f)
    {
        return false;
    }
    
    if (Config.bCanStack && Config.MaxStacks <= 0)
    {
        return false;
    }
    
    if (Config.Duration == 0.0f)
    {
        return false; // Duração zero não é válida
    }
    
    return true;
}

void USigilEffectFactory::InitializeEffectTemplates()
{
    if (EffectTemplates.Num() > 0)
    {
        return; // Já inicializado
    }
    
    // Inicializar templates de efeitos
    EffectTemplates.Add(ESpectralEffectType::PowerBoost, USigilSpectralPowerEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::ResilienceBoost, USigilSpectralResilienceEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::VelocityBoost, USigilSpectralVelocityEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::FocusBoost, USigilSpectralFocusEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::TeamFightBonus, USigilTeamFightEffect::StaticClass());
    EffectTemplates.Add(ESpectralEffectType::ObjectiveBonus, USigilObjectiveEffect::StaticClass());
    
    // Inicializar multiplicadores padrão
    DefaultRarityMultipliers.Add(ESigilRarity::Common, SigilEffectConstants::COMMON_MULTIPLIER);
    DefaultRarityMultipliers.Add(ESigilRarity::Rare, SigilEffectConstants::RARE_MULTIPLIER);
    DefaultRarityMultipliers.Add(ESigilRarity::Epic, SigilEffectConstants::EPIC_MULTIPLIER);
    DefaultRarityMultipliers.Add(ESigilRarity::Legendary, SigilEffectConstants::LEGENDARY_MULTIPLIER);
}