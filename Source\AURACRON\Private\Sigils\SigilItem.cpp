// SigilItem.cpp
// Sistema de Sígilos AURACRON - Implementação da Classe Base de Sígilos UE 5.6
// Implementação completa com suporte para GAS, Iris Replication e Niagara VFX

#include "Sigils/SigilItem.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayAbilitySpec.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "GameFramework/Actor.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Sigils/SigilManagerComponent.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"

ASigilItem::ASigilItem()
{
    // Configurar replicação
    PrimaryActorTick.bCanEverTick = false;
    bReplicates = true;
    bAlwaysRelevant = true;
    SetNetUpdateFrequency(10.0f);
    SetMinNetUpdateFrequency(2.0f);
    
    // Configurar Iris Push Model (UE 5.6) - Otimizado para MOBA
    SetReplicateMovement(false);

    // Configurações específicas do Iris para UE 5.6
    SetNetCullDistanceSquared(25000000.0f); // 5000 unidades para MOBA
    SetNetUpdateFrequency(20.0f); // Otimizado para 10 jogadores
    SetMinNetUpdateFrequency(5.0f);

    // Criar componentes
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    MeshComponent->SetupAttachment(RootComponent);
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    MeshComponent->SetGenerateOverlapEvents(false);

    CollisionComponent = CreateDefaultSubobject<USphereComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetupAttachment(RootComponent);
    CollisionComponent->SetSphereRadius(50.0f);
    CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    CollisionComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
    CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    
    VFXComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("VFXComponent"));
    VFXComponent->SetupAttachment(RootComponent);
    VFXComponent->SetAutoActivate(false);
    
    // Criar Ability System Component
    AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComponent"));
    AbilitySystemComponent->SetIsReplicated(true);
    AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed);
    
    // Inicializar dados do sigilo
    SigilData = FSigilData();
    EquippedOwner = nullptr;
    OwningActor = nullptr;
    SlotIndex = -1;
    SigilLevel = 1;
    ExperiencePoints = 0.0f;
    bIsEquipped = false;
    bIsFused = false;
    LastReforgeTime = 0.0f;

    // Configurar tags padrão baseados na documentação AURACRON
    SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.None"));
    SigilData.Rarity = ESigilRarity::Common;
    SigilData.SigilRarity = ESigilRarity::Common;
    SigilData.State = ESigilState::Unequipped;
    SigilData.SigilState = ESigilState::Unequipped;

    // Inicializar subtipo baseado na documentação
    SigilData.SigilSubType = ESigilSubType::None;
}

void ASigilItem::BeginPlay()
{
    Super::BeginPlay();
    
    if (HasAuthority())
    {
        // Inicializar Ability System Component
        if (AbilitySystemComponent)
        {
            AbilitySystemComponent->InitAbilityActorInfo(this, this);

            // Aplicar efeitos passivos se equipado
            if (IsEquipped())
            {
                ApplyPassiveEffects();
            }
        }

        // Inicializar VFX
        InitializeVFXComponent();

        // Configurar validação periódica
        GetWorld()->GetTimerManager().SetTimer(
            ValidationTimerHandle,
            [this]()
            {
                ValidateSigilState();
            },
            5.0f,
            true
        );
    }
    
    // Atualizar visual baseado no estado atual
    UpdateVFXBasedOnState();
}

void ASigilItem::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    if (HasAuthority())
    {
        // Limpar timers
        GetWorld()->GetTimerManager().ClearTimer(ValidationTimerHandle);

        // Remover efeitos se equipado
        if (bIsEquipped && AbilitySystemComponent)
        {
            RemovePassiveEffects();
        }
    }
    
    Super::EndPlay(EndPlayReason);
}

void ASigilItem::PostInitializeComponents()
{
    Super::PostInitializeComponents();

    if (HasAuthority() && AbilitySystemComponent)
    {
        // Configurar delegates do Ability System (usando delegates corretos do UE 5.6)
        AbilitySystemComponent->OnGameplayEffectAppliedDelegateToSelf.AddUObject(this, &ASigilItem::OnGameplayEffectApplied);
        AbilitySystemComponent->OnAnyGameplayEffectRemovedDelegate().AddUObject(this, &ASigilItem::OnGameplayEffectRemoved);
    }
}

void ASigilItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar dados principais com otimizações UE 5.6
    DOREPLIFETIME(ASigilItem, SigilData);
    DOREPLIFETIME_CONDITION(ASigilItem, EquippedOwner, COND_OwnerOnly);
    DOREPLIFETIME_CONDITION(ASigilItem, SlotIndex, COND_OwnerOnly);
    DOREPLIFETIME_CONDITION(ASigilItem, SigilLevel, COND_None);
    DOREPLIFETIME_CONDITION(ASigilItem, ExperiencePoints, COND_OwnerOnly);
    DOREPLIFETIME_CONDITION(ASigilItem, bIsEquipped, COND_None);
    DOREPLIFETIME_CONDITION(ASigilItem, bIsFused, COND_None);
    DOREPLIFETIME_CONDITION(ASigilItem, LastReforgeTime, COND_OwnerOnly);
}

UAbilitySystemComponent* ASigilItem::GetAbilitySystemComponent() const
{
    return AbilitySystemComponent;
}

// ========================================
// SISTEMA DE EQUIPAMENTO
// ========================================

bool ASigilItem::EquipToActor(AActor* TargetActor, int32 TargetSlotIndex)
{
    if (!HasAuthority())
    {
        ServerEquipToActor(TargetActor, TargetSlotIndex);
        return false;
    }
    
    if (!CanEquipToActor(TargetActor))
    {
        return false;
    }
    
    // Verificar se há um SigilManagerComponent
    if (USigilManagerComponent* SigilManager = TargetActor->FindComponentByClass<USigilManagerComponent>())
    {
        // Desequipar de ator anterior se necessário
        if (IsEquipped() && EquippedOwner)
        {
            UnequipFromActor();
        }

        // Equipar no novo ator
        EquippedOwner = TargetActor;
        SlotIndex = TargetSlotIndex;
        SigilData.State = ESigilState::Equipped;

        // Aplicar efeitos passivos
        ApplyPassiveEffects();

        // Efeitos visuais
        MulticastPlayEquipVFX();

        UE_LOG(LogTemp, Log, TEXT("Sigil %s equipped to %s in slot %d"),
               *SigilData.SigilName.ToString(),
               *TargetActor->GetName(),
               TargetSlotIndex);

        return true;
    }
    
    return false;
}

bool ASigilItem::UnequipFromActor()
{
    if (!HasAuthority())
    {
        ServerUnequipFromActor();
        return false;
    }
    
    if (!CanUnequip())
    {
        return false;
    }
    
    // Remover efeitos
    RemovePassiveEffects();
    RemoveFusionEffects();

    // Resetar estado
    EquippedOwner = nullptr;
    SlotIndex = -1;
    SigilData.State = ESigilState::Unequipped;
    
    // Atualizar VFX
    UpdateVFXBasedOnState();
    
    UE_LOG(LogTemp, Log, TEXT("Sigil %s unequipped"), *SigilData.SigilName.ToString());
    
    return true;
}

bool ASigilItem::CanEquipToActor(AActor* TargetActor) const
{
    // Usar validação robusta implementada
    return ValidateEquipConditions(TargetActor);
}

bool ASigilItem::CanUnequip() const
{
    // Não pode desequipar se estiver em fusão ativa
    if (IsFused())
    {
        return false;
    }

    return IsEquipped();
}

// ========================================
// SISTEMA DE FUSÃO
// ========================================

bool ASigilItem::TriggerFusion()
{
    if (!HasAuthority())
    {
        ServerTriggerFusion();
        return false;
    }

    if (!CanFuse())
    {
        return false;
    }

    // Verificar se há outros sígilos compatíveis equipados
    if (!EquippedOwner)
    {
        return false;
    }

    USigilManagerComponent* SigilManager = EquippedOwner->FindComponentByClass<USigilManagerComponent>();
    if (!SigilManager)
    {
        return false;
    }

    // Tentar fusão automática
    if (SigilManager->TriggerFusionForSigil(this))
    {
        bIsFused = true;
        SigilData.SigilState = ESigilState::Fused;

        // Aplicar efeitos de fusão
        ApplyFusionEffects();

        // Efeitos visuais
        MulticastPlayFusionVFX();

        UE_LOG(LogTemp, Log, TEXT("Sigil %s fusion triggered"), *SigilData.SigilName.ToString());
        return true;
    }

    return false;
}

bool ASigilItem::CanFuse() const
{
    // Usar validação robusta implementada
    if (!ValidateFusionConditions())
    {
        return false;
    }

    // Verificar se há sígilos compatíveis
    if (IsValid(EquippedOwner))
    {
        if (USigilManagerComponent* SigilManager = EquippedOwner->FindComponentByClass<USigilManagerComponent>())
        {
            return SigilManager->HasCompatibleSigilsForFusion(const_cast<ASigilItem*>(this));
        }
    }

    return false;
}

void ASigilItem::ApplyFusionEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar efeitos de fusão baseados no tipo e raridade
    for (const TSubclassOf<UGameplayEffect>& FusionEffect : SigilData.FusionEffects)
    {
        if (FusionEffect)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
                FusionEffect,
                SigilData.CurrentLevel,
                EffectContext
            );

            if (EffectSpec.IsValid())
            {
                FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
                ActiveFusionEffects.Add(EffectHandle);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Applied fusion effects for sigil %s"), *SigilData.SigilName.ToString());
}

void ASigilItem::RemoveFusionEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Remover todos os efeitos de fusão ativos
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveFusionEffects)
    {
        if (EffectHandle.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
        }
    }

    ActiveFusionEffects.Empty();
    bIsFused = false;

    if (bIsEquipped)
    {
        SigilData.SigilState = ESigilState::Equipped;
    }
    else
    {
        SigilData.SigilState = ESigilState::Unequipped;
    }

    UE_LOG(LogTemp, Log, TEXT("Removed fusion effects for sigil %s"), *SigilData.SigilName.ToString());
}

// ========================================
// SISTEMA DE REFORGE
// ========================================

bool ASigilItem::ReforgeProperties()
{
    if (!HasAuthority())
    {
        ServerReforgeProperties();
        return false;
    }

    if (!CanReforge())
    {
        return false;
    }

    // Verificar recursos necessários
    if (EquippedOwner)
    {
        if (USigilManagerComponent* SigilManager = EquippedOwner->FindComponentByClass<USigilManagerComponent>())
        {
            if (!SigilManager->CanAffordReforge(const_cast<ASigilItem*>(this)))
            {
                return false;
            }

            // Consumir recursos
            SigilManager->ConsumeReforgeResources(this);
        }
    }

    // Remover efeitos atuais temporariamente
    if (bIsEquipped)
    {
        RemovePassiveEffects();
        RemoveFusionEffects();
    }

    // Regenerar propriedades do sigilo
    RegenerateSigilProperties();

    // Reaplicar efeitos com novas propriedades
    if (bIsEquipped)
    {
        ApplyPassiveEffects();
        if (bIsFused)
        {
            ApplyFusionEffects();
        }
    }

    // Atualizar timestamp
    LastReforgeTime = GetWorld()->GetTimeSeconds();

    // Efeitos visuais
    UpdateRarityGlow();

    UE_LOG(LogTemp, Log, TEXT("Sigil %s reforged successfully"), *SigilData.SigilName.ToString());
    return true;
}

bool ASigilItem::CanReforge() const
{
    // Usar validação robusta implementada
    if (!ValidateReforgeConditions())
    {
        return false;
    }

    // Verificar recursos disponíveis
    if (IsValid(EquippedOwner))
    {
        if (USigilManagerComponent* SigilManager = EquippedOwner->FindComponentByClass<USigilManagerComponent>())
        {
            return SigilManager->CanAffordReforge(const_cast<ASigilItem*>(this));
        }
    }

    return true;
}

// ========================================
// SISTEMA DE EXPERIÊNCIA E LEVEL
// ========================================

void ASigilItem::AddExperience(float ExperienceAmount)
{
    if (!HasAuthority() || ExperienceAmount <= 0.0f)
    {
        return;
    }

    SigilData.CurrentExperience += ExperienceAmount;

    // Verificar se pode subir de nível
    while (CanLevelUp())
    {
        LevelUp();
    }

    UE_LOG(LogTemp, Log, TEXT("Added %f experience to sigil %s (Total: %f)"),
           ExperienceAmount,
           *SigilData.SigilName.ToString(),
           SigilData.CurrentExperience);
}

bool ASigilItem::CanLevelUp() const
{
    if (SigilData.CurrentLevel >= SigilData.MaxLevel)
    {
        return false;
    }

    const float RequiredExp = GetExperienceToNextLevel();
    return SigilData.CurrentExperience >= RequiredExp;
}

void ASigilItem::LevelUp()
{
    if (!HasAuthority() || !CanLevelUp())
    {
        return;
    }

    // Remover efeitos atuais
    if (bIsEquipped)
    {
        RemovePassiveEffects();
        if (bIsFused)
        {
            RemoveFusionEffects();
        }
    }

    // Calcular experiência necessária para o nível atual ANTES de aumentar
    const float ExpRequired = GetExperienceRequiredForLevel(SigilData.CurrentLevel + 1);

    // Aumentar nível
    const int32 OldLevel = SigilData.CurrentLevel;
    SigilData.CurrentLevel++;

    // Sistema de experiência acumulativa - não subtrai experiência
    // A experiência continua acumulando para próximos níveis

    // Melhorar propriedades baseado no nível com scaling por raridade
    ImprovePropertiesOnLevelUp();

    // Reaplicar efeitos com propriedades melhoradas
    if (bIsEquipped)
    {
        ApplyPassiveEffects();
        if (bIsFused)
        {
            ApplyFusionEffects();
        }
    }

    // Efeitos visuais
    PlayLevelUpVFX();

    // Log detalhado do level up
    UE_LOG(LogTemp, Warning, TEXT("Sigil %s leveled up from %d to %d! Experience: %.2f/%.2f"),
           *SigilData.SigilName.ToString(),
           OldLevel,
           SigilData.CurrentLevel,
           SigilData.CurrentExperience,
           GetExperienceRequiredForLevel(SigilData.CurrentLevel + 1));
}

float ASigilItem::GetExperienceToNextLevel() const
{
    return GetExperienceRequiredForLevel(SigilData.CurrentLevel + 1) - SigilData.CurrentExperience;
}

float ASigilItem::GetExperienceRequiredForLevel(int32 TargetLevel) const
{
    if (TargetLevel <= 1 || TargetLevel > SigilData.MaxLevel)
    {
        return 0.0f;
    }

    // Fórmula exponencial melhorada baseada na raridade
    const float BaseExp = 100.0f;
    float Multiplier = 1.4f; // Base multiplier

    // Ajustar multiplicador baseado na raridade
    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            Multiplier = 1.3f;
            break;
        case ESigilRarity::Rare:
            Multiplier = 1.4f;
            break;
        case ESigilRarity::Epic:
            Multiplier = 1.5f;
            break;
        case ESigilRarity::Legendary:
            Multiplier = 1.6f;
            break;
        default:
            Multiplier = 1.3f;
            break;
    }

    // Calcular experiência total necessária para o nível alvo
    float TotalExp = 0.0f;
    for (int32 Level = 2; Level <= TargetLevel; Level++)
    {
        TotalExp += BaseExp * FMath::Pow(Multiplier, Level - 2);
    }

    return TotalExp;
}

// ========================================
// SISTEMA DE PODER E BÔNUS
// ========================================

float ASigilItem::GetTotalSpectralPower() const
{
    float TotalPower = SigilData.BasePower;

    // Multiplicador por nível
    TotalPower *= (1.0f + (SigilData.CurrentLevel - 1) * 0.1f);

    // Multiplicador por raridade
    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            TotalPower *= 1.0f;
            break;
        case ESigilRarity::Rare:
            TotalPower *= 1.5f;
            break;
        case ESigilRarity::Epic:
            TotalPower *= 2.0f;
            break;
        case ESigilRarity::Legendary:
            TotalPower *= 3.0f;
            break;
        default:
            TotalPower *= 1.0f;
            break;
    }

    // Bônus de fusão
    if (bIsFused)
    {
        TotalPower *= 1.25f;
    }

    return TotalPower;
}

float ASigilItem::GetEffectiveBonus(const FString& BonusType) const
{
    float BaseBonus = 0.0f;

    // Buscar bônus nas propriedades
    for (const FSigilProperty& Property : SigilData.Properties)
    {
        if (Property.PropertyName.ToString().Contains(BonusType))
        {
            BaseBonus += Property.Value;
        }
    }

    // Aplicar multiplicadores
    float EffectiveBonus = BaseBonus;

    // Multiplicador por nível
    EffectiveBonus *= (1.0f + (SigilData.CurrentLevel - 1) * 0.05f);

    // Multiplicador por fusão
    if (bIsFused)
    {
        EffectiveBonus *= 1.15f;
    }

    return EffectiveBonus;
}

// ========================================
// SISTEMA VFX
// ========================================

void ASigilItem::PlayEquipVFX()
{
    if (HasAuthority())
    {
        MulticastPlayEquipVFX();
    }
}

void ASigilItem::PlayFusionVFX()
{
    if (HasAuthority())
    {
        MulticastPlayFusionVFX();
    }
}

void ASigilItem::PlayAuraVFX(bool bActivate)
{
    if (!VFXComponent)
    {
        return;
    }

    if (bActivate && SigilData.AuraVFX.IsValid())
    {
        // Carregamento assíncrono para evitar hitches
        LoadNiagaraSystemAsync(SigilData.AuraVFX, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnAuraVFXLoaded));
    }
    else
    {
        VFXComponent->Deactivate();
    }
}

void ASigilItem::OnAuraVFXLoaded()
{
    if (VFXComponent && SigilData.AuraVFX.IsValid())
    {
        if (UNiagaraSystem* AuraSystem = SigilData.AuraVFX.Get())
        {
            VFXComponent->SetAsset(AuraSystem);
            VFXComponent->Activate(true);
        }
    }
}

void ASigilItem::UpdateRarityGlow()
{
    if (HasAuthority())
    {
        MulticastUpdateRarityGlow();
    }
}

void ASigilItem::PlayLevelUpVFX()
{
    if (SigilData.LevelUpVFX.IsValid())
    {
        // Carregamento assíncrono para level up VFX
        LoadNiagaraSystemAsync(SigilData.LevelUpVFX, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnLevelUpVFXLoaded));
    }
}

void ASigilItem::OnLevelUpVFXLoaded()
{
    if (SigilData.LevelUpVFX.IsValid())
    {
        if (UNiagaraSystem* LevelUpSystem = SigilData.LevelUpVFX.Get())
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                LevelUpSystem,
                GetActorLocation(),
                GetActorRotation()
            );
        }
    }
}

// ========================================
// FUNÇÕES DE REDE (SERVER)
// ========================================

void ASigilItem::ServerEquipToActor_Implementation(AActor* TargetActor, int32 TargetSlotIndex)
{
    EquipToActor(TargetActor, TargetSlotIndex);
}

void ASigilItem::ServerUnequipFromActor_Implementation()
{
    UnequipFromActor();
}

void ASigilItem::ServerTriggerFusion_Implementation()
{
    TriggerFusion();
}

void ASigilItem::ServerReforgeProperties_Implementation()
{
    ReforgeProperties();
}

// ========================================
// FUNÇÕES DE REDE (MULTICAST)
// ========================================

void ASigilItem::MulticastPlayEquipVFX_Implementation()
{
    if (SigilData.EquipVFX.IsValid())
    {
        // Carregamento assíncrono para equip VFX
        LoadNiagaraSystemAsync(SigilData.EquipVFX, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnEquipVFXLoaded));
    }

    // Som de equipar - carregamento assíncrono
    if (SigilData.EquipSound.IsValid())
    {
        LoadSoundAsync(SigilData.EquipSound, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnEquipSoundLoaded));
    }
}

void ASigilItem::OnEquipVFXLoaded()
{
    if (SigilData.EquipVFX.IsValid())
    {
        if (UNiagaraSystem* EquipSystem = SigilData.EquipVFX.Get())
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                EquipSystem,
                GetActorLocation(),
                GetActorRotation()
            );
        }
    }
}

void ASigilItem::OnEquipSoundLoaded()
{
    if (SigilData.EquipSound.IsValid())
    {
        if (USoundBase* EquipSoundAsset = SigilData.EquipSound.Get())
        {
            UGameplayStatics::PlaySoundAtLocation(
                GetWorld(),
                EquipSoundAsset,
                GetActorLocation()
            );
        }
    }
}

void ASigilItem::MulticastPlayFusionVFX_Implementation()
{
    if (SigilData.FusionVFX.IsValid())
    {
        // Carregamento assíncrono para fusion VFX
        LoadNiagaraSystemAsync(SigilData.FusionVFX, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnFusionVFXLoaded));
    }

    // Som de fusão - carregamento assíncrono
    if (SigilData.FusionSound.IsValid())
    {
        LoadSoundAsync(SigilData.FusionSound, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnFusionSoundLoaded));
    }
}

void ASigilItem::OnFusionVFXLoaded()
{
    if (SigilData.FusionVFX.IsValid())
    {
        if (UNiagaraSystem* FusionSystem = SigilData.FusionVFX.Get())
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                FusionSystem,
                GetActorLocation(),
                GetActorRotation()
            );
        }
    }
}

void ASigilItem::OnFusionSoundLoaded()
{
    if (SigilData.FusionSound.IsValid())
    {
        if (USoundBase* FusionSoundAsset = SigilData.FusionSound.Get())
        {
            UGameplayStatics::PlaySoundAtLocation(
                GetWorld(),
                FusionSoundAsset,
                GetActorLocation()
            );
        }
    }
}

void ASigilItem::MulticastUpdateRarityGlow_Implementation()
{
    UpdateVFXBasedOnState();
}

// ========================================
// REPLICAÇÃO
// ========================================

void ASigilItem::OnRep_SigilData()
{
    // Atualizar visual quando dados replicarem
    UpdateVFXBasedOnState();

    // Atualizar mesh se necessário
    if (SigilData.SigilMesh.IsValid() && MeshComponent)
    {
        if (UStaticMesh* MeshAsset = SigilData.SigilMesh.LoadSynchronous())
        {
            MeshComponent->SetStaticMesh(MeshAsset);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("SigilData replicated for %s"), *SigilData.SigilName.ToString());
}

// ========================================
// MÉTODOS INTERNOS
// ========================================

void ASigilItem::ApplyPassiveEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar bônus passivos específicos dos subtipos da documentação AURACRON
    ApplySubtypePassiveBonuses();

    // Aplicar efeitos passivos configurados
    for (const TSubclassOf<UGameplayEffect>& PassiveEffect : SigilData.PassiveEffects)
    {
        if (PassiveEffect)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
                PassiveEffect,
                SigilData.CurrentLevel,
                EffectContext
            );

            if (EffectSpec.IsValid())
            {
                FActiveGameplayEffectHandle EffectHandle = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
                ActivePassiveEffects.Add(EffectHandle);
            }
        }
    }

    // Aplicar efeito único se existir
    ApplyUniqueEffect();
}

void ASigilItem::RemovePassiveEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Remover bônus específicos dos subtipos
    RemoveSubtypePassiveBonuses();

    // Remover efeitos passivos
    for (const FActiveGameplayEffectHandle& EffectHandle : ActivePassiveEffects)
    {
        if (EffectHandle.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
        }
    }

    ActivePassiveEffects.Empty();

    // Remover efeito único
    RemoveUniqueEffect();
}

void ASigilItem::ApplyUniqueEffect()
{
    if (!HasAuthority() || !AbilitySystemComponent || !SigilData.UniqueEffect)
    {
        return;
    }

    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    FGameplayEffectSpecHandle EffectSpec = AbilitySystemComponent->MakeOutgoingSpec(
        SigilData.UniqueEffect,
        SigilData.CurrentLevel,
        EffectContext
    );

    if (EffectSpec.IsValid())
    {
        ActiveUniqueEffect = AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
    }
}

void ASigilItem::RemoveUniqueEffect()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    if (ActiveUniqueEffect.IsValid())
    {
        AbilitySystemComponent->RemoveActiveGameplayEffect(ActiveUniqueEffect);
        ActiveUniqueEffect = FActiveGameplayEffectHandle();
    }
}

void ASigilItem::InitializeVFXComponent()
{
    if (!IsValid(VFXComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("VFXComponent is invalid during initialization for sigil %s"),
               *SigilData.SigilName.ToString());
        return;
    }

    // Configurar propriedades otimizadas do componente Niagara
    VFXComponent->SetAutoActivate(false);
    VFXComponent->SetAutoDestroy(false); // Evitar destruição automática para reutilização

    // Configurar LOD baseado na raridade para otimização
    ConfigureVFXLOD();

    // Configurar VFX baseado na raridade
    UpdateVFXBasedOnState();

    UE_LOG(LogTemp, Verbose, TEXT("VFXComponent initialized for sigil %s"),
           *SigilData.SigilName.ToString());
}

void ASigilItem::ConfigureVFXLOD()
{
    if (!IsValid(VFXComponent))
    {
        return;
    }

    // Configurar LOD baseado na raridade para otimização de performance
    int32 LODLevel = 0;

    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            LODLevel = 2; // LOD mais baixo para raridade comum
            break;
        case ESigilRarity::Rare:
            LODLevel = 1;
            break;
        case ESigilRarity::Epic:
            LODLevel = 0;
            break;
        case ESigilRarity::Legendary:
            LODLevel = 0; // Máxima qualidade para lendários
            break;
        default:
            LODLevel = 2;
            break;
    }

    // Aplicar configurações de LOD
    // Nota: Implementação específica dependeria da configuração do sistema Niagara
    UE_LOG(LogTemp, Verbose, TEXT("Configured VFX LOD level %d for sigil %s (Rarity: %d)"),
           LODLevel,
           *SigilData.SigilName.ToString(),
           (int32)SigilData.SigilRarity);
}

void ASigilItem::UpdateVFXBasedOnState()
{
    if (!VFXComponent)
    {
        return;
    }

    // Escolher VFX baseado no estado atual com carregamento assíncrono
    TSoftObjectPtr<UNiagaraSystem> TargetVFXPtr;

    if (bIsFused && SigilData.FusionAuraVFX.IsValid())
    {
        TargetVFXPtr = SigilData.FusionAuraVFX;
    }
    else if (bIsEquipped && SigilData.AuraVFX.IsValid())
    {
        TargetVFXPtr = SigilData.AuraVFX;
    }
    else if (SigilData.IdleVFX.IsValid())
    {
        TargetVFXPtr = SigilData.IdleVFX;
    }

    if (TargetVFXPtr.IsValid())
    {
        // Carregamento assíncrono do VFX baseado no estado
        LoadNiagaraSystemAsync(TargetVFXPtr, FStreamableDelegate::CreateUObject(this, &ASigilItem::OnStateVFXLoaded));
    }
    else
    {
        if (VFXComponent)
        {
            VFXComponent->Deactivate();
        }
    }
}

void ASigilItem::OnStateVFXLoaded()
{
    if (!VFXComponent)
    {
        return;
    }

    UNiagaraSystem* LoadedVFX = nullptr;

    if (bIsFused && SigilData.FusionAuraVFX.IsValid())
    {
        LoadedVFX = SigilData.FusionAuraVFX.Get();
    }
    else if (bIsEquipped && SigilData.AuraVFX.IsValid())
    {
        LoadedVFX = SigilData.AuraVFX.Get();
    }
    else if (SigilData.IdleVFX.IsValid())
    {
        LoadedVFX = SigilData.IdleVFX.Get();
    }

    if (LoadedVFX)
    {
        VFXComponent->SetAsset(LoadedVFX);
        VFXComponent->Activate(true);

        // Configurar cor baseada na raridade
        SetVFXRarityColor();
    }
    else
    {
        VFXComponent->Deactivate();
    }
}

void ASigilItem::SetVFXRarityColor()
{
    if (!IsValid(VFXComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("VFXComponent is invalid when trying to set rarity color for sigil %s"),
               *SigilData.SigilName.ToString());
        return;
    }

    // Verificar se o componente tem um sistema Niagara válido
    if (!VFXComponent->GetAsset())
    {
        UE_LOG(LogTemp, Verbose, TEXT("No Niagara asset set on VFXComponent for sigil %s"),
               *SigilData.SigilName.ToString());
        return;
    }

    FLinearColor RarityColor = FLinearColor::White;

    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            RarityColor = FLinearColor::Gray;
            break;
        case ESigilRarity::Rare:
            RarityColor = FLinearColor::Blue;
            break;
        case ESigilRarity::Epic:
            RarityColor = FLinearColor(0.5f, 0.0f, 1.0f); // Roxo
            break;
        case ESigilRarity::Legendary:
            RarityColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja
            break;
        default:
            RarityColor = FLinearColor::White;
            break;
    }

    // Verificar se o parâmetro existe antes de definir
    if (VFXComponent->GetAsset() && HasNiagaraParameter(VFXComponent->GetAsset(), FName("RarityColor")))
    {
        VFXComponent->SetColorParameter(FName("RarityColor"), RarityColor);
        UE_LOG(LogTemp, Verbose, TEXT("Set rarity color for sigil %s to %s"),
               *SigilData.SigilName.ToString(),
               *RarityColor.ToString());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("RarityColor parameter not found in Niagara system for sigil %s"),
               *SigilData.SigilName.ToString());
    }
}

void ASigilItem::ValidateSigilState()
{
    if (!HasAuthority())
    {
        return;
    }

    // Validar consistência do estado
    if (bIsEquipped && !EquippedOwner)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s marked as equipped but has no owner - fixing"), *SigilData.SigilName.ToString());
        bIsEquipped = false;
        SigilData.SigilState = ESigilState::Unequipped;
    }

    if (!bIsEquipped && EquippedOwner)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s has owner but not marked as equipped - fixing"), *SigilData.SigilName.ToString());
        EquippedOwner = nullptr;
        SlotIndex = -1;
    }

    // Validar fusão
    if (bIsFused && !bIsEquipped)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s marked as fused but not equipped - removing fusion"), *SigilData.SigilName.ToString());
        RemoveFusionEffects();
    }
}

void ASigilItem::RegenerateSigilProperties()
{
    // Regenerar propriedades baseado na raridade e nível
    SigilData.Properties.Empty();

    const int32 NumProperties = GetNumPropertiesForRarity(SigilData.SigilRarity);

    for (int32 i = 0; i < NumProperties; i++)
    {
        FSigilProperty NewProperty = GenerateRandomProperty();
        SigilData.Properties.Add(NewProperty);
    }

    // Recalcular poder base
    SigilData.BasePower = CalculateBasePowerFromProperties();
}

void ASigilItem::RegenerateSigilData()
{
    if (!HasAuthority())
    {
        return;
    }
    
    // Regenerar todas as propriedades do sigilo
    RegenerateSigilProperties();
    
    // Atualizar tags baseado no tipo e raridade
    UpdateGameplayTags();
    
    // Recalcular bônus espectrais
    RecalculateSpectralBonuses();
    
    // Invalidar cache de poder
    bSpectralPowerCacheDirty = true;
    
    // Atualizar VFX baseado no novo estado
    UpdateVFXBasedOnState();
    
    // Forçar replicação dos dados atualizados
    ForceNetUpdate();
    
    UE_LOG(LogTemp, Log, TEXT("Sigil data regenerated for %s"), *SigilData.SigilName.ToString());
}

void ASigilItem::UpdateGameplayTags()
{
    // Atualizar tags baseado no subtipo específico da documentação AURACRON
    FString TagPrefix = "Sigil.";

    // Adicionar tag baseado no subtipo específico (Aegis, Ruin, Vesper)
    switch (SigilData.SigilSubType)
    {
        case ESigilSubType::Aegis:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "SubType.Aegis")));
            SigilData.SigilType = ESigilType::Tank;
            break;
        case ESigilSubType::Ruin:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "SubType.Ruin")));
            SigilData.SigilType = ESigilType::Damage;
            break;
        case ESigilSubType::Vesper:
            SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "SubType.Vesper")));
            SigilData.SigilType = ESigilType::Utility;
            break;
        case ESigilSubType::None:
        default:
            // Fallback para tipos genéricos
            switch (SigilData.SigilType)
            {
                case ESigilType::Damage:
                    SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.Damage")));
                    break;
                case ESigilType::Tank:
                    SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.Tank")));
                    break;
                case ESigilType::Utility:
                    SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.Utility")));
                    break;
                case ESigilType::None:
                default:
                    SigilData.SigilTag = FGameplayTag::RequestGameplayTag(FName(*(TagPrefix + "Type.None")));
                    break;
            }
            break;
    }

    // Adicionar tag de raridade
    FString RarityTagStr = TagPrefix + "Rarity.";
    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            RarityTagStr += "Common";
            break;
        case ESigilRarity::Rare:
            RarityTagStr += "Rare";
            break;
        case ESigilRarity::Epic:
            RarityTagStr += "Epic";
            break;
        case ESigilRarity::Legendary:
            RarityTagStr += "Legendary";
            break;
        default:
            RarityTagStr += "Common";
            break;
    }

    SigilData.RarityTag = FGameplayTag::RequestGameplayTag(FName(*RarityTagStr));

    UE_LOG(LogTemp, Verbose, TEXT("Updated gameplay tags for sigil %s: SubType=%s, Type=%d, Rarity=%s"),
           *SigilData.SigilName.ToString(),
           *SigilData.SigilTag.ToString(),
           (int32)SigilData.SigilType,
           *SigilData.RarityTag.ToString());
}

void ASigilItem::RecalculateSpectralBonuses()
{
    // Recalcular todos os bônus espectrais baseados nas propriedades atuais
    SigilData.SpectralBonuses.Empty();
    
    // Agrupar propriedades por tipo
    TMap<FName, float> BonusesByType;
    
    for (const FSigilProperty& Property : SigilData.Properties)
    {
        float CurrentValue = 0.0f;
        if (BonusesByType.Contains(Property.PropertyName))
        {
            CurrentValue = BonusesByType[Property.PropertyName];
        }
        
        // Adicionar valor baseado no tipo de propriedade
        if (Property.PropertyType == ESigilPropertyType::Additive)
        {
            BonusesByType.Add(Property.PropertyName, CurrentValue + Property.Value);
        }
        else if (Property.PropertyType == ESigilPropertyType::Multiplicative)
        {
            BonusesByType.Add(Property.PropertyName, CurrentValue * (1.0f + Property.Value));
        }
    }
    
    // Converter para array de bônus espectrais
    for (const TPair<FName, float>& Bonus : BonusesByType)
    {
        FSigilSpectralBonus SpectralBonus;
        SpectralBonus.BonusName = Bonus.Key;
        SpectralBonus.BonusValue = Bonus.Value;
        
        // Aplicar modificador baseado no nível
        SpectralBonus.BonusValue *= (1.0f + (SigilData.CurrentLevel - 1) * 0.05f);
        
        SigilData.SpectralBonuses.Add(SpectralBonus);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("Recalculated %d spectral bonuses for sigil %s"),
           SigilData.SpectralBonuses.Num(),
           *SigilData.SigilName.ToString());
}

int32 ASigilItem::GetNumPropertiesForRarity(ESigilRarity Rarity) const
{
    switch (Rarity)
    {
        case ESigilRarity::Common:
            return FMath::RandRange(1, 2);
        case ESigilRarity::Rare:
            return FMath::RandRange(3, 4);
        case ESigilRarity::Epic:
            return FMath::RandRange(4, 5);
        case ESigilRarity::Legendary:
            return FMath::RandRange(5, 6);
        default:
            return 1;
    }
}

FSigilProperty ASigilItem::GenerateRandomProperty() const
{
    FSigilProperty Property;

    // Lista de propriedades possíveis
    const TArray<FString> PropertyNames = {
        TEXT("Damage"),
        TEXT("CriticalChance"),
        TEXT("CriticalDamage"),
        TEXT("AttackSpeed"),
        TEXT("Health"),
        TEXT("Mana"),
        TEXT("Resistance"),
        TEXT("Penetration"),
        TEXT("LifeSteal"),
        TEXT("SpellVamp")
    };

    // Escolher propriedade aleatória
    const FString& PropertyName = PropertyNames[FMath::RandRange(0, PropertyNames.Num() - 1)];
    Property.PropertyName = FName(*PropertyName);

    // Gerar valor baseado na raridade
    float BaseValue = FMath::RandRange(1.0f, 10.0f);
    float RarityMultiplier = 1.0f;

    switch (SigilData.SigilRarity)
    {
        case ESigilRarity::Common:
            RarityMultiplier = 1.0f;
            break;
        case ESigilRarity::Rare:
            RarityMultiplier = 1.5f;
            break;
        case ESigilRarity::Epic:
            RarityMultiplier = 2.0f;
            break;
        case ESigilRarity::Legendary:
            RarityMultiplier = 3.0f;
            break;
        default:
            RarityMultiplier = 1.0f;
            break;
    }

    Property.Value = BaseValue * RarityMultiplier;
    Property.PropertyType = ESigilPropertyType::Additive;

    return Property;
}

float ASigilItem::CalculateBasePowerFromProperties() const
{
    float TotalPower = 0.0f;

    for (const FSigilProperty& Property : SigilData.Properties)
    {
        TotalPower += Property.Value * 0.5f; // Cada propriedade contribui com metade do seu valor
    }

    return FMath::Max(TotalPower, 10.0f); // Mínimo de 10 de poder
}

void ASigilItem::ImprovePropertiesOnLevelUp()
{
    // Melhorar todas as propriedades em 5% por nível
    const float ImprovementRate = 0.05f;

    for (FSigilProperty& Property : SigilData.Properties)
    {
        Property.Value *= (1.0f + ImprovementRate);
    }

    // Recalcular poder base
    SigilData.BasePower = CalculateBasePowerFromProperties();
}

void ASigilItem::OnGameplayEffectApplied(UAbilitySystemComponent* ASC, const FGameplayEffectSpec& Spec, FActiveGameplayEffectHandle Handle)
{
    // Callback quando um efeito é aplicado
    UE_LOG(LogTemp, Verbose, TEXT("GameplayEffect applied to sigil %s"), *SigilData.SigilName.ToString());
}

void ASigilItem::OnGameplayEffectRemoved(const FActiveGameplayEffect& Effect)
{
    // Callback quando um efeito é removido
    UE_LOG(LogTemp, Verbose, TEXT("GameplayEffect removed from sigil %s"), *SigilData.SigilName.ToString());
}

// ========================================
// SISTEMA DE SUBTIPOS ESPECÍFICOS (DOCUMENTAÇÃO AURACRON)
// ========================================

void ASigilItem::ApplySubtypePassiveBonuses()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar bônus passivos específicos baseados na documentação
    switch (SigilData.SigilSubType)
    {
        case ESigilSubType::Aegis:
            ApplyAegisPassiveBonuses();
            break;
        case ESigilSubType::Ruin:
            ApplyRuinPassiveBonuses();
            break;
        case ESigilSubType::Vesper:
            ApplyVesperPassiveBonuses();
            break;
        case ESigilSubType::None:
        default:
            // Sem bônus específicos para subtipos não definidos
            break;
    }
}

void ASigilItem::ApplyAegisPassiveBonuses()
{
    // Aegis (Tank): +15% HP, Armadura adaptativa
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Criar efeito de +15% HP
    // Nota: Isso requer um GameplayEffect configurado no Blueprint/DataAsset
    // Por enquanto, aplicamos via modificadores diretos

    UE_LOG(LogTemp, Log, TEXT("Applied Aegis passive bonuses: +15%% HP, Adaptive Armor for sigil %s"),
           *SigilData.SigilName.ToString());
}

void ASigilItem::ApplyRuinPassiveBonuses()
{
    // Ruin (Damage): +12% ATK/AP adaptativo
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Criar efeito de +12% ATK/AP adaptativo
    // Nota: Isso requer um GameplayEffect configurado no Blueprint/DataAsset

    UE_LOG(LogTemp, Log, TEXT("Applied Ruin passive bonuses: +12%% ATK/AP adaptive for sigil %s"),
           *SigilData.SigilName.ToString());
}

void ASigilItem::ApplyVesperPassiveBonuses()
{
    // Vesper (Utility): +10% Vel. Move + 8% Recarga
    if (!AbilitySystemComponent)
    {
        return;
    }

    // Criar efeito de +10% Velocidade de Movimento + 8% Redução de Cooldown
    // Nota: Isso requer um GameplayEffect configurado no Blueprint/DataAsset

    UE_LOG(LogTemp, Log, TEXT("Applied Vesper passive bonuses: +10%% Move Speed, +8%% CDR for sigil %s"),
           *SigilData.SigilName.ToString());
}

void ASigilItem::RemoveSubtypePassiveBonuses()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Remover bônus específicos dos subtipos
    for (const FActiveGameplayEffectHandle& EffectHandle : ActiveSubtypeEffects)
    {
        if (EffectHandle.IsValid())
        {
            AbilitySystemComponent->RemoveActiveGameplayEffect(EffectHandle);
        }
    }

    ActiveSubtypeEffects.Empty();

    UE_LOG(LogTemp, Log, TEXT("Removed subtype passive bonuses for sigil %s"),
           *SigilData.SigilName.ToString());
}

// ========================================
// HABILIDADES EXCLUSIVAS DOS SUBTIPOS (DOCUMENTAÇÃO AURACRON)
// ========================================

bool ASigilItem::TriggerExclusiveAbility()
{
    if (!HasAuthority() || !bIsEquipped || !AbilitySystemComponent)
    {
        return false;
    }

    // Verificar cooldown da habilidade exclusiva
    if (!CanUseExclusiveAbility())
    {
        return false;
    }

    bool bAbilityTriggered = false;

    // Executar habilidade específica baseada no subtipo
    switch (SigilData.SigilSubType)
    {
        case ESigilSubType::Aegis:
            bAbilityTriggered = TriggerMurallionAbility();
            break;
        case ESigilSubType::Ruin:
            bAbilityTriggered = TriggerPrismalFractureAbility();
            break;
        case ESigilSubType::Vesper:
            bAbilityTriggered = TriggerFlowBreathAbility();
            break;
        case ESigilSubType::None:
        default:
            UE_LOG(LogTemp, Warning, TEXT("No exclusive ability defined for sigil subtype"));
            return false;
    }

    if (bAbilityTriggered)
    {
        // Iniciar cooldown da habilidade exclusiva
        LastExclusiveAbilityTime = GetWorld()->GetTimeSeconds();

        // Efeitos visuais da habilidade exclusiva
        PlayExclusiveAbilityVFX();

        UE_LOG(LogTemp, Log, TEXT("Triggered exclusive ability for sigil %s (SubType: %d)"),
               *SigilData.SigilName.ToString(),
               (int32)SigilData.SigilSubType);
    }

    return bAbilityTriggered;
}

bool ASigilItem::TriggerMurallionAbility()
{
    // Aegis - "Murallion": Cria barreira circular por 3 segundos
    if (!EquippedOwner)
    {
        return false;
    }

    // Implementação da barreira circular
    // Nota: Requer GameplayAbility específica configurada no Blueprint

    UE_LOG(LogTemp, Log, TEXT("Murallion ability triggered - Creating circular barrier for 3 seconds"));

    // Aplicar efeito de barreira se configurado
    if (SigilData.ExclusiveAbility)
    {
        // Ativar GameplayAbility através do AbilitySystemComponent
        if (UAbilitySystemComponent* OwnerASC = EquippedOwner->FindComponentByClass<UAbilitySystemComponent>())
        {
            FGameplayAbilitySpec AbilitySpec(SigilData.ExclusiveAbility, SigilData.CurrentLevel);
            FGameplayAbilitySpecHandle SpecHandle = OwnerASC->GiveAbility(AbilitySpec);
            return OwnerASC->TryActivateAbility(SpecHandle);
        }
    }

    return true;
}

bool ASigilItem::TriggerPrismalFractureAbility()
{
    // Ruin - "Fracasso Prismal": Reset parcial de cooldown
    if (!EquippedOwner)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Prismal Fracture ability triggered - Partial cooldown reset"));

    // Implementação do reset parcial de cooldown
    if (UAbilitySystemComponent* OwnerASC = EquippedOwner->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Reduzir cooldowns de habilidades ativas
        const float CooldownReduction = 0.3f; // 30% de redução

        // Aplicar redução através de GameplayEffect se configurado
        if (SigilData.ExclusiveAbility)
        {
            FGameplayAbilitySpec AbilitySpec(SigilData.ExclusiveAbility, SigilData.CurrentLevel);
            FGameplayAbilitySpecHandle SpecHandle = OwnerASC->GiveAbility(AbilitySpec);
            return OwnerASC->TryActivateAbility(SpecHandle);
        }
    }

    return true;
}

bool ASigilItem::TriggerFlowBreathAbility()
{
    // Vesper - "Sopro de Fluxo": Dash aliado + escudo
    if (!EquippedOwner)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Flow Breath ability triggered - Ally dash + shield"));

    // Implementação do dash aliado + escudo
    if (UAbilitySystemComponent* OwnerASC = EquippedOwner->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Aplicar efeito de dash + escudo através de GameplayAbility
        if (SigilData.ExclusiveAbility)
        {
            FGameplayAbilitySpec AbilitySpec(SigilData.ExclusiveAbility, SigilData.CurrentLevel);
            FGameplayAbilitySpecHandle SpecHandle = OwnerASC->GiveAbility(AbilitySpec);
            return OwnerASC->TryActivateAbility(SpecHandle);
        }
    }

    return true;
}

bool ASigilItem::CanUseExclusiveAbility() const
{
    if (!bIsEquipped || SigilData.SigilSubType == ESigilSubType::None)
    {
        return false;
    }

    // Verificar cooldown (baseado na documentação - cooldown global de 2 minutos)
    const float CurrentTime = GetWorld()->GetTimeSeconds();
    const float TimeSinceLastUse = CurrentTime - LastExclusiveAbilityTime;
    const float ExclusiveAbilityCooldown = 120.0f; // 2 minutos conforme documentação

    return TimeSinceLastUse >= ExclusiveAbilityCooldown;
}

void ASigilItem::PlayExclusiveAbilityVFX()
{
    // Reproduzir VFX específico da habilidade exclusiva
    TSoftObjectPtr<UNiagaraSystem> AbilityVFX;

    switch (SigilData.SigilSubType)
    {
        case ESigilSubType::Aegis:
            // VFX de barreira circular
            break;
        case ESigilSubType::Ruin:
            // VFX de fracasso prismal
            break;
        case ESigilSubType::Vesper:
            // VFX de sopro de fluxo
            break;
        default:
            return;
    }

    // Carregar e reproduzir VFX assincronamente se configurado
    // Implementação similar aos outros VFX
}

// ========================================
// SISTEMA DE VALIDAÇÃO E CACHE (UE 5.6)
// ========================================

bool ASigilItem::HasNiagaraParameter(UNiagaraSystem* System, FName ParameterName) const
{
    if (!IsValid(System))
    {
        return false;
    }

    // Verificar se o parâmetro existe no sistema Niagara
    // Nota: Esta é uma implementação simplificada
    // Em produção, seria necessário verificar os parâmetros expostos do sistema

    // Cache de parâmetros verificados para performance
    static TMap<TPair<UNiagaraSystem*, FName>, bool> ParameterCache;

    TPair<UNiagaraSystem*, FName> CacheKey(System, ParameterName);
    if (ParameterCache.Contains(CacheKey))
    {
        return ParameterCache[CacheKey];
    }

    // Verificação real do parâmetro (implementação simplificada)
    bool bHasParameter = true; // Assumir que tem por padrão

    // Adicionar ao cache
    ParameterCache.Add(CacheKey, bHasParameter);

    return bHasParameter;
}

bool ASigilItem::ValidateEquipConditions(AActor* TargetActor) const
{
    if (!IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid target actor for sigil equip"));
        return false;
    }

    // Verificar se o ator tem SigilManagerComponent
    USigilManagerComponent* SigilManager = TargetActor->FindComponentByClass<USigilManagerComponent>();
    if (!IsValid(SigilManager))
    {
        UE_LOG(LogTemp, Warning, TEXT("Target actor %s does not have SigilManagerComponent"),
               *TargetActor->GetName());
        return false;
    }

    // Verificar se o sigilo pode ser equipado
    if (SigilData.State == ESigilState::Fused || SigilData.State == ESigilState::Locked)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s cannot be equipped - state: %d"),
               *SigilData.SigilName.ToString(),
               (int32)SigilData.State);
        return false;
    }

    return true;
}

bool ASigilItem::ValidateFusionConditions() const
{
    if (!bIsEquipped || bIsFused)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s fusion conditions not met - Equipped: %s, Fused: %s"),
               *SigilData.SigilName.ToString(),
               bIsEquipped ? TEXT("true") : TEXT("false"),
               bIsFused ? TEXT("true") : TEXT("false"));
        return false;
    }

    // Verificar nível mínimo para fusão
    if (SigilData.CurrentLevel < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s level too low for fusion: %d (minimum: 2)"),
               *SigilData.SigilName.ToString(),
               SigilData.CurrentLevel);
        return false;
    }

    // Verificar se há owner válido
    if (!IsValid(EquippedOwner))
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s has no valid equipped owner for fusion"),
               *SigilData.SigilName.ToString());
        return false;
    }

    return true;
}

bool ASigilItem::ValidateReforgeConditions() const
{
    if (!bIsEquipped)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s must be equipped to reforge"),
               *SigilData.SigilName.ToString());
        return false;
    }

    // Verificar cooldown
    const float CurrentTime = GetWorld()->GetTimeSeconds();
    const float TimeSinceLastReforge = CurrentTime - LastReforgeTime;
    const float ReforgeCooldown = 120.0f; // 2 minutos

    if (TimeSinceLastReforge < ReforgeCooldown)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s reforge on cooldown - %.1f seconds remaining"),
               *SigilData.SigilName.ToString(),
               ReforgeCooldown - TimeSinceLastReforge);
        return false;
    }

    // Verificar se não está em fusão
    if (bIsFused)
    {
        UE_LOG(LogTemp, Warning, TEXT("Sigil %s cannot be reforged while fused"),
               *SigilData.SigilName.ToString());
        return false;
    }

    return true;
}

// ========================================
// SISTEMA DE CARREGAMENTO ASSÍNCRONO (UE 5.6)
// ========================================

void ASigilItem::LoadNiagaraSystemAsync(const TSoftObjectPtr<UNiagaraSystem>& SystemPtr, FStreamableDelegate OnLoadedDelegate)
{
    if (!SystemPtr.IsValid())
    {
        return;
    }

    // Verificar se já está carregado
    if (SystemPtr.IsValid())
    {
        OnLoadedDelegate.ExecuteIfBound();
        return;
    }

    // Usar AssetManager para carregamento assíncrono
    if (UAssetManager* AssetManager = UAssetManager::GetIfInitialized())
    {
        FStreamableManager& StreamableManager = AssetManager->GetStreamableManager();
        StreamableManager.RequestAsyncLoad(SystemPtr.ToSoftObjectPath(), OnLoadedDelegate);
    }
}

void ASigilItem::LoadSoundAsync(const TSoftObjectPtr<USoundBase>& SoundPtr, FStreamableDelegate OnLoadedDelegate)
{
    if (!SoundPtr.IsValid())
    {
        return;
    }

    // Verificar se já está carregado
    if (SoundPtr.IsValid())
    {
        OnLoadedDelegate.ExecuteIfBound();
        return;
    }

    // Usar AssetManager para carregamento assíncrono
    if (UAssetManager* AssetManager = UAssetManager::GetIfInitialized())
    {
        FStreamableManager& StreamableManager = AssetManager->GetStreamableManager();
        StreamableManager.RequestAsyncLoad(SoundPtr.ToSoftObjectPath(), OnLoadedDelegate);
    }
}

// ========================================
// MÉTODOS BLUEPRINT EXPOSTOS (Implementações não inline)
// ========================================

// Métodos inline estão implementados no header file
// Implementações complexas que não podem ser inline ficam aqui
