// AURACRONCharacter.h
// Sistema de Sígilos AURACRON - Classe Base do Personagem UE 5.6
// Implementação robusta com GAS, Sígilos e sistemas modernos

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "GameplayTagContainer.h"
#include "Data/AURACRONEnums.h"
#include "Data/AURACRONStructs.h"
#include "InputActionValue.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;
class UInputAction;
class UInputMappingContext;
class UNiagaraSystem;
class UNiagaraComponent;
class UMaterialInterface;

// Includes para classes específicas
#include "GAS/AURACRONAttributeSet.h"
#include "Components/AURACRONSigilComponent.h"
#include "Components/AURACRONMovementComponent.h"
#include "AURACRONCharacter.generated.h"

/**
 * Classe base para todos os personagens do AURACRON
 * Implementa sistema completo de Sígilos, GAS e mecânicas específicas
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API AAURACRONCharacter : public ACharacter, public IAbilitySystemInterface
{
    GENERATED_BODY()

public:
    AAURACRONCharacter(const FObjectInitializer& ObjectInitializer);

    // IAbilitySystemInterface
    virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;

    // APawn interface
    virtual void PossessedBy(AController* NewController) override;
    virtual void OnRep_PlayerState() override;

    // ACharacter interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;
    virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;

    // Network Replication
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    /** Inicializa o sistema de habilidades */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Abilities")
    virtual void InitializeAbilitySystem();

    /** Concede habilidades padrão ao personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Abilities")
    virtual void GiveDefaultAbilities();

    /** Aplica efeitos de gameplay padrão */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Abilities")
    virtual void ApplyDefaultEffects();

    // Sistema de Sígilos
    /** Equipa um Sígilo específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sigilos", Server, Reliable)
    void EquipSigil(EAURACRONSigilType SigilType);

    /** Remove um Sígilo equipado */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sigilos", Server, Reliable)
    void UnequipSigil(EAURACRONSigilType SigilType);

    /** Verifica se um Sígilo está equipado */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sigilos")
    bool IsSigilEquipped(EAURACRONSigilType SigilType) const;

    /** Obtém todos os Sígilos equipados */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sigilos")
    TArray<EAURACRONSigilType> GetEquippedSigils() const;

    // Sistema de Atributos
    /** Obtém vida atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetHealth() const;

    /** Obtém vida máxima */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetMaxHealth() const;

    /** Obtém mana atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetMana() const;

    /** Obtém mana máxima */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetMaxMana() const;

    /** Obtém dano de ataque */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetAttackDamage() const;

    /** Obtém poder de habilidade */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Attributes")
    float GetAbilityPower() const;

    // Sistema de Equipe
    /** Define a equipe do personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Team", Server, Reliable)
    void SetTeamID(int32 NewTeamID);

    /** Obtém a equipe do personagem */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Team")
    int32 GetTeamID() const;

    /** Verifica se é aliado de outro personagem */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Team")
    bool IsAlly(const AAURACRONCharacter* OtherCharacter) const;

    // Eventos Blueprint
    /** Evento chamado quando um Sígilo é equipado */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnSigilEquipped(EAURACRONSigilType SigilType);

    /** Evento chamado quando um Sígilo é removido */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnSigilUnequipped(EAURACRONSigilType SigilType);

    /** Evento chamado quando a vida muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnHealthChanged(float NewHealth, float MaxHealth);

    /** Evento chamado quando a mana muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnManaChanged(float NewMana, float MaxMana);

    // Efeitos Temporais
    /** Aplica um efeito temporal ao personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Temporal")
    void ApplyTemporalEffect(EAURACRONTemporalEffectType EffectType, float Duration);

    // Enhanced Input System
    /** Função de movimento */
    void Move(const FInputActionValue& Value);

    /** Função de câmera */
    void Look(const FInputActionValue& Value);

    /** Ativar habilidade específica do Sígilo Aegis */
    void ActivateSigilAbility1();

    /** Ativar habilidade específica do Sígilo Ruin */
    void ActivateSigilAbility2();

    /** Ativar habilidade específica do Sígilo Vesper */
    void ActivateSigilAbility3();

    /** Ativar fusão de sígilos */
    void TriggerSigilFusion();

    /** Ativar rewind temporal */
    void TriggerTemporalRewind();

    // Eventos Blueprint Adicionais
    /** Evento chamado quando o time muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnTeamChanged(int32 NewTeamID);

    /** Evento chamado quando sígilos equipados mudam */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnEquippedSigilsChanged(const TArray<EAURACRONSigilType>& NewSigils);

    /** Evento chamado quando fusão automática ocorre */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnAutomaticSigilFusion();

    /** Evento chamado quando re-forjamento fica disponível */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnReforgeAvailable();

    /** Evento chamado quando UI de sígilos precisa ser atualizada */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Events")
    void OnSigilUIUpdateRequired();

protected:
    // Componentes do Sistema de Habilidades
    /** Componente do sistema de habilidades */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** Set de atributos do personagem */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TObjectPtr<UAURACRONAttributeSet> AttributeSet;

    /** Componente de Sígilos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Sigilos")
    TObjectPtr<UAURACRONSigilComponent> SigilComponent;

    /** Componente de movimento customizado */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Movement")
    TObjectPtr<UAURACRONMovementComponent> AURACRONMovementComponent;

    // Configurações de Habilidades
    /** Habilidades padrão concedidas ao personagem */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TArray<TSubclassOf<UGameplayAbility>> DefaultAbilities;

    /** Efeitos padrão aplicados ao personagem */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Abilities")
    TArray<TSubclassOf<UGameplayEffect>> DefaultEffects;

    /** Tags de gameplay iniciais */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Abilities")
    FGameplayTagContainer InitialGameplayTags;

    // Enhanced Input System
    /** Contexto de mapeamento de input padrão */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputMappingContext> DefaultMappingContext;

    /** Ação de movimento */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> MoveAction;

    /** Ação de câmera */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> LookAction;

    /** Ação de habilidade de sígilo 1 */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> SigilAbility1Action;

    /** Ação de habilidade de sígilo 2 */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> SigilAbility2Action;

    /** Ação de habilidade de sígilo 3 */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> SigilAbility3Action;

    /** Ação de fusão de sígilos */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> SigilFusionAction;

    /** Ação de rewind temporal */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Input")
    TObjectPtr<UInputAction> TemporalRewindAction;

    // VFX Systems
    /** VFX para efeito temporal de rewind */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Temporal")
    TObjectPtr<UNiagaraSystem> TemporalRewindVFX;

    /** VFX para efeito temporal de slow */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Temporal")
    TObjectPtr<UNiagaraSystem> TemporalSlowVFX;

    /** VFX para efeito temporal de accelerate */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Temporal")
    TObjectPtr<UNiagaraSystem> TemporalAccelerateVFX;

    /** VFX para efeito temporal de freeze */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Temporal")
    TObjectPtr<UNiagaraSystem> TemporalFreezeVFX;

    /** VFX para efeito temporal de loop */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Temporal")
    TObjectPtr<UNiagaraSystem> TemporalLoopVFX;

    // VFX das habilidades específicas são gerenciados pelas classes USigilAbility_*
    // Murallion, Fracasso Prismal e Sopro de Fluxo têm seus próprios sistemas VFX

    /** VFX para fusão de sígilos */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Sigils")
    TObjectPtr<UNiagaraSystem> SigilFusionVFX;

    /** VFX para fusão automática */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Sigils")
    TObjectPtr<UNiagaraSystem> AutomaticFusionVFX;

    /** VFX para equipar sígilo Aegis */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Sigils")
    TObjectPtr<UNiagaraSystem> AegisEquipVFX;

    /** VFX para equipar sígilo Ruin */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Sigils")
    TObjectPtr<UNiagaraSystem> RuinEquipVFX;

    /** VFX para equipar sígilo Vesper */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|VFX|Sigils")
    TObjectPtr<UNiagaraSystem> VesperEquipVFX;

    // GameplayEffects para sistema temporal
    /** Efeito para restaurar estado temporal */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Effects")
    TSubclassOf<UGameplayEffect> TemporalRestoreEffect;

    /** Efeito para aplicar cooldown */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Effects")
    TSubclassOf<UGameplayEffect> CooldownEffect;

    // Materiais de equipe
    /** Materiais por equipe */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Team")
    TMap<int32, TObjectPtr<UMaterialInterface>> TeamMaterials;

    // Propriedades de Equipe
    /** ID da equipe (0 = Equipe A, 1 = Equipe B) */
    UPROPERTY(ReplicatedUsing = OnRep_TeamID, BlueprintReadOnly, Category = "AURACRON|Team")
    int32 TeamID;

    /** Sígilos atualmente equipados */
    UPROPERTY(ReplicatedUsing = OnRep_EquippedSigils, BlueprintReadOnly, Category = "AURACRON|Sigilos")
    TArray<EAURACRONSigilType> EquippedSigils;

    // Funções de Replicação
    UFUNCTION()
    void OnRep_TeamID();

    UFUNCTION()
    void OnRep_EquippedSigils();

    // Callbacks de Atributos
    virtual void OnHealthAttributeChanged(const struct FOnAttributeChangeData& Data);
    virtual void OnManaAttributeChanged(const struct FOnAttributeChangeData& Data);

    // Funções auxiliares
    /** Configura Enhanced Input System */
    void SetupEnhancedInputSystem();

    /** Inicializa sistema temporal */
    void InitializeTemporalSystem();

    /** Limpa sistema temporal */
    void CleanupTemporalSystem();

    /** Inicializa sistema de fusão de sígilos */
    void InitializeSigilFusionSystem();

    /** Limpa sistema de fusão de sígilos */
    void CleanupSigilFusionSystem();

    /** Limpa efeitos visuais */
    void CleanupVisualEffects();

    /** Atualiza visuais do time */
    void UpdateTeamVisuals();

    /** Atualiza configurações de colisão do time */
    void UpdateTeamCollisionSettings();

    /** Atualiza efeitos visuais dos sígilos */
    void UpdateSigilVisualEffects();

    /** Atualiza UI dos sígilos */
    void UpdateSigilUI();

    // Funções de VFX
    /** Spawna VFX temporal */
    void SpawnTemporalVFX(EAURACRONTemporalEffectType EffectType, float Duration);

    // SpawnSigilAbilityVFX removida - VFX gerenciados pelas classes USigilAbility_*

    /** Spawna VFX de equipar sígilo */
    void SpawnSigilEquipVFX(EAURACRONSigilType SigilType);

    /** Spawna VFX de fusão de sígilos */
    void SpawnSigilFusionVFX();

    /** Spawna VFX de fusão automática */
    void SpawnAutomaticFusionVFX();

    // Sistema temporal
    /** Captura estado temporal atual */
    void CaptureTemporalState();

    /** Executa rewind temporal */
    void ExecuteTemporalRewind(float Duration);

    /** Executa loop temporal */
    void ExecuteTemporalLoop(float Duration);

    /** Restaura estado temporal */
    void RestoreTemporalState(const FAURACRONTemporalState& State);

    /** Inicia loop temporal */
    void StartTemporalLoop(const TArray<FAURACRONTemporalState>& LoopSequence, float Duration);

    /** Atualiza loop temporal */
    void UpdateTemporalLoop();

    /** Finaliza loop temporal */
    void EndTemporalLoop();

    // Sistema de fusão
    /** Ativa fusão automática de sígilos */
    void TriggerAutomaticSigilFusion();

    /** Verifica se pode ativar fusão de sígilos */
    bool CanTriggerSigilFusion() const;

    /** Verifica se pode usar habilidades temporais */
    bool CanUseTemporalAbilities() const;

    /** Habilita re-forjamento */
    void EnableReforge();

private:
    /** Se o sistema de habilidades foi inicializado */
    bool bAbilitySystemInitialized;

    /** Se o sistema temporal foi inicializado */
    bool bTemporalSystemInitialized;

    /** Se o sistema de fusão foi inicializado */
    bool bSigilFusionSystemInitialized;

    /** Handles para callbacks de atributos */
    FDelegateHandle HealthChangedDelegateHandle;
    FDelegateHandle ManaChangedDelegateHandle;

    // Sistema de fusão de sígilos
    /** Timer para fusão automática (6 minutos) */
    float SigilFusionTimer;

    /** Cooldown para re-forjamento (2 minutos) */
    float SigilFusionCooldown;

    /** Se pode fazer re-forjamento */
    bool bCanReforge;

    /** Tempo do último re-forjamento */
    float LastReforgeTime;

    // Sistema temporal
    /** Tamanho do histórico de estados temporais */
    int32 TemporalStateHistorySize;

    /** Histórico de estados temporais */
    TArray<FAURACRONTemporalState> TemporalStateHistory;

    /** Índice atual no histórico temporal */
    int32 CurrentTemporalStateIndex;

    /** Se está em loop temporal */
    bool bIsInTemporalLoop;

    /** Sequência atual do loop */
    TArray<FAURACRONTemporalState> CurrentLoopSequence;

    /** Índice atual no loop */
    int32 CurrentLoopIndex;

    /** Tempo de fim do loop */
    float LoopEndTime;

    /** Timer do loop temporal */
    FTimerHandle TemporalLoopTimer;

    /** VFX temporais ativos */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveTemporalVFX;
};
