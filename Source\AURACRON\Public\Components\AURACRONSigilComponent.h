// AURACRONSigilComponent.h
// Sistema de Sígilos AURACRON - Componente de Gerenciamento de Sígilos UE 5.6
// Implementação robusta para gerenciar Sígilos equipados e seus efeitos

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "ActiveGameplayEffectHandle.h"
#include "GameplayAbilitySpecHandle.h"
#include "Data/AURACRONEnums.h"
#include "AURACRONSigilComponent.generated.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayEffect;
class UGameplayAbility;

/**
 * Informações de um Sígilo equipado
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEquippedSigilInfo
{
    GENERATED_BODY()

    /** Tipo do Sígilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo")
    EAURACRONSigilType SigilType;

    /** Nível do Sígilo (1-5) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo", meta = (ClampMin = "1", ClampMax = "5"))
    int32 Level;

    /** Raridade do Sígilo (afeta eficiência) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo")
    int32 Rarity;

    /** Tempo de equipamento (para cooldowns) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo")
    float EquipTime;

    /** Se o Sígilo está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo")
    bool bIsActive;

    /** Efeitos aplicados por este Sígilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo")
    TArray<TSubclassOf<UGameplayEffect>> AppliedEffects;

    /** Habilidades concedidas por este Sígilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sígilo")
    TArray<TSubclassOf<UGameplayAbility>> GrantedAbilities;

    /** Construtor padrão */
    FAURACRONEquippedSigilInfo()
    {
        SigilType = EAURACRONSigilType::Aegis;
        Level = 1;
        Rarity = 1;
        EquipTime = 0.0f;
        bIsActive = true;
        AppliedEffects.Empty();
        GrantedAbilities.Empty();
    }
};

/**
 * Componente responsável por gerenciar os Sígilos equipados no personagem
 * Integra com o Gameplay Ability System para aplicar efeitos e habilidades
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API UAURACRONSigilComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UAURACRONSigilComponent();

    // UActorComponent interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Network Replication
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    /** Inicializa o componente com o sistema de habilidades */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sígilos")
    void InitializeWithAbilitySystem(UAbilitySystemComponent* InAbilitySystemComponent);

    // Gerenciamento de Sígilos
    /** Equipa um Sígilo específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sígilos", Server, Reliable)
    void EquipSigil(EAURACRONSigilType SigilType, int32 Level = 1, int32 Rarity = 1);

    /** Remove um Sígilo equipado */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sígilos", Server, Reliable)
    void UnequipSigil(EAURACRONSigilType SigilType);

    /** Verifica se um Sígilo está equipado */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos")
    bool IsSigilEquipped(EAURACRONSigilType SigilType) const;

    /** Obtém informações de um Sígilo equipado */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos")
    FAURACRONEquippedSigilInfo GetSigilInfo(EAURACRONSigilType SigilType) const;

    /** Obtém todos os Sígilos equipados */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos")
    TArray<FAURACRONEquippedSigilInfo> GetAllEquippedSigils() const;

    /** Obtém o número de Sígilos equipados */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos")
    int32 GetEquippedSigilCount() const;

    /** Obtém o número máximo de Sígilos que podem ser equipados */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos")
    int32 GetMaxSigilSlots() const;

    /** Verifica se pode equipar mais Sígilos */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos")
    bool CanEquipMoreSigils() const;

    // Sistema de Fusão
    /** Ativa a fusão de Sígilos (habilidade especial) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sígilos|Fusão", Server, Reliable)
    void ActivateSigilFusion();

    /** Verifica se a fusão está disponível */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos|Fusão")
    bool IsFusionAvailable() const;

    /** Obtém o tempo restante de cooldown da fusão */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos|Fusão")
    float GetFusionCooldownRemaining() const;

    /** Verifica se o jogador pode re-forjar Sígilos (no Nexus) */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos|Reforjamento")
    bool CanReforge() const;

    /** Verifica se o jogador está próximo ao Nexus */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON|Sígilos|Reforjamento")
    bool IsNearNexus() const;

    /** Re-forja um Sígilo (troca por outro tipo) - apenas no Nexus */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sígilos|Reforjamento", Server, Reliable)
    void ReforgeSigil(EAURACRONSigilType OldSigilType, EAURACRONSigilType NewSigilType);

    /** Ativa habilidade específica do Sígilo durante fusão */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Sígilos|Habilidades", Server, Reliable)
    void ActivateSigilSpecificAbility(EAURACRONSigilType SigilType);

    // Eventos Blueprint
    /** Evento chamado quando um Sígilo é equipado */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnSigilEquipped(EAURACRONSigilType SigilType, int32 Level, int32 Rarity);

    /** Evento chamado quando um Sígilo é removido */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnSigilUnequipped(EAURACRONSigilType SigilType);

    /** Evento chamado quando a fusão é ativada */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnFusionActivated();

    /** Evento chamado quando um Sígilo é re-forjado */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnSigilReforged(EAURACRONSigilType OldSigilType, EAURACRONSigilType NewSigilType);

    /** Evento chamado quando fusão automática é ativada aos 6 minutos */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnAutoFusionTriggered();

    /** Evento chamado quando habilidade específica de Sígilo é ativada */
    UFUNCTION(BlueprintImplementableEvent, Category = "AURACRON|Eventos")
    void OnSigilSpecificAbilityActivated(EAURACRONSigilType SigilType);

protected:
    /** Sistema de habilidades do proprietário */
    UPROPERTY()
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** Sígilos atualmente equipados */
    UPROPERTY(ReplicatedUsing = OnRep_EquippedSigils, BlueprintReadOnly, Category = "AURACRON|Sígilos")
    TArray<FAURACRONEquippedSigilInfo> EquippedSigils;

    /** Número máximo de slots de Sígilos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Sígilos", meta = (ClampMin = "1", ClampMax = "6"))
    int32 MaxSigilSlots;

    /** Cooldown da fusão em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Sígilos|Fusão")
    float FusionCooldown;

    /** Duração da fusão em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Sígilos|Fusão")
    float FusionDuration;

    /** Tempo restante de cooldown da fusão */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Sígilos|Fusão")
    float FusionCooldownRemaining;

    /** Se a fusão está atualmente ativa */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Sígilos|Fusão")
    bool bIsFusionActive;

    /** Tempo restante da fusão ativa */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Sígilos|Fusão")
    float FusionTimeRemaining;

    /** Tempo de início da partida para fusão automática */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|Sígilos|Timing")
    float GameStartTime;

    /** Se a fusão automática já foi ativada */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Sígilos|Timing")
    bool bAutoFusionTriggered;

    /** Número de re-forjamentos realizados nesta partida */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "AURACRON|Sígilos|Reforjamento")
    int32 ReforgeCount;

    /** Tempo do último re-forjamento */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|Sígilos|Reforjamento")
    float LastReforgeTime;

    /** Distância máxima do Nexus para re-forjamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Sígilos|Reforjamento")
    float NexusProximityDistance;

    // Configurações de Efeitos
    /** Efeitos base para Aegis */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Configuração")
    TArray<TSubclassOf<UGameplayEffect>> AegisBaseEffects;

    /** Efeitos base para Ruin */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Configuração")
    TArray<TSubclassOf<UGameplayEffect>> RuinBaseEffects;

    /** Efeitos base para Vesper */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Configuração")
    TArray<TSubclassOf<UGameplayEffect>> VesperBaseEffects;

    /** Habilidades base para Aegis */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Configuração")
    TArray<TSubclassOf<UGameplayAbility>> AegisBaseAbilities;

    /** Habilidades base para Ruin */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Configuração")
    TArray<TSubclassOf<UGameplayAbility>> RuinBaseAbilities;

    /** Habilidades base para Vesper */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Configuração")
    TArray<TSubclassOf<UGameplayAbility>> VesperBaseAbilities;

    /** Efeito aplicado durante a fusão */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Fusão")
    TSubclassOf<UGameplayEffect> FusionEffect;

    /** Habilidade específica do Aegis - Murallion (barreira circular) */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Habilidades Específicas")
    TSubclassOf<UGameplayAbility> AegisMurallionAbility;

    /** Habilidade específica do Ruin - Fracasso Prismal (reset parcial de recarga) */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Habilidades Específicas")
    TSubclassOf<UGameplayAbility> RuinFracassoPrismalAbility;

    /** Habilidade específica do Vesper - Sopro de Fluxo (dash aliado + escudo) */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AURACRON|Sígilos|Habilidades Específicas")
    TSubclassOf<UGameplayAbility> VesperSoproDeFluxoAbility;

    // Funções Internas
    /** Aplica os efeitos de um Sígilo */
    void ApplySigilEffects(FAURACRONEquippedSigilInfo& SigilInfo);

    /** Remove os efeitos de um Sígilo */
    void RemoveSigilEffects(const FAURACRONEquippedSigilInfo& SigilInfo);

    /** Concede as habilidades de um Sígilo */
    void GrantSigilAbilities(FAURACRONEquippedSigilInfo& SigilInfo);

    /** Remove as habilidades de um Sígilo */
    void RemoveSigilAbilities(const FAURACRONEquippedSigilInfo& SigilInfo);

    /** Atualiza os cooldowns */
    void UpdateCooldowns(float DeltaTime);

    /** Aplica bônus passivos específicos de um Sígilo */
    void ApplySigilPassiveBonuses(FAURACRONEquippedSigilInfo& SigilInfo);

    /** Remove bônus passivos específicos de um Sígilo */
    void RemoveSigilPassiveBonuses(const FAURACRONEquippedSigilInfo& SigilInfo);

    /** Verifica e ativa fusão automática aos 6 minutos */
    void CheckAutoFusion();

    /** Encontra o Nexus mais próximo */
    class AActor* FindNearestNexus() const;

    // Funções de Replicação
    UFUNCTION()
    void OnRep_EquippedSigils();

private:
    /** Se o componente foi inicializado */
    bool bIsInitialized;

    /** Handles dos efeitos ativos para limpeza */
    TMap<EAURACRONSigilType, TArray<FActiveGameplayEffectHandle>> ActiveEffectHandles;

    /** Handles das habilidades concedidas para limpeza */
    TMap<EAURACRONSigilType, TArray<FGameplayAbilitySpecHandle>> GrantedAbilityHandles;
};
