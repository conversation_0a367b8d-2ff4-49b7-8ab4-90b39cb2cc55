// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "DamageZoneComponent.generated.h"

// ========================================
// FORWARD DECLARATIONS - UE 5.6 MODERN APIS
// ========================================
class UNiagaraComponent;
class UMaterialParameterCollection;

// ========================================
// DELEGATES PARA EVENTOS DE DANO - UE 5.6 MODERN APIS
// ========================================
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnPlayerDamagedByZone, AActor*, Player, float, DamageAmount, float, DamageMultiplier);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerInWarningZone, AActor*, Player, float, Distance);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDamageZoneActivated);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDamageZoneDeactivated);

/**
 * Componente responsável por aplicar dano a jogadores que saem da área segura do mapa
 * durante a fase de Resolução quando ocorre a contração do mapa.
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API UDamageZoneComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    // Sets default values for this component's properties
    UDamageZoneComponent();

    // Called every frame
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    /**
     * Define o raio da área segura onde jogadores não recebem dano
     * @param NewRadius - Novo raio da área segura em unidades do mundo
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetSafeRadius(float NewRadius);

    /**
     * Define a quantidade de dano aplicado por segundo aos jogadores fora da área segura
     * @param NewDamagePerSecond - Novo valor de dano por segundo
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetDamagePerSecond(float NewDamagePerSecond);

    /**
     * Define o fator de escala do dano conforme o jogador se afasta da área segura
     * @param NewScalingFactor - Novo fator de escala (1.0 = sem escala)
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetDamageScalingFactor(float NewScalingFactor);

    /**
     * Ativa ou desativa o componente de zona de dano
     * @param bNewActive - Se o componente deve estar ativo
     * @param bReset - Se deve resetar o componente
     */
    virtual void SetActive(bool bNewActive, bool bReset = false) override;

    /**
     * Define o raio da área de aviso, onde jogadores recebem alertas visuais
     * @param NewWarningRadius - Novo raio da área de aviso em unidades do mundo
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetWarningRadius(float NewWarningRadius);

    /**
     * Ativa ou desativa o componente de zona de dano
     * @param bNewActive - Novo estado de ativação
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetDamageZoneActive(bool bNewActive);

    // ========================================
    // NOVAS FUNÇÕES PÚBLICAS - UE 5.6 MODERN APIS
    // ========================================

    /**
     * Habilita ou desabilita efeitos visuais da zona de dano
     * @param bEnabled - Se os efeitos visuais devem estar habilitados
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone|Visual Effects")
    void SetVisualEffectsEnabled(bool bEnabled);

    /**
     * Define a intensidade dos efeitos visuais
     * @param NewIntensity - Nova intensidade (0.0 a 2.0)
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone|Visual Effects")
    void SetVisualEffectsIntensity(float NewIntensity);

    /**
     * Obtém o centro atual da zona de dano
     * @return Centro da zona de dano
     */
    UFUNCTION(BlueprintPure, Category = "Damage Zone")
    FVector GetZoneCenter() const;

    /**
     * Obtém o raio seguro atual
     * @return Raio seguro atual
     */
    UFUNCTION(BlueprintPure, Category = "Damage Zone")
    float GetCurrentSafeRadius() const;

    /**
     * Obtém o raio de aviso atual
     * @return Raio de aviso atual
     */
    UFUNCTION(BlueprintPure, Category = "Damage Zone")
    float GetCurrentWarningRadius() const;

    /**
     * Verifica se a zona de dano está ativa
     * @return True se a zona de dano estiver ativa
     */
    UFUNCTION(BlueprintPure, Category = "Damage Zone")
    bool IsDamageZoneActive() const;

    // ========================================
    // DELEGATES PÚBLICOS - UE 5.6 MODERN APIS
    // ========================================

    /** Delegate chamado quando um jogador recebe dano da zona */
    UPROPERTY(BlueprintAssignable, Category = "Damage Zone|Events")
    FOnPlayerDamagedByZone OnPlayerDamagedByZone;

    /** Delegate chamado quando um jogador entra na zona de aviso */
    UPROPERTY(BlueprintAssignable, Category = "Damage Zone|Events")
    FOnPlayerInWarningZone OnPlayerInWarningZone;

    /** Delegate chamado quando a zona de dano é ativada */
    UPROPERTY(BlueprintAssignable, Category = "Damage Zone|Events")
    FOnDamageZoneActivated OnDamageZoneActivated;

    /** Delegate chamado quando a zona de dano é desativada */
    UPROPERTY(BlueprintAssignable, Category = "Damage Zone|Events")
    FOnDamageZoneDeactivated OnDamageZoneDeactivated;

protected:
    // Called when the game starts
    virtual void BeginPlay() override;

    /** Raio da área segura onde jogadores não recebem dano */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float SafeRadius;

    /** Raio da área de aviso, onde jogadores recebem alertas visuais */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float WarningRadius;

    /** Quantidade de dano aplicado por segundo aos jogadores fora da área segura */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float DamagePerSecond;

    /** Fator de escala do dano conforme o jogador se afasta da área segura */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float DamageScalingFactor;

    /** Determina se a zona de dano está ativa */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    bool bIsDamageZoneActive;

    /** Tempo acumulado desde o último tick de dano */
    UPROPERTY()
    float AccumulatedTime;

    /** Intervalo entre aplicações de dano em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float DamageInterval;

    /** Aplica dano aos jogadores fora da área segura */
    void ApplyDamageToPlayersOutsideSafeZone(float DeltaTime);

    /** Verifica se um jogador está fora da área segura */
    bool IsPlayerOutsideSafeZone(AActor* Player) const;

    /** Calcula o multiplicador de dano baseado na distância do jogador da área segura */
    float CalculateDamageMultiplier(AActor* Player) const;

    /** Obtém todos os jogadores no mundo */
    TArray<AActor*> GetAllPlayers() const;

    /** Centro da zona de dano, normalmente o centro do mapa */
    UPROPERTY()
    FVector ZoneCenter;

    // ========================================
    // NOVAS PROPRIEDADES - UE 5.6 MODERN APIS
    // ========================================

    /** Componente Niagara para efeitos visuais da zona de dano */
    UPROPERTY()
    TObjectPtr<UNiagaraComponent> DamageZoneVFXComponent;

    /** Componente Niagara para efeitos visuais da zona de aviso */
    UPROPERTY()
    TObjectPtr<UNiagaraComponent> WarningZoneVFXComponent;

    /** Material Parameter Collection para controle visual */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone|Visual Effects")
    TObjectPtr<UMaterialParameterCollection> ZoneMaterialParameterCollection;

    /** Se os efeitos visuais devem ser mostrados */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone|Visual Effects")
    bool bShowVisualEffects;

    /** Intensidade dos efeitos visuais */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone|Visual Effects", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float VisualEffectsIntensity;

    // ========================================
    // FUNÇÕES PROTEGIDAS ADICIONAIS - UE 5.6 MODERN APIS
    // ========================================

    /** Inicializa os efeitos visuais */
    void InitializeVisualEffects();

    /** Inicializa os parâmetros de material */
    void InitializeMaterialParameters();

    /** Configura os delegates de eventos de dano */
    void SetupDamageEventDelegates();

    /** Ativa os efeitos visuais */
    void ActivateVisualEffects();

    /** Desativa os efeitos visuais */
    void DeactivateVisualEffects();

    /** Atualiza os efeitos visuais continuamente */
    void UpdateVisualEffects(float DeltaTime);

    /** Atualiza os raios dos efeitos visuais */
    void UpdateVisualEffectsRadius();

    /** Atualiza o sistema de avisos para jogadores */
    void UpdateWarningSystem(float DeltaTime);
};