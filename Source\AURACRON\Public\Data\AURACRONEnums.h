// AURACRONEnums.h
// Sistema de Sígilos AURACRON - Enumerações Centrais UE 5.6
// Definições robustas e completas para todos os enums do sistema

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "AURACRONEnums.generated.h"

// Forward declarations para evitar dependências circulares
enum class EAURACRONIslandType : uint8;

/**
 * Fases do mapa durante a partida - Sistema de Alternância de Mapas
 * Baseado na documentação: FASE 1: DESPERTAR (0-15 min), FASE 2: CONVERGÊNCIA (15-25 min), etc.
 */
UENUM(BlueprintType)
enum class EAURACRONMapPhase : uint8
{
    Awakening       UMETA(DisplayName = "Despertar"),           // 0-15 minutos
    Expansion       UMETA(DisplayName = "Expansão"),            // 15-20 minutos (fase intermediária)
    Convergence     UMETA(DisplayName = "Convergência"),        // 15-25 minutos
    Intensification UMETA(DisplayName = "Intensificação"),      // 25-35 minutos
    Resolution      UMETA(DisplayName = "Resolução")            // 35+ minutos
};

/**
 * Tipos de ambiente no sistema de alternância de mapas
 * Baseado na documentação: Planície Radiante, Firmamento Zephyr, Reino Purgatório
 */
UENUM(BlueprintType)
enum class EAURACRONEnvironmentType : uint8
{
    RadiantPlains   UMETA(DisplayName = "Planície Radiante"),   // Mapa base terrestre
    ZephyrFirmament UMETA(DisplayName = "Firmamento Zephyr"),   // Plataformas celestiais
    PurgatoryRealm  UMETA(DisplayName = "Reino Purgatório"),    // Dimensão espelhada
    PrismaticNexus  UMETA(DisplayName = "Nexus Prismático"),    // Nexus prismático
    CelestialBasin  UMETA(DisplayName = "Bacia Celestial"),     // Bacia celestial
    CrystalCaverns  UMETA(DisplayName = "Cavernas de Cristal")  // Cavernas de cristal
};

/**
 * Tipos de objetivos procedurais
 * Sistema de geração dinâmica baseado no estado da partida
 */
UENUM(BlueprintType)
enum class EAURACRONObjectiveType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),                   // Valor padrão/inválido
    FragmentAuracron    UMETA(DisplayName = "Fragmento Auracron"),      // Mini-objetivos que constroem buff maior
    NexusFragment       UMETA(DisplayName = "Fragmento de Nexus"),      // Fragmentos de nexus para construir buffs
    TemporalRift        UMETA(DisplayName = "Fenda Temporal"),          // Permite rewind de 10 segundos
    EnvironmentAnchor   UMETA(DisplayName = "Âncora de Ambiente"),      // Controla ambiente ativo
    MapAnchor           UMETA(DisplayName = "Âncora de Mapa"),          // Controla qual mapa está ativo
    FusionCatalyst      UMETA(DisplayName = "Catalisador de Fusão"),    // Reduz cooldown de Sígilos
    TransitionPortal    UMETA(DisplayName = "Portal de Transição"),     // Ativa transições entre ambientes
    PrismalGuardian     UMETA(DisplayName = "Guardião Prismal"),        // Objetivo principal terrestre
    PrismalNexus        UMETA(DisplayName = "Nexus Prismal"),           // Nexus de energia prismática
    StormCore           UMETA(DisplayName = "Núcleo de Tempestade"),    // Objetivo principal celestial
    SpectralGuardian    UMETA(DisplayName = "Guardião Espectral"),      // Objetivo principal espectral
    UmbraticLeviathan   UMETA(DisplayName = "Leviatã Umbrático"),       // Objetivo especial do Reino Purgatório
    RadiantShrine       UMETA(DisplayName = "Santuário Radiante"),      // Santuário de energia radiante
    WindSanctuary       UMETA(DisplayName = "Santuário do Vento"),      // Santuário de energia eólica
    PurgatoryShrine     UMETA(DisplayName = "Santuário do Purgatório"), // Santuário de energia sombria
    ChaosRift           UMETA(DisplayName = "Fenda do Caos"),           // Fenda de energia caótica
    RadiantAnchor       UMETA(DisplayName = "Âncora Radiante"),         // Âncora de energia radiante
    ZephyrAnchor        UMETA(DisplayName = "Âncora Zéfiro"),           // Âncora de energia eólica
    PurgatoryAnchor     UMETA(DisplayName = "Âncora do Purgatório"),    // Âncora de energia sombria
    NexusIsland         UMETA(DisplayName = "Ilha Nexus"),              // Ilha com nexus de energia
    SanctuaryIsland     UMETA(DisplayName = "Ilha Santuário"),          // Ilha com santuário
    ArsenalIsland       UMETA(DisplayName = "Ilha Arsenal"),            // Ilha com arsenal
    ChaosIsland         UMETA(DisplayName = "Ilha do Caos"),            // Ilha com energia caótica
    CapturePoint        UMETA(DisplayName = "Ponto de Captura"),        // Ponto de captura estratégico
    PowerCore           UMETA(DisplayName = "Núcleo de Energia"),       // Núcleo de energia
    ResourceNode        UMETA(DisplayName = "Nó de Recursos"),          // Nó de recursos
    AncientRelic        UMETA(DisplayName = "Relíquia Antiga"),         // Relíquia antiga
    EnergyConduit       UMETA(DisplayName = "Condutor de Energia"),     // Condutor de energia
    DefenseTower        UMETA(DisplayName = "Torre de Defesa")          // Torre de defesa
};

/**
 * Tipos de buff aplicados por objetivos
 * Sistema robusto de buffs para equipes e áreas
 */
UENUM(BlueprintType)
enum class EAURACRONBuffType : uint8
{
    MovementSpeed       UMETA(DisplayName = "Velocidade de Movimento"),
    DamageBoost         UMETA(DisplayName = "Aumento de Dano"),
    DefenseBoost        UMETA(DisplayName = "Aumento de Defesa"),
    CooldownReduction   UMETA(DisplayName = "Redução de Recarga"),
    HealthRegeneration  UMETA(DisplayName = "Regeneração de Vida"),
    ManaRegeneration    UMETA(DisplayName = "Regeneração de Mana"),
    CriticalChance      UMETA(DisplayName = "Chance Crítica"),
    AttackSpeed         UMETA(DisplayName = "Velocidade de Ataque"),
    SpellPower          UMETA(DisplayName = "Poder Mágico"),
    Armor               UMETA(DisplayName = "Armadura"),
    MagicResistance     UMETA(DisplayName = "Resistência Mágica"),
    Lifesteal           UMETA(DisplayName = "Roubo de Vida"),
    Tenacity            UMETA(DisplayName = "Tenacidade")
};

/**
 * Tipos de efeitos temporais
 * Sistema de manipulação temporal para Fendas Temporais
 */
UENUM(BlueprintType)
enum class EAURACRONTemporalEffectType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),                  // Sem efeito temporal
    Rewind              UMETA(DisplayName = "Retrocesso"),               // Volta posição/vida 10 segundos
    Slow                UMETA(DisplayName = "Lentidão"),                // Reduz velocidade temporal
    Accelerate          UMETA(DisplayName = "Aceleração"),              // Aumenta velocidade temporal
    Freeze              UMETA(DisplayName = "Congelamento"),            // Congela no tempo
    Loop                UMETA(DisplayName = "Loop"),                    // Cria loop temporal
    TimeAcceleration    UMETA(DisplayName = "Aceleração Temporal"),     // Acelera cooldowns
    TimeDeceleration    UMETA(DisplayName = "Desaceleração Temporal"),  // Desacelera inimigos
    ChronoShield        UMETA(DisplayName = "Escudo Temporal"),         // Imunidade temporal breve
    TemporalEcho        UMETA(DisplayName = "Eco Temporal")             // Duplica próxima habilidade
};

/**
 * Tipos de trilhos dinâmicos
 * Sistema de trilhos que mudam baseado em condições
 */
UENUM(BlueprintType)
enum class EAURACRONTrailType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),                  // Valor padrão/inválido
    Solar               UMETA(DisplayName = "Trilho Solar"),            // Energia dourada, boost velocidade
    Axis                UMETA(DisplayName = "Trilho Axis"),             // Canais neutros, transição instantânea
    Lunar               UMETA(DisplayName = "Trilho Lunar"),            // Caminhos etéreos, furtividade noturna
    PrismalFlow         UMETA(DisplayName = "Fluxo Prismal"),           // Trilho do fluxo prismal
    EtherealPath        UMETA(DisplayName = "Caminho Etéreo"),          // Trilho etéreo
    NexusConnection     UMETA(DisplayName = "Conexão Nexus")            // Trilho de conexão com nexus
};

/**
 * Estados de qualidade de hardware para otimização adaptativa
 * Sistema de detecção automática de performance
 */
UENUM(BlueprintType)
enum class EAURACRONHardwareQuality : uint8
{
    Entry               UMETA(DisplayName = "Entry Level"),             // 2-3GB RAM, GPU básica
    MidRange            UMETA(DisplayName = "Mid Range"),               // 3-4GB RAM, GPU intermediária  
    HighEnd             UMETA(DisplayName = "High End")                 // 4GB+ RAM, GPU avançada
};

/**
 * Tipos de sígilos do sistema de fusão
 * Sistema de Sígilos Auracron (Fusion 2.0)
 */
UENUM(BlueprintType)
enum class EAURACRONSigilType : uint8
{
    Aegis               UMETA(DisplayName = "Aegis"),                   // Tanque - +15% HP, Armadura adaptativa
    Ruin                UMETA(DisplayName = "Ruin"),                    // Dano - +12% ATK/AP adaptativo
    Vesper              UMETA(DisplayName = "Vesper")                   // Utilidade - +10% Vel. Move + 8% Recarga
};

/**
 * Estados de replicação de rede
 * Sistema robusto para multiplayer
 */
UENUM(BlueprintType)
enum class EAURACRONNetworkState : uint8
{
    Disconnected        UMETA(DisplayName = "Desconectado"),
    Connecting          UMETA(DisplayName = "Conectando"),
    Connected           UMETA(DisplayName = "Conectado"),
    Synchronizing       UMETA(DisplayName = "Sincronizando"),
    Ready               UMETA(DisplayName = "Pronto"),
    InGame              UMETA(DisplayName = "Em Jogo"),
    Reconnecting        UMETA(DisplayName = "Reconectando"),
    Error               UMETA(DisplayName = "Erro")
};

/**
 * Categoria de objetivos procedurais
 * Sistema de classificação para balanceamento
 */
UENUM(BlueprintType)
enum class EAURACRONObjectiveCategory : uint8
{
    Core                UMETA(DisplayName = "Principal"),               // Objetivos principais
    CatchUp             UMETA(DisplayName = "Recuperação"),             // Objetivos de catch-up
    Bonus               UMETA(DisplayName = "Bônus"),                   // Objetivos extras
    Event               UMETA(DisplayName = "Evento"),                  // Objetivos de eventos especiais
    Exploration         UMETA(DisplayName = "Exploração"),             // Objetivos de exploração
    Combat              UMETA(DisplayName = "Combate"),                // Objetivos de combate
    Strategic           UMETA(DisplayName = "Estratégico"),            // Objetivos estratégicos
    Elite               UMETA(DisplayName = "Elite"),                  // Objetivos elite
    Standard            UMETA(DisplayName = "Padrão"),                 // Objetivos padrão
    Environment         UMETA(DisplayName = "Ambiente")                // Objetivos ambientais
};

/**
 * Estados de objetivos procedurais
 * Sistema de controle de estado dos objetivos
 */
UENUM(BlueprintType)
enum class EAURACRONObjectiveState : uint8
{
    Inactive            UMETA(DisplayName = "Inativo"),                 // Objetivo não ativo
    Active              UMETA(DisplayName = "Ativo"),                   // Objetivo ativo e disponível
    Available           UMETA(DisplayName = "Disponível"),              // Objetivo disponível para captura
    InProgress          UMETA(DisplayName = "Em Progresso"),            // Objetivo sendo capturado
    InCombat            UMETA(DisplayName = "Em Combate"),              // Objetivo em combate
    Contested           UMETA(DisplayName = "Contestado"),              // Objetivo sendo disputado por múltiplas equipes
    Captured            UMETA(DisplayName = "Capturado"),               // Objetivo capturado por uma equipe
    Completed           UMETA(DisplayName = "Completado"),              // Objetivo completado
    Respawning          UMETA(DisplayName = "Renascendo"),              // Objetivo renascendo
    Expired             UMETA(DisplayName = "Expirado")                 // Objetivo expirou
};

/**
 * Tipos de energia para efeitos especiais
 * Sistema de classificação de energia
 */
UENUM(BlueprintType)
enum class EAURACRONEnergyType : uint8
{
    Golden              UMETA(DisplayName = "Energia Dourada"),         // Energia dos Portais Radiantes
    Silver              UMETA(DisplayName = "Energia Prateada"),        // Energia dos Portais Zephyr
    Violet              UMETA(DisplayName = "Energia Violeta"),         // Energia dos Portais Umbrais
    Solar               UMETA(DisplayName = "Solar"),                   // Energia solar dourada
    Lunar               UMETA(DisplayName = "Lunar"),                   // Energia lunar prateada
    Prismal             UMETA(DisplayName = "Prismal"),                 // Energia prismática multicolor
    Chaos               UMETA(DisplayName = "Caos"),                    // Energia caótica vermelha
    Void                UMETA(DisplayName = "Vazio")                    // Energia do vazio roxa
};

/**
 * Tipos de ilhas no sistema PCG
 * Sistema de classificação de ilhas procedurais
 */
UENUM(BlueprintType)
enum class EAURACRONIslandType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),
    Nexus               UMETA(DisplayName = "Nexus Island"),
    Sanctuary           UMETA(DisplayName = "Sanctuary Island"),
    Arsenal             UMETA(DisplayName = "Arsenal Island"),
    Chaos               UMETA(DisplayName = "Chaos Island"),
    Battlefield         UMETA(DisplayName = "Battlefield Island")
};

/**
 * Tipos de acampamentos da selva baseados no LoL
 * Sistema de classificação de acampamentos procedurais
 */
UENUM(BlueprintType)
enum class EAURACRONJungleCampType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),

    // BUFF CAMPS (equivalentes ao Blue/Red Buff)
    RadiantEssence      UMETA(DisplayName = "Radiant Essence"),     // Blue Buff equivalent (mana regen + CDR)
    ChaosEssence        UMETA(DisplayName = "Chaos Essence"),       // Red Buff equivalent (damage + slow)

    // REGULAR CAMPS
    SpectralPack        UMETA(DisplayName = "Spectral Pack"),       // Krugs equivalent
    EtherealGrove       UMETA(DisplayName = "Ethereal Grove"),      // Gromp equivalent
    VoidRaptors         UMETA(DisplayName = "Void Raptors"),        // Raptors equivalent
    CrystalWolves       UMETA(DisplayName = "Crystal Wolves"),      // Wolves equivalent
    StoneGuardians      UMETA(DisplayName = "Stone Guardians"),     // Stone guardian camp
    PrismalToad         UMETA(DisplayName = "Prismal Toad"),        // Prismal toad camp
    WindSpirits         UMETA(DisplayName = "Wind Spirits"),        // Wind spirit camp
    FluxCrawler         UMETA(DisplayName = "Flux Crawler"),        // Flux crawler camp

    // EPIC MONSTERS (equivalentes ao Dragon/Baron)
    PrismalDragon       UMETA(DisplayName = "Prismal Dragon"),      // Dragon equivalent
    AncientGuardian     UMETA(DisplayName = "Ancient Guardian"),    // Baron equivalent

    // Additional camp types
    VoidHarpy           UMETA(DisplayName = "Void Harpy"),          // Void harpy camp
    ShadowWolf          UMETA(DisplayName = "Shadow Wolf"),         // Shadow wolf camp
    CrystalGolem        UMETA(DisplayName = "Crystal Golem"),       // Crystal golem camp

    // Legacy values for compatibility
    Small               UMETA(DisplayName = "Pequeno"),
    Medium              UMETA(DisplayName = "Médio"),
    Large               UMETA(DisplayName = "Grande"),
    Epic                UMETA(DisplayName = "Épico"),
    MAX                 UMETA(Hidden)                               // Maximum value for iteration
};

/**
 * Tipos de dispositivos para adaptação de performance
 * Sistema de classificação de dispositivos procedurais e performance
 */
UENUM(BlueprintType)
enum class EAURACRONDeviceType : uint8
{
    None                UMETA(DisplayName = "Nenhum"),

    // Performance device types
    Entry               UMETA(DisplayName = "Entry Device - Low Performance"),
    Mid                 UMETA(DisplayName = "Mid Device - Medium Performance"),
    High                UMETA(DisplayName = "High Device - High Performance"),
    Unknown             UMETA(DisplayName = "Unknown Device Type"),

    // Legacy PCG device types
    Generator           UMETA(DisplayName = "Gerador"),
    Amplifier           UMETA(DisplayName = "Amplificador"),
    Stabilizer          UMETA(DisplayName = "Estabilizador"),
    Resonator           UMETA(DisplayName = "Ressonador")
};

/**
 * Perfis de qualidade de streaming
 * Sistema de otimização de streaming baseado em hardware
 */
UENUM(BlueprintType)
enum class EAURACRONStreamingQualityProfile : uint8
{
    Low                 UMETA(DisplayName = "Baixa Qualidade"),
    Medium              UMETA(DisplayName = "Média Qualidade"),
    High                UMETA(DisplayName = "Alta Qualidade"),
    Ultra               UMETA(DisplayName = "Ultra Qualidade"),
    Custom              UMETA(DisplayName = "Personalizado")
};

// ========================================
// DEFINIÇÕES DE RANGE PARA ENUMS (UE 5.6)
// ========================================

// Definir range para EAURACRONEnvironmentType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONEnvironmentType, EAURACRONEnvironmentType::RadiantPlains, EAURACRONEnvironmentType::CrystalCaverns)

// Definir range para EAURACRONMapPhase
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONMapPhase, EAURACRONMapPhase::Awakening, EAURACRONMapPhase::Resolution)

// Definir range para EAURACRONObjectiveType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONObjectiveType, EAURACRONObjectiveType::None, EAURACRONObjectiveType::DefenseTower)

// Definir range para EAURACRONIslandType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONIslandType, EAURACRONIslandType::None, EAURACRONIslandType::Battlefield)

// Definir range para EAURACRONJungleCampType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONJungleCampType, EAURACRONJungleCampType::None, EAURACRONJungleCampType::MAX)

// Definir range para EAURACRONDeviceType
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONDeviceType, EAURACRONDeviceType::None, EAURACRONDeviceType::Resonator)

// Definir range para EAURACRONStreamingQualityProfile
ENUM_RANGE_BY_FIRST_AND_LAST(EAURACRONStreamingQualityProfile, EAURACRONStreamingQualityProfile::Low, EAURACRONStreamingQualityProfile::Custom)

/**
 * Tipos de blur para bordas de ambiente
 * Sistema de efeitos visuais para transições
 */
UENUM(BlueprintType)
enum class EEnvironmentBlurType : uint8
{
    Gaussian            UMETA(DisplayName = "Gaussiano"),               // Blur gaussiano suave
    Atmospheric         UMETA(DisplayName = "Atmosférico"),            // Blur atmosférico etéreo
    Spectral            UMETA(DisplayName = "Espectral"),              // Blur espectral sombrio
    Radial              UMETA(DisplayName = "Radial"),                 // Blur radial
    Motion              UMETA(DisplayName = "Movimento"),              // Blur de movimento
    Depth               UMETA(DisplayName = "Profundidade")            // Blur baseado em profundidade
};

// Definir range para EEnvironmentBlurType
ENUM_RANGE_BY_FIRST_AND_LAST(EEnvironmentBlurType, EEnvironmentBlurType::Gaussian, EEnvironmentBlurType::Depth)




