// SigilDebugCommands.h
#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "GameplayTagContainer.h"
#include "SigilDebugCommands.generated.h"

// Forward declarations
class ASigilItem;
class USigilManagerComponent;
class USigilFusionSystem;
class USigilVFXManager;
class USigilReplicationManager;
class APlayerController;
class APawn;

/**
 * Configurações de depuração para o sistema de sígilos
 */
UCLASS(Config=Game, DefaultConfig, meta=(DisplayName="Sigil Debug Settings"))
class AURACRON_API USigilDebugSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    USigilDebugSettings();

    // Configurações gerais de debug
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bEnableDebugCommands;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bShowDebugInfo;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bLogDebugEvents;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bShowDebugWidgets;

    // Configurações específicas para MOBA
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "MOBA Debug")
    bool bEnableMOBACommands;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "MOBA Debug")
    int32 DefaultTeamSize;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "MOBA Debug")
    float DefaultMatchDuration;

    // Configurações de spawn de sígilos
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Sigil Debug")
    TArray<FGameplayTag> DefaultSigilTypes;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Sigil Debug")
    int32 MaxDebugSigils;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Sigil Debug")
    float SigilSpawnRadius;

    // Configurações de VFX debug
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "VFX Debug")
    bool bShowVFXDebugInfo;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "VFX Debug")
    float VFXDebugDuration;

    // Configurações de rede debug
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Network Debug")
    bool bShowNetworkDebugInfo;

    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Network Debug")
    float NetworkDebugUpdateRate;
};

/**
 * Sistema de comandos de console para depuração do sistema de sígilos
 * Otimizado para ambiente MOBA 5x5
 */
UCLASS()
class AURACRON_API USigilDebugCommands : public UObject
{
    GENERATED_BODY()

public:
    USigilDebugCommands();

    // Inicialização e limpeza
    static void RegisterConsoleCommands();
    static void UnregisterConsoleCommands();

    // Comandos gerais de sígilo
    static void SpawnSigil(const TArray<FString>& Args);
    static void RemoveSigil(const TArray<FString>& Args);
    static void ListSigils(const TArray<FString>& Args);
    static void ClearAllSigils(const TArray<FString>& Args);
    static void SetSigilType(const TArray<FString>& Args);
    static void SetSigilRarity(const TArray<FString>& Args);

    // Comandos de fusão
    static void StartFusion(const TArray<FString>& Args);
    static void CancelFusion(const TArray<FString>& Args);
    static void ListActiveFusions(const TArray<FString>& Args);
    static void SetFusionTime(const TArray<FString>& Args);
    static void ToggleAutoFusion(const TArray<FString>& Args);

    // Comandos de VFX
    static void PlayVFX(const TArray<FString>& Args);
    static void StopVFX(const TArray<FString>& Args);
    static void ListActiveVFX(const TArray<FString>& Args);
    static void ToggleVFXPooling(const TArray<FString>& Args);
    static void ClearVFXPools(const TArray<FString>& Args);

    // Comandos específicos para MOBA
    static void SetTeam(const TArray<FString>& Args);
    static void StartMOBAMatch(const TArray<FString>& Args);
    static void EndMOBAMatch(const TArray<FString>& Args);
    static void SetMOBAPhase(const TArray<FString>& Args);
    static void SpawnTeamSigils(const TArray<FString>& Args);
    static void SimulateTeamFight(const TArray<FString>& Args);
    static void TriggerObjectiveEvent(const TArray<FString>& Args);

    // Comandos de rede
    static void ShowNetworkStats(const TArray<FString>& Args);
    static void SimulateLag(const TArray<FString>& Args);
    static void ForceReplication(const TArray<FString>& Args);
    static void ToggleNetworkDebug(const TArray<FString>& Args);

    // Comandos de performance
    static void ShowPerformanceStats(const TArray<FString>& Args);
    static void ProfileSigilSystem(const TArray<FString>& Args);
    static void OptimizeForMOBA(const TArray<FString>& Args);
    static void ResetPerformanceCounters(const TArray<FString>& Args);

    // Comandos de configuração
    static void SetDebugMode(const TArray<FString>& Args);
    static void SaveDebugConfig(const TArray<FString>& Args);
    static void LoadDebugConfig(const TArray<FString>& Args);
    static void ResetToDefaults(const TArray<FString>& Args);

    // Comandos de teste automatizado
    static void RunSigilTests(const TArray<FString>& Args);
    static void RunFusionTests(const TArray<FString>& Args);
    static void RunVFXTests(const TArray<FString>& Args);
    static void RunNetworkTests(const TArray<FString>& Args);
    static void RunMOBAScenario(const TArray<FString>& Args);

protected:
    // Funções auxiliares
    static APlayerController* GetPlayerController(int32 PlayerIndex = 0);
    static APawn* GetPlayerPawn(int32 PlayerIndex = 0);
    static USigilManagerComponent* GetSigilManager(APawn* Pawn = nullptr);
    static USigilFusionSystem* GetFusionSystem(APawn* Pawn = nullptr);
    static USigilVFXManager* GetVFXManager(APawn* Pawn = nullptr);
    static USigilReplicationManager* GetReplicationManager(APawn* Pawn = nullptr);
    
    // Funções de validação
    static bool ValidateArgs(const TArray<FString>& Args, int32 MinArgs, const FString& Usage);
    static bool ValidatePlayerIndex(int32 PlayerIndex);
    
    // Configuração de cenários MOBA
    static FMOBAScenarioConfig GetDefaultMOBAScenario();
    static void SetupMOBAScenario(const FMOBAScenarioConfig& Config);
    
    // Funções de teste
    static void LogTestResult(const FSigilTestResult& Result);
    static bool ValidateTeamIndex(int32 TeamIndex);
    static FGameplayTag ParseGameplayTag(const FString& TagString);
    
    // Funções de logging
    static void LogCommand(const FString& Command, const TArray<FString>& Args);
    static void LogSuccess(const FString& Message);
    static void LogWarning(const FString& Message);
    static void LogError(const FString& Message);
    
    // Funções de spawn
    static ASigilItem* SpawnSigilAtLocation(const FVector& Location, const FGameplayTag& SigilType, APawn* Owner = nullptr);
    static void SpawnSigilsInRadius(const FVector& Center, float Radius, int32 Count, const FGameplayTag& SigilType, APawn* Owner = nullptr);
    
    // Funções de MOBA
    static void SetupMOBATeams(int32 TeamSize);
    static void AssignPlayerToTeam(int32 PlayerIndex, int32 TeamIndex);
    static void StartMOBAPhase(const FGameplayTag& PhaseTag);
    
    // Funções de teste
    static void RunBasicSigilTest();
    static void RunFusionStressTest();
    static void RunVFXPerformanceTest();
    static void RunNetworkLatencyTest();
    static void RunMOBA5v5Simulation();

private:
    // Console commands estáticos
    static TArray<struct IConsoleCommand*> RegisteredCommands;
    
    // Configurações de debug
    static USigilDebugSettings* DebugSettings;
    
    // Estado de debug
    static bool bDebugModeActive;
    static bool bMOBAMatchActive;
    static float DebugStartTime;
    
    // Contadores de performance
    static int32 SigilsSpawned;
    static int32 FusionsCompleted;
    static int32 VFXPlayed;
    static int32 NetworkMessages;
    
    // Constantes
    static const int32 MAX_DEBUG_SIGILS;
    static const int32 MAX_MOBA_PLAYERS;
    static const float DEFAULT_SPAWN_RADIUS;
    static const float DEFAULT_VFX_DURATION;
};

/**
 * Macro para registrar comandos de console facilmente
 */
#define REGISTER_SIGIL_CONSOLE_COMMAND(CommandName, Function, Help) \
    RegisteredCommands.Add(IConsoleManager::Get().RegisterConsoleCommand( \
        TEXT(CommandName), \
        TEXT(Help), \
        FConsoleCommandWithArgsDelegate::CreateStatic(&USigilDebugCommands::Function), \
        ECVF_Cheat \
    ));

/**
 * Estrutura para armazenar informações de teste
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilTestResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    FString TestName;

    UPROPERTY(BlueprintReadOnly)
    bool bPassed;

    UPROPERTY(BlueprintReadOnly)
    float ExecutionTime;

    UPROPERTY(BlueprintReadOnly)
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly)
    TMap<FString, float> PerformanceMetrics;

    FSigilTestResult()
    {
        bPassed = false;
        ExecutionTime = 0.0f;
    }
};

/**
 * Estrutura para configuração de cenários MOBA
 */
USTRUCT(BlueprintType)
struct AURACRON_API FMOBAScenarioConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 TeamSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MatchDuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 SigilsPerPlayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FusionFrequency;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableReplication;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FGameplayTag> AllowedSigilTypes;

    FMOBAScenarioConfig()
    {
        TeamSize = 5;
        MatchDuration = 1800.0f; // 30 minutos
        SigilsPerPlayer = 6;
        FusionFrequency = 360.0f; // 6 minutos
        bEnableVFX = true;
        bEnableReplication = true;
    }
};