// SigilFusionSystem.h
#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "Engine/TimerHandle.h"
#include "Delegates/DelegateCombinations.h"
#include "SigilFusionSystem.generated.h"

// Forward declarations
class ASigilItem;
class USigilVFXManager;
class UNiagaraComponent;
class USoundBase;
class UUserWidget;

// Enums para o sistema de fusão
UENUM(BlueprintType)
enum class ESigilFusionState : uint8
{
    None            UMETA(DisplayName = "None"),
    Preparing       UMETA(DisplayName = "Preparing"),
    InProgress      UMETA(DisplayName = "In Progress"),
    Completed       UMETA(DisplayName = "Completed"),
    Failed          UMETA(DisplayName = "Failed"),
    Cancelled       UMETA(DisplayName = "Cancelled")
};

UENUM(BlueprintType)
enum class ESigilFusionType : uint8
{
    Basic           UMETA(DisplayName = "Basic Fusion"),
    Advanced        UMETA(DisplayName = "Advanced Fusion"),
    Legendary       UMETA(DisplayName = "Legendary Fusion"),
    Spectral        UMETA(DisplayName = "Spectral Fusion")
};

UENUM(BlueprintType)
enum class ESigilFusionResult : uint8
{
    Success         UMETA(DisplayName = "Success"),
    Failed          UMETA(DisplayName = "Failed"),
    CriticalSuccess UMETA(DisplayName = "Critical Success"),
    Cancelled       UMETA(DisplayName = "Cancelled")
};

// Estruturas para dados de fusão
USTRUCT(BlueprintType)
struct AURACRON_API FSigilFusionRecipe
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    FGameplayTag RecipeID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    TArray<FGameplayTag> RequiredSigilTypes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    FGameplayTag ResultSigilType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    ESigilFusionType FusionType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    float SuccessRate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    float FusionTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipe")
    int32 RequiredLevel;

    FSigilFusionRecipe()
    {
        SuccessRate = 0.8f;
        FusionTime = 360.0f; // 6 minutos
        RequiredLevel = 1;
        FusionType = ESigilFusionType::Basic;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FSigilFusionInstance
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    FGuid FusionID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    FSigilFusionRecipe Recipe;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    TArray<ASigilItem*> InputSigils;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    ESigilFusionState CurrentState;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    float StartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    float RemainingTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    AActor* OwnerActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Instance")
    bool bIsAutomatic;

    FSigilFusionInstance()
    {
        FusionID = FGuid::NewGuid();
        CurrentState = ESigilFusionState::None;
        StartTime = 0.0f;
        RemainingTime = 0.0f;
        OwnerActor = nullptr;
        bIsAutomatic = true;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FSigilFusionNotification
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FText Title;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FText Message;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    ESigilFusionState FusionState;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    float DisplayDuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Notification")
    FLinearColor NotificationColor;

    FSigilFusionNotification()
    {
        DisplayDuration = 5.0f;
        NotificationColor = FLinearColor::White;
        FusionState = ESigilFusionState::None;
    }
};

// Delegados para eventos de fusão
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilFusionStarted, const FSigilFusionInstance&, FusionInstance, AActor*, Owner);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilFusionCompleted, const FSigilFusionInstance&, FusionInstance, ESigilFusionResult, Result, ASigilItem*, ResultSigil);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilFusionProgress, const FSigilFusionInstance&, FusionInstance, float, ProgressPercent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilFusionCancelled, const FSigilFusionInstance&, FusionInstance, AActor*, Owner);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilFusionNotification, const FSigilFusionNotification&, Notification, AActor*, Owner);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilCreated, ASigilItem*, CreatedSigil, AActor*, Owner, const FSigilFusionRecipe&, Recipe);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilConsumed, ASigilItem*, ConsumedSigil, AActor*, Owner);

/**
 * Sistema de fusão automática de sígilos para MOBA 5x5
 * Gerencia fusões automáticas com temporizador de 6 minutos, notificações e efeitos visuais
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API USigilFusionSystem : public UActorComponent
{
    GENERATED_BODY()

public:
    USigilFusionSystem();

    // Configurações do sistema
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Settings")
    bool bEnableAutomaticFusion;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Settings")
    float DefaultFusionTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Settings")
    int32 MaxSimultaneousFusions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Settings")
    bool bShowNotifications;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Settings")
    bool bPlaySounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Settings")
    bool bShowVFX;

    // Receitas de fusão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Recipes")
    TArray<FSigilFusionRecipe> FusionRecipes;

    // Fusões ativas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Fusion State")
    TArray<FSigilFusionInstance> ActiveFusions;

    // Eventos de fusão
    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilFusionStarted OnFusionStarted;

    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilFusionCompleted OnFusionCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilFusionProgress OnFusionProgress;

    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilFusionCancelled OnFusionCancelled;

    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilFusionNotification OnFusionNotification;

    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilCreated OnSigilCreated;

    UPROPERTY(BlueprintAssignable, Category = "Fusion Events")
    FOnSigilConsumed OnSigilConsumed;

    // Referências para VFX e áudio
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    USigilVFXManager* VFXManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    USoundBase* FusionStartSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    USoundBase* FusionCompleteSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    USoundBase* FusionFailSound;

    // Widget de notificação
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    TSubclassOf<UUserWidget> NotificationWidgetClass;

protected:
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Timers para fusões
    TMap<FGuid, FTimerHandle> FusionTimers;

    // Timers para notificações
    TMap<UUserWidget*, FTimerHandle> NotificationTimers;

    // Widgets de notificação ativos
    TArray<UUserWidget*> ActiveNotificationWidgets;

public:
    // Funções principais de fusão
    UFUNCTION(BlueprintCallable, Category = "Fusion")
    bool StartAutomaticFusion(const TArray<ASigilItem*>& InputSigils, AActor* Owner);

    UFUNCTION(BlueprintCallable, Category = "Fusion")
    bool StartManualFusion(const FSigilFusionRecipe& Recipe, const TArray<ASigilItem*>& InputSigils, AActor* Owner);

    UFUNCTION(BlueprintCallable, Category = "Fusion")
    bool CancelFusion(const FGuid& FusionID);

    UFUNCTION(BlueprintCallable, Category = "Fusion")
    void CancelAllFusions(AActor* Owner = nullptr);

    // Funções de consulta
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion")
    bool CanStartFusion(const TArray<ASigilItem*>& InputSigils) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion")
    FSigilFusionRecipe FindBestRecipe(const TArray<ASigilItem*>& InputSigils) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion")
    TArray<FSigilFusionInstance> GetActiveFusions(AActor* Owner = nullptr) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion")
    FSigilFusionInstance GetFusionByID(const FGuid& FusionID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion")
    float GetFusionProgress(const FGuid& FusionID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion")
    int32 GetActiveFusionCount(AActor* Owner = nullptr) const;

    // Funções de receitas
    UFUNCTION(BlueprintCallable, Category = "Fusion Recipes")
    void AddFusionRecipe(const FSigilFusionRecipe& Recipe);

    UFUNCTION(BlueprintCallable, Category = "Fusion Recipes")
    bool RemoveFusionRecipe(const FGameplayTag& RecipeID);

    UFUNCTION(BlueprintCallable, Category = "Fusion Recipes")
    void LoadDefaultRecipes();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fusion Recipes")
    TArray<FSigilFusionRecipe> GetAvailableRecipes(const TArray<ASigilItem*>& InputSigils) const;

    // Funções de notificação
    UFUNCTION(BlueprintCallable, Category = "Fusion Notifications")
    void ShowFusionNotification(const FSigilFusionNotification& Notification, AActor* Owner);

    UFUNCTION(BlueprintCallable, Category = "Fusion Notifications")
    void HideAllNotifications();

    // Funções de configuração
    UFUNCTION(BlueprintCallable, Category = "Fusion Settings")
    void SetAutomaticFusionEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Fusion Settings")
    void SetDefaultFusionTime(float NewTime);

    UFUNCTION(BlueprintCallable, Category = "Fusion Settings")
    void SetMaxSimultaneousFusions(int32 NewMax);

protected:
    // Funções internas de fusão
    void ProcessFusionCompletion(const FGuid& FusionID);
    void UpdateFusionProgress(float DeltaTime);
    ASigilItem* CreateResultSigil(const FSigilFusionRecipe& Recipe, AActor* Owner);
    bool ValidateFusionInputs(const TArray<ASigilItem*>& InputSigils, const FSigilFusionRecipe& Recipe) const;
    bool IsSigilInActiveFusion(const ASigilItem* Sigil) const;
    float CalculateSuccessRate(const TArray<ASigilItem*>& InputSigils, const FSigilFusionRecipe& Recipe) const;
    int32 GetCompletedFusionCount() const;
    void ConsumeFusionInputs(const TArray<ASigilItem*>& InputSigils);
    
    // Funções de efeitos
    void PlayFusionVFX(const FSigilFusionInstance& FusionInstance, ESigilFusionState State);
    void PlayFusionSound(ESigilFusionState State);
    
    // Funções de notificação internas
    FSigilFusionNotification CreateNotificationForState(ESigilFusionState State, const FSigilFusionInstance& FusionInstance) const;
    void CreateNotificationWidget(const FSigilFusionNotification& Notification);
    void RemoveNotificationWidget(UUserWidget* Widget);

    // Funções de timer
    void OnFusionTimerComplete(FGuid FusionID);
    void ClearFusionTimer(const FGuid& FusionID);

public:
    // Funções de depuração
    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugPrintActiveFusions();

    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugStartTestFusion();

    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugCancelAllFusions();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void SetDebugMode(bool bEnabled);

protected:
    // Configurações de depuração
    UPROPERTY(EditAnywhere, Category = "Debug")
    bool bDebugMode;

    UPROPERTY(EditAnywhere, Category = "Debug")
    bool bLogFusionEvents;

    // Constantes do sistema
    static const float DEFAULT_FUSION_TIME;
    static const int32 MAX_FUSION_INSTANCES;
    static const float FUSION_PROGRESS_UPDATE_RATE;
    static const float NOTIFICATION_DISPLAY_TIME;
};