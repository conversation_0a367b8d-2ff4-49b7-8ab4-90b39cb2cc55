// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilReplicationManager.h
// Descrição: Gerenciador de replicação multiplayer para sistema de sígilos

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/NetSerialization.h"
#include "GameplayTagContainer.h"
#include "Net/UnrealNetwork.h"
#include "Net/Serialization/FastArraySerializer.h"
#include "Sigils/SigilItem.h"
#include "Engine/TimerHandle.h"
#include "GameFramework/PlayerController.h"
#include "AbilitySystemComponent.h"
#include "SigilReplicationManager.generated.h"

class USigilManagerComponent;
class ASigilItem;
class UAbilitySystemComponent;
class APlayerController;

// Estrutura para replicação de dados de sígilo
USTRUCT(BlueprintType)
struct AURACRON_API FSigilReplicationData
{
    GENERATED_BODY()

    UPROPERTY()
    int32 SigilID = -1;

    UPROPERTY()
    ESigilType SigilType = ESigilType::None;

    UPROPERTY()
    ESigilRarity Rarity = ESigilRarity::Common;

    UPROPERTY()
    int32 SlotIndex = -1;

    UPROPERTY()
    float FusionProgress = 0.0f;

    UPROPERTY()
    bool bIsEquipped = false;

    UPROPERTY()
    FGameplayTagContainer SigilTags;

    FSigilReplicationData()
    {
        SigilID = -1;
        SigilType = ESigilType::None;
        Rarity = ESigilRarity::Common;
        SlotIndex = -1;
        FusionProgress = 0.0f;
        bIsEquipped = false;
    }

    FSigilReplicationData(ASigilItem* Sigil, int32 InSlotIndex = -1);

    bool NetSerialize(FArchive& Ar, class UPackageMap* Map, bool& bOutSuccess);
};

template<>
struct TStructOpsTypeTraits<FSigilReplicationData> : public TStructOpsTypeTraitsBase2<FSigilReplicationData>
{
    enum
    {
        WithNetSerializer = true,
    };
};

// Estrutura para replicação de estatísticas do sistema
USTRUCT(BlueprintType)
struct AURACRON_API FSigilReplicationStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    int32 TotalSigils = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 EquippedSigils = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 UnlockedSlots = 0;

    UPROPERTY(BlueprintReadOnly)
    float LastReforgeTimestamp = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    bool bSystemActive = true;

    FSigilReplicationStats()
    {
        TotalSigils = 0;
        EquippedSigils = 0;
        UnlockedSlots = 0;
        LastReforgeTimestamp = 0.0f;
        bSystemActive = true;
    }
};

// Estrutura para dados de fusão replicados
USTRUCT(BlueprintType)
struct AURACRON_API FSigilFusionReplicationData
{
    GENERATED_BODY()

    UPROPERTY()
    int32 SigilID = -1;

    UPROPERTY()
    float FusionProgress = 0.0f;

    UPROPERTY()
    float FusionStartTime = 0.0f;

    UPROPERTY()
    bool bIsFusing = false;

    UPROPERTY()
    ESigilRarity TargetRarity = ESigilRarity::Common;

    FSigilFusionReplicationData()
    {
        SigilID = -1;
        FusionProgress = 0.0f;
        FusionStartTime = 0.0f;
        bIsFusing = false;
        TargetRarity = ESigilRarity::Common;
    }
};

// FastArray Item para replicação de dados de Sigil por jogador
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerDataEntry : public FFastArraySerializerItem
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    TArray<FSigilReplicationData> SigilData;

    FSigilPlayerDataEntry()
    {
        PlayerID = -1;
    }

    FSigilPlayerDataEntry(int32 InPlayerID, const TArray<FSigilReplicationData>& InSigilData)
        : PlayerID(InPlayerID), SigilData(InSigilData)
    {
    }

    void PreReplicatedRemove(const struct FSigilPlayerDataArray& InArraySerializer);
    void PostReplicatedAdd(const struct FSigilPlayerDataArray& InArraySerializer);
    void PostReplicatedChange(const struct FSigilPlayerDataArray& InArraySerializer);
};

// FastArray Serializer para dados de Sigil por jogador - UE 5.6 PRODUCTION READY
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerDataArray : public FFastArraySerializer
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FSigilPlayerDataEntry> Items;

    FSigilPlayerDataArray()
    {
        Items.Empty();
    }

    // Métodos para gerenciar dados de jogador
    void AddOrUpdatePlayerData(const FSigilPlayerDataEntry& Entry)
    {
        int32 ExistingIndex = Items.IndexOfByPredicate([&Entry](const FSigilPlayerDataEntry& Item)
        {
            return Item.PlayerID == Entry.PlayerID;
        });

        if (ExistingIndex != INDEX_NONE)
        {
            Items[ExistingIndex] = Entry;
        }
        else
        {
            Items.Add(Entry);
        }
    }

    void RemovePlayerData(int32 PlayerID)
    {
        Items.RemoveAll([PlayerID](const FSigilPlayerDataEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    FSigilPlayerDataEntry* FindPlayerData(int32 PlayerID)
    {
        return Items.FindByPredicate([PlayerID](const FSigilPlayerDataEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    // Funções helper para manter compatibilidade com TMap
    TArray<FSigilReplicationData>* Find(int32 PlayerID);
    const TArray<FSigilReplicationData>* Find(int32 PlayerID) const;
    TArray<FSigilReplicationData>& FindOrAdd(int32 PlayerID);
    void Add(int32 PlayerID, const TArray<FSigilReplicationData>& SigilData);
    void Remove(int32 PlayerID);
    bool Contains(int32 PlayerID) const;
    void Empty();

    // UE 5.6 MODERNA: FFastArraySerializer funciona automaticamente sem NetDeltaSerialize manual!
    // Baseado na implementação do GameplayAbilities que não usa NetDeltaSerialize explícito
};

// FastArray Item para replicação de estatísticas por jogador
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerStatsEntry : public FFastArraySerializerItem
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    FSigilReplicationStats Stats;

    FSigilPlayerStatsEntry()
    {
        PlayerID = -1;
    }

    FSigilPlayerStatsEntry(int32 InPlayerID, const FSigilReplicationStats& InStats)
        : PlayerID(InPlayerID), Stats(InStats)
    {
    }

    void PreReplicatedRemove(const struct FSigilPlayerStatsArray& InArraySerializer);
    void PostReplicatedAdd(const struct FSigilPlayerStatsArray& InArraySerializer);
    void PostReplicatedChange(const struct FSigilPlayerStatsArray& InArraySerializer);
};

// FastArray Serializer para estatísticas por jogador - UE 5.6 PRODUCTION READY
USTRUCT(BlueprintType)
struct AURACRON_API FSigilPlayerStatsArray : public FFastArraySerializer
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FSigilPlayerStatsEntry> Items;

    FSigilPlayerStatsArray()
    {
        Items.Empty();
    }

    // Métodos para gerenciar estatísticas de jogador
    void AddOrUpdatePlayerStats(const FSigilPlayerStatsEntry& Entry)
    {
        int32 ExistingIndex = Items.IndexOfByPredicate([&Entry](const FSigilPlayerStatsEntry& Item)
        {
            return Item.PlayerID == Entry.PlayerID;
        });

        if (ExistingIndex != INDEX_NONE)
        {
            Items[ExistingIndex] = Entry;
        }
        else
        {
            Items.Add(Entry);
        }
    }

    void RemovePlayerStats(int32 PlayerID)
    {
        Items.RemoveAll([PlayerID](const FSigilPlayerStatsEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    FSigilPlayerStatsEntry* FindPlayerStats(int32 PlayerID)
    {
        return Items.FindByPredicate([PlayerID](const FSigilPlayerStatsEntry& Entry)
        {
            return Entry.PlayerID == PlayerID;
        });
    }

    // Funções helper para manter compatibilidade com TMap
    FSigilReplicationStats* Find(int32 PlayerID);
    const FSigilReplicationStats* Find(int32 PlayerID) const;
    FSigilReplicationStats& FindOrAdd(int32 PlayerID);
    void Add(int32 PlayerID, const FSigilReplicationStats& Stats);
    void Remove(int32 PlayerID);
    bool Contains(int32 PlayerID) const;
    void Empty();

    // UE 5.6 MODERNA: FFastArraySerializer funciona automaticamente sem NetDeltaSerialize manual!
    // Baseado na implementação do GameplayAbilities que não usa NetDeltaSerialize explícito
};

// FastArray Item para replicação de fusões ativas por jogador
USTRUCT(BlueprintType)
struct AURACRON_API FSigilActiveFusionsEntry : public FFastArraySerializerItem
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    FString FusionID;

    UPROPERTY()
    TArray<FSigilFusionReplicationData> FusionData;

    FSigilActiveFusionsEntry()
    {
        PlayerID = -1;
    }

    FSigilActiveFusionsEntry(int32 InPlayerID, const TArray<FSigilFusionReplicationData>& InFusionData)
        : PlayerID(InPlayerID), FusionData(InFusionData)
    {
    }

    void PreReplicatedRemove(const struct FSigilActiveFusionsArray& InArraySerializer);
    void PostReplicatedAdd(const struct FSigilActiveFusionsArray& InArraySerializer);
    void PostReplicatedChange(const struct FSigilActiveFusionsArray& InArraySerializer);

    // Operador de comparação necessário para TArray::AddUnique
    bool operator==(const FSigilActiveFusionsEntry& Other) const
    {
        return PlayerID == Other.PlayerID && FusionID == Other.FusionID;
    }
};

// FastArray Serializer para fusões ativas por jogador - UE 5.6 PRODUCTION READY
USTRUCT(BlueprintType)
struct AURACRON_API FSigilActiveFusionsArray : public FFastArraySerializer
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FSigilActiveFusionsEntry> Items;

    FSigilActiveFusionsArray()
    {
        Items.Empty();
    }

    // Métodos para gerenciar fusões ativas
    void AddFusion(const FSigilActiveFusionsEntry& Entry)
    {
        Items.AddUnique(Entry);
    }

    void RemoveFusion(const FString& FusionID)
    {
        Items.RemoveAll([&FusionID](const FSigilActiveFusionsEntry& Entry)
        {
            return Entry.FusionID == FusionID;
        });
    }

    bool HasFusion(const FString& FusionID) const
    {
        return Items.ContainsByPredicate([&FusionID](const FSigilActiveFusionsEntry& Entry)
        {
            return Entry.FusionID == FusionID;
        });
    }

    // Funções helper para manter compatibilidade com TMap
    TArray<FSigilFusionReplicationData>* Find(int32 PlayerID);
    const TArray<FSigilFusionReplicationData>* Find(int32 PlayerID) const;
    TArray<FSigilFusionReplicationData>& FindOrAdd(int32 PlayerID);
    void Add(int32 PlayerID, const TArray<FSigilFusionReplicationData>& FusionData);
    void Remove(int32 PlayerID);
    bool Contains(int32 PlayerID) const;
    void Empty();

    // UE 5.6 MODERNA: FFastArraySerializer funciona automaticamente sem NetDeltaSerialize manual!
    // Baseado na implementação do GameplayAbilities que não usa NetDeltaSerialize explícito
};

// Estruturas para configurações avançadas do UE 5.6

// Enums para sistemas avançados
UENUM(BlueprintType)
enum class EPredictionType : uint8
{
    Movement,
    Ability,
    Sigil
};

UENUM(BlueprintType)
enum class ERollbackType : uint8
{
    Movement,
    Ability,
    Sigil
};

// Estruturas de dados para sistemas avançados
USTRUCT(BlueprintType)
struct AURACRON_API FPredictionEntry
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    int32 PlayerID = -1;

    UPROPERTY(BlueprintReadOnly)
    float Timestamp = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    FVector PredictedLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly)
    FVector PredictedVelocity = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly)
    int32 AbilityID = -1;

    UPROPERTY(BlueprintReadOnly)
    int32 SigilID = -1;

    UPROPERTY(BlueprintReadOnly)
    EPredictionType PredictionType = EPredictionType::Movement;
};

USTRUCT(BlueprintType)
struct AURACRON_API FRollbackEntry
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    int32 PlayerID = -1;

    UPROPERTY(BlueprintReadOnly)
    float Timestamp = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    FVector CorrectedLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly)
    FVector CorrectedVelocity = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly)
    int32 AbilityID = -1;

    UPROPERTY(BlueprintReadOnly)
    int32 SigilID = -1;

    UPROPERTY(BlueprintReadOnly)
    bool bShouldCancelAbility = false;

    UPROPERTY(BlueprintReadOnly)
    ERollbackType RollbackType = ERollbackType::Movement;
};

// Configurações de predição de cliente
USTRUCT(BlueprintType)
struct AURACRON_API FClientPredictionSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableMovementPrediction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableAbilityPrediction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableSigilPrediction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float MaxPredictionTime = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.01", ClampMax = "0.5"))
    float PredictionTolerance = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "30", ClampMax = "120"))
    int32 PredictionBufferSize = 60;
};

// Configurações de rollback networking
USTRUCT(BlueprintType)
struct AURACRON_API FRollbackSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "30", ClampMax = "120"))
    int32 MaxRollbackFrames = 60;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.01", ClampMax = "0.2"))
    float RollbackTolerance = 0.05f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableRollbackLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "60", ClampMax = "240"))
    int32 RollbackBufferSize = 120;
};

// Delegados para eventos de replicação
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilEquipped, int32, PlayerID, const FSigilReplicationData&, SigilData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilUnequipped, int32, PlayerID, int32, SlotIndex);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilReplicationFusionStarted, int32, PlayerID, const FSigilFusionReplicationData&, FusionData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilReplicationFusionCompleted, int32, PlayerID, const FSigilReplicationData&, NewSigilData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigilSystemStatsUpdated, const FSigilReplicationStats&, Stats);

// Estruturas para sistemas avançados de replicação - UE 5.6 APIs modernas
USTRUCT(BlueprintType)
struct AURACRON_API FPlayerValidationData
{
    GENERATED_BODY()

    UPROPERTY()
    float LastMovementValidation = 0.0f;

    UPROPERTY()
    FVector LastValidPosition = FVector::ZeroVector;

    UPROPERTY()
    float LastAbilityValidation = 0.0f;

    UPROPERTY()
    int32 ValidationFailures = 0;

    UPROPERTY()
    bool bIsValidated = true;

    FPlayerValidationData()
    {
        LastMovementValidation = 0.0f;
        LastValidPosition = FVector::ZeroVector;
        LastAbilityValidation = 0.0f;
        ValidationFailures = 0;
        bIsValidated = true;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FDynamicObjectState
{
    GENERATED_BODY()

    UPROPERTY()
    int32 ObjectID = -1;

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY()
    FVector Velocity = FVector::ZeroVector;

    UPROPERTY()
    float LastUpdateTime = 0.0f;

    UPROPERTY()
    bool bIsActive = true;

    FDynamicObjectState()
    {
        ObjectID = -1;
        Position = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Velocity = FVector::ZeroVector;
        LastUpdateTime = 0.0f;
        bIsActive = true;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FEnvironmentState
{
    GENERATED_BODY()

    UPROPERTY()
    int32 EnvironmentID = -1;

    UPROPERTY()
    FString EnvironmentName;

    UPROPERTY()
    bool bIsActive = false;

    UPROPERTY()
    float TransitionProgress = 0.0f;

    UPROPERTY()
    TArray<int32> ActivePortals;

    UPROPERTY()
    FGameplayTagContainer EnvironmentTags;

    FEnvironmentState()
    {
        EnvironmentID = -1;
        EnvironmentName = TEXT("");
        bIsActive = false;
        TransitionProgress = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FTeamState
{
    GENERATED_BODY()

    UPROPERTY()
    int32 TeamID = -1;

    UPROPERTY()
    TArray<int32> PlayerIDs;

    UPROPERTY()
    int32 TeamScore = 0;

    UPROPERTY()
    float TeamGold = 0.0f;

    UPROPERTY()
    int32 ObjectivesControlled = 0;

    UPROPERTY()
    bool bIsWinning = false;

    FTeamState()
    {
        TeamID = -1;
        TeamScore = 0;
        TeamGold = 0.0f;
        ObjectivesControlled = 0;
        bIsWinning = false;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FAntiCheatEvent
{
    GENERATED_BODY()

    UPROPERTY()
    int32 PlayerID = -1;

    UPROPERTY()
    FString EventType;

    UPROPERTY()
    float Timestamp = 0.0f;

    UPROPERTY()
    FString Details;

    UPROPERTY()
    int32 Severity = 0;

    FAntiCheatEvent()
    {
        PlayerID = -1;
        EventType = TEXT("");
        Timestamp = 0.0f;
        Details = TEXT("");
        Severity = 0;
    }
};

// Structs para sistemas avançados - UE 5.6 APIs modernas
USTRUCT(BlueprintType)
struct AURACRON_API FRollbackState
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY()
    float Timestamp = 0.0f;

    UPROPERTY()
    int32 FrameNumber = 0;

    FRollbackState()
    {
        Position = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Timestamp = 0.0f;
        FrameNumber = 0;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FPredictionSettings
{
    GENERATED_BODY()

    UPROPERTY()
    float MaxPredictionTime = 0.5f;

    UPROPERTY()
    float RollbackTolerance = 0.1f;

    UPROPERTY()
    int32 MaxPredictionFrames = 30;

    FPredictionSettings()
    {
        MaxPredictionTime = 0.5f;
        RollbackTolerance = 0.1f;
        MaxPredictionFrames = 30;
    }
};

// Struct wrapper para TArray em TMap - UE 5.6 compatível
USTRUCT(BlueprintType)
struct AURACRON_API FRollbackStateArray
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FRollbackState> States;

    FRollbackStateArray()
    {
        States.Empty();
    }
};

// Struct para dados de predição do cliente - UE 5.6 APIs modernas
USTRUCT(BlueprintType)
struct AURACRON_API FClientPredictionData
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FVector> PredictionHistory;

    UPROPERTY()
    TArray<FRollbackState> RollbackStates;

    UPROPERTY()
    float LastPredictionTime = 0.0f;

    FClientPredictionData()
    {
        LastPredictionTime = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FReplicationMetrics
{
    GENERATED_BODY()

    UPROPERTY()
    float CurrentBandwidth = 0.0f;

    UPROPERTY()
    float AverageLatency = 0.0f;

    UPROPERTY()
    int32 ReplicationRate = 0;

    UPROPERTY()
    int32 DroppedPackets = 0;

    UPROPERTY()
    float CompressionRatio = 1.0f;

    UPROPERTY()
    float StartTime = 0.0f;

    UPROPERTY()
    int32 TotalReplications = 0;

    UPROPERTY()
    float TotalBandwidth = 0.0f;

    UPROPERTY()
    int32 AntiCheatEvents = 0;

    UPROPERTY()
    int32 RollbackEvents = 0;

    FReplicationMetrics()
    {
        CurrentBandwidth = 0.0f;
        AverageLatency = 0.0f;
        ReplicationRate = 0;
        DroppedPackets = 0;
        CompressionRatio = 1.0f;
        StartTime = 0.0f;
        TotalReplications = 0;
        TotalBandwidth = 0.0f;
        AntiCheatEvents = 0;
        RollbackEvents = 0;
    }
};

// Estruturas de configuração para sistemas avançados - UE 5.6 APIs modernas
USTRUCT(BlueprintType)
struct AURACRON_API FAdvancedInterestSettings
{
    GENERATED_BODY()

    UPROPERTY()
    int32 MaxRelevantActors = 200;

    UPROPERTY()
    float RelevanceUpdateFrequency = 10.0f;

    UPROPERTY()
    float SpatialHashGridSize = 1000.0f;

    UPROPERTY()
    bool bUseDistanceCulling = true;

    UPROPERTY()
    bool bUseFrustumCulling = true;

    UPROPERTY()
    bool bUseGameplayRelevance = true;

    UPROPERTY()
    bool bUsePriorityScaling = true;

    FAdvancedInterestSettings()
    {
        MaxRelevantActors = 200;
        RelevanceUpdateFrequency = 10.0f;
        SpatialHashGridSize = 1000.0f;
        bUseDistanceCulling = true;
        bUseFrustumCulling = true;
        bUseGameplayRelevance = true;
        bUsePriorityScaling = true;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FAntiCheatSettings
{
    GENERATED_BODY()

    UPROPERTY()
    bool bValidateMovement = true;

    UPROPERTY()
    bool bValidateAbilities = true;

    UPROPERTY()
    bool bValidateResources = true;

    UPROPERTY()
    bool bValidateSigilActions = true;

    UPROPERTY()
    float MovementTolerance = 0.1f;

    UPROPERTY()
    float AbilityTolerance = 0.05f;

    UPROPERTY()
    float MaxMovementSpeedTolerance = 1.2f;

    UPROPERTY()
    float ValidationFrequency = 20.0f;

    UPROPERTY()
    int32 MaxValidationFailures = 5;

    UPROPERTY()
    int32 SuspiciousActionThreshold = 5;

    UPROPERTY()
    int32 AntiCheatLogLevel = 2;

    FAntiCheatSettings()
    {
        bValidateMovement = true;
        bValidateAbilities = true;
        bValidateResources = true;
        bValidateSigilActions = true;
        MovementTolerance = 0.1f;
        AbilityTolerance = 0.05f;
        MaxMovementSpeedTolerance = 1.2f;
        ValidationFrequency = 20.0f;
        MaxValidationFailures = 5;
        SuspiciousActionThreshold = 5;
        AntiCheatLogLevel = 2;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FDynamicObjectSettings
{
    GENERATED_BODY()

    UPROPERTY()
    int32 MaxDynamicObjects = 100;

    UPROPERTY()
    float UpdateFrequency = 30.0f;

    UPROPERTY()
    float PredictionTime = 0.1f;

    UPROPERTY()
    bool bUseInterpolation = true;

    UPROPERTY()
    bool bUsePrediction = true;

    UPROPERTY()
    bool bReplicateFluxoPrismal = true;

    UPROPERTY()
    bool bReplicateTrilhoEffects = true;

    UPROPERTY()
    bool bReplicateEnvironmentTransitions = true;

    UPROPERTY()
    bool bReplicateTerrainChanges = true;

    UPROPERTY()
    float FluxoPrismalUpdateFrequency = 30.0f;

    UPROPERTY()
    float TrilhoEffectRadius = 1500.0f;

    UPROPERTY()
    float EnvironmentTransitionRadius = 3000.0f;

    UPROPERTY()
    float TerrainChangeRadius = 2000.0f;

    FDynamicObjectSettings()
    {
        MaxDynamicObjects = 100;
        UpdateFrequency = 30.0f;
        PredictionTime = 0.1f;
        bUseInterpolation = true;
        bUsePrediction = true;
        bReplicateFluxoPrismal = true;
        bReplicateTrilhoEffects = true;
        bReplicateEnvironmentTransitions = true;
        bReplicateTerrainChanges = true;
        FluxoPrismalUpdateFrequency = 30.0f;
        TrilhoEffectRadius = 1500.0f;
        EnvironmentTransitionRadius = 3000.0f;
        TerrainChangeRadius = 2000.0f;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FTelemetrySettings
{
    GENERATED_BODY()

    UPROPERTY()
    bool bEnableTelemetry = true;

    UPROPERTY()
    float CollectionInterval = 1.0f;

    UPROPERTY()
    int32 MaxHistorySize = 1000;

    UPROPERTY()
    bool bSendToServer = true;

    UPROPERTY()
    bool bCollectReplicationStats = true;

    UPROPERTY()
    bool bCollectBandwidthStats = true;

    UPROPERTY()
    bool bCollectLatencyStats = true;

    UPROPERTY()
    bool bCollectAntiCheatStats = true;

    UPROPERTY()
    float TelemetryUpdateFrequency = 5.0f;

    UPROPERTY()
    int32 TelemetryHistorySize = 300;

    FTelemetrySettings()
    {
        bEnableTelemetry = true;
        CollectionInterval = 1.0f;
        MaxHistorySize = 1000;
        bSendToServer = true;
        bCollectReplicationStats = true;
        bCollectBandwidthStats = true;
        bCollectLatencyStats = true;
        bCollectAntiCheatStats = true;
        TelemetryUpdateFrequency = 5.0f;
        TelemetryHistorySize = 300;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONReplicationSettings
{
    GENERATED_BODY()

    UPROPERTY()
    float SigilReplicationRate = 20.0f;

    UPROPERTY()
    bool bUseCompression = true;

    UPROPERTY()
    bool bUseDeltaCompression = true;

    UPROPERTY()
    int32 MaxSigilsPerPlayer = 10;

    UPROPERTY()
    bool bReplicateAegisEffects = true;

    UPROPERTY()
    bool bReplicateRuinEffects = true;

    UPROPERTY()
    bool bReplicateVesperEffects = true;

    UPROPERTY()
    bool bReplicateEnvironmentStates = true;

    UPROPERTY()
    bool bReplicateTrilhoStates = true;

    UPROPERTY()
    bool bReplicateFluxoPrismalFlow = true;

    UPROPERTY()
    float SigilEffectReplicationPriority = 8.0f;

    UPROPERTY()
    float EnvironmentReplicationPriority = 9.0f;

    UPROPERTY()
    float TrilhoReplicationPriority = 7.0f;

    UPROPERTY()
    float FluxoPrismalReplicationPriority = 6.0f;

    FAURACRONReplicationSettings()
    {
        SigilReplicationRate = 20.0f;
        bUseCompression = true;
        bUseDeltaCompression = true;
        MaxSigilsPerPlayer = 10;
        bReplicateAegisEffects = true;
        bReplicateRuinEffects = true;
        bReplicateVesperEffects = true;
        bReplicateEnvironmentStates = true;
        bReplicateTrilhoStates = true;
        bReplicateFluxoPrismalFlow = true;
        SigilEffectReplicationPriority = 8.0f;
        EnvironmentReplicationPriority = 9.0f;
        TrilhoReplicationPriority = 7.0f;
        FluxoPrismalReplicationPriority = 6.0f;
    }
};

USTRUCT(BlueprintType)
struct AURACRON_API FSpatialHashGrid
{
    GENERATED_BODY()

    UPROPERTY()
    float GridSize = 1000.0f;

    UPROPERTY()
    int32 GridWidth = 32;

    UPROPERTY()
    int32 GridHeight = 32;

    UPROPERTY()
    TArray<int32> GridCells;

    FSpatialHashGrid()
    {
        GridSize = 1000.0f;
        GridWidth = 32;
        GridHeight = 32;
    }

    void Initialize(float InGridSize)
    {
        GridSize = InGridSize;
        GridCells.Empty();
        GridCells.SetNum(GridWidth * GridHeight);
    }
};

/**
 * Gerenciador de replicação para o sistema de sígilos em ambiente MOBA 5x5
 * Suporta até 10 jogadores simultâneos com otimizações de rede
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API USigilReplicationManager : public UActorComponent
{
    GENERATED_BODY()

public:
    USigilReplicationManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    // Configuração de replicação
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    int32 MaxPlayers = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    float ReplicationFrequency = 20.0f; // Hz

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    bool bOptimizeForMOBA = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replication")
    float MaxReplicationDistance = 5000.0f; // Unreal Units

    // Dados replicados usando FFastArraySerializer
    UPROPERTY(Replicated)
    FSigilPlayerDataArray PlayerSigilDataArray;
    
    UPROPERTY(Replicated)
    FSigilPlayerStatsArray PlayerSystemStatsArray;
    
    UPROPERTY(Replicated)
    FSigilActiveFusionsArray ActiveFusionsArray;

    // Eventos de replicação
    UPROPERTY(BlueprintAssignable)
    FOnSigilEquipped OnSigilEquipped;

    UPROPERTY(BlueprintAssignable)
    FOnSigilUnequipped OnSigilUnequipped;

    UPROPERTY(BlueprintAssignable)
    FOnSigilReplicationFusionStarted OnSigilFusionStarted;

    UPROPERTY(BlueprintAssignable)
    FOnSigilReplicationFusionCompleted OnSigilFusionCompleted;

    UPROPERTY(BlueprintAssignable)
    FOnSigilSystemStatsUpdated OnSigilSystemStatsUpdated;

    // Funções principais de replicação
    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void RegisterPlayer(int32 PlayerID, USigilManagerComponent* SigilManager);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void UnregisterPlayer(int32 PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateSigilEquip(int32 PlayerID, ASigilItem* Sigil, int32 SlotIndex);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateSigilUnequip(int32 PlayerID, int32 SlotIndex);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateFusionStart(int32 PlayerID, ASigilItem* Sigil);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void ReplicateFusionComplete(int32 PlayerID, ASigilItem* OldSigil, ASigilItem* NewSigil);

    UFUNCTION(BlueprintCallable, Category = "Sigil Replication")
    void UpdatePlayerStats(int32 PlayerID, const FSigilReplicationStats& Stats);

    // Funções de consulta
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    TArray<FSigilReplicationData> GetPlayerSigils(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    FSigilReplicationStats GetPlayerStats(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    TArray<FSigilFusionReplicationData> GetPlayerActiveFusions(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    bool IsPlayerRegistered(int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sigil Replication")
    TArray<int32> GetRegisteredPlayers() const;

    // Otimizações de rede
    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void SetReplicationPriority(int32 PlayerID, float Priority);

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void OptimizeReplicationForDistance(AActor* ViewerActor);

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void EnableMOBAOptimizations(bool bEnable);

    // RPCs para comunicação cliente-servidor
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerEquipSigil(int32 PlayerID, int32 SigilID, int32 SlotIndex);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerUnequipSigil(int32 PlayerID, int32 SlotIndex);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerStartFusion(int32 PlayerID, int32 SigilID);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerForceFusion(int32 PlayerID, int32 SigilID);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerReforge(int32 PlayerID);

    // Multicast RPCs para notificações
    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyEquip(int32 PlayerID, const FSigilReplicationData& SigilData);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyUnequip(int32 PlayerID, int32 SlotIndex);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyFusionStart(int32 PlayerID, const FSigilFusionReplicationData& FusionData);

    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyFusionComplete(int32 PlayerID, const FSigilReplicationData& NewSigilData);

    // Funções de depuração
    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugPrintReplicationStats();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DebugSimulateNetworkLag(float LagSeconds);

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DebugForceFullReplication();

protected:
    // Funções de callback de replicação
    UFUNCTION()
    void OnRep_PlayerSigilData();

    UFUNCTION()
    void OnRep_PlayerSystemStats();

    UFUNCTION()
    void OnRep_ActiveFusions();

    // Funções internas
    void InitializeReplicationSettings();
    void CleanupPlayerData(int32 PlayerID);
    bool ValidatePlayerID(int32 PlayerID) const;
    bool ShouldReplicateToClient(int32 PlayerID, AActor* ClientActor) const;
    void UpdateReplicationFrequency();
    void ProcessPendingReplications();

    // Dados internos
    UPROPERTY()
    TMap<int32, USigilManagerComponent*> RegisteredManagers;

    UPROPERTY()
    TMap<int32, float> PlayerReplicationPriorities;

    UPROPERTY()
    TArray<FSigilReplicationData> PendingReplications;

    // Timers
    FTimerHandle ReplicationTimerHandle;
    FTimerHandle OptimizationTimerHandle;

    // Estatísticas de rede
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalReplicationsSent = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalReplicationsReceived = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageReplicationSize = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float NetworkBandwidthUsed = 0.0f;

    // Configurações avançadas do UE 5.6
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableClientPrediction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableRollbackNetworking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableAdvancedDeltaCompression = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableAdvancedInterestManagement = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableAntiCheatValidation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableEOSIntegration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableNetworkTelemetry = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableDynamicObjectReplication = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableEnvironmentReplication = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableTeamStateReplication = true;

    // Configurações específicas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Client Prediction")
    FClientPredictionSettings ClientPredictionSettings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rollback")
    FRollbackSettings RollbackSettings;

private:
    // Dados internos para sistemas avançados
    TArray<FPredictionEntry> PredictionBuffer;
    TArray<FRollbackEntry> RollbackBuffer;

    // Timers para sistemas avançados
    FTimerHandle PredictionTimerHandle;
    FTimerHandle RollbackTimerHandle;
    FTimerHandle AntiCheatTimerHandle;
    FTimerHandle TelemetryTimerHandle;
    FTimerHandle InterestManagementTimerHandle;
    FTimerHandle DynamicObjectTimerHandle;

    // Dados de validação e métricas
    TMap<int32, struct FPlayerValidationData> PlayerValidationData;
    TMap<int32, struct FDynamicObjectState> DynamicObjectStates;
    TMap<int32, struct FEnvironmentState> EnvironmentStates;
    TMap<int32, struct FTeamState> TeamStates;

    // Históricos para telemetria
    TArray<float> BandwidthHistory;
    TArray<float> LatencyHistory;
    TArray<int32> ReplicationHistory;
    TArray<struct FAntiCheatEvent> AntiCheatEventHistory;

    // Métricas atuais
    struct FReplicationMetrics CurrentReplicationMetrics;

    // Configurações específicas (estruturas definidas no .cpp)
    struct FAdvancedInterestSettings AdvancedInterestSettings;
    struct FAntiCheatSettings AntiCheatSettings;
    struct FDynamicObjectSettings DynamicObjectSettings;
    struct FTelemetrySettings TelemetrySettings;
    struct FAURACRONReplicationSettings AURACRONReplicationSettings;

    // Sistema de spatial hash para interest management
    struct FSpatialHashGrid SpatialHashGrid;

    // Funções de inicialização dos sistemas avançados
    void InitializeAdvancedReplicationSystems();
    void InitializeClientPrediction();
    void InitializeRollbackSystem();
    void InitializeAdvancedInterestManagement();
    void InitializeAntiCheatSystem();
    void InitializeDynamicObjectReplication();
    void InitializeTelemetrySystem();
    void InitializeEOSIntegration();
    void InitializeAURACRONGameplayTags();
    void InitializeReplicationMetrics();

    // Funções de finalização
    void ShutdownAdvancedReplicationSystems();
    void ShutdownClientPrediction();
    void ShutdownRollbackSystem();
    void ShutdownAntiCheatSystem();
    void ShutdownTelemetrySystem();
    void ShutdownEOSIntegration();

    // Funções de processamento
    void ProcessClientPrediction();
    void ProcessMovementPrediction(int32 PlayerID, USigilManagerComponent* Manager);
    void ProcessAbilityPrediction(int32 PlayerID, USigilManagerComponent* Manager);
    void ProcessSigilPrediction(int32 PlayerID, USigilManagerComponent* Manager);
    void ProcessRollbackCorrections();
    void ApplyRollbackCorrection(const FRollbackEntry& RollbackEntry);
    void ApplyMovementRollback(const FRollbackEntry& RollbackEntry, USigilManagerComponent* Manager);
    void ApplyAbilityRollback(const FRollbackEntry& RollbackEntry, USigilManagerComponent* Manager);
    void ApplySigilRollback(const FRollbackEntry& RollbackEntry, USigilManagerComponent* Manager);
    void ProcessAntiCheatValidation();
    void ProcessDynamicObjectReplication();
    void UpdateAdvancedInterestManagement();
    void CollectTelemetryData();

    // Constantes de otimização
    static const float MOBA_OPTIMIZATION_INTERVAL;
    static const float MAX_REPLICATION_DISTANCE_SQUARED;
    static const int32 MAX_REPLICATIONS_PER_FRAME;
    static const float PRIORITY_UPDATE_INTERVAL;

private:
    // ============================================================================
    // PROPRIEDADES ADICIONAIS PARA SISTEMAS AVANÇADOS - UE 5.6 APIs MODERNAS
    // ============================================================================

    // Dados de predição do cliente
    TMap<int32, FClientPredictionData> ClientPredictionData;

    // Histórico de rollback - UE 5.6 compatível com struct wrapper
    TMap<int32, FRollbackStateArray> RollbackHistory;

    // Timestamps para controle
    float LastDynamicObjectUpdate = 0.0f;
    float LastTelemetryUpdate = 0.0f;

    // ============================================================================
    // MÉTODOS PRIVADOS PARA SISTEMAS AVANÇADOS - UE 5.6 APIs MODERNAS
    // ============================================================================

    // Métodos de validação
    void ValidatePlayerMovement(int32 PlayerID, FPlayerValidationData& ValidationData);
    void ValidatePlayerAbilities(int32 PlayerID, FPlayerValidationData& ValidationData);
    void ValidateAbilityCooldowns(UAbilitySystemComponent* ASC, FPlayerValidationData& ValidationData);
    void HandleAntiCheatViolation(int32 PlayerID, const FString& Reason);

    // Métodos de processamento
    void CleanupInactiveDynamicObjects();
    void UpdateSpatialHashGrid();
    void ProcessDistanceCulling(APlayerController* PlayerController, const FVector& PlayerLocation);
    void ProcessFrustumCulling(APlayerController* PlayerController, const FVector& PlayerLocation);
    void ProcessGameplayRelevance(APlayerController* PlayerController);

    // Métodos de telemetria
    float CalculateCurrentBandwidth();
    float CalculateAverageLatency();
    int32 CountRollbackEvents();
    void SendTelemetryToServer();
};

