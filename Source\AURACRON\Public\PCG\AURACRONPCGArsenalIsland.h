// AURACRONPCGArsenalIsland.h
// Definição da classe AArsenalIsland para o sistema Prismal Flow

#pragma once

#include "CoreMinimal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCG/AURACRONPCGTypes.h"
#include "GameplayEffect.h"
#include "ActiveGameplayEffectHandle.h"
#include "Components/StaticMeshComponent.h"
#include "NiagaraComponent.h"
#include "AURACRONPCGArsenalIsland.generated.h"

/**
 * Implementação específica da Arsenal Island
 * Ilha com plataformas de armas, depósitos de munição e rampas táticas
 * 
 * Conforme documentação:
 * - Localização: Próximas a pontos de transição entre ambientes
 * - Características: Upgrades de armas, potencializadores de habilidades, buffs temporários
 * - Valor Estratégico: Oportunidades de power spike
 */
UCLASS()
class AURACRON_API AArsenalIsland : public APrismalFlowIsland
{
    GENERATED_BODY()
    
public:
    AArsenalIsland();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult) override;
    
    // Concede bônus de armas ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Arsenal Island")
    void GrantWeaponBonus(AActor* TargetActor);
    
    // Concede munição especial ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Arsenal Island")
    void GrantSpecialAmmo(AActor* TargetActor);
    
    // Concede potencializador de habilidades ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Arsenal Island")
    void GrantAbilityBoost(AActor* TargetActor);
    
    // Verifica se esta ilha está próxima a um ponto de transição entre ambientes
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Arsenal Island")
    bool IsNearEnvironmentTransition() const;
    
    // Define se esta ilha está próxima a um ponto de transição entre ambientes
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Arsenal Island")
    void SetNearEnvironmentTransition(bool bIsNearTransition);
    
protected:
    // Remove os efeitos de bônus
    UFUNCTION()
    void RemoveArsenalEffects(AActor* TargetActor);
    
    // Torre central do arsenal
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    UStaticMeshComponent* ArsenalTower;
    
    // Plataformas de armas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    TArray<UStaticMeshComponent*> WeaponPlatforms;
    
    // Depósitos de munição
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    TArray<UStaticMeshComponent*> AmmoDepots;
    
    // Rampas táticas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    TArray<UStaticMeshComponent*> TacticalRamps;
    
    // Indica se esta ilha está próxima a um ponto de transição entre ambientes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Arsenal Island")
    bool bIsNearEnvironmentTransition;
    
    // Tipos de ambiente entre os quais esta ilha faz a transição
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Arsenal Island", meta = (EditCondition = "bIsNearEnvironmentTransition"))
    TArray<EAURACRONEnvironmentType> TransitionEnvironments;
    
    // Intensidade do bônus de armas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    float WeaponBonusIntensity;
    
    // Duração do bônus de armas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    float WeaponBonusDuration;
    
    // Quantidade de munição especial
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    int32 SpecialAmmoCount;
    
    // Intensidade do potencializador de habilidades
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    float AbilityBoostIntensity;
    
    // Duração do potencializador de habilidades
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    float AbilityBoostDuration;
    
    // Efeito visual do bônus de armas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Arsenal Island")
    UNiagaraSystem* WeaponBonusVisualEffect;
    
    // Efeito de gameplay para bônus de armas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    TSubclassOf<UGameplayEffect> WeaponBonusEffect;
    
    // Efeito de gameplay para munição especial
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    TSubclassOf<UGameplayEffect> SpecialAmmoEffect;
    
    // Efeito de gameplay para potencializador de habilidades
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Arsenal Island")
    TSubclassOf<UGameplayEffect> AbilityBoostEffect;
    
    // Efeito visual do potencializador de habilidades
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Arsenal Island")
    UNiagaraSystem* AbilityBoostVisualEffect;
    
    // Mapa de efeitos ativos por ator (não serializado devido a limitações do FActiveGameplayEffectHandle)
    // Usado para rastrear efeitos de arsenal aplicados aos jogadores na ilha
    TMap<TWeakObjectPtr<AActor>, TArray<FActiveGameplayEffectHandle>> ActiveArsenalEffects;

    // Lista de atores afetados (alias para compatibilidade)
    TArray<TWeakObjectPtr<AActor>> AffectedActors;
    
    // Tempo acumulado para efeitos visuais
    UPROPERTY()
    float AccumulatedTime;

    // Multiplicador de potencialização de habilidades
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Arsenal Island")
    float AbilityBoostMultiplier;

    // Plataforma de armas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    TObjectPtr<UStaticMeshComponent> WeaponPlatform;

    // Efeito de energia da plataforma
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    TObjectPtr<UNiagaraComponent> PlatformEnergyEffect;

    // Depósitos de munição
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Arsenal Island")
    TArray<TObjectPtr<UStaticMeshComponent>> AmmoDeposits;
    
    virtual void UpdateIslandVisuals() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};