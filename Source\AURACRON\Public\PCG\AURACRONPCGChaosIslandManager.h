// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "NiagaraFunctionLibrary.h"
#include "Sound/SoundBase.h"
#include "AURACRONPCGChaosIslandManager.generated.h"

// Forward declarations para UE 5.6
class UNiagaraComponent;
class UAudioComponent;
class UPointLightComponent;

// Delegates para eventos do sistema
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChaosManagerFullyInitialized);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChaosIslandsListChanged, const TArray<AAURACRONPCGChaosIsland*>&, Islands);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapPhaseUpdated, EAURACRONMapPhase, NewPhase);

/**
 * Gerenciador de Ilhas Caos
 * Responsável por coordenar a geração, posicionamento e comportamento das Ilhas Caos
 * Garante que as ilhas estejam corretamente posicionadas em pontos de interseção do Fluxo
 * Implementa os requisitos do GDD:
 * - Localização: Em pontos de interseção do Fluxo
 * - Características: Perigos ambientais, recompensas de alto risco, terreno instável
 * - Valor Estratégico: Itens que mudam o jogo com risco significativo
 */
UCLASS()
class AURACRON_API AAURACRONPCGChaosIslandManager : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    AAURACRONPCGChaosIslandManager();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;

    // Called when the actor is being destroyed
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // Networking support
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    
    /** Inicializar o gerenciador com referência ao fluxo prismal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void Initialize(AAURACRONPCGPrismalFlow* InPrismalFlow);
    
    /** Gerar as ilhas caos nos pontos de interseção do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void GenerateChaosIslands();
    
    /** Atualizar as ilhas caos para a fase atual do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Obter todas as ilhas caos - CORRIGIDO: usar AAURACRONPCGChaosIsland */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    TArray<AAURACRONPCGChaosIsland*> GetAllChaosIslands() const { return ChaosIslands; }

    /** Obter todos os portais caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    TArray<AAURACRONPCGChaosPortal*> GetAllChaosPortals() const { return ChaosPortals; }
    
    /** Verificar se um ponto está em uma interseção do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    bool IsPointAtFlowIntersection(const FVector& Point, float Tolerance = 500.0f) const;
    
    /** Encontrar todos os pontos de interseção do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    TArray<FVector> FindAllFlowIntersections() const;
    
    /** Ativar/desativar todas as ilhas caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void SetAllChaosIslandsActive(bool bActive);
    
    /** Ativar/desativar todos os portais caos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosIsland")
    void SetAllChaosPortalsActive(bool bActive, float Duration = 0.0f, float Intensity = 1.0f);

    // ========================================
    // DELEGATES PARA EVENTOS DO SISTEMA
    // ========================================

    /** Evento disparado quando o manager está totalmente inicializado */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON|ChaosIsland|Events")
    FOnChaosManagerFullyInitialized OnChaosManagerFullyInitialized;

    /** Evento disparado quando a lista de ilhas muda */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON|ChaosIsland|Events")
    FOnChaosIslandsListChanged OnChaosIslandsListChanged;

    /** Evento disparado quando a fase do mapa muda */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON|ChaosIsland|Events")
    FOnMapPhaseUpdated OnMapPhaseUpdated;

    // ========================================
    // RPCs PARA NETWORKING
    // ========================================

    /** RPC para spawnar ilha no servidor */
    UFUNCTION(Server, Reliable, WithValidation, BlueprintCallable, Category = "AURACRON|ChaosIsland|Network")
    void ServerSpawnChaosIsland(const FVector& Location);

    /** RPC para destruir todas as ilhas no servidor */
    UFUNCTION(Server, Reliable, WithValidation, BlueprintCallable, Category = "AURACRON|ChaosIsland|Network")
    void ServerDestroyAllChaosIslands();

    /** RPC para notificar clientes sobre nova ilha */
    UFUNCTION(Client, Reliable, Category = "AURACRON|ChaosIsland|Network")
    void ClientNotifyIslandSpawned(AAURACRONPCGChaosIsland* Island, const FVector& Location);

    /** RPC para notificar clientes sobre destruição de ilhas */
    UFUNCTION(Client, Reliable, Category = "AURACRON|ChaosIsland|Network")
    void ClientNotifyAllIslandsDestroyed();

protected:
    /** Referência ao fluxo prismal */
    UPROPERTY(Replicated)
    AAURACRONPCGPrismalFlow* PrismalFlow;

    /** Lista de ilhas caos - CORRIGIDO: usar AAURACRONPCGChaosIsland */
    UPROPERTY(Replicated)
    TArray<AAURACRONPCGChaosIsland*> ChaosIslands;

    /** Lista de portais caos */
    UPROPERTY(Replicated)
    TArray<AAURACRONPCGChaosPortal*> ChaosPortals;
    
    /** Número máximo de ilhas caos (conforme GDD) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|ChaosIsland")
    int32 MaxChaosIslands;

    /** Classe da ilha caos para spawn - CORRIGIDO: usar AAURACRONPCGChaosIsland */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    TSubclassOf<AAURACRONPCGChaosIsland> ChaosIslandClass;

    /** Classe do portal caos para spawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    TSubclassOf<AAURACRONPCGChaosPortal> ChaosPortalClass;

    /** Distância mínima entre ilhas caos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland")
    float MinDistanceBetweenIslands;

    /** Auto-spawnar portais quando criar ilhas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|ChaosIsland")
    bool bAutoSpawnPortals;

    /** Fase atual do mapa */
    UPROPERTY(Replicated, ReplicatedUsing = OnRep_MapPhaseChanged)
    EAURACRONMapPhase CurrentMapPhase;

    // ========================================
    // COMPONENTES MODERNOS UE 5.6
    // ========================================

    /** Componente de áudio para efeitos ambiente */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosIsland|Components")
    UAudioComponent* AudioComponent;

    /** Componente de VFX global usando Niagara */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosIsland|Components")
    UNiagaraComponent* GlobalVFXComponent;

    /** Componente de iluminação ambiente */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosIsland|Components")
    UPointLightComponent* AmbientLightComponent;

    // ========================================
    // SISTEMA DE CACHE E PERFORMANCE
    // ========================================

    /** Cache de pontos de interseção para performance */
    UPROPERTY()
    TArray<FVector> CachedIntersectionPoints;

    /** Timestamp da última calculação de interseções */
    UPROPERTY()
    float LastIntersectionCalculationTime;

    /** Duração de validade do cache de interseções */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosIsland|Performance")
    float IntersectionCacheValidDuration;

    /** Indica se o sistema está totalmente inicializado */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|ChaosIsland|State")
    bool bSystemFullyInitialized;

    // ========================================
    // SISTEMA DE CARREGAMENTO ASSÍNCRONO
    // ========================================

    /** Referência ao StreamableManager para carregamento assíncrono */
    FStreamableManager* StreamableManager;

    // ========================================
    // HANDLES DE TIMERS
    // ========================================

    /** Handle do timer de atualização de efeitos */
    FTimerHandle EffectsUpdateTimerHandle;

    /** Handle do timer de limpeza de cache */
    FTimerHandle CacheCleanupTimerHandle;

    /** Handle do timer de validação de networking */
    FTimerHandle NetworkValidationTimerHandle;
    
    // ========================================
    // FUNÇÕES PRINCIPAIS DO SISTEMA
    // ========================================

    /** Criar uma ilha caos em um ponto específico - CORRIGIDO: usar AAURACRONPCGChaosIsland */
    UFUNCTION()
    AAURACRONPCGChaosIsland* SpawnChaosIsland(const FVector& Location);

    /** Criar um portal caos em um ponto específico */
    UFUNCTION()
    AAURACRONPCGChaosPortal* SpawnChaosPortal(const FVector& Location);

    /** Verificar se um ponto está muito próximo de ilhas existentes */
    UFUNCTION()
    bool IsPointTooCloseToExistingIslands(const FVector& Point) const;

    /** Atualizar a intensidade dos efeitos baseado na fase do mapa */
    UFUNCTION()
    void UpdateEffectsIntensity();

    /** Calcular interseção entre duas linhas 2D */
    UFUNCTION()
    bool CalculateLineIntersection(const FVector& P1, const FVector& P2, const FVector& P3, const FVector& P4, FVector& OutIntersection) const;

    // ========================================
    // FUNÇÕES DE INICIALIZAÇÃO MODERNAS UE 5.6
    // ========================================

    /** Inicializar sistema de áudio */
    void InitializeAudioSystem();

    /** Inicializar sistema de VFX */
    void InitializeVFXSystem();

    /** Carregar assets necessários assincronamente */
    void LoadRequiredAssetsAsync();

    // ========================================
    // FUNÇÕES AUXILIARES DE PERFORMANCE
    // ========================================

    /** Obter multiplicador de intensidade baseado na fase */
    float GetPhaseIntensityMultiplier(EAURACRONMapPhase Phase) const;

    /** Verificar se há jogadores próximos a uma ilha */
    bool CheckPlayersNearIsland(AAURACRONPCGChaosIsland* Island, float Distance) const;

    /** Atualizar áudio ambiente */
    void UpdateAmbientAudio(float DeltaTime);

    /** Atualizar VFX globais */
    void UpdateGlobalVFX(float DeltaTime);

    /** Validar integridade do sistema */
    void ValidateSystemIntegrity();

    /** Obter pontos de interseção com cache */
    TArray<FVector> GetCachedIntersectionPoints();

    /** Configurar ilha para a fase atual */
    void ConfigureIslandForCurrentPhase(AAURACRONPCGChaosIsland* Island);

    /** Atualizar efeitos globais para uma fase específica */
    void UpdateGlobalEffectsForPhase(EAURACRONMapPhase Phase);

    /** Obter cor baseada na fase do mapa */
    FLinearColor GetPhaseColor(EAURACRONMapPhase Phase) const;

    /** Limpar cache periodicamente */
    void CleanupCache();

    /** Validar estado de networking */
    void ValidateNetworkState();

    // ========================================
    // CALLBACKS DE CARREGAMENTO ASSÍNCRONO
    // ========================================

    /** Callback quando som ambiente é carregado */
    void OnAmbientSoundLoaded(FSoftObjectPath AssetPath);

    /** Callback quando VFX global é carregado */
    void OnGlobalVFXLoaded(FSoftObjectPath AssetPath);

    /** Callback quando todos os assets são carregados */
    void OnRequiredAssetsLoaded();

    // ========================================
    // FUNÇÕES DE REPLICAÇÃO
    // ========================================

    /** Callback de replicação para lista de ilhas */
    UFUNCTION()
    void OnRep_ChaosIslandsUpdated();

    /** Callback de replicação para mudança de fase */
    UFUNCTION()
    void OnRep_MapPhaseChanged();

    // ========================================
    // FUNÇÕES DE EFEITOS VISUAIS
    // ========================================

    /** Reproduzir efeito de spawn de ilha */
    void PlayIslandSpawnEffect(const FVector& Location);
};