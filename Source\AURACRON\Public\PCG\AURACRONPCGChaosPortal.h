// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "AURACRONPCGChaosPortal.generated.h"

class UStaticMeshComponent;
class UNiagaraComponent;
class UPointLightComponent;
class UAudioComponent;
class USphereComponent;
class UMaterialInstanceDynamic;

/**
 * Tipos de portais de caos
 */
UENUM(BlueprintType)
enum class EChaosPortalType : uint8
{
    Standard UMETA(DisplayName = "Padrão"),
    Elite UMETA(DisplayName = "Elite"),
    Legendary UMETA(DisplayName = "Lendário")
};

/**
 * Ator que representa um portal de caos no mapa
 * Implementa o efeito especial ChaosPortals para a Fase 4 (Resolução)
 * 
 * Integração com Ilhas Caos:
 * - Localização: Em pontos de interseção do Fluxo
 * - Características: Perigos ambientais, recompensas de alto risco, terreno instável
 * - Valor Estratégico: Itens que mudam o jogo com risco significativo
 */
UCLASS()
class AURACRON_API AAURACRONPCGChaosPortal : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    AAURACRONPCGChaosPortal();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;

    /** Ativar portal com duração e intensidade específicas */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void ActivatePortal(float Duration = 0.0f, float Intensity = 1.0f);

    /** Desativar portal com fade out */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void DeactivatePortal(float FadeOutTime = 1.0f);

    /** Configurar escala de qualidade (para ajuste de performance) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetQualityScale(float NewQualityScale);

    /** Atualizar portal para fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);

    /** Disparar efeito do portal com probabilidade */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void TriggerPortalEffect(float SpawnProbability = 1.0f);
    
    /** Gerar recompensa de alto risco */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SpawnHighRiskReward(float RewardTier = 1.0f);
    
    /** Ativar perigo ambiental */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void ActivateEnvironmentalHazard(float HazardIntensity = 1.0f);
    
    /** Ativar instabilidade de terreno */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void ActivateTerrainInstability(float InstabilityIntensity = 1.0f);

    /** Configurar raio do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalRadius(float Radius) { EffectRadius = Radius; }

    /** Configurar intensidade do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalIntensity(float Intensity) { PortalIntensity = Intensity; }

    /** Configurar tempo de vida do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalLifetime(float Lifetime) { PortalDuration = Lifetime; }

    /** Configurar tipo do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|ChaosPortal")
    void SetPortalType(EChaosPortalType Type);

protected:
    /** Componente de malha estática */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UStaticMeshComponent* PortalMesh;

    /** Componente de efeito de partículas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UNiagaraComponent* PortalEffect;

    /** Componente de luz */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UPointLightComponent* PortalLight;

    /** Componente de áudio */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    UAudioComponent* PortalSound;

    /** Componente de colisão para trigger */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|ChaosPortal")
    USphereComponent* TriggerSphere;

    /** Material dinâmico do portal */
    UPROPERTY()
    UMaterialInstanceDynamic* PortalDynamicMaterial;

    /** Raio de efeito do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float EffectRadius;

    /** Duração do portal (0 = permanente) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PortalDuration;

    /** Intensidade do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PortalIntensity;

    /** Cor do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    FLinearColor PortalColor;

    /** Velocidade de rotação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float RotationSpeed;

    /** Frequência de pulsação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PulsateFrequency;

    /** Intensidade de pulsação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float PulsateIntensity;

    /** Intervalo entre efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float EffectInterval;

    /** Escala de qualidade (para ajuste de performance) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float QualityScale;

    /** Tipo do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    EChaosPortalType PortalType;
    
    /** Tabela de recompensas de alto risco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    UDataTable* HighRiskRewardsTable;
    
    /** Probabilidade de gerar perigo ambiental */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float EnvironmentalHazardProbability;
    
    /** Probabilidade de instabilidade de terreno */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float TerrainInstabilityProbability;
    
    /** Probabilidade de recompensa de alto risco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|ChaosPortal")
    float HighRiskRewardProbability;

    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

private:
    /** Tempo decorrido desde ativação */
    float ElapsedTime;

    /** Tempo desde último efeito */
    float TimeSinceLastEffect;

    /** Se o portal está ativo */
    bool bPortalActive;

    /** Se está em fade out */
    bool bFadingOut;

    /** Tempo de fade out */
    float FadeOutTime;

    /** Tempo decorrido de fade out */
    float FadeOutElapsed;

    /** Atualizar efeitos visuais */
    void UpdateVisualEffects(float DeltaTime);

    /** Aplicar efeitos aos jogadores */
    void ApplyEffectsToPlayers();

    /** Processar fade out */
    void ProcessFadeOut(float DeltaTime);

    /** Callback quando jogador entra no raio do portal */
    UFUNCTION()
    void OnPlayerEnterPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                 UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                 bool bFromSweep, const FHitResult& SweepResult);

    /** Callback quando jogador sai do raio do portal */
    UFUNCTION()
    void OnPlayerExitPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
    
    /** Gerar efeito visual de perigo ambiental */
    UFUNCTION()
    void SpawnEnvironmentalHazardVisual(FVector Location, float Intensity);
    
    /** Gerar efeito visual de instabilidade de terreno */
    UFUNCTION()
    void SpawnTerrainInstabilityVisual(FVector Location, float Intensity);
    
    /** Gerar efeito visual de recompensa de alto risco */
    UFUNCTION()
    void SpawnHighRiskRewardVisual(FVector Location, float RewardTier);
    
    /** Verificar se o portal está em um ponto de interseção do Fluxo */
    UFUNCTION()
    bool IsAtFlowIntersection() const;
};