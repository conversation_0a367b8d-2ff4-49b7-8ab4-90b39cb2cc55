// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGPortal.h"
#include "Data/AURACRONEnums.h"
#include "AURACRONPCGEnergyPulse.generated.h"

class UNiagaraComponent;
class UPointLightComponent;
class UAudioComponent;
class USphereComponent;



/**
 * Ator que representa um pulso de energia no mapa
 * Implementa o efeito especial EnergyPulses para a Fase 4 (Resolução)
 */
UCLASS()
class AURACRON_API AAURACRONPCGEnergyPulse : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    AAURACRONPCGEnergyPulse();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;

    /** Disparar pulso com duração e intensidade específicas */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void TriggerPulse(float Duration = 0.0f, float Intensity = 1.0f);
    
    /** Criar pulso de energia dourada (Portal Radiante) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void CreateGoldenEnergyPulse(float Duration = 3.0f, float Intensity = 1.2f);
    
    /** Criar pulso de energia prateada (Portal Zephyr) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void CreateSilverEnergyPulse(float Duration = 2.5f, float Intensity = 1.0f);
    
    /** Criar pulso de energia violeta (Portal Umbral) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void CreateVioletEnergyPulse(float Duration = 3.5f, float Intensity = 1.5f);
    
    /** Criar pulso de energia baseado no tipo de portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void CreateEnergyPulseForPortalType(EAURACRONPortalType PortalType, float Duration = 0.0f, float Intensity = 1.0f);

    /** Configurar escala de qualidade (para ajuste de performance) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void SetQualityScale(float NewQualityScale);

    /** Atualizar pulso para fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);

    /** Aplicar efeitos aos jogadores e ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void ApplyPulseEffects();

    /** Configurar raio do pulso */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void SetPulseRadius(float Radius) { PulseRadius = Radius; }

    /** Configurar intensidade do pulso */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void SetPulseIntensity(float Intensity) { PulseIntensity = Intensity; }

    /** Configurar tempo de vida do pulso */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnergyPulse")
    void SetPulseDuration(float Duration) { PulseDuration = Duration; }

protected:
    /** Componente de efeito de partículas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnergyPulse")
    UNiagaraComponent* PulseEffect;

    /** Componente de luz */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnergyPulse")
    UPointLightComponent* PulseLight;

    /** Componente de áudio */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnergyPulse")
    UAudioComponent* PulseSound;

    /** Componente de colisão para detecção */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnergyPulse")
    USphereComponent* PulseSphere;

    /** Raio do pulso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    float PulseRadius;

    /** Duração do pulso (0 = único) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    float PulseDuration;

    /** Intensidade do pulso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    float PulseIntensity;

    /** Cor do pulso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    FLinearColor PulseColor;
    
    /** Tipo de energia do pulso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    EAURACRONEnergyType EnergyType;

    /** Velocidade de expansão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    float ExpansionSpeed;

    /** Escala de qualidade (para ajuste de performance) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnergyPulse")
    float QualityScale;

    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

private:
    /** Tempo decorrido desde ativação */
    float ElapsedTime;

    /** Se o pulso está ativo */
    bool bPulseActive;

    /** Raio atual do pulso */
    float CurrentRadius;

    /** Dano base do pulso */
    float BaseDamage;

    /** Velocidade do pulso (multiplicador de tempo) */
    float PulseSpeed;

    /** Verificar posição dos jogadores */
    void CheckPlayerPositions();

    /** Calcular multiplicador de dano baseado na distância */
    float CalculateDamageMultiplier(float Distance);

    /** Aplicar dano ao jogador */
    void ApplyDamageToPlayer(AActor* Player, float DamageAmount);

    /** Atualizar efeitos visuais do pulso */
    void UpdateVisualEffects();

    /** Aplicar efeitos aos jogadores */
    void ApplyEffectsToPlayers();

    /** Aplicar efeitos ao ambiente */
    void ApplyEffectsToEnvironment();

    /** Calcular raio atual baseado no tempo */
    float CalculateCurrentRadius();

    /** Callback quando jogador entra no raio do pulso */
    UFUNCTION()
    void OnPlayerEnterPulseRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                UPrimitiveComponent* OtherComp, int32 OtherBodyIndex,
                                bool bFromSweep, const FHitResult& SweepResult);

    // Funções auxiliares para efeitos específicos de energia
    void ApplyGoldenEnergyEffects(ACharacter* Character);
    void ApplyChaosEnergyEffects(ACharacter* Character);
    void ApplyVoidEnergyEffects(ACharacter* Character);
    void ApplyGenericEnergyEffects(ACharacter* Character);

    // Funções de efeitos ambientais
    void ApplyGoldenEnvironmentEffects();
    void ApplyChaosEnvironmentEffects();
    void ApplyVoidEnvironmentEffects();
    void ApplyGenericEnvironmentEffects();
};