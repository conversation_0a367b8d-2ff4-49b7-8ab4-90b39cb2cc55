// AURACRONPCGMathLibrary.h
// Biblioteca Matemática para PCG AURACRON - UE 5.6
// Funções matemáticas avançadas para geração procedural

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "AURACRONPCGMathLibrary.generated.h"

/**
 * Estrutura para definir uma curva spline matemática
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONSplineCurve
{
    GENERATED_BODY()

    /** Pontos de controle da curva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> ControlPoints;
    
    /** Tangentes nos pontos de controle */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> Tangents;
    
    /** Tipo de interpolação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TEnumAsByte<EInterpCurveMode> InterpolationMode;
    
    FAURACRONSplineCurve()
        : InterpolationMode(CIM_CurveAuto)
    {
    }
};

/**
 * Estrutura para definir padrões de ruído procedural
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONNoisePattern
{
    GENERATED_BODY()

    /** Frequência do ruído */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.001", ClampMax = "10.0"))
    float Frequency = 1.0f;
    
    /** Amplitude do ruído */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "1000.0"))
    float Amplitude = 100.0f;
    
    /** Número de oitavas para ruído fractal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1", ClampMax = "8"))
    int32 Octaves = 4;
    
    /** Persistência entre oitavas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "1.0"))
    float Persistence = 0.5f;
    
    /** Lacunaridade (frequência entre oitavas) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1.0", ClampMax = "4.0"))
    float Lacunarity = 2.0f;
    
    /** Seed para reprodutibilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Seed = 12345;
};

/**
 * Biblioteca de funções matemáticas avançadas para geração procedural do AURACRON
 */
UCLASS()
class AURACRON_API UAURACRONPCGMathLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // ========================================
    // FUNÇÕES DE CURVAS E SPLINES
    // ========================================
    
    /** Criar uma curva serpentina suave entre pontos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static FAURACRONSplineCurve CreateSerpentineCurve(
        const FVector& StartPoint,
        const FVector& EndPoint,
        int32 NumControlPoints = 10,
        float Amplitude = 1000.0f,
        float Frequency = 2.0f
    );
    
    /** Avaliar posição em uma curva spline */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static FVector EvaluateSplineCurve(const FAURACRONSplineCurve& Curve, float T);
    
    /** Obter tangente em uma posição da curva */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static FVector GetSplineTangent(const FAURACRONSplineCurve& Curve, float T);
    
    /** Criar curva orbital para ilhas flutuantes */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> CreateOrbitalPath(
        const FVector& Center,
        float Radius,
        float Height,
        int32 NumPoints = 16,
        float PhaseOffset = 0.0f
    );
    
    // ========================================
    // FUNÇÕES DE RUÍDO PROCEDURAL
    // ========================================
    
    /** Gerar ruído Perlin 2D */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float GeneratePerlinNoise2D(float X, float Y, const FAURACRONNoisePattern& Pattern);
    
    /** Gerar ruído Perlin 3D */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float GeneratePerlinNoise3D(float X, float Y, float Z, const FAURACRONNoisePattern& Pattern);
    
    /** Gerar ruído fractal (múltiplas oitavas) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float GenerateFractalNoise(float X, float Y, const FAURACRONNoisePattern& Pattern);
    
    /** Gerar mapa de altura usando ruído */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<float> GenerateHeightMap(
        int32 Width,
        int32 Height,
        const FAURACRONNoisePattern& Pattern,
        float MinHeight = 0.0f,
        float MaxHeight = 1000.0f
    );
    
    // ========================================
    // FUNÇÕES DE DISTRIBUIÇÃO ESPACIAL
    // ========================================
    
    /** Distribuir pontos usando amostragem de Poisson */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> GeneratePoissonDiscSampling(
        const FVector& Center,
        float Radius,
        float MinDistance,
        int32 MaxAttempts = 30,
        int32 Seed = 12345
    );
    
    /** Distribuir pontos em padrão hexagonal */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> GenerateHexagonalGrid(
        const FVector& Center,
        float Radius,
        float Spacing,
        bool bAddRandomOffset = true,
        float RandomOffsetAmount = 0.2f
    );
    
    /** Distribuir pontos ao longo de uma curva */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> DistributePointsAlongCurve(
        const FAURACRONSplineCurve& Curve,
        float Spacing,
        bool bAlignToTangent = true
    );
    
    // ========================================
    // FUNÇÕES DE GEOMETRIA PROCEDURAL
    // ========================================
    
    /** Gerar vértices para um platô cristalino */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> GenerateCrystallinePlateauVertices(
        const FVector& Center,
        float Radius,
        float Height,
        int32 NumSides = 6,
        float Irregularity = 0.2f
    );
    
    /** Gerar pontos para um cânion vivo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> GenerateLivingCanyonPath(
        const FVector& StartPoint,
        const FVector& EndPoint,
        float Width,
        float Depth,
        int32 NumSegments = 20
    );
    
    /** Gerar posições para árvores em floresta respirante */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> GenerateBreathingForestPositions(
        const FVector& Center,
        float Radius,
        int32 TreeCount,
        float MinDistance = 200.0f,
        float BreathingAmplitude = 50.0f,
        float Time = 0.0f
    );
    
    /** Gerar pontos para ponte tectônica */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static TArray<FVector> GenerateTectonicBridgePoints(
        const FVector& StartPoint,
        const FVector& EndPoint,
        float Width,
        int32 NumSupports = 5,
        float ArchHeight = 300.0f
    );
    
    // ========================================
    // FUNÇÕES DE TRANSFORMAÇÃO TEMPORAL
    // ========================================
    
    /** Calcular posição baseada no ciclo dia/noite */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static FVector GetTimeBasedPosition(
        const FVector& BasePosition,
        float TimeOfDay,
        float Amplitude = 100.0f,
        float Frequency = 1.0f
    );
    
    /** Calcular intensidade baseada na fase da lua */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float GetLunarPhaseIntensity(float TimeOfDay, float LunarCycle = 28.0f);
    
    /** Calcular intensidade solar baseada na hora do dia */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float GetSolarIntensity(float TimeOfDay);
    
    // ========================================
    // FUNÇÕES DE OTIMIZAÇÃO E PERFORMANCE
    // ========================================
    
    /** Calcular LOD baseado na distância */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static int32 CalculateLODLevel(
        const FVector& ObjectPosition,
        const FVector& ViewerPosition,
        float LOD0Distance = 1000.0f,
        float LOD1Distance = 2000.0f,
        float LOD2Distance = 4000.0f
    );
    
    /** Verificar se um objeto deve ser renderizado (frustum culling) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static bool ShouldRenderObject(
        const FVector& ObjectPosition,
        float ObjectRadius,
        const FVector& ViewerPosition,
        const FVector& ViewerForward,
        float ViewDistance = 5000.0f,
        float FOVDegrees = 90.0f
    );
    
    /** Calcular densidade de objetos baseada na performance */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float CalculateObjectDensity(
        float BaselineFrameRate = 60.0f,
        float CurrentFrameRate = 60.0f,
        float MinDensity = 0.1f,
        float MaxDensity = 1.0f
    );

private:
    // ========================================
    // FUNÇÕES AUXILIARES INTERNAS
    // ========================================
    
    /** Função de interpolação suave (smoothstep) */
    static float SmoothStep(float Edge0, float Edge1, float X);
    
    /** Função de ruído Perlin básica */
    static float PerlinNoise(float X, float Y, int32 Seed);
    
    /** Função de fade para ruído Perlin */
    static float Fade(float T);
    
    /** Função de gradiente para ruído Perlin */
    static float Grad(int32 Hash, float X, float Y);
    
    /** Função de hash para ruído */
    static int32 Hash(int32 X, int32 Y, int32 Seed);

public:
    // ========================================
    // FUNÇÕES DE COR E EFEITOS VISUAIS
    // ========================================

    /** Alterar matiz de uma cor */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math|Color")
    static FLinearColor ShiftHue(const FLinearColor& Color, float HueShift);

    /** Calcular intensidade da fase lunar */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math|Time")
    static float CalculateLunarPhaseIntensity(const UWorld* World);

    /** Obter altura do terreno em uma localização específica */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Math")
    static float GetTerrainHeightAtLocation(const UWorld* World, const FVector& Location);

};
