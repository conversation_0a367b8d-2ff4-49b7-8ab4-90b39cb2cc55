// AURACRONPCGNexusIsland.h
// Definição da classe ANexusIsland para o sistema Prismal Flow

#pragma once

#include "CoreMinimal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "GameplayEffect.h"
#include "ActiveGameplayEffectHandle.h"
#include "AURACRONPCGNexusIsland.generated.h"

class UNiagaraSystem;

/**
 * Implementação específica da Nexus Island
 * Ilha central com poderes especiais que concede habilidades de manipulação do flow
 */
UCLASS()
class AURACRON_API ANexusIsland : public APrismalFlowIsland
{
    GENERATED_BODY()
    
public:
    ANexusIsland();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult) override;
    
    // Concede habilidade de manipulação do flow ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Nexus Island")
    void GrantFlowManipulationAbility(AActor* TargetActor);
    
protected:
    // Remove a habilidade de manipulação do flow
    UFUNCTION()
    void RemoveFlowManipulationAbility(AActor* TargetActor);
    
    // Intensidade do poder concedido
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Nexus Island")
    float PowerIntensity;
    
    // Duração do poder concedido
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Nexus Island")
    float PowerDuration;
    
    // Efeito visual do poder
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Nexus Island")
    UNiagaraSystem* PowerEffect;
    
    // Efeito de gameplay para manipulação de fluxo
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Nexus Island")
    TSubclassOf<UGameplayEffect> FlowManipulationEffect;
    
    // Mapa de efeitos ativos de manipulação de fluxo por ator
    UPROPERTY()
    TMap<AActor*, FActiveGameplayEffectHandle> ActiveFlowManipulationEffects;
    
    // Torre de controle central
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UStaticMeshComponent* CentralTower;
    
    // Efeito de energia da torre
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UNiagaraComponent* TowerEnergyEffect;
    
    // Plataformas defensivas em múltiplos níveis
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    TArray<UStaticMeshComponent*> DefensivePlatforms;
    
    // Gerador de recursos
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UStaticMeshComponent* ResourceGenerator;
    
    // Efeito do gerador de recursos
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UNiagaraComponent* ResourceEffect;
    
    // Tempo acumulado para efeitos
    UPROPERTY()
    float AccumulatedTime;
    
    virtual void UpdateIslandVisuals() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};