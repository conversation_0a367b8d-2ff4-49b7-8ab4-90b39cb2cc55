// AURACRONPCGUtility.h
// Classe Utilitária para Funções Comuns PCG - UE 5.6
// Centraliza funcionalidades compartilhadas entre sistemas PCG para evitar duplicação

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "AURACRONPCGUtility.generated.h"

// Forward declarations
class AAURACRONPCGEnvironment;
class AAURACRONPCGTrail;
class AAURACRONPCGIsland;
class AAURACRONPCGPrismalFlow;

/**
 * Estrutura para armazenar referências de atores PCG encontrados
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGActorReferences
{
    GENERATED_BODY()

    /** Atores de ambiente encontrados */
    UPROPERTY(BlueprintReadOnly)
    TArray<AAURACRONPCGEnvironment*> EnvironmentActors;

    /** Atores de trilha encontrados */
    UPROPERTY(BlueprintReadOnly)
    TArray<AAURACRONPCGTrail*> TrailActors;

    /** Atores de ilha encontrados */
    UPROPERTY(BlueprintReadOnly)
    TArray<AAURACRONPCGIsland*> IslandActors;

    /** Ator Prismal Flow (único) */
    UPROPERTY(BlueprintReadOnly)
    AAURACRONPCGPrismalFlow* PrismalFlowActor;

    FAURACRONPCGActorReferences()
        : PrismalFlowActor(nullptr)
    {
    }

    /** Limpar todas as referências */
    void Clear()
    {
        EnvironmentActors.Empty();
        TrailActors.Empty();
        IslandActors.Empty();
        PrismalFlowActor = nullptr;
    }

    /** Verificar se há atores válidos */
    bool HasValidActors() const
    {
        return EnvironmentActors.Num() > 0 || TrailActors.Num() > 0 ||
               IslandActors.Num() > 0 || (PrismalFlowActor != nullptr);
    }

    /** Obter contagem total de atores */
    int32 GetTotalActorCount() const
    {
        return EnvironmentActors.Num() + TrailActors.Num() + IslandActors.Num() +
               (PrismalFlowActor != nullptr ? 1 : 0);
    }

    /** Verificar se a estrutura é válida (alias para HasValidActors) */
    bool IsValid() const
    {
        return HasValidActors();
    }
};

/**
 * Opções para busca de atores PCG
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGSearchOptions
{
    GENERATED_BODY()

    /** Buscar atores de ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bSearchEnvironments;

    /** Buscar atores de trilha */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bSearchTrails;

    /** Buscar atores de ilha */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bSearchIslands;

    /** Buscar ator Prismal Flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bSearchPrismalFlow;

    /** Incluir atores inativos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIncludeInactiveActors;

    /** Log detalhado da busca */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bVerboseLogging;

    FAURACRONPCGSearchOptions()
        : bSearchEnvironments(true)
        , bSearchTrails(true)
        , bSearchIslands(true)
        , bSearchPrismalFlow(true)
        , bIncludeInactiveActors(false)
        , bVerboseLogging(true)
    {
    }

    /** Configuração para buscar todos os tipos */
    static FAURACRONPCGSearchOptions All()
    {
        return FAURACRONPCGSearchOptions();
    }

    /** Configuração para buscar apenas ambientes */
    static FAURACRONPCGSearchOptions EnvironmentsOnly()
    {
        FAURACRONPCGSearchOptions Options;
        Options.bSearchEnvironments = true;
        Options.bSearchTrails = false;
        Options.bSearchIslands = false;
        Options.bSearchPrismalFlow = false;
        return Options;
    }

    /** Configuração para buscar apenas objetivos (ilhas + prismal flow) */
    static FAURACRONPCGSearchOptions ObjectivesOnly()
    {
        FAURACRONPCGSearchOptions Options;
        Options.bSearchEnvironments = false;
        Options.bSearchTrails = false;
        Options.bSearchIslands = true;
        Options.bSearchPrismalFlow = true;
        return Options;
    }
};

/**
 * Classe utilitária estática para funções comuns PCG
 * Centraliza funcionalidades compartilhadas para evitar duplicação de código
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API UAURACRONPCGUtility : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Encontrar todos os atores PCG na cena
     * @param World - Mundo onde buscar os atores
     * @param SearchOptions - Opções de busca (quais tipos buscar)
     * @return Estrutura com referências aos atores encontrados
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Utility")
    static FAURACRONPCGActorReferences FindPCGActors(
        UWorld* World,
        const FAURACRONPCGSearchOptions& SearchOptions
    );

    /** Versão simplificada para Blueprint sem parâmetros */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Utility")
    static FAURACRONPCGActorReferences FindAllPCGActors(UWorld* World);

    /**
     * Encontrar atores PCG de um tipo específico
     * @param World - Mundo onde buscar
     * @param ActorClass - Classe do ator a buscar
     * @param bIncludeInactive - Incluir atores inativos
     * @return Array de atores encontrados
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Utility")
    static TArray<AActor*> FindPCGActorsOfClass(
        UWorld* World, 
        UClass* ActorClass, 
        bool bIncludeInactive = false
    );

    /**
     * Validar se um ator PCG está ativo e funcional
     * @param Actor - Ator a validar
     * @return True se o ator é válido e ativo
     */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Utility")
    static bool IsValidPCGActor(AActor* Actor);

    /**
     * Obter estatísticas dos atores PCG na cena
     * @param World - Mundo a analisar
     * @return String com estatísticas formatadas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Utility")
    static FString GetPCGActorStatistics(UWorld* World);

    /**
     * Aplicar configuração a todos os atores PCG de um tipo
     * @param ActorReferences - Referências dos atores
     * @param ConfigurationName - Nome da configuração a aplicar
     * @param Value - Valor da configuração
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Utility")
    static void ApplyConfigurationToAllActors(
        const FAURACRONPCGActorReferences& ActorReferences,
        const FString& ConfigurationName,
        float Value
    );

    /**
     * Registrar callback para mudanças em atores PCG
     * @param World - Mundo a monitorar
     * @param Callback - Função a chamar quando houver mudanças
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Utility")
    static void RegisterPCGActorChangeCallback(UWorld* World, const FString& CallbackName);

private:
    /** Cache de referências de atores para otimização */
    static TMap<UWorld*, FAURACRONPCGActorReferences> ActorCache;

    /** Timestamp da última atualização do cache */
    static TMap<UWorld*, float> CacheTimestamps;

    /** Tempo de vida do cache em segundos */
    static constexpr float CACHE_LIFETIME = 5.0f;

    /** Verificar se o cache é válido */
    static bool IsCacheValid(UWorld* World);

    /** Atualizar cache de atores */
    static void UpdateActorCache(UWorld* World, const FAURACRONPCGActorReferences& NewReferences);

    /** Limpar cache expirado */
    static void CleanExpiredCache();
};
