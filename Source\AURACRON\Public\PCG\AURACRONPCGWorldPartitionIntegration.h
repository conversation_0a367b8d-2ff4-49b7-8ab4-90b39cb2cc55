// AURACRONPCGWorldPartitionIntegration.h
// Integração do Sistema PCG com World Partition UE 5.6
// Gerencia streaming eficiente de conteúdo procedural

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Data/AURACRONEnums.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/LoaderAdapter/LoaderAdapterShape.h"
#include "Engine/World.h"
#include "AURACRONPCGWorldPartitionIntegration.generated.h"

// Enum EAURACRONStreamingQualityProfile definido em Data/AURACRONEnums.h

/** Estatísticas de desempenho de streaming */
USTRUCT(BlueprintType)
struct FAURACRONStreamingPerformanceStats
{
    GENERATED_BODY()

    /** Número de elementos atualmente em streaming */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 ActiveStreamingElements = 0;

    /** Memória total utilizada pelo streaming (em MB) */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float MemoryUsageMB = 0.0f;

    /** Tempo médio de carregamento (em ms) */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float AverageLoadTimeMS = 0.0f;

    /** Número de hitches detectados durante streaming */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 StreamingHitchCount = 0;

    /** Tempo desde a última atualização de estatísticas */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float TimeSinceLastUpdate = 0.0f;

    /** Uso atual de memória em MB */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float CurrentMemoryUsageMB = 0.0f;

    /** Pico de uso de memória em MB */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float PeakMemoryUsageMB = 0.0f;

    /** Número de elementos carregados na última atualização */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 ElementsLoadedLastUpdate = 0;

    /** Número de elementos descarregados na última atualização */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 ElementsUnloadedLastUpdate = 0;

    /** Tempo total de carregamento de elementos */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float TotalElementLoadTime = 0.0f;

    /** Contador de elementos carregados */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 ElementLoadCount = 0;

    /** Tempo médio de carregamento de elementos */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float AverageElementLoadTime = 0.0f;

    /** Contador de carregamentos lentos */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 SlowLoadCount = 0;

    /** Número de hitches detectados */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 HitchesDetected = 0;

    /** Tempo médio de frame */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float AverageFrameTime = 0.0f;

    /** FPS médio */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float AverageFPS = 60.0f;

    /** Elementos atualmente em streaming */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    int32 ElementsCurrentlyStreamed = 0;

    /** Operações de streaming por segundo */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float StreamingOperationsPerSecond = 0.0f;

    /** Tempo da última atualização */
    UPROPERTY(BlueprintReadOnly, Category = "Streaming|Stats")
    float LastUpdateTime = 0.0f;
};

/** Configurações de hardware para streaming */
USTRUCT(BlueprintType)
struct FAURACRONHardwareStreamingConfig
{
    GENERATED_BODY()

    /** Perfil de qualidade atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    EAURACRONStreamingQualityProfile QualityProfile = EAURACRONStreamingQualityProfile::Medium;

    /** Multiplicador de distância de streaming baseado no hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "0.1", ClampMax = "2.0", UIMin = "0.1", UIMax = "2.0"))
    float HardwareDistanceMultiplier = 1.0f;

    /** Indica se o hardware é considerado de baixo desempenho */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    bool bIsLowEndHardware = false;

    /** Limite de memória para streaming (em MB) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "64", UIMin = "64"))
    int32 MemoryBudgetMB = 512;

    /** Número máximo de elementos em streaming simultâneo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "1", UIMin = "1"))
    int32 MaxConcurrentStreamingElements = 16;

    /** Multiplicador de distância */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "0.1", ClampMax = "3.0"))
    float DistanceMultiplier = 1.0f;

    /** FPS alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "30", ClampMax = "120"))
    float TargetFPS = 60.0f;

    /** Se deve habilitar ajuste dinâmico de FPS */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    bool bEnableDynamicFPSAdjustment = true;

    /** Fatores de distância LOD */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    TArray<float> LODDistanceFactors;

    /** Usar streaming assíncrono */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    bool bUseAsyncStreaming = true;

    /** Priorizar streaming baseado em visibilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    bool bPrioritizeVisibleElements = true;

    /** Usar LODs dinâmicos baseados em hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware")
    bool bUseDynamicLODs = true;

    /** Multiplicador de distância de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "0.1", ClampMax = "3.0", UIMin = "0.1", UIMax = "3.0"))
    float StreamingDistanceMultiplier = 1.0f;

    /** FPS mínimo aceitável */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "15.0", ClampMax = "120.0", UIMin = "15.0", UIMax = "120.0"))
    float MinAcceptableFPS = 30.0f;

    /** Nível de LOD base para hardware atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming|Hardware", meta = (ClampMin = "0", ClampMax = "5", UIMin = "0", UIMax = "5"))
    int32 BaseLODLevel = 0;
};

/**
 * Configurações de streaming para elementos PCG
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGStreamingConfig
{
    GENERATED_BODY()

    /** Distância de carregamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1000.0"))
    float LoadingDistance;
    
    /** Distância de descarregamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1000.0"))
    float UnloadingDistance;
    
    /** Prioridade de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0", ClampMax = "100"))
    int32 StreamingPriority;
    
    /** Se deve usar streaming assíncrono */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseAsyncStreaming;
    
    /** Tamanho do grid de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1000.0"))
    float GridSize;

    /** Distância de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1000.0"))
    float StreamingDistance;

    /** Se deve começar ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bStartActive;
    
    /** Nível de detalhe para streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0", ClampMax = "3"))
    int32 LODLevel;
    
    /** Otimização para hardware de baixo desempenho */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bOptimizeForLowEndHardware;

    FAURACRONPCGStreamingConfig()
        : LoadingDistance(5000.0f)
        , UnloadingDistance(7500.0f)
        , StreamingPriority(50)
        , bUseAsyncStreaming(true)
        , GridSize(2000.0f)
        , StreamingDistance(5000.0f)
        , bStartActive(true)
        , LODLevel(0)
        , bOptimizeForLowEndHardware(false)
    {
    }
};

/**
 * Configurações de streaming específicas para World Partition UE 5.6
 * Usa FStreamingSourceShape ao invés de EStreamingSourceShapeType
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGWorldPartitionStreamingConfig : public FAURACRONPCGStreamingConfig
{
    GENERATED_BODY()

    /** Configuração da forma de streaming usando UE 5.6 API */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FStreamingSourceShape StreamingShape;

    /** Rotação da forma de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FRotator ShapeRotation;

    /** Se deve usar streaming baseado em visibilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseVisibilityStreaming;

    /** Se deve usar streaming baseado em gameplay */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseGameplayStreaming;

    /** Prioridade de streaming para World Partition */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "-100", ClampMax = "100"))
    int32 WorldPartitionStreamingPriority;

    /** Otimização para diferentes plataformas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FName, float> PlatformSpecificSettings;

    FAURACRONPCGWorldPartitionStreamingConfig()
        : ShapeRotation(FRotator::ZeroRotator)
        , bUseVisibilityStreaming(true)
        , bUseGameplayStreaming(true)
        , WorldPartitionStreamingPriority(0)
    {
        // Configurar StreamingShape com valores padrão para UE 5.6
        StreamingShape.bUseGridLoadingRange = true;
        StreamingShape.Radius = 1000.0f;
        // Configurar propriedades de streaming para UE 5.6
        StreamingShape.bUseGridLoadingRange = true;
    }
};

/**
 * Entrada de streaming para elemento PCG
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGStreamingEntry
{
    GENERATED_BODY()

    /** Elemento PCG */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    AActor* PCGElement;

    /** Configuração de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FAURACRONPCGStreamingConfig StreamingConfig;

    /** Se está atualmente sendo streamed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsCurrentlyStreamed;

    /** Último tempo de atualização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LastUpdateTime;

    /** ID único do elemento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FGuid ElementId;

    /** Último tempo de carregamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LastLoadTime;

    FAURACRONPCGStreamingEntry()
        : PCGElement(nullptr)
        , bIsCurrentlyStreamed(false)
        , LastUpdateTime(0.0f)
        , ElementId(FGuid::NewGuid())
        , LastLoadTime(0.0f)
    {
    }
};

/**
 * Região de streaming
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGStreamingRegion
{
    GENERATED_BODY()

    /** Nome da região */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString RegionName;

    /** Centro da região */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector Center;

    /** Raio da região */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Radius;

    /** Se a região está ativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive;

    FAURACRONPCGStreamingRegion()
        : RegionName(TEXT("DefaultRegion"))
        , Center(FVector::ZeroVector)
        , Radius(1000.0f)
        , bIsActive(true)
    {
    }
};

/**
 * Integração do sistema PCG com World Partition do UE 5.6
 * Gerencia streaming eficiente de conteúdo procedural
 * Suporta otimizações para diferentes hardwares
 */
UCLASS()
class AURACRON_API AAURACRONPCGWorldPartitionIntegration : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGWorldPartitionIntegration();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // ========================================
    // FUNÇÕES PÚBLICAS - BÁSICAS
    // ========================================
    
    /** Inicializar integração com World Partition */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition")
    void InitializeWorldPartitionIntegration();
    
    /** Registrar elemento PCG para streaming */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition")
    void RegisterPCGElementForStreaming(AActor* Element, const FAURACRONPCGStreamingConfig& Config);
    
    /** Atualizar streaming baseado na posição do jogador */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition")
    void UpdateStreamingForPlayerLocation(const FVector& PlayerLocation);
    
    // ========================================
    // FUNÇÕES PÚBLICAS - OTIMIZAÇÃO DE HARDWARE
    // ========================================
    
    /** Detectar capacidades do hardware e configurar otimizações */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    void DetectHardwareCapabilities();
    
    /** Configurar otimizações para hardware específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    void ConfigureHardwareOptimizations(bool bIsLowEndHardware = false);
    
    /** Obter multiplicador de distância de streaming baseado no hardware */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    float GetHardwareStreamingDistanceMultiplier() const;
    
    /** Definir perfil de qualidade para streaming */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    void SetStreamingQualityProfile(EAURACRONStreamingQualityProfile QualityProfile);
    
    /** Otimizar streaming para plataforma atual */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    void OptimizeStreamingForCurrentPlatform();

    
    /** Aplicar configurações de otimização para todos os elementos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    void ApplyOptimizationToAllElements();
    
    /** Obter estatísticas de desempenho de streaming */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|WorldPartition|Optimization")
    FAURACRONStreamingPerformanceStats GetStreamingPerformanceStats() const;

protected:
    // ========================================
    // CONFIGURAÇÕES
    // ========================================
    
    /** Configurações padrão de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition")
    FAURACRONPCGStreamingConfig DefaultStreamingConfig;

    /** Raio de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition", meta = (ClampMin = "1000.0", UIMin = "1000.0"))
    float StreamingRadius = 10000.0f;

    /** Intervalo de atualização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition", meta = (ClampMin = "0.1", UIMin = "0.1"))
    float UpdateInterval = 1.0f;

    /** Se deve atualizar streaming automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition")
    bool bAutoUpdateStreaming = true;
    
    // ========================================
    // PROPRIEDADES PROTEGIDAS - OTIMIZAÇÃO DE HARDWARE
    // ========================================
    
    /** Configurações de hardware para streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization")
    FAURACRONHardwareStreamingConfig HardwareConfig;
    
    /** Estatísticas de desempenho de streaming */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|WorldPartition|Optimization")
    FAURACRONStreamingPerformanceStats PerformanceStats;
    
    /** Intervalo de atualização das estatísticas de desempenho (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization", meta = (ClampMin = "0.5", UIMin = "0.5"))
    float StatsUpdateInterval = 2.0f;
    
    /** Ativar otimizações dinâmicas baseadas em desempenho */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization")
    bool bEnableDynamicOptimizations = true;
    
    /** Limite de FPS para ativar otimizações adicionais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization", meta = (ClampMin = "15", UIMin = "15", ClampMax = "60", UIMax = "60"))
    int32 LowFPSThreshold = 30;
    
    /** Curva de ajuste de distância de streaming baseada em FPS */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization")
    UCurveFloat* FPSToStreamingDistanceCurve;
    
    /** Usar otimizações específicas para plataforma */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization")
    bool bUsePlatformSpecificOptimizations = true;
    
    /** Tempo mínimo entre adaptações de otimização (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization", meta = (ClampMin = "1.0", UIMin = "1.0"))
    float MinTimeBetweenOptimizationAdjustments = 5.0f;

    /** Ajustar automaticamente o perfil de qualidade baseado no desempenho */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|WorldPartition|Optimization")
    bool bAutoAdjustQualityProfile = true;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================

    /** Referência ao World Partition */
    UPROPERTY()
    TObjectPtr<UWorldPartition> WorldPartition;

    /** Elementos registrados para streaming */
    UPROPERTY()
    TMap<TWeakObjectPtr<AActor>, FAURACRONPCGStreamingConfig> StreamingElements;

    /** Elementos registrados com ID */
    UPROPERTY()
    TMap<FGuid, FAURACRONPCGStreamingEntry> RegisteredElements;

    /** Regiões de streaming */
    UPROPERTY()
    TArray<FAURACRONPCGStreamingRegion> StreamingRegions;
    
    /** Tempo desde a última adaptação de otimização */
    float TimeSinceLastOptimizationAdjustment;
    
    /** Histórico de FPS para análise de desempenho */
    TArray<float> FPSHistory;
    
    /** Contador de elementos carregados na sessão atual */
    int32 TotalElementsLoaded;
    
    /** Contador de elementos descarregados na sessão atual */
    int32 TotalElementsUnloaded;
    
    /** Tempo acumulado para atualização de estatísticas */
    float StatsUpdateAccumulator;

    // ========================================
    // FUNÇÕES INTERNAS - BÁSICAS
    // ========================================

    /** Configurar data layers para PCG */
    void SetupPCGDataLayers();

    /** Atualizar streaming de elementos */
    void UpdateElementStreaming();

    /** Verificar se elemento deve ser carregado baseado na distância e configurações de hardware */
    bool ShouldLoadElement(const FAURACRONPCGStreamingEntry& StreamingEntry, float Distance);

    /** Registrar região de streaming */
    void RegisterStreamingRegion(const FString& RegionName, const FVector& Center, float Radius);

    /** Configurar data layers */
    void ConfigureDataLayers(class UDataLayerSubsystem* DataLayerSubsystem);

    /** Stream in elemento */
    void StreamInElement(FAURACRONPCGStreamingEntry& StreamingEntry);

    /** Stream out elemento */
    void StreamOutElement(FAURACRONPCGStreamingEntry& StreamingEntry);

    /** Atualizar streaming para todos os jogadores */
    void UpdateStreamingForAllPlayers();
    
    // ========================================
    // FUNÇÕES INTERNAS - OTIMIZAÇÃO DE HARDWARE
    // ========================================
    
    /** Detectar capacidades do hardware atual */
    void DetectHardwareCapabilitiesInternal();
    
    /** Aplicar configurações de otimização para hardware específico */
    void ApplyHardwareSpecificOptimizations();

    /** Aplicar configurações de LOD para elemento específico */
    void ApplyLODSettingsToElement(FAURACRONPCGStreamingEntry& StreamingEntry);

    /** Atualizar estatísticas de desempenho de streaming */
    void UpdatePerformanceStats(float DeltaTime);
    
    /** Calcular multiplicador de distância baseado no FPS atual */
    float CalculateFPSBasedDistanceMultiplier() const;
    
    /** Ajustar configurações de streaming baseado no desempenho atual */
    void AdjustStreamingBasedOnPerformance();

    /** Ajustar otimizações baseado no desempenho atual */
    void AdjustOptimizationsForPerformance();
    
    /** Verificar se é necessário ajustar otimizações */
    bool ShouldAdjustOptimizations() const;
    
    /** Aplicar otimizações específicas para plataforma atual */
    void ApplyPlatformSpecificOptimizations();
    
    /** Calcular configurações de LOD baseadas no hardware */
    int32 CalculateOptimalLODLevel() const;
    
    /** Aplicar configurações de streaming para elemento específico */
    void ApplyStreamingConfigToElement(FAURACRONPCGStreamingEntry& Element);
    
    /** Otimizar uso de memória para streaming */
    void OptimizeMemoryUsage();
    
    /** Registrar tempo de carregamento para elemento */
    void RegisterElementLoadTime(const FAURACRONPCGStreamingEntry& Element, float LoadTimeMS);
    
    /** Detectar e registrar hitches durante streaming */
    void DetectStreamingHitches(float DeltaTime);
    
    /** Calcular prioridade de streaming para elemento */
    float CalculateStreamingPriority(const FAURACRONPCGStreamingEntry& Element, const FVector& PlayerLocation) const;
};
