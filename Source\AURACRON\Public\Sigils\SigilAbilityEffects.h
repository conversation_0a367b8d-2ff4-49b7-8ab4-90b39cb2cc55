// SigilAbilityEffects.h
// AURACRON - GameplayEffects para as Habilidades Exclusivas dos Sígilos
// Define os efeitos de gameplay para Murallion, Fracasso Prismal e Sopro de Fluxo

#pragma once

#include "CoreMinimal.h"
#include "GameplayEffect.h"
#include "GameplayTagContainer.h"
#include "AttributeSet.h"
#include "GameplayEffectExecutionCalculation.h"
#include "AbilitySystemComponent.h"
#include "Net/UnrealNetwork.h"
#include "SigilAbilityEffects.generated.h"

// Definindo ATTRIBUTE_ACCESSORS para UE 5.6
#define ATTRIBUTE_ACCESSORS(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_GETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_SETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_INITTER(PropertyName)

// ========================================
// TAGS PARA HABILIDADES DOS SÍGILOS
// ========================================

namespace SigilAbilityTags
{
    // Tags base
    AURACRON_API extern const FGameplayTag Ability_Sigil;
    
    // Tags específicas das habilidades
    AURACRON_API extern const FGameplayTag Ability_Sigil_Aegis_Murallion;
    AURACRON_API extern const FGameplayTag Ability_Sigil_Ruin_FracassoPrismal;
    AURACRON_API extern const FGameplayTag Ability_Sigil_Vesper_SoproDeFluxo;
    
    // Tags de estado
    AURACRON_API extern const FGameplayTag State_Barrier;
    AURACRON_API extern const FGameplayTag State_CooldownReset;
    AURACRON_API extern const FGameplayTag State_Dash;
    AURACRON_API extern const FGameplayTag State_Shield;
    
    // Tags de efeito
    AURACRON_API extern const FGameplayTag Effect_BarrierProtection;
    AURACRON_API extern const FGameplayTag Effect_DamageBuff;
    AURACRON_API extern const FGameplayTag Effect_Shield;
    
    // Tags de imunidade
    AURACRON_API extern const FGameplayTag Immunity_Damage;
    AURACRON_API extern const FGameplayTag Immunity_Debuff;
}

// ========================================
// ATTRIBUTE SET PARA HABILIDADES DOS SÍGILOS
// ========================================

UCLASS(BlueprintType)
class AURACRON_API USigilAbilityAttributeSet : public UAttributeSet
{
    GENERATED_BODY()

public:
    USigilAbilityAttributeSet();

    // Atributos de proteção
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities", ReplicatedUsing = OnRep_BarrierProtection)
    FGameplayAttributeData BarrierProtection;
    ATTRIBUTE_ACCESSORS(USigilAbilityAttributeSet, BarrierProtection)

    // Atributos de dano
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities", ReplicatedUsing = OnRep_DamageMultiplier)
    FGameplayAttributeData DamageMultiplier;
    ATTRIBUTE_ACCESSORS(USigilAbilityAttributeSet, DamageMultiplier)

    // Atributos de escudo
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities", ReplicatedUsing = OnRep_ShieldAmount)
    FGameplayAttributeData ShieldAmount;
    ATTRIBUTE_ACCESSORS(USigilAbilityAttributeSet, ShieldAmount)

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities", ReplicatedUsing = OnRep_MaxShieldAmount)
    FGameplayAttributeData MaxShieldAmount;
    ATTRIBUTE_ACCESSORS(USigilAbilityAttributeSet, MaxShieldAmount)

    // Atributos de dano para UE 5.6
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities", ReplicatedUsing = OnRep_IncomingDamage)
    FGameplayAttributeData IncomingDamage;
    ATTRIBUTE_ACCESSORS(USigilAbilityAttributeSet, IncomingDamage)

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities", ReplicatedUsing = OnRep_OutgoingDamage)
    FGameplayAttributeData OutgoingDamage;
    ATTRIBUTE_ACCESSORS(USigilAbilityAttributeSet, OutgoingDamage)

    // Replication
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
    // Rep notifies
    UFUNCTION()
    virtual void OnRep_BarrierProtection(const FGameplayAttributeData& OldBarrierProtection);

    UFUNCTION()
    virtual void OnRep_DamageMultiplier(const FGameplayAttributeData& OldDamageMultiplier);

    UFUNCTION()
    virtual void OnRep_ShieldAmount(const FGameplayAttributeData& OldShieldAmount);

    UFUNCTION()
    virtual void OnRep_MaxShieldAmount(const FGameplayAttributeData& OldMaxShieldAmount);

    UFUNCTION()
    virtual void OnRep_IncomingDamage(const FGameplayAttributeData& OldIncomingDamage);

    UFUNCTION()
    virtual void OnRep_OutgoingDamage(const FGameplayAttributeData& OldOutgoingDamage);
};

// ========================================
// EXECUTION CALCULATIONS
// ========================================

/**
 * Cálculo de execução para proteção da barreira Murallion
 */
UCLASS()
class AURACRON_API USigilBarrierProtectionCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilBarrierProtectionCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    // Capture definitions
    FGameplayEffectAttributeCaptureDefinition IncomingDamageDef;
    FGameplayEffectAttributeCaptureDefinition BarrierProtectionDef;
};

/**
 * Cálculo de execução para buff de dano do Fracasso Prismal
 */
UCLASS()
class AURACRON_API USigilDamageBuffCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilDamageBuffCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    // Capture definitions
    FGameplayEffectAttributeCaptureDefinition BaseDamageDef;
    FGameplayEffectAttributeCaptureDefinition DamageMultiplierDef;
};

/**
 * Cálculo de execução para escudo do Sopro de Fluxo
 */
UCLASS()
class AURACRON_API USigilShieldCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilShieldCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    // Capture definitions
    FGameplayEffectAttributeCaptureDefinition IncomingDamageDef;
    FGameplayEffectAttributeCaptureDefinition ShieldAmountDef;
};

/**
 * Cálculo de execução para redução de cooldown do Fracasso Prismal
 */
UCLASS()
class AURACRON_API USigilCooldownReductionCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilCooldownReductionCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    // Capture definitions
    FGameplayEffectAttributeCaptureDefinition CooldownReductionDef;
};

// ========================================
// GAMEPLAY EFFECTS BASE
// ========================================

/**
 * Classe base para todos os GameplayEffects das habilidades dos sígilos
 */
UCLASS(Abstract, BlueprintType)
class AURACRON_API USigilAbilityEffectBase : public UGameplayEffect
{
    GENERATED_BODY()

public:
    USigilAbilityEffectBase();

protected:
    // Configurações base
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability Effect")
    float BaseEffectMagnitude;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability Effect")
    FGameplayTagContainer RequiredTags;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability Effect")
    FGameplayTagContainer BlockedTags;
};

// ========================================
// EFEITOS ESPECÍFICOS - MURALLION (AEGIS)
// ========================================

/**
 * GameplayEffect para proteção da barreira Murallion
 */
UCLASS(BlueprintType)
class AURACRON_API UGE_Murallion_BarrierProtection : public USigilAbilityEffectBase
{
    GENERATED_BODY()

public:
    UGE_Murallion_BarrierProtection();

public:
    // Configurações específicas da barreira
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier Protection")
    float DamageReductionPercent;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier Protection")
    bool bBlocksDebuffs;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier Protection")
    FGameplayTagContainer ImmunityTags;
};

/**
 * GameplayEffect para regeneração dentro da barreira
 */
UCLASS(BlueprintType)
class AURACRON_API UGE_Murallion_BarrierRegeneration : public USigilAbilityEffectBase
{
    GENERATED_BODY()

public:
    UGE_Murallion_BarrierRegeneration();

public:
    // Configurações de regeneração
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier Regeneration")
    float HealthRegenPerSecond;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Barrier Regeneration")
    float ManaRegenPerSecond;
};

// ========================================
// EFEITOS ESPECÍFICOS - FRACASSO PRISMAL (RUIN)
// ========================================

/**
 * GameplayEffect para buff de dano do Fracasso Prismal
 */
UCLASS(BlueprintType)
class AURACRON_API UGE_FracassoPrismal_DamageBuff : public USigilAbilityEffectBase
{
    GENERATED_BODY()

public:
    UGE_FracassoPrismal_DamageBuff();

public:
    // Configurações do buff de dano
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage Buff")
    float DamageMultiplier;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage Buff")
    bool bAffectsAbilities;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage Buff")
    bool bAffectsBasicAttacks;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage Buff")
    FGameplayTagContainer AffectedDamageTypes;
};

/**
 * GameplayEffect para redução de cooldown do Fracasso Prismal
 */
UCLASS(BlueprintType)
class AURACRON_API UGE_FracassoPrismal_CooldownReduction : public USigilAbilityEffectBase
{
    GENERATED_BODY()

public:
    UGE_FracassoPrismal_CooldownReduction();

public:
    // Configurações de redução de cooldown
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Cooldown Reduction")
    float CooldownReductionPercent;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Cooldown Reduction")
    FGameplayTagContainer AffectedAbilities;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Cooldown Reduction")
    bool bAffectsAllAbilities;
};

// ========================================
// EFEITOS ESPECÍFICOS - SOPRO DE FLUXO (VESPER)
// ========================================

/**
 * GameplayEffect para escudo do Sopro de Fluxo
 */
UCLASS(BlueprintType)
class AURACRON_API UGE_SoproDeFluxo_Shield : public USigilAbilityEffectBase
{
    GENERATED_BODY()

public:
    UGE_SoproDeFluxo_Shield();

public:
    // Configurações do escudo
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Shield")
    float ShieldAmount;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Shield")
    bool bBlocksMagicalDamage;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Shield")
    bool bBlocksPhysicalDamage;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Shield")
    float ShieldDecayRate;
};

/**
 * GameplayEffect para mobilidade durante o dash
 */
UCLASS(BlueprintType)
class AURACRON_API UGE_SoproDeFluxo_DashMobility : public USigilAbilityEffectBase
{
    GENERATED_BODY()

public:
    UGE_SoproDeFluxo_DashMobility();

public:
    // Configurações de mobilidade
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash Mobility")
    float MovementSpeedMultiplier;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash Mobility")
    bool bIgnoresCollision;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash Mobility")
    bool bImmuneToSlows;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash Mobility")
    FGameplayTagContainer ImmunityTags;
};

// ========================================
// FACTORY PARA CRIAÇÃO DE EFEITOS
// ========================================

/**
 * Factory para criar GameplayEffects das habilidades dos sígilos
 */
UCLASS(BlueprintType)
class AURACRON_API USigilAbilityEffectFactory : public UObject
{
    GENERATED_BODY()

public:
    USigilAbilityEffectFactory();

    // Métodos de criação de efeitos
    UFUNCTION(BlueprintCallable, Category = "Sigil Ability Effects")
    static UGameplayEffect* CreateMurallionBarrierProtection(float DamageReduction = 0.5f, float Duration = 3.0f);

    UFUNCTION(BlueprintCallable, Category = "Sigil Ability Effects")
    static UGameplayEffect* CreateMurallionBarrierRegeneration(float HealthRegen = 10.0f, float ManaRegen = 5.0f, float Duration = 3.0f);

    UFUNCTION(BlueprintCallable, Category = "Sigil Ability Effects")
    static UGameplayEffect* CreateFracassoPrismalDamageBuff(float DamageMultiplier = 1.25f, float Duration = 8.0f);

    UFUNCTION(BlueprintCallable, Category = "Sigil Ability Effects")
    static UGameplayEffect* CreateFracassoPrismalCooldownReduction(float ReductionPercent = 0.5f);

    UFUNCTION(BlueprintCallable, Category = "Sigil Ability Effects")
    static UGameplayEffect* CreateSoproDeFluxoShield(float ShieldAmount = 200.0f, float Duration = 5.0f);

    UFUNCTION(BlueprintCallable, Category = "Sigil Ability Effects")
    static UGameplayEffect* CreateSoproDeFluxoDashMobility(float SpeedMultiplier = 2.0f, float Duration = 2.0f);

protected:
    // Templates de efeitos
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effect Templates")
    TSubclassOf<UGE_Murallion_BarrierProtection> MurallionBarrierProtectionClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effect Templates")
    TSubclassOf<UGE_Murallion_BarrierRegeneration> MurallionBarrierRegenerationClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effect Templates")
    TSubclassOf<UGE_FracassoPrismal_DamageBuff> FracassoPrismalDamageBuffClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effect Templates")
    TSubclassOf<UGE_FracassoPrismal_CooldownReduction> FracassoPrismalCooldownReductionClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effect Templates")
    TSubclassOf<UGE_SoproDeFluxo_Shield> SoproDeFluxoShieldClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effect Templates")
    TSubclassOf<UGE_SoproDeFluxo_DashMobility> SoproDeFluxoDashMobilityClass;

private:
    // Métodos auxiliares
    static void ConfigureBaseEffect(UGameplayEffect* Effect, float Duration, const FGameplayTagContainer& GrantedTags);
    static void AddAttributeModifier(UGameplayEffect* Effect, FGameplayAttribute Attribute, float Magnitude, EGameplayModOp::Type ModOp = EGameplayModOp::Additive);
};

// ========================================
// CONSTANTES E CONFIGURAÇÕES
// ========================================

namespace SigilAbilityConstants
{
    // Durações padrão
    constexpr float DEFAULT_BARRIER_DURATION = 3.0f;
    constexpr float DEFAULT_DAMAGE_BUFF_DURATION = 8.0f;
    constexpr float DEFAULT_SHIELD_DURATION = 5.0f;
    constexpr float DEFAULT_DASH_DURATION = 2.0f;
    
    // Magnitudes padrão
    constexpr float DEFAULT_DAMAGE_REDUCTION = 0.5f;
    constexpr float DEFAULT_DAMAGE_MULTIPLIER = 1.25f;
    constexpr float DEFAULT_COOLDOWN_REDUCTION = 0.5f;
    constexpr float DEFAULT_SHIELD_AMOUNT = 200.0f;
    
    // Limites
    constexpr float MAX_DAMAGE_REDUCTION = 0.9f;
    constexpr float MAX_DAMAGE_MULTIPLIER = 3.0f;
    constexpr float MAX_COOLDOWN_REDUCTION = 0.9f;
    constexpr float MAX_SHIELD_AMOUNT = 1000.0f;
}