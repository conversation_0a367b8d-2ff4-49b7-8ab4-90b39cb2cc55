// SigilAttributeSet.h
// AURACRON - Sistema de Sígilos
// Conjunto de atributos espectrais para GameplayAbilities System UE 5.6
// APIs verificadas: AttributeSet.h, AbilitySystemComponent.h, GameplayEffectExtension.h

#pragma once

#include "CoreMinimal.h"
#include "AttributeSet.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectExtension.h"
#include "GameplayEffect.h"
#include "Net/UnrealNetwork.h"
#include "SigilAttributeSet.generated.h"

// Macro para criar getters/setters de atributos de forma otimizada
// Compatível com UE 5.6 GameplayAbilities System
#define ATTRIBUTE_ACCESSORS(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_GETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_SETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_INITTER(PropertyName)

/**
 * Conjunto de atributos espectrais para sígilos
 * Implementa sistema de atributos primários, derivados e de estado
 * Suporta replicação multiplayer para MOBA 5x5 (10 jogadores)
 */
UCLASS(BlueprintType)
class AURACRON_API USigilAttributeSet : public UAttributeSet
{
    GENERATED_BODY()

public:
    USigilAttributeSet();

    // UAttributeSet Interface
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    virtual void PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue) override;
    virtual void PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data) override;
    virtual void PreAttributeBaseChange(const FGameplayAttribute& Attribute, float& NewValue) const override;
    virtual void PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue) override;

    // ========================================
    // ATRIBUTOS PRIMÁRIOS ESPECTRAIS
    // ========================================

    /**
     * Poder Espectral - Aumenta dano de ataques e habilidades
     * Base para cálculo de AttackPower
     */
    UPROPERTY(BlueprintReadOnly, Category = "Primary Spectral Attributes", ReplicatedUsing = OnRep_SpectralPower)
    FGameplayAttributeData SpectralPower;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SpectralPower)

    /**
     * Resistência Espectral - Aumenta defesa e resistência a dano
     * Base para cálculo de DefensePower
     */
    UPROPERTY(BlueprintReadOnly, Category = "Primary Spectral Attributes", ReplicatedUsing = OnRep_SpectralResilience)
    FGameplayAttributeData SpectralResilience;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SpectralResilience)

    /**
     * Velocidade Espectral - Aumenta velocidade de movimento e ataque
     * Base para cálculo de MovementSpeed e AttackSpeed
     */
    UPROPERTY(BlueprintReadOnly, Category = "Primary Spectral Attributes", ReplicatedUsing = OnRep_SpectralVelocity)
    FGameplayAttributeData SpectralVelocity;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SpectralVelocity)

    /**
     * Foco Espectral - Reduz cooldowns e aumenta regeneração de mana
     * Base para cálculo de CooldownReduction e ManaRegeneration
     */
    UPROPERTY(BlueprintReadOnly, Category = "Primary Spectral Attributes", ReplicatedUsing = OnRep_SpectralFocus)
    FGameplayAttributeData SpectralFocus;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SpectralFocus)

    // ========================================
    // ATRIBUTOS DERIVADOS DE COMBATE
    // ========================================

    /**
     * Poder de Ataque - Derivado de SpectralPower
     * Fórmula: BaseDamage + (SpectralPower * 1.5)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Derived Combat Attributes", ReplicatedUsing = OnRep_AttackPower)
    FGameplayAttributeData AttackPower;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, AttackPower)

    /**
     * Poder de Defesa - Derivado de SpectralResilience
     * Fórmula: BaseDefense + (SpectralResilience * 1.2)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Derived Combat Attributes", ReplicatedUsing = OnRep_DefensePower)
    FGameplayAttributeData DefensePower;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, DefensePower)

    /**
     * Velocidade de Ataque - Derivado de SpectralVelocity
     * Fórmula: BaseAttackSpeed + (SpectralVelocity * 0.01)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Derived Combat Attributes", ReplicatedUsing = OnRep_AttackSpeed)
    FGameplayAttributeData AttackSpeed;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, AttackSpeed)

    /**
     * Chance de Crítico - Baseado em combinação de atributos
     * Fórmula: BaseCritChance + ((SpectralPower + SpectralFocus) * 0.001)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Derived Combat Attributes", ReplicatedUsing = OnRep_CriticalChance)
    FGameplayAttributeData CriticalChance;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, CriticalChance)

    /**
     * Multiplicador de Crítico - Baseado em SpectralPower
     * Fórmula: BaseCritMultiplier + (SpectralPower * 0.002)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Derived Combat Attributes", ReplicatedUsing = OnRep_CriticalMultiplier)
    FGameplayAttributeData CriticalMultiplier;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, CriticalMultiplier)

    // ========================================
    // ATRIBUTOS DE MOBILIDADE
    // ========================================

    /**
     * Velocidade de Movimento - Derivado de SpectralVelocity
     * Fórmula: BaseMovementSpeed + (SpectralVelocity * 2.0)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Mobility Attributes", ReplicatedUsing = OnRep_MovementSpeed)
    FGameplayAttributeData MovementSpeed;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, MovementSpeed)

    /**
     * Redução de Cooldown - Derivado de SpectralFocus
     * Fórmula: BaseCDR + (SpectralFocus * 0.01) [Max 40%]
     */
    UPROPERTY(BlueprintReadOnly, Category = "Mobility Attributes", ReplicatedUsing = OnRep_CooldownReduction)
    FGameplayAttributeData CooldownReduction;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, CooldownReduction)

    // ========================================
    // ATRIBUTOS DE RECURSOS
    // ========================================

    /**
     * Regeneração de Mana - Derivado de SpectralFocus
     * Fórmula: BaseManaRegen + (SpectralFocus * 0.5)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Resource Attributes", ReplicatedUsing = OnRep_ManaRegeneration)
    FGameplayAttributeData ManaRegeneration;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, ManaRegeneration)

    /**
     * Regeneração de Vida - Derivado de SpectralResilience
     * Fórmula: BaseHealthRegen + (SpectralResilience * 0.3)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Resource Attributes", ReplicatedUsing = OnRep_HealthRegeneration)
    FGameplayAttributeData HealthRegeneration;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, HealthRegeneration)

    // ========================================
    // ATRIBUTOS DE ESTADO DO SIGILO
    // ========================================

    /**
     * Multiplicador de Fusão - Aplicado após 6 minutos
     * Valor padrão: 1.0, Fusão: 1.5-2.0 baseado na raridade
     */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil State Attributes", ReplicatedUsing = OnRep_FusionMultiplier)
    FGameplayAttributeData FusionMultiplier;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, FusionMultiplier)

    /**
     * Slots de Sigilo Disponíveis - Máximo 6
     * Aumenta com level do jogador
     */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil State Attributes", ReplicatedUsing = OnRep_SigilSlots)
    FGameplayAttributeData SigilSlots;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SigilSlots)

    /**
     * Eficiência de Sigilo - Reduz penalidades por múltiplos sígilos
     * Fórmula: BaseEfficiency + (SpectralFocus * 0.005)
     */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil State Attributes", ReplicatedUsing = OnRep_SigilEfficiency)
    FGameplayAttributeData SigilEfficiency;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SigilEfficiency)

    /**
     * Experiência de Sigilo - Para progressão de sígilos individuais
     * Ganha experiência através de combate e objetivos
     */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil State Attributes", ReplicatedUsing = OnRep_SigilExperience)
    FGameplayAttributeData SigilExperience;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, SigilExperience)

    // ========================================
    // ATRIBUTOS ESPECIAIS MOBA
    // ========================================

    /**
     * Bônus de Team Fight - Aumenta com proximidade de aliados
     * Máximo 25% com 4 aliados próximos
     */
    UPROPERTY(BlueprintReadOnly, Category = "MOBA Attributes", ReplicatedUsing = OnRep_TeamFightBonus)
    FGameplayAttributeData TeamFightBonus;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, TeamFightBonus)

    /**
     * Bônus de Objetivo - Aumenta dano contra estruturas e monstros épicos
     * Baseado em combinação de atributos espectrais
     */
    UPROPERTY(BlueprintReadOnly, Category = "MOBA Attributes", ReplicatedUsing = OnRep_ObjectiveBonus)
    FGameplayAttributeData ObjectiveBonus;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, ObjectiveBonus)

    /**
     * Resistência a Crowd Control - Reduz duração de CCs
     * Fórmula: BaseCCResistance + (SpectralResilience * 0.002)
     */
    UPROPERTY(BlueprintReadOnly, Category = "MOBA Attributes", ReplicatedUsing = OnRep_CCResistance)
    FGameplayAttributeData CCResistance;
    ATTRIBUTE_ACCESSORS(USigilAttributeSet, CCResistance)

    // ========================================
    // FUNÇÕES PÚBLICAS
    // ========================================

    /**
     * Recalcula todos os atributos derivados
     * Chamado quando atributos primários mudam
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Attributes")
    void RecalculateDerivedAttributes();

    /**
     * Aplica multiplicador de fusão a todos os atributos
     * Chamado quando fusão é ativada aos 6 minutos
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Attributes")
    void ApplyFusionMultiplier(float Multiplier);

    /**
     * Remove multiplicador de fusão
     * Usado para reset ou debugging
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Attributes")
    void RemoveFusionMultiplier();

    /**
     * Calcula poder total do sigilo
     * Soma ponderada de todos os atributos espectrais
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Attributes")
    float CalculateTotalSigilPower() const;

    /**
     * Verifica se atributo está no máximo
     * Usado para validação de upgrades
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Attributes")
    bool IsAttributeAtMaximum(const FGameplayAttribute& Attribute) const;

    /**
     * Obtém valor efetivo de atributo (com multiplicadores)
     * Inclui fusão e outros modificadores temporários
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Attributes")
    float GetEffectiveAttributeValue(const FGameplayAttribute& Attribute) const;

protected:
    // ========================================
    // FUNÇÕES DE REPLICAÇÃO
    // ========================================

    // Atributos Primários
    UFUNCTION()
    virtual void OnRep_SpectralPower(const FGameplayAttributeData& OldSpectralPower);

    UFUNCTION()
    virtual void OnRep_SpectralResilience(const FGameplayAttributeData& OldSpectralResilience);

    UFUNCTION()
    virtual void OnRep_SpectralVelocity(const FGameplayAttributeData& OldSpectralVelocity);

    UFUNCTION()
    virtual void OnRep_SpectralFocus(const FGameplayAttributeData& OldSpectralFocus);

    // Atributos Derivados de Combate
    UFUNCTION()
    virtual void OnRep_AttackPower(const FGameplayAttributeData& OldAttackPower);

    UFUNCTION()
    virtual void OnRep_DefensePower(const FGameplayAttributeData& OldDefensePower);

    UFUNCTION()
    virtual void OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed);

    UFUNCTION()
    virtual void OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance);

    UFUNCTION()
    virtual void OnRep_CriticalMultiplier(const FGameplayAttributeData& OldCriticalMultiplier);

    // Atributos de Mobilidade
    UFUNCTION()
    virtual void OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed);

    UFUNCTION()
    virtual void OnRep_CooldownReduction(const FGameplayAttributeData& OldCooldownReduction);

    // Atributos de Recursos
    UFUNCTION()
    virtual void OnRep_ManaRegeneration(const FGameplayAttributeData& OldManaRegeneration);

    UFUNCTION()
    virtual void OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration);

    // Atributos de Estado
    UFUNCTION()
    virtual void OnRep_FusionMultiplier(const FGameplayAttributeData& OldFusionMultiplier);

    UFUNCTION()
    virtual void OnRep_SigilSlots(const FGameplayAttributeData& OldSigilSlots);

    UFUNCTION()
    virtual void OnRep_SigilEfficiency(const FGameplayAttributeData& OldSigilEfficiency);

    UFUNCTION()
    virtual void OnRep_SigilExperience(const FGameplayAttributeData& OldSigilExperience);

    // Atributos MOBA
    UFUNCTION()
    virtual void OnRep_TeamFightBonus(const FGameplayAttributeData& OldTeamFightBonus);

    UFUNCTION()
    virtual void OnRep_ObjectiveBonus(const FGameplayAttributeData& OldObjectiveBonus);

    UFUNCTION()
    virtual void OnRep_CCResistance(const FGameplayAttributeData& OldCCResistance);

private:
    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================

    /**
     * Ajusta atributo quando seu máximo muda
     * Garante que valor atual não exceda o máximo
     */
    void AdjustAttributeForMaxChange(const FGameplayAttributeData& AffectedAttribute, 
                                   const FGameplayAttributeData& MaxAttribute, 
                                   float NewMaxValue, 
                                   const FGameplayAttribute& AffectedAttributeProperty) const;

    /**
     * Calcula valor derivado baseado em fórmula específica
     * Usado para recalcular atributos derivados
     */
    float CalculateDerivedValue(const FGameplayAttribute& PrimaryAttribute, 
                              float Multiplier, 
                              float BaseValue = 0.0f) const;

    /**
     * Aplica limites mínimos e máximos aos atributos
     * Garante que valores permaneçam dentro de ranges válidos
     */
    void ClampAttributeValue(const FGameplayAttribute& Attribute, float& Value) const;

    /**
     * Notifica mudanças de atributos para sistemas dependentes
     * Usado para atualizar UI e outros componentes
     */
    void NotifyAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue);

    // ========================================
    // CONSTANTES DO SISTEMA
    // ========================================

    // Multiplicadores para cálculo de atributos derivados
    static constexpr float ATTACK_POWER_MULTIPLIER = 1.5f;
    static constexpr float DEFENSE_POWER_MULTIPLIER = 1.2f;
    static constexpr float ATTACK_SPEED_MULTIPLIER = 0.01f;
    static constexpr float MOVEMENT_SPEED_MULTIPLIER = 2.0f;
    static constexpr float COOLDOWN_REDUCTION_MULTIPLIER = 0.01f;
    static constexpr float MANA_REGEN_MULTIPLIER = 0.5f;
    static constexpr float HEALTH_REGEN_MULTIPLIER = 0.3f;
    static constexpr float CRIT_CHANCE_MULTIPLIER = 0.001f;
    static constexpr float CRIT_MULTIPLIER_MULTIPLIER = 0.002f;

    // Limites máximos para atributos
    static constexpr float MAX_COOLDOWN_REDUCTION = 40.0f;
    static constexpr float MAX_CRITICAL_CHANCE = 100.0f;
    static constexpr float MAX_CC_RESISTANCE = 75.0f;
    static constexpr int32 MAX_SIGIL_SLOTS = 6;

    // Valores base para cálculos
    static constexpr float BASE_CRITICAL_MULTIPLIER = 150.0f;
    static constexpr float BASE_MOVEMENT_SPEED = 300.0f;
    static constexpr float BASE_ATTACK_SPEED = 1.0f;

    // Cache para otimização de performance
    mutable float CachedTotalPower = -1.0f;
    mutable bool bTotalPowerCacheDirty = true;
};