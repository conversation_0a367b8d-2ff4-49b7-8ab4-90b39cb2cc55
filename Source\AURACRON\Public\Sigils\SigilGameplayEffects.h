// SigilGameplayEffects.h
// AURACRON - Sistema de Sígilos
// GameplayEffects para fusão automática e modificadores espectrais
// APIs verificadas: GameplayEffect.h, GameplayEffectExecutionCalculation.h

#pragma once

#include "CoreMinimal.h"
#include "GameplayEffect.h"
#include "GameplayEffectExecutionCalculation.h"
#include "GameplayEffectTypes.h"
#include "GameplayTagContainer.h"
#include "Sigils/SigilItem.h"
#include "SigilGameplayEffects.generated.h"

// ========================================
// ENUMS E ESTRUTURAS
// ========================================

/**
 * Tipos de efeitos espectrais para sígilos
 */
UENUM(BlueprintType)
enum class ESpectralEffectType : uint8
{
    None            UMETA(DisplayName = "None"),
    PowerBoost      UMETA(DisplayName = "Power Boost"),
    ResilienceBoost UMETA(DisplayName = "Resilience Boost"),
    VelocityBoost   UMETA(DisplayName = "Velocity Boost"),
    FocusBoost      UMETA(DisplayName = "Focus Boost"),
    CombatBonus     UMETA(DisplayName = "Combat Bonus"),
    MobilityBonus   UMETA(DisplayName = "Mobility Bonus"),
    ResourceBonus   UMETA(DisplayName = "Resource Bonus"),
    TeamFightBonus  UMETA(DisplayName = "Team Fight Bonus"),
    ObjectiveBonus  UMETA(DisplayName = "Objective Bonus"),
    FusionMultiplier UMETA(DisplayName = "Fusion Multiplier")
};

/**
 * Configuração de efeito espectral
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSpectralEffectConfig
{
    GENERATED_BODY()

    /** Tipo do efeito espectral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect")
    ESpectralEffectType EffectType = ESpectralEffectType::None;

    /** Magnitude base do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect")
    float BaseMagnitude = 1.0f;

    /** Multiplicador por raridade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect")
    TMap<ESigilRarity, float> RarityMultipliers;

    /** Duração do efeito (-1 para infinito) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect")
    float Duration = -1.0f;

    /** Tags de gameplay associadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect")
    FGameplayTagContainer EffectTags;

    /** Se o efeito pode ser empilhado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect")
    bool bCanStack = false;

    /** Máximo de empilhamentos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Effect", meta = (EditCondition = "bCanStack"))
    int32 MaxStacks = 1;

    FSpectralEffectConfig()
    {
        // Configurar multiplicadores padrão por raridade
        RarityMultipliers.Add(ESigilRarity::Common, 1.0f);
        RarityMultipliers.Add(ESigilRarity::Rare, 1.25f);
        RarityMultipliers.Add(ESigilRarity::Epic, 1.5f);
        RarityMultipliers.Add(ESigilRarity::Legendary, 2.0f);
    }
};

// ========================================
// GAMEPLAY EFFECT BASE
// ========================================

/**
 * GameplayEffect base para todos os efeitos de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilGameplayEffectBase : public UGameplayEffect
{
    GENERATED_BODY()

public:
    USigilGameplayEffectBase();

    /** Configuração do efeito espectral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Effect")
    FSpectralEffectConfig SpectralConfig;

    /** Raridade mínima necessária para aplicar o efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Effect")
    ESigilRarity MinimumRarity = ESigilRarity::Common;

    /** Tipo de sigilo necessário (None = qualquer tipo) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Effect")
    ESigilType RequiredSigilType = ESigilType::None;

    /** Se o efeito é aplicado apenas durante fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Effect")
    bool bFusionOnly = false;

    /** Multiplicador adicional durante team fights */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "MOBA")
    float TeamFightMultiplier = 1.0f;

    /** Multiplicador adicional perto de objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "MOBA")
    float ObjectiveMultiplier = 1.0f;

protected:
    virtual void PostInitProperties() override;
};

// ========================================
// EFEITOS ESPECÍFICOS DE FUSÃO
// ========================================

/**
 * Efeito de fusão automática - aplicado após 6 minutos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilAutoFusionEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilAutoFusionEffect();

    /** Tempo para fusão automática (padrão: 360 segundos = 6 minutos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Fusion")
    float FusionTimeSeconds = 360.0f;

    /** Multiplicador de poder após fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Fusion")
    float FusionPowerMultiplier = 1.5f;

    /** Multiplicadores específicos por raridade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Fusion")
    TMap<ESigilRarity, float> FusionMultipliersByRarity;

    /** Se a fusão é permanente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Fusion")
    bool bPermanentFusion = true;

protected:
    virtual void PostInitProperties() override;
};

/**
 * Efeito de fusão forçada - aplicado instantaneamente
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilForceFusionEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilForceFusionEffect();

    /** Custo de mana para fusão forçada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Force Fusion")
    float ManaCost = 100.0f;

    /** Cooldown após fusão forçada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Force Fusion")
    float CooldownSeconds = 30.0f;

    /** Multiplicador reduzido para fusão forçada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Force Fusion")
    float ForcedFusionPenalty = 0.8f;
};

// ========================================
// EFEITOS ESPECTRAIS POR ATRIBUTO
// ========================================

/**
 * Efeito de aumento de Poder Espectral
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilSpectralPowerEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilSpectralPowerEffect();

    /** Aumento base de poder espectral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Power")
    float PowerIncrease = 10.0f;

    /** Aumento percentual adicional */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Power")
    float PowerIncreasePercent = 0.0f;
};

/**
 * Efeito de aumento de Resistência Espectral
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilSpectralResilienceEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilSpectralResilienceEffect();

    /** Aumento base de resistência espectral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Resilience")
    float ResilienceIncrease = 8.0f;

    /** Redução de dano percentual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Resilience")
    float DamageReductionPercent = 5.0f;
};

/**
 * Efeito de aumento de Velocidade Espectral
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilSpectralVelocityEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilSpectralVelocityEffect();

    /** Aumento base de velocidade espectral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Velocity")
    float VelocityIncrease = 12.0f;

    /** Aumento de velocidade de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Velocity")
    float MovementSpeedIncrease = 25.0f;

    /** Redução de cooldown percentual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Velocity")
    float CooldownReductionPercent = 10.0f;
};

/**
 * Efeito de aumento de Foco Espectral
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilSpectralFocusEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilSpectralFocusEffect();

    /** Aumento base de foco espectral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Focus")
    float FocusIncrease = 15.0f;

    /** Aumento de chance crítica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Focus")
    float CriticalChanceIncrease = 8.0f;

    /** Aumento de regeneração de mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Focus")
    float ManaRegenIncrease = 5.0f;
};

// ========================================
// EFEITOS MOBA ESPECÍFICOS
// ========================================

/**
 * Efeito de bônus em team fights
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilTeamFightEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilTeamFightEffect();

    /** Bônus de dano em team fights */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Fight")
    float DamageBonus = 15.0f;

    /** Bônus de resistência em team fights */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Fight")
    float ResistanceBonus = 10.0f;

    /** Raio para detectar team fight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Fight")
    float TeamFightRadius = 1200.0f;

    /** Número mínimo de inimigos para ativar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Fight")
    int32 MinEnemiesForActivation = 2;
};

/**
 * Efeito de bônus perto de objetivos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilObjectiveEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilObjectiveEffect();

    /** Bônus de dano perto de objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    float ObjectiveDamageBonus = 20.0f;

    /** Bônus de velocidade de ataque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    float AttackSpeedBonus = 25.0f;

    /** Raio para detectar objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    float ObjectiveRadius = 800.0f;

    /** Tags de objetivos válidos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    FGameplayTagContainer ValidObjectiveTags;
};

// ========================================
// EFEITOS DE REFORGE
// ========================================

/**
 * Efeito aplicado durante reforge de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilReforgeEffect : public USigilGameplayEffectBase
{
    GENERATED_BODY()

public:
    USigilReforgeEffect();

    /** Cooldown do reforge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reforge")
    float ReforgeCooldown = 120.0f;

    /** Custo de ouro para reforge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reforge")
    int32 ReforgeGoldCost = 500;

    /** Chance de melhorar raridade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reforge")
    float RarityUpgradeChance = 10.0f;

    /** Chance de manter estatísticas atuais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reforge")
    float KeepStatsChance = 25.0f;
};

// ========================================
// EXECUTION CALCULATIONS
// ========================================

/**
 * Cálculo de execução para fusão de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilFusionExecutionCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilFusionExecutionCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams,
                                      FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    /** Calcular multiplicador de fusão baseado na raridade */
    float CalculateFusionMultiplier(ESigilRarity Rarity, ESigilType Type) const;

    /** Calcular bônus de team fight */
    float CalculateTeamFightBonus(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const;

    /** Calcular bônus de objetivo */
    float CalculateObjectiveBonus(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const;

    /** Verificar se está em team fight */
    bool IsInTeamFight(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const;

    /** Verificar se está perto de objetivo */
    bool IsNearObjective(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const;
};

/**
 * Cálculo de execução para efeitos espectrais
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilSpectralExecutionCalculation : public UGameplayEffectExecutionCalculation
{
    GENERATED_BODY()

public:
    USigilSpectralExecutionCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams,
                                      FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;

protected:
    /** Calcular modificação de atributo espectral */
    float CalculateSpectralModification(const FGameplayEffectCustomExecutionParameters& ExecutionParams,
                                      ESpectralEffectType EffectType) const;

    /** Aplicar multiplicadores de raridade */
    float ApplyRarityMultipliers(float BaseValue, ESigilRarity Rarity) const;

    /** Calcular sinergia entre sígilos */
    float CalculateSigilSynergy(const FGameplayEffectCustomExecutionParameters& ExecutionParams) const;
};

// ========================================
// FACTORY E UTILITY
// ========================================

/**
 * Factory para criar GameplayEffects de sígilos
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API USigilEffectFactory : public UObject
{
    GENERATED_BODY()

public:
    /** Criar efeito de fusão automática */
    UFUNCTION(BlueprintCallable, Category = "Sigil Effects")
    static TSubclassOf<UGameplayEffect> CreateAutoFusionEffect(ESigilRarity Rarity, ESigilType Type);

    /** Criar efeito espectral */
    UFUNCTION(BlueprintCallable, Category = "Sigil Effects")
    static TSubclassOf<UGameplayEffect> CreateSpectralEffect(ESpectralEffectType EffectType, 
                                                           ESigilRarity Rarity, 
                                                           float Magnitude);

    /** Criar efeito de team fight */
    UFUNCTION(BlueprintCallable, Category = "Sigil Effects")
    static TSubclassOf<UGameplayEffect> CreateTeamFightEffect(float DamageBonus, float ResistanceBonus);

    /** Criar efeito de objetivo */
    UFUNCTION(BlueprintCallable, Category = "Sigil Effects")
    static TSubclassOf<UGameplayEffect> CreateObjectiveEffect(float DamageBonus, float AttackSpeedBonus);

    /** Obter multiplicador padrão por raridade */
    UFUNCTION(BlueprintPure, Category = "Sigil Effects")
    static float GetDefaultRarityMultiplier(ESigilRarity Rarity);

    /** Obter duração padrão de fusão */
    UFUNCTION(BlueprintPure, Category = "Sigil Effects")
    static float GetDefaultFusionDuration() { return 360.0f; } // 6 minutos

    /** Validar configuração de efeito */
    UFUNCTION(BlueprintPure, Category = "Sigil Effects")
    static bool ValidateEffectConfig(const FSpectralEffectConfig& Config);

protected:
    /** Templates de efeitos por tipo */
    static TMap<ESpectralEffectType, TSubclassOf<UGameplayEffect>> EffectTemplates;

    /** Multiplicadores padrão por raridade */
    static TMap<ESigilRarity, float> DefaultRarityMultipliers;

    /** Inicializar templates estáticos */
    static void InitializeEffectTemplates();
};

// ========================================
// CONSTANTES E CONFIGURAÇÕES
// ========================================

namespace SigilEffectConstants
{
    // Tempos
    constexpr float DEFAULT_FUSION_TIME = 360.0f;        // 6 minutos
    constexpr float FORCE_FUSION_COOLDOWN = 30.0f;       // 30 segundos
    constexpr float REFORGE_COOLDOWN = 120.0f;           // 2 minutos
    
    // Multiplicadores
    constexpr float COMMON_MULTIPLIER = 1.0f;
    constexpr float RARE_MULTIPLIER = 1.25f;
    constexpr float EPIC_MULTIPLIER = 1.5f;
    constexpr float LEGENDARY_MULTIPLIER = 2.0f;
    
    // Bônus MOBA
    constexpr float TEAM_FIGHT_RADIUS = 1200.0f;
    constexpr float OBJECTIVE_RADIUS = 800.0f;
    constexpr int32 MIN_ENEMIES_TEAM_FIGHT = 2;
    
    // Custos
    constexpr float FORCE_FUSION_MANA_COST = 100.0f;
    constexpr int32 REFORGE_GOLD_COST = 500;
    
    // Chances
    constexpr float RARITY_UPGRADE_CHANCE = 10.0f;
    constexpr float KEEP_STATS_CHANCE = 25.0f;
}