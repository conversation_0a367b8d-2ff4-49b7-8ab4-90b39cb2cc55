// SigilItem.h
// AURACRON - Sistema de Sígilos
// Classe base para itens de sigilo com GameplayAbilities System UE 5.6
// APIs verificadas: AbilitySystemComponent.h, AttributeSet.h, GameplayTags.h

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "AbilitySystemInterface.h"
#include "AbilitySystemComponent.h"
#include "AttributeSet.h"
#include "GameplayTags.h"
#include "GameplayEffect.h"
#include "GameplayAbilitySpec.h"
#include "Engine/DataAsset.h"
#include "Engine/Texture2D.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Engine/StreamableManager.h"
#include "Net/UnrealNetwork.h"
#include "SigilItem.generated.h"

// Forward Declarations
class USigilAttributeSet;
class UGameplayEffect;
class UNiagaraSystem;
class UTexture2D;
class UStaticMeshComponent;

/**
 * Enum para tipos de sígilos espectrais
 * Usado para categorizar sígilos em Tank, Damage e Utility
 */
UENUM(BlueprintType)
enum class ESigilType : uint8
{
    None        UMETA(DisplayName = "None"),
    Tank        UMETA(DisplayName = "Tank - Defensive Focus"),
    Damage      UMETA(DisplayName = "Damage - Offensive Focus"),
    Utility     UMETA(DisplayName = "Utility - Support Focus")
};

/**
 * Enum para subtipos específicos de sígilos
 * Implementa os tipos Aegis, Ruin e Vesper do documento de design
 */
UENUM(BlueprintType)
enum class ESigilSubType : uint8
{
    None        UMETA(DisplayName = "None"),
    // Tank Subtypes
    Aegis       UMETA(DisplayName = "Aegis - Frontliner/Iniciado"),
    // Damage Subtypes  
    Ruin        UMETA(DisplayName = "Ruin - Burst/Skirmisher"),
    // Utility Subtypes
    Vesper      UMETA(DisplayName = "Vesper - Roamer/Suporte")
};

/**
 * Enum para raridade dos sígilos
 * Determina a potência dos efeitos e aparência visual
 */
UENUM(BlueprintType)
enum class ESigilRarity : uint8
{
    Common      UMETA(DisplayName = "Common - Basic Effects"),
    Rare        UMETA(DisplayName = "Rare - Moderate Effects"),
    Epic        UMETA(DisplayName = "Epic - High Effects"),
    Legendary   UMETA(DisplayName = "Legendary - Maximum Effects")
};

/**
 * Enum para estado do sigilo
 * Controla quando e como o sigilo pode ser modificado
 */
UENUM(BlueprintType)
enum class ESigilState : uint8
{
    Unequipped  UMETA(DisplayName = "Unequipped"),
    Equipped    UMETA(DisplayName = "Equipped"),
    Fused       UMETA(DisplayName = "Fused - Enhanced"),
    Locked      UMETA(DisplayName = "Locked - Cannot Modify")
};

/**
 * Enum para tipos de propriedades de sígilos
 */
UENUM(BlueprintType)
enum class ESigilPropertyType : uint8
{
    None        UMETA(DisplayName = "None"),
    Additive    UMETA(DisplayName = "Additive"),
    Multiplicative UMETA(DisplayName = "Multiplicative"),
    Override    UMETA(DisplayName = "Override")
};

/**
 * Estrutura para propriedades dinâmicas de sígilos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilProperty
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    FName PropertyName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    float Value = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Property")
    ESigilPropertyType PropertyType = ESigilPropertyType::Additive;

    FSigilProperty()
    {
        PropertyName = NAME_None;
        Value = 0.0f;
        PropertyType = ESigilPropertyType::Additive;
    }

    FSigilProperty(FName InName, float InValue, ESigilPropertyType InType = ESigilPropertyType::Additive)
        : PropertyName(InName), Value(InValue), PropertyType(InType)
    {
    }
};

/**
 * Estrutura para armazenar bônus espectrais calculados
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilSpectralBonus
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Bonus")
    FName BonusName;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Bonus")
    float BonusValue = 0.0f;
    
    FSigilSpectralBonus()
    {
        BonusName = NAME_None;
        BonusValue = 0.0f;
    }
    
    FSigilSpectralBonus(FName InName, float InValue)
    {
        BonusName = InName;
        BonusValue = InValue;
    }
};

/**
 * Struct para dados completos do sigilo
 * Contém todas as informações necessárias para funcionamento
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilData
{
    GENERATED_BODY()

    // Informações Básicas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    int32 SigilID = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    FText SigilName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    FText Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    ESigilType SigilType = ESigilType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    ESigilSubType SigilSubType = ESigilSubType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    ESigilRarity Rarity = ESigilRarity::Common;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    ESigilState State = ESigilState::Unequipped;

    // Visual Assets
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visuals")
    TSoftObjectPtr<UTexture2D> Icon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visuals")
    TSoftObjectPtr<UStaticMesh> SigilMesh;

    // GameplayTags System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTag SigilTag;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTagContainer RequiredTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTagContainer BlockedTags;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTag RarityTag;

    // GameplayEffects System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TArray<TSubclassOf<UGameplayEffect>> PassiveEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TArray<TSubclassOf<UGameplayEffect>> FusionEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TSubclassOf<UGameplayEffect> UniqueEffect;

    // Habilidades Exclusivas dos Subtipos
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Abilities")
    TSubclassOf<class UGameplayAbility> ExclusiveAbility;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Abilities")
    TArray<TSubclassOf<UGameplayEffect>> ExclusiveAbilityEffects;

    // Niagara VFX System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> EquipVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> FusionVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> AuraVFX;

    // Modificadores Espectrais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Modifiers")
    float SpectralPowerBonus = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Modifiers")
    float SpectralResilienceBonus = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Modifiers")
    float SpectralVelocityBonus = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Modifiers")
    float SpectralFocusBonus = 0.0f;

    // Sistema de Fusão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion")
    float FusionMultiplier = 1.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion")
    bool bCanBeFused = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion")
    bool bIsFused = false;

    // Propriedades de progressão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
    int32 CurrentLevel = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
    int32 MaxLevel = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
    float CurrentExperience = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
    int32 RequiredLevel = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
    float BasePower = 100.0f;

    // Estados adicionais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    ESigilRarity SigilRarity = ESigilRarity::Common;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    ESigilState SigilState = ESigilState::Unequipped;

    // VFX adicionais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> FusionAuraVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> IdleVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> LevelUpVFX;

    // Sons
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<class USoundBase> EquipSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<class USoundBase> FusionSound;

    // Propriedades dinâmicas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
    TArray<struct FSigilProperty> Properties;
    
    // Bônus espectrais calculados
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spectral Modifiers")
    TArray<FSigilSpectralBonus> SpectralBonuses;

    // Construtor padrão
    FSigilData()
    {
        SigilName = FText::FromString(TEXT("Unknown Sigil"));
        Description = FText::FromString(TEXT("A mysterious spectral sigil with unknown properties."));
        SigilType = ESigilType::None;
        Rarity = ESigilRarity::Common;
        State = ESigilState::Unequipped;
        
        // Tags padrão baseados no tipo
        SigilTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Type.None"));
        RarityTag = FGameplayTag::RequestGameplayTag(FName("Sigil.Rarity.Common"));
        
        // Valores espectrais padrão
        SpectralPowerBonus = 0.0f;
        SpectralResilienceBonus = 0.0f;
        SpectralVelocityBonus = 0.0f;
        SpectralFocusBonus = 0.0f;
        
        // Configurações de fusão
        FusionMultiplier = 1.5f;
        bCanBeFused = true;
        bIsFused = false;
        
        // Inicializar array de bônus espectrais
        SpectralBonuses.Empty();
    }
};

/**
 * Classe base para itens de sigilo espectral
 * Implementa IAbilitySystemInterface para integração com GAS
 * Suporta replicação multiplayer para 10 jogadores
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API ASigilItem : public AActor, public IAbilitySystemInterface
{
    GENERATED_BODY()

public:
    ASigilItem();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    virtual void PostInitializeComponents() override;

    // IAbilitySystemInterface Implementation
    virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;

    // Core Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USigilAttributeSet> AttributeSet;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> MeshComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UNiagaraComponent> VFXComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<class USphereComponent> CollisionComponent;

public:
    // Sigil Data - Replicated for multiplayer
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Data", ReplicatedUsing = OnRep_SigilData)
    FSigilData SigilData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Data", Replicated)
    int32 SigilLevel = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Data", Replicated)
    float ExperiencePoints = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Data", Replicated)
    TObjectPtr<AActor> EquippedOwner;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Data", Replicated)
    int32 SlotIndex = -1;

    // Propriedades adicionais para funcionalidade completa
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Data", Replicated)
    bool bIsEquipped = false;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Data", Replicated)
    bool bIsFused = false;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Data", Replicated)
    float LastReforgeTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Abilities")
    float LastExclusiveAbilityTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Data")
    AActor* OwningActor = nullptr;

    // Core Sigil Functions
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool EquipToActor(AActor* TargetActor, int32 TargetSlotIndex);

    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool UnequipFromActor();

    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool CanEquipToActor(AActor* TargetActor) const;

    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool CanUnequip() const;

    // Fusion System
    UFUNCTION(BlueprintCallable, Category = "Fusion System")
    bool TriggerFusion();

    UFUNCTION(BlueprintCallable, Category = "Fusion System")
    bool CanFuse() const;

    UFUNCTION(BlueprintCallable, Category = "Fusion System")
    void ApplyFusionEffects();

    UFUNCTION(BlueprintCallable, Category = "Fusion System")
    void RemoveFusionEffects();

    // Reforge System
    UFUNCTION(BlueprintCallable, Category = "Reforge System")
    bool ReforgeProperties();

    UFUNCTION(BlueprintCallable, Category = "Reforge System")
    bool CanReforge() const;

    // Getters - BlueprintPure for performance
    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    ESigilType GetSigilType() const { return SigilData.SigilType; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    ESigilRarity GetSigilRarity() const { return SigilData.Rarity; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    ESigilState GetSigilState() const { return SigilData.State; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    FGameplayTag GetSigilTag() const { return SigilData.SigilTag; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    int32 GetSigilID() const { return SigilData.SigilID; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    const FSigilData& GetSigilData() const { return SigilData; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    bool IsEquipped() const { return SigilData.State == ESigilState::Equipped || SigilData.State == ESigilState::Fused; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    bool IsFused() const { return SigilData.State == ESigilState::Fused; }

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    float GetTotalSpectralPower() const;

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    float GetEffectiveBonus(const FString& AttributeName) const;

    UFUNCTION(BlueprintPure, Category = "Sigil Info")
    float CalculateSigilPower() const
    {
        float BasePower = 100.0f; // Poder base
        float RarityMultiplier = 1.0f;
        
        // Multiplicador baseado na raridade
        switch (SigilData.Rarity)
        {
            case ESigilRarity::Common: RarityMultiplier = 1.0f; break;
            case ESigilRarity::Rare: RarityMultiplier = 2.0f; break;
            case ESigilRarity::Epic: RarityMultiplier = 3.0f; break;
            case ESigilRarity::Legendary: RarityMultiplier = 5.0f; break;
            default: RarityMultiplier = 1.0f; break;
        }
        
        return BasePower * RarityMultiplier;
    }

    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    void SetOwningSlot(int32 NewSlotIndex) { SlotIndex = NewSlotIndex; }

    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    void RegenerateSigilData();

    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    void SetSigilState(ESigilState NewState) { SigilData.State = NewState; }

    // Level and Experience System
    UFUNCTION(BlueprintCallable, Category = "Progression")
    void AddExperience(float Amount);

    UFUNCTION(BlueprintCallable, Category = "Progression")
    bool CanLevelUp() const;

    UFUNCTION(BlueprintCallable, Category = "Progression")
    void LevelUp();

    UFUNCTION(BlueprintPure, Category = "Progression")
    float GetExperienceToNextLevel() const;

    UFUNCTION(BlueprintPure, Category = "Progression")
    float GetExperienceRequiredForLevel(int32 TargetLevel) const;

    // VFX Management
    UFUNCTION(BlueprintCallable, Category = "VFX")
    void PlayEquipVFX();

    UFUNCTION(BlueprintCallable, Category = "VFX")
    void PlayFusionVFX();

    UFUNCTION(BlueprintCallable, Category = "VFX")
    void PlayAuraVFX(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "VFX")
    void UpdateRarityGlow();

protected:
    // Internal GameplayEffect Management
    void ApplyPassiveEffects();
    void RemovePassiveEffects();
    void ApplyUniqueEffect();
    void RemoveUniqueEffect();

    // Internal VFX Management
    void InitializeVFXComponent();
    void UpdateVFXBasedOnState();

    // Network Functions
    UFUNCTION(Server, Reliable)
    void ServerEquipToActor(AActor* TargetActor, int32 TargetSlotIndex);

    UFUNCTION(Server, Reliable)
    void ServerUnequipFromActor();

    UFUNCTION(Server, Reliable)
    void ServerTriggerFusion();

    UFUNCTION(Server, Reliable)
    void ServerReforgeProperties();

    UFUNCTION(NetMulticast, Reliable)
    void MulticastPlayEquipVFX();

    UFUNCTION(NetMulticast, Reliable)
    void MulticastPlayFusionVFX();

    UFUNCTION(NetMulticast, Reliable)
    void MulticastUpdateRarityGlow();

    // Replication Functions
    UFUNCTION()
    void OnRep_SigilData();

    // Internal State Management
    void UpdateSigilState(ESigilState NewState);
    void UpdateGameplayTags();
    void RecalculateSpectralBonuses();

    // Validation Functions
    bool ValidateEquipConditions(AActor* TargetActor) const;
    bool ValidateFusionConditions() const;
    bool ValidateReforgeConditions() const;

    // Métodos internos adicionais
    void ValidateSigilState();
    void RegenerateSigilProperties();
    int32 GetNumPropertiesForRarity(ESigilRarity Rarity) const;
    FSigilProperty GenerateRandomProperty() const;
    float CalculateBasePowerFromProperties() const;
    void ImprovePropertiesOnLevelUp();
    void PlayLevelUpVFX();
    void SetVFXRarityColor();
    void OnGameplayEffectApplied(UAbilitySystemComponent* ASC, const FGameplayEffectSpec& Spec, FActiveGameplayEffectHandle Handle);
    void OnGameplayEffectRemoved(const FActiveGameplayEffect& Effect);

    // Async Loading System (UE 5.6)
    void LoadNiagaraSystemAsync(const TSoftObjectPtr<UNiagaraSystem>& SystemPtr, FStreamableDelegate OnLoadedDelegate);
    void LoadSoundAsync(const TSoftObjectPtr<USoundBase>& SoundPtr, FStreamableDelegate OnLoadedDelegate);

    // Async Loading Callbacks
    void OnAuraVFXLoaded();
    void OnLevelUpVFXLoaded();
    void OnEquipVFXLoaded();
    void OnEquipSoundLoaded();
    void OnFusionVFXLoaded();
    void OnFusionSoundLoaded();
    void OnStateVFXLoaded();

    // Subtype-specific bonuses (AURACRON Documentation)
    void ApplySubtypePassiveBonuses();
    void ApplyAegisPassiveBonuses();
    void ApplyRuinPassiveBonuses();
    void ApplyVesperPassiveBonuses();
    void RemoveSubtypePassiveBonuses();

    // Exclusive Abilities (AURACRON Documentation)
    UFUNCTION(BlueprintCallable, Category = "Sigil Abilities")
    bool TriggerExclusiveAbility();

    UFUNCTION(BlueprintPure, Category = "Sigil Abilities")
    bool CanUseExclusiveAbility() const;

    bool TriggerMurallionAbility();      // Aegis - Circular barrier 3s
    bool TriggerPrismalFractureAbility(); // Ruin - Partial CD reset
    bool TriggerFlowBreathAbility();     // Vesper - Ally dash + shield
    void PlayExclusiveAbilityVFX();

    // Validation and Cache System (UE 5.6)
    bool HasNiagaraParameter(UNiagaraSystem* System, FName ParameterName) const;
    void ConfigureVFXLOD();

private:
    // Active GameplayEffect Handles for cleanup
    UPROPERTY()
    TArray<FActiveGameplayEffectHandle> ActivePassiveEffects;

    UPROPERTY()
    FActiveGameplayEffectHandle ActiveUniqueEffect;

    UPROPERTY()
    TArray<FActiveGameplayEffectHandle> ActiveFusionEffects;

    UPROPERTY()
    TArray<FActiveGameplayEffectHandle> ActiveSubtypeEffects;

    // Internal timers and state
    FTimerHandle FusionTimerHandle;
    FTimerHandle VFXUpdateTimerHandle;
    FTimerHandle ValidationTimerHandle;
    
    // Cached values for performance
    mutable float CachedTotalSpectralPower = -1.0f;
    mutable bool bSpectralPowerCacheDirty = true;

    // Constants for progression system
    static constexpr float BASE_EXPERIENCE_REQUIREMENT = 100.0f;
    static constexpr float EXPERIENCE_SCALING_FACTOR = 1.5f;
    static constexpr int32 MAX_SIGIL_LEVEL = 20;
};

/**
 * Data Asset para configuração de sígilos
 * Permite criação de templates de sígilos no editor
 */
UCLASS(BlueprintType)
class AURACRON_API USigilDataAsset : public UDataAsset
{
    GENERATED_BODY()

public:
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Template")
    FSigilData SigilTemplate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Settings")
    TSubclassOf<ASigilItem> SigilClass = ASigilItem::StaticClass();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Settings")
    float SpawnWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Settings")
    int32 MinPlayerLevel = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Settings")
    TArray<FGameplayTag> RequiredGameModes;
};