// SigilManagerComponent.h
// AURACRON - Sistema de Sígilos
// Componente principal para gerenciar sígilos de jogadores em MOBA 5x5
// APIs verificadas: AbilitySystemComponent.h, GameplayTags.h, GameplayEffect.h

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayTags.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "Net/UnrealNetwork.h"
#include "Sigils/SigilItem.h"
#include "SigilManagerComponent.generated.h"

// Forward Declarations
class ASigilItem;
class USigilAttributeSet;
class UGameplayEffect;
class UNiagaraComponent;
class UNiagaraSystem;

/**
 * Estrutura para dados de slot de sigilo
 * Contém informações sobre cada slot individual
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilSlotData
{
    GENERATED_BODY()

    /** Sigilo atualmente equipado neste slot */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Slot")
    TObjectPtr<ASigilItem> EquippedSigil;

    /** Se o slot está desbloqueado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Slot")
    bool bIsUnlocked;

    /** Level necessário para desbloquear este slot */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Slot")
    int32 UnlockLevel;

    /** Timestamp quando sigilo foi equipado (para fusão) */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Slot")
    float EquipTimestamp;

    /** Se o sigilo está pronto para fusão (6 minutos) */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Slot")
    bool bReadyForFusion;

    /** Multiplicador de fusão aplicado */
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Slot")
    float FusionMultiplier;

    /** GameplayTags específicos do slot */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil Slot")
    FGameplayTagContainer SlotTags;

    FSigilSlotData()
    {
        EquippedSigil = nullptr;
        bIsUnlocked = false;
        UnlockLevel = 1;
        EquipTimestamp = 0.0f;
        bReadyForFusion = false;
        FusionMultiplier = 1.0f;
    }
};

/**
 * Estrutura para configuração de fusão de sígilos
 * Define como sígilos se fundem após 6 minutos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilFusionConfig
{
    GENERATED_BODY()

    /** Tempo em segundos para fusão automática */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Config")
    float FusionTimeSeconds;

    /** Multiplicadores por raridade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Config")
    TMap<ESigilRarity, float> RarityMultipliers;

    /** GameplayEffect aplicado durante fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Config")
    TSubclassOf<UGameplayEffect> FusionEffect;

    /** Sistema de partículas para fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Config")
    TObjectPtr<UNiagaraSystem> FusionVFX;

    /** Som de fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Config")
    TObjectPtr<USoundBase> FusionSound;

    /** Tags aplicadas durante fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion Config")
    FGameplayTagContainer FusionTags;

    FSigilFusionConfig()
    {
        FusionTimeSeconds = 360.0f; // 6 minutos
        FusionEffect = nullptr;
        FusionVFX = nullptr;
        FusionSound = nullptr;
        
        // Configurar multiplicadores padrão
        RarityMultipliers.Add(ESigilRarity::Common, 1.2f);
        RarityMultipliers.Add(ESigilRarity::Rare, 1.4f);
        RarityMultipliers.Add(ESigilRarity::Epic, 1.6f);
        RarityMultipliers.Add(ESigilRarity::Legendary, 2.0f);
    }
};

/**
 * Estrutura para estatísticas do sistema de sígilos
 * Usada para debugging e balanceamento
 */
USTRUCT(BlueprintType)
struct AURACRON_API FSigilSystemStats
{
    GENERATED_BODY()

    /** Total de sígilos equipados */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalEquippedSigils;

    /** Total de sígilos fundidos */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalFusedSigils;

    /** Poder total dos sígilos */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float TotalSigilPower;

    /** Tempo médio para fusão */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageFusionTime;

    /** Total de reforges realizados */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalReforges;

    /** Sígilos por raridade */
    TMap<ESigilRarity, int32> SigilsByRarity;

    /** Sígilos por tipo */
    TMap<ESigilType, int32> SigilsByType;

    FSigilSystemStats()
    {
        TotalEquippedSigils = 0;
        TotalFusedSigils = 0;
        TotalSigilPower = 0.0f;
        AverageFusionTime = 0.0f;
        TotalReforges = 0;
    }
};

/**
 * Delegate para eventos do sistema de sígilos
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilEvent, ASigilItem*, Sigil, int32, SlotIndex, FGameplayTag, EventTag);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilFusion, ASigilItem*, Sigil, float, Multiplier);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigilSlotUnlocked, int32, SlotIndex);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigilStatsChanged, const FSigilSystemStats&, NewStats);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnExclusiveAbilityActivated, ESigilSubType, SubType, float, CooldownDuration);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnExclusiveAbilityCooldownChanged, ESigilSubType, SubType);

/**
 * Componente principal para gerenciar sistema de sígilos
 * Responsável por equipar, desequipar, fusão automática e replicação
 * Suporta MOBA 5x5 com 10 jogadores simultâneos
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType)
class AURACRON_API USigilManagerComponent : public UActorComponent
{
    GENERATED_BODY()

    // Friend classes para acesso aos métodos Server*
    friend class USigilReplicationManager;
    friend class USigilWidget;
    friend class USigilSlotWidget;

public:
    USigilManagerComponent();

    // UActorComponent Interface
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    // ========================================
    // CONFIGURAÇÃO DO SISTEMA
    // ========================================

    /** Configuração de fusão de sígilos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil System")
    FSigilFusionConfig FusionConfig;

    /** Número máximo de slots de sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil System", meta = (ClampMin = "1", ClampMax = "6"))
    int32 MaxSigilSlots;

    /** Se fusão automática está habilitada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil System")
    bool bAutoFusionEnabled;

    /** Se notificações visuais estão habilitadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil System")
    bool bVisualNotificationsEnabled;

    /** Cooldown para reforge em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil System")
    float ReforgeCooldownSeconds;

    /** GameplayTags do sistema */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigil System")
    FGameplayTagContainer SystemTags;

    // ========================================
    // DADOS REPLICADOS
    // ========================================

    /** Array de slots de sigilo (replicado) */
    UPROPERTY(ReplicatedUsing = OnRep_SigilSlots, BlueprintReadOnly, Category = "Sigil Data")
    TArray<FSigilSlotData> SigilSlots;

    /** Estatísticas do sistema (replicado) */
    UPROPERTY(ReplicatedUsing = OnRep_SystemStats, BlueprintReadOnly, Category = "Sigil Data")
    FSigilSystemStats SystemStats;

    /** Timestamp do último reforge */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Sigil Data")
    float LastReforgeTimestamp;

    /** Se o sistema está ativo */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Sigil Data")
    bool bSystemActive;

    // ========================================
    // EVENTOS PÚBLICOS
    // ========================================

    /** Evento disparado quando sigilo é equipado/desequipado */
    UPROPERTY(BlueprintAssignable, Category = "Sigil Events")
    FOnSigilEvent OnSigilEvent;

    /** Evento disparado quando fusão ocorre */
    UPROPERTY(BlueprintAssignable, Category = "Sigil Events")
    FOnSigilFusion OnSigilFusion;

    /** Evento disparado quando slot é desbloqueado */
    UPROPERTY(BlueprintAssignable, Category = "Sigil Events")
    FOnSigilSlotUnlocked OnSigilSlotUnlocked;

    /** Evento disparado quando estatísticas mudam */
    UPROPERTY(BlueprintAssignable, Category = "Sigil Events")
    FOnSigilStatsChanged OnSigilStatsChanged;

    UPROPERTY(BlueprintAssignable, Category = "Sigil Events")
    FOnExclusiveAbilityActivated OnExclusiveAbilityActivated;

    UPROPERTY(BlueprintAssignable, Category = "Sigil Events")
    FOnExclusiveAbilityCooldownChanged OnExclusiveAbilityCooldownChanged;

    // ========================================
    // FUNÇÕES PRINCIPAIS
    // ========================================

    /**
     * Equipa sigilo em slot específico
     * @param Sigil - Sigilo para equipar
     * @param SlotIndex - Índice do slot (0-5)
     * @return true se equipado com sucesso
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management", CallInEditor)
    bool EquipSigil(ASigilItem* Sigil, int32 SlotIndex);

    /**
     * Remove sigilo de slot específico
     * @param SlotIndex - Índice do slot
     * @return Sigilo removido (pode ser nullptr)
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management", CallInEditor)
    ASigilItem* UnequipSigil(int32 SlotIndex);

    /** RPC para reforge (Server) */
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerReforge(int32 SlotIndex);

    /**
     * Troca sígilos entre dois slots
     * @param FromSlot - Slot de origem
     * @param ToSlot - Slot de destino
     * @return true se troca foi bem-sucedida
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool SwapSigils(int32 FromSlot, int32 ToSlot);

    /**
     * Força fusão de sigilo específico
     * @param SlotIndex - Índice do slot
     * @return true se fusão foi aplicada
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool ForceFuseSigil(int32 SlotIndex);

    /**
     * Reforge sigilo (reset com nova configuração)
     * @param SlotIndex - Índice do slot
     * @return true se reforge foi bem-sucedido
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool ReforgeSigil(int32 SlotIndex);

    /**
     * Desbloqueia slot de sigilo
     * @param SlotIndex - Índice do slot
     * @return true se desbloqueado com sucesso
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool UnlockSigilSlot(int32 SlotIndex);

    // ========================================
    // FUNÇÕES DE CONSULTA
    // ========================================

    /**
     * Obtém sigilo equipado em slot específico
     * @param SlotIndex - Índice do slot
     * @return Sigilo equipado ou nullptr
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    ASigilItem* GetEquippedSigil(int32 SlotIndex) const;

    /**
     * Verifica se slot está desbloqueado
     * @param SlotIndex - Índice do slot
     * @return true se desbloqueado
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool IsSlotUnlocked(int32 SlotIndex) const;

    /**
     * Verifica se sigilo está pronto para fusão
     * @param SlotIndex - Índice do slot
     * @return true se pronto para fusão
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool IsSigilReadyForFusion(int32 SlotIndex) const;

    /**
     * Obtém tempo restante para fusão
     * @param SlotIndex - Índice do slot
     * @return Tempo em segundos (0 se pronto)
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    float GetTimeToFusion(int32 SlotIndex) const;

    /**
     * Obtém progresso da fusão
     * @param SlotIndex - Índice do slot
     * @return Progresso de 0.0 a 1.0
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    float GetFusionProgress(int32 SlotIndex) const;

    /**
     * Verifica se reforge está disponível
     * @return true se pode reforjar
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool CanReforge() const;

    /**
     * Obtém tempo restante para próximo reforge
     * @return Tempo em segundos
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    float GetReforgeTimeRemaining() const;

    /**
     * Obtém todos os sígilos equipados
     * @return Array de sígilos equipados
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    TArray<ASigilItem*> GetAllEquippedSigils() const;

    /**
     * Obtém sígilos por tipo
     * @param Type - Tipo de sigilo
     * @return Array de sígilos do tipo especificado
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    TArray<ASigilItem*> GetSigilsByType(ESigilType Type) const;

    /**
     * Obtém sígilos por raridade
     * @param Rarity - Raridade do sigilo
     * @return Array de sígilos da raridade especificada
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    TArray<ASigilItem*> GetSigilsByRarity(ESigilRarity Rarity) const;

    /**
     * Calcula poder total de todos os sígilos
     * @return Poder total combinado
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    float CalculateTotalSigilPower() const;

    /**
     * Verifica se há slots disponíveis
     * @return true se há slots livres
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool HasAvailableSlots() const;

    /**
     * Verifica se slot específico está disponível
     * @param SlotIndex - Índice do slot
     * @return true se slot está livre
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool IsSlotAvailable(int32 SlotIndex) const;

    /**
     * Dispara fusão para sigilo específico
     * @param Sigil - Sigilo para fusão
     * @return true se fusão foi iniciada
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    bool TriggerFusionForSigil(ASigilItem* Sigil);

    /**
     * Verifica se há sígilos compatíveis para fusão
     * @param Sigil - Sigilo de referência
     * @return true se há sígilos compatíveis
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool HasCompatibleSigilsForFusion(ASigilItem* Sigil) const;

    /**
     * Verifica se pode pagar pelo reforge
     * @param Sigil - Sigilo para reforge
     * @return true se tem recursos suficientes
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    bool CanAffordReforge(ASigilItem* Sigil) const;

    /**
     * Consome recursos necessários para reforge
     * @param Sigil - Sigilo sendo reforjado
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Management")
    void ConsumeReforgeResources(ASigilItem* Sigil);

    /**
     * Callback quando sigilo é equipado
     * @param Sigil - Sigilo equipado
     * @param SlotIndex - Índice do slot
     */
    void OnSigilEquipped(ASigilItem* Sigil, int32 SlotIndex);

    /**
     * Callback quando sigilo é desequipado
     * @param Sigil - Sigilo desequipado
     * @param SlotIndex - Índice do slot
     */
    void OnSigilUnequipped(ASigilItem* Sigil, int32 SlotIndex);

    /**
     * Obtém estatísticas do sistema
     * @return Estrutura com estatísticas atuais
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    FSigilSystemStats GetSystemStatistics() const;

    /**
     * Obtém sigilo por ID
     * @param SigilID - ID do sigilo para buscar
     * @return Sigilo encontrado ou nullptr
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Query")
    ASigilItem* GetSigilByID(int32 SigilID) const;

    // ========================================
    // FUNÇÕES DE HABILIDADES EXCLUSIVAS
    // ========================================

    /**
     * Ativa habilidade exclusiva de subtipo específico
     * @param SubType - Subtipo do sigilo
     * @return true se habilidade foi ativada
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Abilities")
    bool ActivateExclusiveAbility(ESigilSubType SubType);

    /**
     * Verifica se pode ativar habilidade exclusiva
     * @param SubType - Subtipo do sigilo
     * @return true se pode ativar
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Abilities")
    bool CanActivateExclusiveAbility(ESigilSubType SubType) const;

    /**
     * Obtém cooldown restante da habilidade exclusiva
     * @param SubType - Subtipo do sigilo
     * @return Tempo em segundos
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Abilities")
    float GetExclusiveAbilityCooldown(ESigilSubType SubType) const;

    /**
     * Obtém lista de habilidades exclusivas disponíveis
     * @return Array de subtipos disponíveis
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Abilities")
    TArray<ESigilSubType> GetAvailableExclusiveAbilities() const;

    /**
      * Força ativação de habilidade exclusiva (debug)
      * @param SubType - Subtipo do sigilo
      */
     UFUNCTION(BlueprintCallable, Category = "Sigil Debug")
     void DEBUG_ForceActivateExclusiveAbility(ESigilSubType SubType);

private:
     // ========================================
     // FUNÇÕES AUXILIARES PARA HABILIDADES EXCLUSIVAS
     // ========================================

     /** Inicializa sistema de habilidades exclusivas */
     void InitializeExclusiveAbilities();

     /** Implementações das habilidades específicas */
     bool ActivateMurallion();
     bool ActivateFracassoPrismal();
     bool ActivateSoproDeFluxo();

     /** Funções de cálculo para Murallion */
     float CalculateBarrierRadius() const;
     float CalculateBarrierDuration() const;
     float CalculateProtectionAmount() const;
     void SpawnBarrierVFX(const FVector& Location, float Radius, float Duration);
     void ApplyBarrierProtectionToAllies(const FVector& Location, float Radius, float Protection, float Duration);

     /** Funções de cálculo para Fracasso Prismal */
     float CalculateCooldownReduction() const;
     float CalculateDamageBonus() const;
     float CalculateBuffDuration() const;
     void ResetAbilityCooldowns(float ReductionPercentage);
     void SpawnPrismalEnergyVFX();

     /** Funções de cálculo para Sopro de Fluxo */
     float CalculateDashSpeed() const;
     float CalculateShieldAmount() const;
     float CalculateShieldDuration() const;
     AActor* FindClosestAlly() const;
     void ExecuteDashToTarget(AActor* Target, float Speed);
     void SpawnDashVFX(const FVector& TargetLocation);
     void SpawnShieldVFX(AActor* Target);

     /** Funções auxiliares gerais */
     float CalculateAbilityCooldown(ESigilSubType SubType, float BaseCooldown) const;
     bool HasSigilOfSubType(ESigilSubType SubType) const;
     int32 CountSigilsOfSubType(ESigilSubType SubType) const;
     bool IsExclusiveAbilityOnCooldown(ESigilSubType SubType) const;
     float GetExclusiveAbilityCooldownRemaining(ESigilSubType SubType) const;
     void SetExclusiveAbilityCooldown(ESigilSubType SubType, float CooldownTime);

     /** Funções de rede */
     UFUNCTION(Server, Reliable, WithValidation)
     void ServerActivateExclusiveAbility(ESigilSubType SubType);

     UFUNCTION(NetMulticast, Reliable)
    void MulticastPlayExclusiveAbilityVFX(ESigilSubType SubType, FVector Location = FVector::ZeroVector);

    // ========================================
    // FUNÇÕES DE VALIDAÇÃO
    // ========================================

    /**
     * Verifica se sigilo pode ser equipado em slot
     * @param Sigil - Sigilo para verificar
     * @param SlotIndex - Índice do slot
     * @return true se pode equipar
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Validation")
    bool CanEquipSigil(ASigilItem* Sigil, int32 SlotIndex) const;

    /**
     * Verifica se slot é válido
     * @param SlotIndex - Índice do slot
     * @return true se slot é válido
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Validation")
    bool IsValidSlotIndex(int32 SlotIndex) const;

    /**
     * Verifica se sistema está ativo
     * @return true se sistema está funcionando
     */
    UFUNCTION(BlueprintPure, Category = "Sigil Validation")
    bool IsSystemActive() const { return bSystemActive; }

    // ========================================
    // FUNÇÕES DE DEBUGGING
    // ========================================

    /**
     * Força desbloqueio de todos os slots (debug)
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Debug", CallInEditor)
    void DEBUG_UnlockAllSlots();

    /**
     * Força fusão de todos os sígilos (debug)
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Debug", CallInEditor)
    void DEBUG_ForceAllFusions();

    /**
     * Reset completo do sistema (debug)
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Debug", CallInEditor)
    void DEBUG_ResetSystem();

    /**
     * Mostra informações detalhadas no log
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Debug")
    void DEBUG_PrintSystemInfo();

    /**
     * Simula equipar sigilo aleatório (debug)
     */
    UFUNCTION(BlueprintCallable, Category = "Sigil Debug", CallInEditor)
    void DEBUG_EquipRandomSigil(int32 SlotIndex);

    /** RPC para fusão forçada (Server) */
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerForceFusion(int32 SlotIndex);

    /** RPC para equipar sigilo (Server) */
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerEquipSigil(ASigilItem* Sigil, int32 SlotIndex);

    /** RPC para desequipar sigilo (Server) */
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerUnequipSigil(int32 SlotIndex);

protected:
    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================

    /**
     * Inicializa slots de sigilo
     */
    void InitializeSigilSlots();

    /**
     * Atualiza timers de fusão
     */
    void UpdateFusionTimers(float DeltaTime);

    /**
     * Processa fusão automática
     */
    void ProcessAutoFusion(int32 SlotIndex);

    /**
     * Aplica efeitos de sigilo
     */
    void ApplySigilEffects(ASigilItem* Sigil, bool bApply);

    /**
     * Atualiza estatísticas do sistema
     */
    void UpdateSystemStatistics();

    /**
     * Valida configuração do sistema
     */
    bool ValidateSystemConfiguration() const;

    /**
     * Obtém AbilitySystemComponent do owner
     */
    UAbilitySystemComponent* GetOwnerAbilitySystemComponent() const;

    /**
     * Obtém SigilAttributeSet do owner
     */
    USigilAttributeSet* GetOwnerSigilAttributeSet() const;

    // ========================================
    // FUNÇÕES DE REPLICAÇÃO
    // ========================================

    /** Chamado quando SigilSlots replica */
    UFUNCTION()
    void OnRep_SigilSlots();

    /** Chamado quando SystemStats replica */
    UFUNCTION()
    void OnRep_SystemStats();

    // ========================================
    // FUNÇÕES RPC
    // ========================================

    /** RPC para notificação de fusão (Multicast) */
    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifyFusion(int32 SlotIndex, float Multiplier);

    /** RPC para efeitos visuais (Multicast) */
    UFUNCTION(NetMulticast, Reliable)
    void MulticastPlayVFX(int32 SlotIndex, FGameplayTag VFXTag);

private:
    // ========================================
    // VARIÁVEIS INTERNAS
    // ========================================

    /** Cache do AbilitySystemComponent */
    UPROPERTY()
    TObjectPtr<UAbilitySystemComponent> CachedAbilitySystemComponent;

    /** Cache do SigilAttributeSet */
    UPROPERTY()
    TObjectPtr<USigilAttributeSet> CachedSigilAttributeSet;

    // Exclusive Abilities Management
    UPROPERTY()
    TMap<ESigilSubType, FGameplayAbilitySpecHandle> ExclusiveAbilityHandles;

    UPROPERTY()
    TMap<ESigilSubType, float> ExclusiveAbilityCooldowns;

    UPROPERTY()
    TMap<ESigilSubType, TSubclassOf<class USigilAbilityBase>> ExclusiveAbilityClasses;

    /** Componentes de VFX para cada slot */
    UPROPERTY()
    TArray<TObjectPtr<UNiagaraComponent>> SlotVFXComponents;

    /** Handles de GameplayEffects ativos */
    TArray<FActiveGameplayEffectHandle> ActiveSigilEffects;

    /** Timer para validação periódica */
    FTimerHandle ValidationTimerHandle;

    /** Timestamp da última atualização */
    float LastUpdateTimestamp;

    /** Se componente foi inicializado */
    bool bIsInitialized;

    /** Se o level do jogador foi verificado */
    bool bPlayerLevelChecked;

    /** Level atual do jogador */
    int32 PlayerLevel;

    /** Cache do VFXManager */
    UPROPERTY()
    TObjectPtr<class USigilVFXManager> CachedVFXManager;

    // ========================================
    // FUNÇÕES AUXILIARES PRIVADAS
    // ========================================

    /** Verificar level do jogador para desbloqueio do sistema */
    void CheckPlayerLevelForSigilUnlock();

    /** Inicializar componentes VFX dos slots */
    void InitializeSlotVFXComponents();

    /** Atualizar componentes VFX */
    void UpdateVFXComponents(float DeltaTime);

    /** RPC para swap de sígilos */
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerSwapSigils(int32 FromSlot, int32 ToSlot);

    /** Multicast para notificar swap */
    UFUNCTION(NetMulticast, Reliable)
    void MulticastNotifySwap(int32 FromSlot, int32 ToSlot);

    // ========================================
    // CONSTANTES
    // ========================================

    /** Tempo de validação periódica em segundos */
    static constexpr float VALIDATION_INTERVAL = 5.0f;

    /** Número máximo de slots suportados */
    static constexpr int32 ABSOLUTE_MAX_SLOTS = 6;

    /** Cooldown padrão de reforge em segundos - conforme documentação AURACRON */
    static constexpr float DEFAULT_REFORGE_COOLDOWN = 120.0f; // 2 minutos
};